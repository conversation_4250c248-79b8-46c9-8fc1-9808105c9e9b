const AWS = require('aws-sdk');
const config = require("../../config/config");
const dynamo = new AWS.DynamoDB.DocumentClient({
  endpoint: config.dynamodb.endpoint,
});
const tableName = config.dynamodb.acctTable;

async function getAccountDetail(accountSid) {
  const params = {
    TableName: tableName,
    IndexName: 'account_sid-index',
    KeyConditionExpression: "account_sid = :sid",
    ExpressionAttributeValues: {
      ":sid": accountSid
    }
  };

  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

module.exports = {
  getAccountDetail,
};