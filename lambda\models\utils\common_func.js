'use strict';

var moment = require('moment-timezone');

var findReplaceBulk = function(str, replaceArray) {
  for (var key in replaceArray) {
      // eslint-disable-next-line no-prototype-builtins
      if (!replaceArray.hasOwnProperty(key)) {
          continue;
      }

      str = str.replace(new RegExp(key, "g"), replaceArray[key]);
  }

  return str;
}

var getOffsetDelta = function(tz) {
  var z = moment.tz.zone(tz);
  var i = z._index(moment());
  return i < z.offsets.length - 1 ? Math.abs(z.offsets[i] - z.offsets[i + 1]) : 0;
}

var stripslashes = function(str) {
  return (str + '')
    .replace(/\\(.?)/g, function(s, n1) {
      switch (n1) {
        case '\\':
          return '\\'
        case '0':
          return '\u0000'
        case '':
          return ''
        default:
          return n1
      }
    })
}

module.exports = {
  stripslashes: stripslashes,
  getOffsetDelta: getOffsetDelta,
  findReplaceBulk: findReplaceBulk
}
