<?php
/**
 * Call model
 *
 * @category Call
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Models\DynamoModel;

/**
 * Call model
 *
 * @category Call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Call")
 */
class Call extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait, CallPotential\CPCommon\Models\DynamoModel;

    /**
     * Table name
     *
     * @var string
     */
    public $table = 'call_logs';

    /**
     * Log Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $log_id;

    /**
     * Lead Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $lead_id;

    /**
     * Twilio Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $twilio_id;

    /**
     * Account Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $account_id;

    /**
     * Call number
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $call_number;

    /**
     * Caller name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256 nullable=true)
     */
    protected $caller_name;

    /**
     * Call name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=25, nullable=true)
     */
    protected $call_name;

    /**
     * Datestamp
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $datestamp;

    /**
     * Call type
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $call_type;

    /**
     * Call destination
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $call_destination;

    /**
     * Roll over index
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $rollover_index;

    /**
     * Answered by
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $answered_by;

    /**
     * Ad id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $ad_id;

    /**
     * Location Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $location_id;

    /**
     * Employee Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $employee_id;

    /**
     * Customer type
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $customer_type;

    /**
     * Customer name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $customer_name;

    /**
     * Customer id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $customer_id;

    /**
     * Current route step
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string" nullable=true)
     */
    protected $current_route_step;

    /**
     * Is route complete
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="boolean")
     */
    protected $is_route_complete;

    /**
     * Is auto call
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="boolean")
     */
    protected $is_auto_call;

    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return $this
     */
    public function setLogId(int $log_id)
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field lead_id
     *
     * @param integer $lead_id value to set
     *
     * @return $this
     */
    public function setLeadId(int $lead_id)
    {
        $this->lead_id = $lead_id;

        return $this;
    }

    /**
     * Method to set the value of field twilio_id
     *
     * @param string $twilio_id value to set
     *
     * @return $this
     */
    public function setTwilioId(string $twilio_id)
    {
        $this->twilio_id = $twilio_id;

        return $this;
    }

    /**
     * Method to set the value of field account_id
     *
     * @param integer $account_id value to set
     *
     * @return $this
     */
    public function setAccountId(int $account_id)
    {
        $this->account_id = $account_id;

        return $this;
    }

    /**
     * Method to set the value of field call_number
     *
     * @param string $call_number value to set
     *
     * @return $this
     */
    public function setCallNumber(string $call_number)
    {
        $this->call_number = $call_number;

        return $this;
    }

    /**
     * Method to set the value of field caller_name
     *
     * @param string $caller_name value to set
     *
     * @return $this
     */
    public function setCallerName(string $caller_name)
    {
        $this->caller_name = $caller_name;

        return $this;
    }

    /**
     * Method to set the value of field call_name
     *
     * @param string $call_name value to set
     *
     * @return $this
     */
    public function setCallName(string $call_name)
    {
        $this->call_name = $call_name;

        return $this;
    }

    /**
     * Method to set the value of field datestamp
     *
     * @param string $datestamp value to set
     *
     * @return $this
     */
    public function setDatestamp(string $datestamp)
    {
        $this->datestamp = $datestamp;

        return $this;
    }

    /**
     * Method to set the value of field call_type
     *
     * @param string $call_type value to set
     *
     * @return $this
     */
    public function setCallType(string $call_type)
    {
        $this->call_type = $call_type;

        return $this;
    }

    /**
     * Method to set the value of field call_destination
     *
     * @param string $call_destination value to set
     *
     * @return $this
     */
    public function setCallDestination(string $call_destination)
    {
        $this->call_destination = $call_destination;

        return $this;
    }

    /**
     * Method to set the value of field rollover_index
     *
     * @param integer $rollover_index value to set
     *
     * @return $this
     */
    public function setRolloverIndex(int $rollover_index)
    {
        $this->rollover_index = $rollover_index;

        return $this;
    }

    /**
     * Method to set the value of field answered_by
     *
     * @param string $answered_by value to set
     *
     * @return $this
     */
    public function setAnsweredBy(string $answered_by)
    {
        $this->answered_by = $answered_by;

        return $this;
    }

    /**
     * Method to set the value of field ad_id
     *
     * @param integer $ad_id value to set
     *
     * @return $this
     */
    public function setAdId(int $ad_id)
    {
        $this->ad_id = $ad_id;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(int $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_id
     *
     * @param integer $employee_id value to set
     *
     * @return $this
     */
    public function setEmployeeId(int $employee_id)
    {
        $this->employee_id = $employee_id;

        return $this;
    }

    /**
     * Method to set the value of field customer_type
     *
     * @param string $customer_type value to set
     *
     * @return $this
     */
    public function setCustomerType(string $customer_type)
    {
        $this->customer_type = $customer_type;

        return $this;
    }

    /**
     * Method to set the value of field customer_name
     *
     * @param string $customer_name value to set
     *
     * @return $this
     */
    public function setCustomerName(string $customer_name)
    {
        $this->customer_name = $customer_name;

        return $this;
    }

    /**
     * Method to set the value of field customer_id
     *
     * @param string $customer_id value to set
     *
     * @return $this
     */
    public function setCustomerId(string $customer_id)
    {
        $this->customer_id = $customer_id;

        return $this;
    }

    /**
     * Method to set the value of field current_route_step
     *
     * @param string $current_route_step value to set
     *
     * @return $this
     */
    public function setCurrentRouteStep(string $current_route_step)
    {
        $this->current_route_step = $current_route_step;

        return $this;
    }

    /**
     * Method to set the value of field is_route_complete
     *
     * @param integer $is_route_complete value to set
     *
     * @return $this
     */
    public function setIsRouteComplete(int $is_route_complete)
    {
        $this->is_route_complete = $is_route_complete;

        return $this;
    }

    /**
     * Method to set the value of field is_auto_call
     *
     * @param integer $is_auto_call value to set
     *
     * @return $this
     */
    public function setIsAutoCall(int $is_auto_call)
    {
        $this->is_auto_call = $is_auto_call;

        return $this;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field lead_id
     *
     * @return integer
     */
    public function getLeadId(): int
    {
        return $this->lead_id;
    }

    /**
     * Returns the value of field twilio_id
     *
     * @return string
     */
    public function getTwilioId(): string
    {
        return $this->twilio_id;
    }

    /**
     * Returns the value of field account_id
     *
     * @return integer
     */
    public function getAccountId(): int
    {
        return $this->account_id;
    }

    /**
     * Returns the value of field call_number
     *
     * @return string
     */
    public function getCallNumber(): string
    {
        return $this->call_number;
    }

    /**
     * Returns the value of field caller_name
     *
     * @return string
     */
    public function getCallerName(): string
    {
        return $this->caller_name;
    }

    /**
     * Returns the value of field call_name
     *
     * @return string
     */
    public function getCallName(): string
    {
        return $this->call_name;
    }

    /**
     * Returns the value of field datestamp
     *
     * @return string
     */
    public function getDatestamp(): string
    {
        return $this->datestamp;
    }

    /**
     * Returns the value of field call_type
     *
     * @return string
     */
    public function getCallType(): string
    {
        return $this->call_type;
    }

    /**
     * Returns the value of field call_destination
     *
     * @return string
     */
    public function getCallDestination(): string
    {
        return $this->call_destination;
    }

    /**
     * Returns the value of field rollover_index
     *
     * @return integer
     */
    public function getRolloverIndex(): int
    {
        return $this->rollover_index;
    }

    /**
     * Returns the value of field answered_by
     *
     * @return string
     */
    public function getAnsweredBy(): string
    {
        return $this->answered_by;
    }

    /**
     * Returns the value of field ad_id
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field employee_id
     *
     * @return integer
     */
    public function getEmployeeId(): int
    {
        return $this->employee_id;
    }

    /**
     * Returns the value of field customer_type
     *
     * @return string
     */
    public function getCustomerType(): string
    {
        return $this->customer_type;
    }

    /**
     * Returns the value of field customer_name
     *
     * @return string
     */
    public function getCustomerName(): string
    {
        return $this->customer_name;
    }

    /**
     * Returns the value of field customer_id
     *
     * @return string
     */
    public function getCustomerId(): string
    {
        return $this->customer_id;
    }

    /**
     * Returns the value of field current_route_step
     *
     * @return string
     */
    public function getCurrentRouteStep(): string
    {
        return $this->current_route_step;
    }

    /**
     * Returns the value of field is_route_complete
     *
     * @return integer
     */
    public function getIsRouteComplete(): int
    {
        return $this->is_route_complete;
    }

    /**
     * Returns the value of field is_auto_call
     *
     * @return integer
     */
    public function getIsAutoCall(): int
    {
        return $this->is_auto_call;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setConnectionService('dbLegacy');
        $this->setSource("call_logs");
    }


    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallDetail[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
