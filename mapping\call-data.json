{"call-route-config": {"_source": {"enabled": true}, "properties": {"user_id": {"type": "long"}, "config": {"type": "object", "enabled": false}, "name": {"type": "text", "analyzer": "standard"}, "cascade_time": {"type": "integer"}, "is_active": {"type": "integer"}, "is_default": {"type": "integer"}, "created_date": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "updated_date": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "attributes": {"type": "nested"}}}, "call-center": {"_source": {"enabled": true}, "properties": {"id": {"type": "long"}, "user_id": {"type": "long"}, "inactivity_time": {"type": "date", "format": "mm:ss"}, "timedout_queue_sid": {"type": "text"}, "default_queue_sid": {"type": "text"}, "on_cascade_inactive": {"type": "integer"}, "on_reject_inactive": {"type": "integer"}, "cooldown_period": {"type": "integer"}, "created": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "updated": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "properties": {"type": "nested"}}}, "ip-acl": {"_source": {"enabled": true}, "properties": {"user_id": {"type": "long"}, "ip_acl_sid": {"type": "text"}, "ip_friendly_name": {"type": "text"}, "ip_address": {"type": "text"}, "ip_address_sid": {"type": "text"}, "active": {"type": "integer"}, "date_created": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "date_remove": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "properties": {"type": "nested"}}}}