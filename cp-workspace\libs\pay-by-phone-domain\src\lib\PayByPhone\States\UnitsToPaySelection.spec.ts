import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { UnitsToPaySelection } from './UnitsToPaySelection';
import { Locale } from '@cp-workspace/shared';
import { Customer } from '@cp-workspace/shared';

describe('UnitsToPaySelection', () => {
  let unitsToPaySelection: UnitsToPaySelection;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UnitsToPaySelection],
    }).compile();

    unitsToPaySelection = module.get<UnitsToPaySelection>(UnitsToPaySelection);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.UnitsToPaySelection,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        matchedTenants: [
          { first_name: '<PERSON>', last_name: '<PERSON><PERSON>', ledgers: [{ unit_name: 'A101', amount_owed: '100', paid_thru_date: '2021-12-31' }] as any } as any,
          { first_name: 'Jane', last_name: 'Smith', ledgers: [{ unit_name: 'B202', amount_owed: '200', paid_thru_date: '2022-01-31' }] as any } as any,
        ],
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { 
        sayInLocale: jest.fn(), 
        gatherWithLocaleSay: jest.fn(),
        sayAccountFoundDetails: jest.fn(),
        sayTotalBalance: jest.fn()
      } as any,
    };
  });

  describe('handler', () => {
    it('should return next state as UnitsToPayPrompt when no response from customer', async () => {
      context.request.Digits = undefined;

      const response: PayByPhoneStateHandlerResponse = await unitsToPaySelection.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrompt);
    });

    it('should return next state as UnitsToPayPrompt when start over response is selected', async () => {
      context.request.Digits = '9';

      const response: PayByPhoneStateHandlerResponse = await unitsToPaySelection.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrompt);
    });

    describe('pay all units', () => {
      it('should return next state as ConfirmCustomerInfo', async () => {
        context.request.Digits = '1';
  
        const response: PayByPhoneStateHandlerResponse = await unitsToPaySelection.handler(context);
  
        expect(response.nextState).toBe(PayByPhoneState.ConfirmCustomerInfo);
        expect(context.storage.selectedUnits).toEqual(Customer.getDelinquentUnits(context.storage.matchedTenants!));
      });
  
      it('should calculate total balance and announce it when tenants are matched', async () => {
        context.request.Digits = '1';
        jest.spyOn(Customer, 'calculateTotalBalance').mockReturnValue(500); // Mock total balance calculation
        await unitsToPaySelection.handler(context);
        expect(Customer.calculateTotalBalance).toHaveBeenCalled();
        expect(context.twilioResponse.sayTotalBalance).toHaveBeenCalledWith(context.storage);
      });
    });

    it('should return next state as UnitsToPayPrompt when invalid unit selection is made', async () => {
      context.request.Digits = '5';

      const response: PayByPhoneStateHandlerResponse = await unitsToPaySelection.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrompt);
    });

    it('should return next state as ConfirmCustomerInfo when valid unit selection is made for delinquent unit', async () => {
      context.request.Digits = '2';

      const response: PayByPhoneStateHandlerResponse = await unitsToPaySelection.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.ConfirmCustomerInfo);
      expect(context.storage.selectedUnits).toEqual([{ unit_name: 'A101', amount_owed: '100', paid_thru_date: '2021-12-31' }]);
      expect(context.twilioResponse.sayAccountFoundDetails).toHaveBeenCalledWith(
        context.storage.selectedUnit,
        context.storage.selectedTenant,
        context.storage
      );
    });

    it('should format unit balance with convenience fee correctly to two decimal places', async () => {
      context.request.Digits = '2';
      context.storage.convenienceFee = 5.5;

      await unitsToPaySelection.handler(context);

      expect(context.twilioResponse.sayAccountFoundDetails).toHaveBeenCalledWith(
        context.storage.selectedUnit,
        context.storage.selectedTenant,
        context.storage
      );
    });

    it('should return next state as ConfirmCustomerInfo when valid unit selection is made for non-delinquent unit', async () => {
      jest.spyOn(Customer, 'isDelinquentUnit').mockReturnValue(false);
      context.request.Digits = '3';

      const response: PayByPhoneStateHandlerResponse = await unitsToPaySelection.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.ConfirmCustomerInfo);
      expect(context.storage.selectedUnits).toEqual([{ unit_name: 'B202', amount_owed: '200', paid_thru_date: '2022-01-31' }]);
    });
  });
});
