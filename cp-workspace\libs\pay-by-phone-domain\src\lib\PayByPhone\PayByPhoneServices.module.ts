import { Module } from '@nestjs/common';
import { AsyncWorkersService, DomainEventsService, SharedServicesModule, SharedServicesModuleOptions } from '@cp-workspace/shared';
import { PayByPhoneService } from './PayByPhone.service';
import { PayByPhoneContextService } from './PayByPhoneContext.service';
import { HttpModule } from '@nestjs/axios';
import { PayByPhoneStateHandlerService } from './PayByPhoneStateHandler.service';
import { ConfigModule } from '@nestjs/config';
import { I18nTranslationService } from './I18nTranslation.service';
import { PayByPhoneStatesModule } from './Generated/PayByPhoneStatesModule.generated';
import { PayByPhoneStorageService } from './PayByPhoneStorage.service';
import { I18nServicesModule } from './I18nServices.module';

export interface PayByPhoneServicesModuleOptions {
  sharedServicesOptions: SharedServicesModuleOptions;
}

@Module({})
export class PayByPhoneServicesModule {
  static forRoot(options: PayByPhoneServicesModuleOptions) {
    return {
      module: PayByPhoneServicesModule,
      imports: [
        HttpModule,
        ConfigModule,
        PayByPhoneStatesModule,
        I18nServicesModule,
        SharedServicesModule.forRoot(options.sharedServicesOptions),
      ],
      providers: [
        PayByPhoneService,
        PayByPhoneContextService,
        PayByPhoneStateHandlerService,
        PayByPhoneStorageService,
        I18nTranslationService,
        DomainEventsService,
        AsyncWorkersService,
      ],
      exports: [
        PayByPhoneService,
        PayByPhoneContextService,
        PayByPhoneStateHandlerService,
        PayByPhoneStorageService,
        I18nTranslationService,
        DomainEventsService,
        AsyncWorkersService,
      ],
    };
  }
}
