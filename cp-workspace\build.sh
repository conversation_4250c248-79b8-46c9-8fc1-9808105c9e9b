#!/bin/sh
##################################################
# This script is executed from within the Docker container. 
# It is responsible for building the dist that includes all 
# the lambda functions that are part of this cp-workspace. 
#
# After the build process is complete, this script will exit 
# and the container will stop, giving control back to the 
# larger deployment process. 
#
# From there, build output will be packaged into zip files for
# deployment to AWS Lambda.
##################################################
#
echo "Building the Communications Api..."
echo "Installing dependencies..."
npm install

echo "Building the lambda functions..."
npx nx run lambda-functions:build-lambda

echo "Prepare the bundle dependencies..."
npm run bundle-dependencies:create-manifest

echo "Install the bundle dependencies..."
npm install --prefix ./dist/apps/lambda-functions

echo "Build complete!"
echo "Copying output files to host transfer directory..."
mkdir -p /app/tmp/output

echo "--> node_modules"
cp -r dist/apps/lambda-functions/node_modules /app/tmp/output
rm -rf dist/apps/lambda-functions/node_modules

echo "--> package.json"
cp dist/apps/lambda-functions/package.json /app/tmp/output
rm -rf dist/apps/lambda-functions/package.json

echo "--> dist"
cp -r dist /app/tmp/output

echo "Copy complete!"
echo "Exiting..."
