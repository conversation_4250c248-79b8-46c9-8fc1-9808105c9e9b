<?php
/**
 * CallHistory model
 *
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: cwalker
 * Date: 4/4/17
 * Time: 11:58 PM
 *
 * @category CallHistory
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Models\S3DataTrait;
use CallPotential\CPCommon\Models\ElasticSearchModel;
use ONGR\ElasticsearchDSL\Search;
use \ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use \ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use \ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use \ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\ExistsQuery;
use \ONGR\ElasticsearchDSL\Sort\FieldSort;
use ONGR\ElasticsearchDSL\Query\FullText\QueryStringQuery;
use ONGR\ElasticsearchDSL\Query\Joining\NestedQuery;

/**
 * CallHistory model
 *
 * @category CallHistory
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="CallHistory")
 */
class CallHistory extends \Phalcon\Di\Injectable
{
    use ElasticSearchModel{
        getIndexName as protected esTraitGetIndexName;
        findById as protected esTraitFindById;
    }
    use S3DataTrait, LoggerTrait, JsonModelTrait;

    /**
     * Primary key Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $id;

    /**
     * Log Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $log_id;

    /**
     * Lead Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $lead_id;

    /**
     * Elasticsearch lead Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $es_lead_id;

    /**
     * Twilio Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $twilio_id;

    /**
     * Account Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $account_id;

    /**
     * Recording SID
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $recording_sid;

    /**
     * Call number
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $call_number;

    /**
     * Caller name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256 nullable=true)
     */
    protected $caller_name;

    /**
     * Call name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=25, nullable=true)
     */
    protected $call_name;

    /**
     * Datestamp
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $datestamp;

    /**
     * Call type
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $call_type;

    /**
     * Call processed by
     *
     * @var string
     *
     * @SWG\Property(type="string",enum={"employee","agent","system"})
     * @Column(type="string",nullable=true)
     */
    protected $call_processed_by;

    /**
     * Recording URL
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $recording_url;

    /**
     * Duration
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $duration;

    /**
     * Call destination
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $call_destination;

    /**
     * Rollover Index
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $rollover_index;

    /**
     * Answered by
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $answered_by;

    /**
     * Ad Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $ad_id;

    /**
     * Location Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $location_id;

    /**
     * Employee Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $employee_id;

    /**
     * Employee name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $employee_name;

    /**
     * Is excluded
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="boolean")
     */
    protected $is_excluded;

    /**
     * Customer type
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $customer_type;

    /**
     * Customer name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $customer_name;

    /**
     * Customer Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $customer_id;

    /**
     * Elasticsearch customer Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $es_tenant_id;

    /**
     * Is auto call
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="boolean")
     */
    protected $is_auto_call;

    /**
     * Neighbor location Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $neighbor_location_id;

    /**
     * Grade
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=1, nullable=true)
     */
    protected $grade;

    /**
     * Halloffame
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="boolean")
     */
    protected $halloffame;

    /**
     * Gradesheet
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $gradesheet;

    /**
     * Gradesheet Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $gradesheet_id;

    /**
     * Customer card
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $customer_card;

    /**
     * Gradesheet points appointed
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $gradesheet_points_appointed;

    /**
     * Gradesheet points possible
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $gradesheet_points_possible;

    /**
     * Manager score
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $manager_score;

    /**
     * Confirm action
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=10, nullable=true)
     */
    protected $confirm_action;

    /**
     * Call status
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $call_status;

    /**
     * Queue time
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $queue_time;

    /**
     * Number of events
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $num_events;

    /**
     * Number of agents
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $num_agents;

    /**
     * Number of quqeue
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $num_queues;

    /**
     * Confirmed by
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $confirmed_by;

    /**
     * Method to set the value of field id
     *
     * @param string $id value to set
     *
     * @return $this
     */
    public function setId(string $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return $this
     */
    public function setLogId(int $log_id)
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field lead_id
     *
     * @param integer $lead_id value to set
     *
     * @return $this
     */
    public function setLeadId(int $lead_id)
    {
        $this->lead_id = $lead_id;

        return $this;
    }

    /**
     * Method to set the value of field twilio_id
     *
     * @param string $twilio_id value to set
     *
     * @return $this
     */
    public function setTwilioId(string $twilio_id)
    {
        $this->twilio_id = $twilio_id;

        return $this;
    }

    /**
     * Method to set the value of field account_id
     *
     * @param integer $account_id value to set
     *
     * @return $this
     */
    public function setAccountId(int $account_id)
    {
        $this->account_id = $account_id;

        return $this;
    }

    /**
     * Method to set the value of field recording_sid
     *
     * @param string $recording_sid value to set
     *
     * @return $this
     */
    public function setRecordingSid(string $recording_sid)
    {
        $this->recording_sid = $recording_sid;

        return $this;
    }

    /**
     * Method to set the value of field call_number
     *
     * @param string $call_number value to set
     *
     * @return $this
     */
    public function setCallNumber(string $call_number)
    {
        $this->call_number = $call_number;

        return $this;
    }

    /**
     * Method to set the value of field caller_name
     *
     * @param string $caller_name value to set
     *
     * @return $this
     */
    public function setCallerName(string $caller_name)
    {
        $this->caller_name = $caller_name;

        return $this;
    }

    /**
     * Method to set the value of field call_name
     *
     * @param string $call_name value to set
     *
     * @return $this
     */
    public function setCallName(string $call_name)
    {
        $this->call_name = $call_name;

        return $this;
    }

    /**
     * Method to set the value of field datestamp
     *
     * @param string $datestamp value to set
     *
     * @return $this
     */
    public function setDatestamp(string $datestamp)
    {
        $this->datestamp = $datestamp;

        return $this;
    }

    /**
     * Method to set the value of field call_type
     *
     * @param string $call_type value to set
     *
     * @return $this
     */
    public function setCallType(string $call_type)
    {
        $this->call_type = $call_type;

        return $this;
    }

    /**
     * Method to set the value of field call_processed_by
     *
     * @param string $call_processed_by value to set
     *
     * @return $this
     */
    public function setCallProcessedBy(string $call_processed_by)
    {
        $this->call_processed_by = $call_processed_by;

        return $this;
    }

    /**
     * Method to set the value of field recording_url
     *
     * @param string $recording_url value to set
     *
     * @return $this
     */
    public function setRecordingUrl(string $recording_url)
    {
        $this->recording_url = $recording_url;

        return $this;
    }

    /**
     * Method to set the value of field duration
     *
     * @param integer $duration value to set
     *
     * @return $this
     */
    public function setDuration(int $duration)
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * Method to set the value of field call_destination
     *
     * @param string $call_destination value to set
     *
     * @return $this
     */
    public function setCallDestination(string $call_destination)
    {
        $this->call_destination = $call_destination;

        return $this;
    }

    /**
     * Method to set the value of field rollover_index
     *
     * @param integer $rollover_index value to set
     *
     * @return $this
     */
    public function setRolloverIndex(int $rollover_index)
    {
        $this->rollover_index = $rollover_index;

        return $this;
    }

    /**
     * Method to set the value of field answered_by
     *
     * @param integer $answered_by value to set
     *
     * @return $this
     */
    public function setAnsweredBy(int $answered_by)
    {
        $this->answered_by = $answered_by;

        return $this;
    }

    /**
     * Method to set the value of field ad_id
     *
     * @param integer $ad_id value to set
     *
     * @return $this
     */
    public function setAdId(int $ad_id)
    {
        $this->ad_id = $ad_id;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(int $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_id
     *
     * @param integer $employee_id value to set
     *
     * @return $this
     */
    public function setEmployeeId(int $employee_id)
    {
        $this->employee_id = $employee_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_name
     *
     * @param string $employee_name value to set
     *
     * @return $this
     */
    public function setEmployeeName(string $employee_name)
    {
        $this->employee_name = $employee_name;

        return $this;
    }

    /**
     * Method to set the value of field is_excluded
     *
     * @param boolean $is_excluded value to set
     *
     * @return $this
     */
    public function setIsExcluded(bool $is_excluded)
    {
        $this->is_excluded = $is_excluded;

        return $this;
    }

    /**
     * Method to set the value of field customer_type
     *
     * @param integer $customer_type value to set
     *
     * @return $this
     */
    public function setCustomerType(int $customer_type)
    {
        $this->customer_type = $customer_type;

        return $this;
    }

    /**
     * Method to set the value of field customer_name
     *
     * @param string $customer_name value to set
     *
     * @return $this
     */
    public function setCustomerName(string $customer_name)
    {
        $this->customer_name = $customer_name;

        return $this;
    }

    /**
     * Method to set the value of field customer_id
     *
     * @param string $customer_id value to set
     *
     * @return $this
     */
    public function setCustomerId(string $customer_id)
    {
        $this->customer_id = $customer_id;

        return $this;
    }

    /**
     * Method to set the value of field is_auto_call
     *
     * @param boolean $is_auto_call value to set
     *
     * @return $this
     */
    public function setIsAutoCall(bool $is_auto_call)
    {
        $this->is_auto_call = $is_auto_call;

        return $this;
    }

    /**
     * Method to set the value of field neighbor_location_id
     *
     * @param integer $neighbor_location_id value to set
     *
     * @return $this
     */
    public function setNeighborLocationId(int $neighbor_location_id)
    {
        $this->neighbor_location_id = $neighbor_location_id;

        return $this;
    }

    /**
     * Method to set the value of field grade
     *
     * @param string $grade value to set
     *
     * @return $this
     */
    public function setGrade(string $grade)
    {
        $this->grade = $grade;

        return $this;
    }

    /**
     * Method to set the value of field halloffame
     *
     * @param boolean $halloffame value to set
     *
     * @return $this
     */
    public function setHalloffame(bool $halloffame)
    {
        $this->halloffame = $halloffame;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet
     *
     * @param string $gradesheet value to set
     *
     * @return $this
     */
    public function setGradesheet(string $gradesheet)
    {
        $this->gradesheet = $gradesheet;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_id
     *
     * @param integer $gradesheet_id value to set
     *
     * @return $this
     */
    public function setGradesheetId(int $gradesheet_id)
    {
        $this->gradesheet_id = $gradesheet_id;

        return $this;
    }

    /**
     * Method to set the value of field customer_card
     *
     * @param string $customer_card value to set
     *
     * @return $this
     */
    public function setCustomerCard(string $customer_card)
    {
        $this->customer_card = $customer_card;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_points_appointed
     *
     * @param integer $gradesheet_points_appointed value to set
     *
     * @return $this
     */
    public function setGradesheetPointsAppointed(int $gradesheet_points_appointed)
    {
        $this->gradesheet_points_appointed = $gradesheet_points_appointed;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_points_possible
     *
     * @param integer $gradesheet_points_possible value to set
     *
     * @return $this
     */
    public function setGradesheetPointsPossible(int $gradesheet_points_possible)
    {
        $this->gradesheet_points_possible = $gradesheet_points_possible;

        return $this;
    }

    /**
     * Method to set the value of field manager_score
     *
     * @param integer $manager_score value to set
     *
     * @return $this
     */
    public function setManagerScore(int $manager_score)
    {
        $this->manager_score = $manager_score;

        return $this;
    }

    /**
     * Method to set the value of field confirm_action
     *
     * @param string $confirm_action value to set
     *
     * @return $this
     */
    public function setConfirmAction(string $confirm_action)
    {
        $this->confirm_action = $confirm_action;

        return $this;
    }

    /**
     * Method to set the value of field call_status
     *
     * @param integer $call_status value to set
     *
     * @return $this
     */
    public function setCallStatus(int $call_status)
    {
        $this->call_status = $call_status;

        return $this;
    }

    /**
     * Method to set the value of field queue_time
     *
     * @param integer $queue_time value to set
     *
     * @return $this
     */
    public function setQueueTime(int $queue_time)
    {
        $this->queue_time = $queue_time;

        return $this;
    }

    /**
     * Method to set the value of field num_events
     *
     * @param integer $num_events value to set
     *
     * @return $this
     */
    public function setNumEvents(int $num_events)
    {
        $this->num_events = $num_events;

        return $this;
    }

    /**
     * Method to set the value of field num_agents
     *
     * @param integer $num_agents value to set
     *
     * @return $this
     */
    public function setNumAgents(int $num_agents)
    {
        $this->num_agents = $num_agents;

        return $this;
    }

    /**
     * Method to set the value of field num_queues
     *
     * @param integer $num_queues value to set
     *
     * @return $this
     */
    public function setNumQueues(int $num_queues)
    {
        $this->num_queues = $num_queues;

        return $this;
    }

    /**
     * Method to set the value of field confirmed_by
     *
     * @param integer $confirmed_by value to set
     *
     * @return $this
     */
    public function setConfirmedBy(int $confirmed_by)
    {
        $this->confirmed_by = $confirmed_by;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field lead_id
     *
     * @return integer
     */
    public function getLeadId(): int
    {
        return $this->lead_id;
    }

    /**
     * Returns the value of field twilio_id
     *
     * @return string
     */
    public function getTwilioId(): string
    {
        return $this->twilio_id;
    }

    /**
     * Returns the value of field account_id
     *
     * @return integer
     */
    public function getAccountId(): int
    {
        return $this->account_id;
    }

    /**
     * Returns the value of field recording_sid
     *
     * @return string
     */
    public function getRecordingSid(): string
    {
        return $this->recording_sid;
    }

    /**
     * Returns the value of field call_number
     *
     * @return string
     */
    public function getCallNumber(): string
    {
        return $this->call_number;
    }

    /**
     * Returns the value of field caller_name
     *
     * @return string
     */
    public function getCallerName(): string
    {
        return $this->caller_name;
    }

    /**
     * Returns the value of field call_name
     *
     * @return string
     */
    public function getCallName(): string
    {
        return $this->call_name;
    }

    /**
     * Returns the value of field datestamp
     *
     * @return string
     */
    public function getDatestamp(): string
    {
        return $this->datestamp;
    }

    /**
     * Returns the value of field call_type
     *
     * @return string
     */
    public function getCallType(): string
    {
        return $this->call_type;
    }

    /**
     * Returns the value of field call_processed_by
     *
     * @return string
     */
    public function getCallProcessedBy(): string
    {
        return $this->call_processed_by;
    }

    /**
     * Returns the value of field recording_url
     *
     * @return string
     */
    public function getRecordingUrl(): string
    {
        return $this->recording_url;
    }

    /**
     * Returns the value of field duration
     *
     * @return integer
     */
    public function getDuration(): int
    {
        return $this->duration;
    }

    /**
     * Returns the value of field call_destination
     *
     * @return string
     */
    public function getCallDestination(): string
    {
        return $this->call_destination;
    }

    /**
     * Returns the value of field rollover_index
     *
     * @return integer
     */
    public function getRolloverIndex(): int
    {
        return $this->rollover_index;
    }

    /**
     * Returns the value of field answered_by
     *
     * @return integer
     */
    public function getAnsweredBy(): int
    {
        return $this->answered_by;
    }

    /**
     * Returns the value of field ad_id
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field employee_id
     *
     * @return integer
     */
    public function getEmployeeId(): int
    {
        return $this->employee_id;
    }

    /**
     * Returns the value of field employee_name
     *
     * @return string
     */
    public function getEmployeeName(): string
    {
        return $this->employee_name;
    }

    /**
     * Returns the value of field is_excluded
     *
     * @return integer
     */
    public function getIsExcluded(): bool
    {
        return $this->is_excluded;
    }

    /**
     * Returns the value of field customer_type
     *
     * @return integer
     */
    public function getCustomerType(): int
    {
        return $this->customer_type;
    }

    /**
     * Returns the value of field customer_name
     *
     * @return string
     */
    public function getCustomerName(): string
    {
        return $this->customer_name;
    }

    /**
     * Returns the value of field customer_id
     *
     * @return string
     */
    public function getCustomerId(): string
    {
        return $this->customer_id;
    }

    /**
     * Returns the value of field is_auto_call
     *
     * @return integer
     */
    public function getIsAutoCall(): bool
    {
        return $this->is_auto_call;
    }

    /**
     * Returns the value of field neighbor_location_id
     *
     * @return integer
     */
    public function getNeighborLocationId(): int
    {
        return $this->neighbor_location_id;
    }

    /**
     * Returns the value of field grade
     *
     * @return string
     */
    public function getGrade(): string
    {
        return $this->grade;
    }

    /**
     * Returns the value of field halloffame
     *
     * @return integer
     */
    public function getHalloffame(): bool
    {
        return $this->halloffame;
    }

    /**
     * Returns the value of field gradesheet
     *
     * @return string
     */
    public function getGradesheet(): string
    {
        return $this->gradesheet;
    }

    /**
     * Returns the value of field gradesheet_id
     *
     * @return integer
     */
    public function getGradesheetId(): int
    {
        return $this->gradesheet_id;
    }

    /**
     * Returns the value of field customer_card
     *
     * @return string
     */
    public function getCustomerCard(): string
    {
        return $this->customer_card;
    }

    /**
     * Returns the value of field gradesheet_points_appointed
     *
     * @return integer
     */
    public function getGradesheetPointsAppointed(): int
    {
        return $this->gradesheet_points_appointed;
    }

    /**
     * Returns the value of field gradesheet_points_possible
     *
     * @return integer
     */
    public function getGradesheetPointsPossible(): int
    {
        return $this->gradesheet_points_possible;
    }

    /**
     * Returns the value of field manager_score
     *
     * @return integer
     */
    public function getManagerScore(): int
    {
        return $this->manager_score;
    }

    /**
     * Returns the value of field confirm_action
     *
     * @return string
     */
    public function getConfirmAction(): string
    {
        return $this->confirm_action;
    }

    /**
     * Returns the value of field call_status
     *
     * @return integer
     */
    public function getCallStatus(): int
    {
        return $this->call_status;
    }

    /**
     * Returns the value of field queue_time
     *
     * @return integer
     */
    public function getQueueTime(): int
    {
        return $this->queue_time;
    }

    /**
     * Returns the value of field num_events
     *
     * @return integer
     */
    public function getNumEvents(): int
    {
        return $this->num_events;
    }

    /**
     * Returns the value of field num_agents
     *
     * @return integer
     */
    public function getNumAgents(): int
    {
        return $this->num_agents;
    }

    /**
     * Returns the value of field num_queues
     *
     * @return integer
     */
    public function getNumQueues(): int
    {
        return $this->num_queues;
    }

    /**
     * Returns the value of field confirmed_by
     *
     * @return integer
     */
    public function getConfirmedBy(): int
    {
        return $this->confirmed_by;
    }

    /**
     * Constructor to initialize data
     */
    public function __construct()
    {
        $this->index = 'call-log';
        $this->type = "call-history";
        $this->s3DataType = 'call-log';
        $this->indexSuffix = '';
    }

    /**
     * Clear model object values
     *
     * @return void
     */
    public function clear()
    {
        $this->log_id = null;
        $this->user_id = null;
        $this->id = null;
        $this->properties = null;
    }

    /**
     * Get index name
     *
     * @return string
     */
    public function getIndexName(): string
    {
        return $this->index.$this->indexSuffix;
    }

    /**
     * FindById
     *
     * @param string $id     primary key Id
     * @param string $parent parent Id if exists
     *
     * @return array
     */
    public function findById(string $id, string $parent = null): array
    {
        unset($parent);
        $idsQuery = new IdsQuery([$id], ['type' => $this->type]);

        $search = new Search();
        $search->addQuery($idsQuery);

        $queryArray = $search->toArray();

        $this->index = $this->index.'*';
        $findData = $this->search($queryArray);
        $this->index = trim($this->index, '*');

        return $findData;
    }

    /**
     * FindById using current & previous month index
     * @param string $id     primary key Id
     * @param string $parent parent Id if exists
     *
     * @return array
     */
    public function findByCurrentPreviousMonthIndex(string $id, string $parent = null): array
    {
        unset($parent);
        $idsQuery = new IdsQuery([$id], ['type' => $this->type]);
        $getInitialIndex =  $this->index;

        $search = new Search();
        $search->addQuery($idsQuery);

        $queryArray = $search->toArray();
        $startDate = date("Y-m-d H:i:s", strtotime("-1 Months"));
        $endDate   = date("Y-m-d H:i:s");
        $this->setMultiIndex($startDate, $endDate);
        $findData = $this->search($queryArray);
        $this->index = $getInitialIndex;
        if (0 >= $findData['hits']['total']) {
            $findData = $this->findById($id);
        }

        return $findData;
    }

    /**
     * Get account calls
     *
     * @param integer    $accountId                account Id
     * @param string     $startDate                start date
     * @param string     $endDate                  end date
     * @param int|string $page                     page number
     * @param integer    $perPage                  per page records
     * @param string     $orderBy                  order by field name
     * @param string     $order                    order asc / desc
     * @param mixed      $locationId               location Id
     * @param string     $callType                 call type
     * @param string     $leadId                   lead Id
     * @param mixed      $employeeId               employee Id
     * @param string     $adId                     ad Id
     * @param string     $customerId               customer Id
     * @param string     $customerName             customer name
     * @param mixed      $callNumber               call number
     * @param string     $duration                 duration
     * @param string     $durationVal              duration value
     * @param string     $callDuration             call duration
     * @param string     $callDurationVal          call duration value
     * @param string     $recordingDuration        recording duration
     * @param string     $recordingDurationVal     recording duration value
     * @param string     $customerEmail            customer email
     * @param string     $logId                    Log Id
     * @param bool|null  $includeNeighborLocations include neighbor locations
     * @param boolean    $recordingUrlCheck        recording URL check
     * @param boolean    $hallOfFameCheck          hall Of Fame Check
     * @param string     $channelType              channel type (voice / video)
     *
     * @return array
     */
    public function getAccountCalls(
        int $accountId,
        string $startDate,
        string $endDate,
        $page = 0,
        int $perPage = 20,
        string $orderBy = null,
        string $order = 'ASC',
        $locationId = null,
        string $callType = null,
        string $leadId = null,
        $employeeId = null,
        string $adId = null,
        string $customerId = null,
        string $customerName = null,
        $callNumber = null,
        string $duration = null,
        string $durationVal = null,
        string $callDuration = null,
        string $callDurationVal = null,
        string $recordingDuration = null,
        string $recordingDurationVal = null,
        string $customerEmail = null,
        $logId = null,
        bool $includeNeighborLocations = null,
        bool $recordingUrlCheck,
        bool $hallOfFameCheck,
        string $channelType
    ): array {
        $search = new Search();
        $search->setSize($perPage);
        $search->setFrom(($page-1) * $perPage);

        $boolQuery = new BoolQuery();

        if (!empty($accountId)) {
            $boolQuery->add(new TermQuery('account_id', $accountId), BoolQuery::FILTER);
        }

        $dateRangeQuery = new RangeQuery(
            'datestamp',
            [
                'gte' => $startDate,
                'lte' => $endDate,
                'boost' => 2.0,
            ]
        );
        $boolQuery->add($dateRangeQuery, BoolQuery::FILTER);

        $boolQuery = $this->arithmeticBoolQuery('duration', $durationVal, $duration, $boolQuery);
        $boolQuery = $this->arithmeticBoolQuery(
            'call_duration',
            $callDurationVal,
            $callDuration,
            $boolQuery
        );

        if ($callType === 'unprocessed') {
            $callType = 'inbound';
            $recordingUrlCheck = true;
            $recordingDuration = '>';
            $recordingDurationVal = 0;
        }

        $boolQuery = $this->arithmeticBoolQuery(
            'recording_duration',
            $recordingDurationVal,
            $recordingDuration,
            $boolQuery
        );

        if ($recordingUrlCheck) {
            $boolQuery->add(new ExistsQuery('recording_url'), BoolQuery::FILTER);
            $boolQuery->add(new TermQuery('recording_url', ''), BoolQuery::MUST_NOT);
        }

        if ($hallOfFameCheck) {
            $boolQuery->add(new TermQuery('halloffame', 1), BoolQuery::FILTER);
        }

        if ($locationId) {
            if ($includeNeighborLocations) {
                $innerBool = $this->prepareLocationInnerQuery($locationId);
                $boolQuery->add($innerBool, BoolQuery::FILTER);
            } else {
                $boolQuery = $this->appendBoolQuery('location_id', $locationId, $boolQuery);
            }
        }

        $boolQuery = $this->prepareCallDataInnerQuery($callType, $customerName, $boolQuery);
        $boolQuery = $this->appendBoolQuery('log_id', $logId, $boolQuery);
        $boolQuery = $this->appendBoolQuery('lead_id', $leadId, $boolQuery);
        $boolQuery = $this->appendBoolQuery('employee_id', $employeeId, $boolQuery);
        $boolQuery = $this->appendBoolQuery('ad_id', $adId, $boolQuery);
        $boolQuery = $this->appendBoolQuery('customer_id', $customerId, $boolQuery);

        if (!empty($channelType)) {
            $boolQuery = $this->appendBoolQuery('channel', $channelType, $boolQuery);
        }

        if ($callNumber) {
            if (is_array($callNumber)) {
                foreach ($callNumber as $value) {
                    $boolQuery->add(new MatchQuery('call_number', $value), BoolQuery::SHOULD);
                }
                $boolQuery->addParameter('minimum_should_match', 1);
            } else {
                $number = str_replace("+", "//+", $callNumber);
                $callNumberMatch = new QueryStringQuery('*'. $number . '*');
                $callNumberMatch->addParameter('fields', ['call_number']);
                $callNumberMatch->addParameter('analyzer', 'standard');
                $boolQuery->add($callNumberMatch, BoolQuery::FILTER);
            }
        }

        if (!empty($customerEmail)) {
            $leadEmailMatch = new QueryStringQuery($customerEmail);
            $leadEmailMatch->addParameter('fields', ['lead.email']);
            $leadEmailMatch->addParameter('analyzer', 'search_email');
            $nested = new NestedQuery('lead', $leadEmailMatch);
            $boolQuery->add($nested, BoolQuery::SHOULD);
            $tenantEmailMatch = new QueryStringQuery($customerEmail);
            $tenantEmailMatch->addParameter('fields', ['tenant.email']);
            $tenantEmailMatch->addParameter('analyzer', 'search_email');
            $nested = new NestedQuery('tenant', $tenantEmailMatch);

            $boolQuery->add($nested, BoolQuery::SHOULD);
            $boolQuery->addParameter('minimum_should_match', 1);
        }

        if ($orderBy) {
            $fieldSort = $this->setOrder($orderBy, $order);
            $search->addSort($fieldSort);
        }

        $search->addQuery($boolQuery);
        $queryArray = $search->toArray();
        $this->setMultiIndex($startDate, $endDate);
        $result = $this->search($queryArray);
        $return = [];
        foreach ($result['hits']['hits'] as $value) {
            if (isset($value['_source']['hits'])) {
                if ($value['_source']['hits']['total'] > 0) {
                    foreach ($value['_source']['hits']['hits'] as $innerHit) {
                        $return[] = $innerHit;
                    }
                }
            } else {
                $return[] = $value;
            }
        }
        $result['hits']['hits'] = $return;

        return $result;
    }

    /**
     * Fetch sort object.
     *
     * This function returns the sort object based on given parameters.
     *
     * @param string   $orderBy order by field name
     * @param string   $order   order sequence
     *
     * @return object sort object
     */
    public function setOrder($orderBy, $order)
    {
        if ($orderBy === 'employee_name') {
            $fieldSort = new FieldSort($orderBy . '.keyword', $order);
        } else {
            $fieldSort = new FieldSort($orderBy, $order);
        }

        return $fieldSort;
    }

    /**
     * Fetch unprocessed count.
     *
     * This function returns the count of unprocessed call records based on given parameters.
     *
     * @param int    $accountId the current user account_id
     * @param array  $locations Contains all the user assigned locations to searchfor.
     * @param int    $interval  Optional. The amount of previous days from
     *                          today to filter. Default 2.
     * @param string $in        time unit for $interval. Default 'years'
     *
     * @return array The amount of records based on the function parameters.
     */
    public function fetchUnprocessedCount(
        int $accountId,
        array $locations,
        int $interval = 2,
        $in = 'years'
    ): array {
        try {
            unset($accountId);
            $boolQuery = new BoolQuery();
            $dateFrom = date('Y-m-d', strtotime('-'.$interval.' '.$in)) . ' 00:00:00';
            $dateTo = date('Y-m-d', time()) . ' 23:59:59';

            // Within date range range
            $dateRangeQuery = new RangeQuery(
                'datestamp',
                [
                    'gte' => $dateFrom,
                    'lte' => $dateTo,
                    'boost' => 2.0,
                ]
            );
            $boolQuery->add($dateRangeQuery, BoolQuery::FILTER);

            //CP-7391 data having recording duration > 0 and recording url present in ES.

            $boolQuery->add(new ExistsQuery('recording_url'), BoolQuery::FILTER);
            $boolQuery->add(new TermQuery('recording_url', ''), BoolQuery::MUST_NOT);

            // Filter by locations
            $locationMatch = new TermsQuery('location_id', $locations);
            $boolQuery->add($locationMatch, BoolQuery::FILTER);

            // Filter by call type
            $boolQuery->add(new TermQuery('call_type', 'inbound'), BoolQuery::FILTER);

            $locationAggregates = new TermsAggregation('location');
            $locationAggregates->setField('location_id');
            $locationAggregates->addParameter('include', $locations);
            $locationAggregates->addParameter('size', 999);
            $locationAggregates->addParameter('min_doc_count', 0);

            $search = new Search();
            $search->addQuery($boolQuery);
            $search->addAggregation($locationAggregates);

            $this->setMultiIndex($dateFrom, $dateTo);
            $params = [
                'index' => $this->getIndex(),
                'type' => $this->type,
                'ignore_unavailable' => true,
                'body' => $search->toArray(),
            ];

            $results = $this->getConnection()->search($params);
            $aggregation = $results['aggregations']['location']['buckets'];
            if (count($aggregation)) {
                return $aggregation;
            }

            return [];
        } catch (Elasticsearch\Common\Exceptions\BadRequest400Exception $ex) {
            return [
                'error' => true,
                'code' => $ex->getCode(),
                'message' => $ex->getMessage(),
            ];
        }
    }

    /**
     * Update record by Id and version
     *
     * @param string      $id     Elastic search Id
     * @param array       $data   data
     * @param null|string $parent parent Id
     *
     * @return array
     */
    public function updateByIdAndVersion(
        string $id,
        array $data,
        string $parent = null
    ): array {
        try {
            $data['_lastupdate'] = time();
            $data["_lastmodified"]  = (string) round(microtime(true)*1000);
            $response = $this->getConnection()->index(
                [
                'index' => $this->getIndex(),
                'type' => $this->type,
                'routing' => $parent ?? $id,
                'id' => $id,
                'body' => $data,
                'version' => $data['version_id'],
                'version_type' => 'external',
                ]
            );

            return $response;
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Prepare Boolean Query
     *
     * @param string $fieldName Column Name
     * @param mixed  $value     Column Value
     * @param mixed  $boolQuery Bool Query
     *
     * @return mixed
     */
    private function appendBoolQuery($fieldName, $value, $boolQuery)
    {
        if (!empty($value)) {
            if (is_array($value)) {
                $boolQuery->add(new TermsQuery($fieldName, $value), BoolQuery::FILTER);
            } else {
                $boolQuery->add(new TermQuery($fieldName, $value), BoolQuery::FILTER);
            }
        }

        return $boolQuery;
    }

    /**
     * Prepare Boolean Query Based on Arithmetic Operations
     *
     * @param string $fieldName Column Name
     * @param mixed  $value     Column Value
     * @param string $operator  Assignment Operator
     * @param mixed  $boolQuery Bool Query
     *
     * @return mixed
     */
    private function arithmeticBoolQuery($fieldName, $value, $operator, $boolQuery)
    {
        if ($value) {
            if ($operator === '=') {
                $boolQuery->add(new TermQuery($fieldName, $value), BoolQuery::FILTER);
            } else {
                if ($operator === '>') {
                    $sign = 'gt';
                } elseif ($operator === '<') {
                    $sign = 'lt';
                }
                $boolQuery->add(
                    new RangeQuery(
                        $fieldName,
                        [$sign => $value]
                    ),
                    BoolQuery::FILTER
                );
            }
        }

        return $boolQuery;
    }

    /**
     * Prepare Location Inner Query
     *
     * @param mixed  $locationId Location Id
     *
     * @return mixed
     */
    private function prepareLocationInnerQuery($locationId)
    {
        $innerBool = new BoolQuery();
        $innerBool->addParameter('minimum_should_match', 1);
        if (is_array($locationId)) {
            $innerBool->add(
                new TermsQuery(
                    'location_id',
                    $locationId
                ),
                BoolQuery::SHOULD
            );
            $innerBool->add(
                new TermsQuery(
                    'neighbor_location_id',
                    $locationId
                ),
                BoolQuery::SHOULD
            );
        } else {
            $innerBool->add(new TermQuery('location_id', $locationId), BoolQuery::SHOULD);
            $innerBool->add(
                new TermQuery(
                    'neighbor_location_id',
                    $locationId
                ),
                BoolQuery::SHOULD
            );
        }

        return $innerBool;
    }

    /**
     * Prepare Call Data Inner Query
     *
     * @param string  $callType     Call type
     * @param string  $customerName Customer Name
     * @param mixed   $boolQuery    Boolean Query
     *
     * @return mixed
     */
    private function prepareCallDataInnerQuery($callType, $customerName, $boolQuery)
    {
        if ($customerName) {
            $boolQuery->add(new MatchQuery('customer_name', $customerName), BoolQuery::FILTER);
        }

        if ($callType === 'all_inbound') {
            //call_type = 'inbound' OR call_type LIKE 'inbound_%;
            $callTypeMatch = new QueryStringQuery('inbound*');
            $callTypeMatch->addParameter('fields', ['call_type']);
            $callTypeMatch->addParameter('analyzer', 'standard');
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } elseif ($callType === 'all_outbound') {
            //call_type = 'outbound' OR call_type LIKE 'outbound_%;
            $callTypeMatch = new QueryStringQuery('outbound*');
            $callTypeMatch->addParameter('fields', ['call_type']);
            $callTypeMatch->addParameter('analyzer', 'standard');
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } elseif ($callType === 'collection_outbound') {
            //call_type = 'outbound_collection' OR call_type = 'outbound_payment')";
            $callTypeMatch = new TermsQuery(
                'call_type',
                [
                    'outbound_collection',
                    'outbound_payment',
                ]
            );
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } elseif ($callType === 'customer_inbound') {
            //call_type = 'inbound_customer' OR call_type = 'inbound_payment
            $callTypeMatch = new TermsQuery(
                'call_type',
                [
                    'inbound_customer',
                    'inbound_payment',
                ]
            );
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } elseif ($callType === 'followup') {
            //call_type = 'outbound' OR call_type = 'outbound_followup
            $callTypeMatch = new TermsQuery(
                'call_type',
                [
                    'outbound',
                    'outbound_followup',
                ]
            );
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } elseif ($callType === 'lead_inbound') {
            //call_type = 'inbound_lead' OR call_type LIKE 'inbound_lead_%
            $callTypeMatch = new QueryStringQuery('inbound_lead*');
            $callTypeMatch->addParameter('fields', ['call_type']);
            $callTypeMatch->addParameter('analyzer', 'standard');
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } elseif ($callType === 'nonlead') {
            //call_type = 'inbound_nolead' OR call_type LIKE 'inbound_nolead_%
            $callTypeMatch = new QueryStringQuery('inbound_nolead*');
            $callTypeMatch->addParameter('fields', ['call_type']);
            $callTypeMatch->addParameter('analyzer', 'standard');
            $boolQuery->add($callTypeMatch, BoolQuery::FILTER);
        } else {
            $boolQuery = $this->appendBoolQuery('call_type', $callType, $boolQuery);
        }

        return $boolQuery;
    }
}
