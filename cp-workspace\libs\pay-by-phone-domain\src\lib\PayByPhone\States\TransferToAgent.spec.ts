import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, ExitPayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { TransferToAgent } from './TransferToAgent';
import { Locale } from '@cp-workspace/shared';

describe('TransferToAgent', () => {
  let transferToAgent: TransferToAgent;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TransferToAgent],
    }).compile();

    transferToAgent = module.get<TransferToAgent>(TransferToAgent);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.TransferToAgent,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: 'https://example.com/transfer',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should set the nextState as TransferToAgent and provide a redirect URL on transfer', async () => {
      const response: ExitPayByPhoneStateHandlerResponse = await transferToAgent.handler(context);

      expect(response.redirectUrl).toBe(context.storage.transferToAgentUrl);
    });
  });
});
