const querystring = require('querystring');
const Twilio = require('twilio');
const _ = require('lodash');
const got = require('got');
const config = require('../config');
const AWS = require('aws-sdk');
AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });

async function prepareEventContext(event) {
  const params = Object.assign({}, event.queryStringParameters, querystring.parse(event.body));
  console.debug("prepareEventContext", event, params);
  const statusCallbackEvent = params.StatusCallbackEvent || null;

  const twilioAccount = ('AccountSid' in params)
    ? await dynamoQuery(params.AccountSid)
    : await twilioCredentials(params.authToken);

  if (_.isEmpty(twilioAccount)) return response(400);

  const twilioClient = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
  const workspace = twilioClient.taskrouter.workspaces(twilioAccount.workspace_sid);
  


  const conferenceContext = {
    twilioAccount,
    twilioClient,
    params,
    statusCallbackEvent,
    workspace,
    };
  
  if(params.FriendlyName)
    conferenceContext.task = await workspace.tasks(params.FriendlyName).fetch();

  return conferenceContext;
}

async function twilioCredentials(authToken) {
  try {
    const url = `https://${config.env}-core.callpotential.com/session`;
    const options = {
      headers: { Authorization: authToken },
    };
    const res = await got.get(url, options);
    const data = JSON.parse(res.body).user;
    const account = await dynamoQuery(data.sid);
    account.userId = data.user_id;

    return account;
  } catch (e) {
    console.error('ERROR obtaning twilio credentials', e);
    return {};
  }
}

async function dynamoQuery(accountSid) {
  const params = {
    TableName: config.dynamodb.acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: 'account_sid = :sid',
    ExpressionAttributeValues: { ':sid': accountSid },
  };

  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

function response(statusCode = 200, message = 'ok') {
  return {
    statusCode,
    body: JSON.stringify({
      message,
    }),
  };
}

module.exports = {prepareEventContext, response, twilioCredentials, dynamoQuery}