import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class LocaleConfirm extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, request } = context;
    
    const customerResponse = request.Digits;
    const noResponseFromCustomer = customerResponse === undefined || customerResponse.length === 0;
    if(noResponseFromCustomer) {
      return { nextState: PayByPhoneState.LocalePrompt };
    }

    const locationDetails = await this.services.locationService.getLocationDetails(storage.locationId);
    const customerResponseAsNumber = Number(customerResponse);
    const validResponse = customerResponseAsNumber > 0 && customerResponseAsNumber <= locationDetails.locales.length;
    if(!validResponse) {
      return { nextState: PayByPhoneState.LocalePrompt };
    }

    storage.locale = locationDetails.locales[customerResponseAsNumber - 1];
    return { nextState: PayByPhoneState.CollectionsPrompt };
  }
}
