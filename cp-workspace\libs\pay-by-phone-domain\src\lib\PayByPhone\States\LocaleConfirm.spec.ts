import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { LocaleConfirm } from './LocaleConfirm';
import { Locale } from '@cp-workspace/shared';

describe('LocaleConfirm', () => {
  let localeConfirm: LocaleConfirm;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LocaleConfirm],
    }).compile();

    localeConfirm = module.get<LocaleConfirm>(LocaleConfirm);
    localeConfirm.services = {
      locationService: {
        getLocationDetails: jest.fn()
      } as any
    } as any;

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.LocaleConfirm,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should proceed to CollectionsPrompt when atleast one locale is configured', async () => {
      context.request.Digits = '1';
      localeConfirm.services.locationService.getLocationDetails = jest.fn().mockResolvedValue({
        location_id: 1,
        locales: [Locale.English]
      })

      const response: PayByPhoneStateHandlerResponse = await localeConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.CollectionsPrompt);
    });
  });
});
