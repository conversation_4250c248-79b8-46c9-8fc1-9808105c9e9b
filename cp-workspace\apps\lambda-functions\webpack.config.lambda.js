const { NxWebpackPlugin } = require('@nx/webpack');
const { join } = require('path');

module.exports = {
  mode: 'production',
  output: {
    path: join(__dirname, '../../dist/apps/lambda-functions'),
    library: {
      type: 'commonjs2',
    },
  },
  plugins: [
    new NxWebpackPlugin({
      target: 'node',
      module: 'commonjs',
      compiler: 'tsc',
      main: './src/lambda.ts',
      tsConfig: 'apps/lambda-functions/tsconfig.app.json',
      assets: ['./src/assets'],
      optimization: false,
      outputHashing: 'none',
      externalDependencies: 'all',
      sourceMap: true,
    }),
  ],
};
