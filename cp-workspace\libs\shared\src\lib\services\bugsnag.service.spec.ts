import { Test, TestingModule } from '@nestjs/testing';
import { BugsnagService } from './bugsnag.service';
import Bugsnag from '@bugsnag/js';
import { ConfigService } from '@nestjs/config';
import BugsnagPluginExpress from '@bugsnag/plugin-express';
import BugsnagPluginAwsLambda from '@bugsnag/plugin-aws-lambda';

// Mock Bugsnag methods AFTER importing
jest.spyOn(Bugsnag, 'leaveBreadcrumb').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'notify').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'setUser').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'addMetadata').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'clearMetadata').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'start').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'getPlugin').mockImplementation(jest.fn());

describe('BugsnagService', () => {
  let service: BugsnagService;
  let configService: ConfigService;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  beforeEach(async () => {
    process.env['BUGSNAG_API_KEY'] = '616b106fc4dba0412968d8c0e91995be';
    process.env['NODE_ENV'] = 'test';
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [BugsnagService, ConfigService],
    }).compile();

    service = module.get<BugsnagService>(BugsnagService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize Bugsnag with correct configuration', () => {
    expect(Bugsnag.start).toHaveBeenCalledWith({
      apiKey: '616b106fc4dba0412968d8c0e91995be',
      plugins: [BugsnagPluginExpress, BugsnagPluginAwsLambda],
      releaseStage: 'test',
      appVersion: '1.0.0',
    });
  });

  it('should add a breadcrumb', () => {
    service.leaveBreadcrumb('Test Breadcrumb', { key: 'value' });
    expect(Bugsnag.leaveBreadcrumb).toHaveBeenCalledWith('Test Breadcrumb', {
      key: 'value',
    });
  });

  it('should capture an error', () => {
    const error = new Error('Test Error');
    const options = { severity: 'error' };
    service.notify(error, options);
    expect(Bugsnag.notify).toHaveBeenCalledWith(error, options);
  });

  it('should set user', () => {
    service.setUser('1', '<EMAIL>', 'Test User');
    expect(Bugsnag.setUser).toHaveBeenCalledWith(
      '1',
      '<EMAIL>',
      'Test User'
    );
  });

  it('should add metadata', () => {
    service.addMetadata('section', { key: 'value' });
    expect(Bugsnag.addMetadata).toHaveBeenCalledWith('section', {
      key: 'value',
    });
  });

  it('should clear metadata', () => {
    service.clearMetadata('section');
    expect(Bugsnag.clearMetadata).toHaveBeenCalledWith('section');
  });

  describe('getExpressMiddleware', () => {
    it('should return middleware when express plugin is available', () => {
      const mockMiddleware = {
        requestHandler: jest.fn(),
        errorHandler: jest.fn(),
      };
      (Bugsnag.getPlugin as jest.Mock).mockReturnValue(mockMiddleware);

      const result = service.getExpressMiddleware();
      expect(result).toBeDefined();
      expect(result).toEqual(mockMiddleware);
    });

    it('should return undefined when express plugin is not available', () => {
      (Bugsnag.getPlugin as jest.Mock).mockReturnValue(undefined);

      const result = service.getExpressMiddleware();
      expect(result).toBeUndefined();
    });
  });

  describe('getLambdaHandler', () => {
    it('should return handler when aws lambda plugin is available', () => {
      const mockHandler = jest.fn();
      const mockCreateHandler = jest.fn().mockReturnValue(mockHandler);
      (Bugsnag.getPlugin as jest.Mock).mockReturnValue({
        createHandler: mockCreateHandler,
      });

      const result = service.getLambdaHandler();
      expect(result).toBeDefined();
      expect(result).toBe(mockHandler);
    });

    it('should return original handler when aws lambda plugin is not available', () => {
      (Bugsnag.getPlugin as jest.Mock).mockReturnValue(undefined);

      const originalHandler = jest.fn();
      const result = service.getLambdaHandler();
      const wrappedHandler = result(originalHandler);
      
      expect(wrappedHandler).toBe(originalHandler);
    });
  });

  it('should handle missing BUGSNAG_API_KEY', async () => {
    delete process.env['BUGSNAG_API_KEY'];
    const module: TestingModule = await Test.createTestingModule({
      providers: [BugsnagService, ConfigService],
    }).compile();

    const service = module.get<BugsnagService>(BugsnagService);
    expect(Bugsnag.start).toHaveBeenCalledWith(expect.objectContaining({
      apiKey: '',
    }));
  });

  it('should handle missing NODE_ENV', async () => {
    delete process.env['NODE_ENV'];
    const module: TestingModule = await Test.createTestingModule({
      providers: [BugsnagService, ConfigService],
    }).compile();

    const service = module.get<BugsnagService>(BugsnagService);
    expect(Bugsnag.start).toHaveBeenCalledWith(expect.objectContaining({
      releaseStage: 'development',
    }));
  });
});
