import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { FinalPayAmountConfirm } from './FinalPayAmountConfirm';
import { Locale } from '@cp-workspace/shared';

describe('FinalPayAmountConfirm', () => {
  let finalPayAmountConfirm: FinalPayAmountConfirm;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FinalPayAmountConfirm],
    }).compile();

    finalPayAmountConfirm = module.get<FinalPayAmountConfirm>(FinalPayAmountConfirm);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.FinalPayAmountConfirm,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 123,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should prompt for final payment amount again when no response from customer', async () => {
      context.request.Digits = undefined; // Simulating no response

      const response: PayByPhoneStateHandlerResponse = await finalPayAmountConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.FinalPayAmountPrompt);
    });

    it('should prompt for final payment amount again when invalid response from customer', async () => {
      context.request.Digits = '0'; // Simulating invalid response

      const response: PayByPhoneStateHandlerResponse = await finalPayAmountConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.FinalPayAmountPrompt);
    });

    it('should proceed to payment submission when customer confirms payment', async () => {
      context.request.Digits = '1'; // Simulating customer confirms payment

      const response: PayByPhoneStateHandlerResponse = await finalPayAmountConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.FinalPayAmountSubmitted);
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({locale: Locale.English, messageId: 'pay-by-phone.payment-under-process'});
    });
  });
});
