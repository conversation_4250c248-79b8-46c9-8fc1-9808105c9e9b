import { Locale } from '@cp-workspace/shared';
import { I18nTranslationService } from './I18nTranslation.service';
import { PayByPhoneResponse, PayByPhoneSayAttributes } from './PayByPhone.model';

describe('PayByPhoneResponse', () => {
  let i18n: I18nTranslationService;
  let response: PayByPhoneResponse;

  beforeEach(() => {
    i18n = {
      translate: (_key: string, _options?: any) => {
        return 'Test Translation';
      }
    } as I18nTranslationService;
    response = new PayByPhoneResponse(i18n);
  });

  describe('sayInLocale', () => {
    it('should call the i18n service translate method with the provided messageId, locale, and options', () => {
      const messageId = 'pay-by-phone.not-allowed';
      const locale = Locale.English;
      const i18nOptions = { lang: locale };
      const sayOptions = { voice: 'alice' as any };
      const payByPhoneSayAttributes: PayByPhoneSayAttributes = { messageId, locale, i18nOptions, sayOptions };

      jest.spyOn(i18n, 'translate').mockReturnValue('Translated Text');
      jest.spyOn(response, 'say');

      response.sayInLocale(payByPhoneSayAttributes);

      expect(i18n.translate).toHaveBeenCalledWith(messageId, {
        ...{ lang: locale },
        ...i18nOptions,
      });
      expect(response.say).toHaveBeenCalledWith(
        { language: locale, ...sayOptions },
        'Translated Text'
      );
    });
  });
});
