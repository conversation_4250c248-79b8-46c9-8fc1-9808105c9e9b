const querystring = require('querystring')
const Twilio = require('twilio')
const AWS = require('aws-sdk')
const express = require('express')
const config = require('../config/config')
const redis = require('redis')
const cpapiClient = require('../libraries/cpapi-client')
const router = express.Router()

/* eslint-disable */
Array.prototype.forEachAsync = async function (fn) {
  for (const t of this) { await fn(t) }
}
/* eslint-enable */

async function getAccountDetail (accountSid) {
  const params = {
    TableName: config.dynamodb.acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: 'account_sid = :sid',
    ExpressionAttributeValues: {
      ':sid': accountSid
    }
  }

  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint})
  const twilioAccount = await dynamo.query(params).promise()
  return twilioAccount.Items[0]
}

// Routes to handle validate agent phone number using a code
// when agent is connecting using phone connection method

/*
 * Route to initiate agent validation call and return the validation code in response
 */
router.route('/init_call')
  .get(async function (req, res) {
    const getData = req.query
    const authToken = req.header('Authorization')

    const coreClient = new cpapiClient.coreClient(authToken) // eslint-disable-line
    const session = await coreClient.validateSession()

    if (!session || !session.user || !session.user.user_id || !session.user.sid) {
      res.status(401)
      res.set('Content-Type', 'application/json')
      res.send({ error: 'Not Authorized' })
      return
    }

    const twilioAccount = await getAccountDetail(session.user.sid)
    const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken)

    await twilio.taskrouter.workspaces(twilioAccount.workspace_sid)
      .workers(getData.workerSid).fetch()
      .then(async (worker) => {
        const updateAttributes = { phoneVerified: 0 }
        await worker.update({ attributes: JSON.stringify({ ...JSON.parse(worker.attributes), ...updateAttributes }) })
      })
      .catch((e) => {
        console.error(e, new Error().stack)
      })

    // Generate random validation code
    const validationCode = Math.floor(Math.random() * 999).toString().padStart(3, '0')

    const queryString = querystring.stringify(getData, '&', '=')
    const callParams = {
      url: `${config.call_url}validate_agent_number/gather_code?${queryString}`,
      to: getData.to,
      from: getData.from
    }

    let callSid;
    try {
      callSid = await twilio.calls.create(callParams)
    } catch (e) {
      console.error(e, new Error().stack)
      res.status(400)
      res.set('Content-Type', 'application/json')
      res.send({ error: e.message })
      return
    }

    // save validation code in redis to retrieve it later
    try {
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`})
      await redisClient.connect();

      const callKey = 'agentValidationCall-' + callSid.sid
      const callKeyValue = validationCode

      const redisTtl = 300
      await redisClient.set(callKey, JSON.stringify(callKeyValue), {EX: redisTtl});
    } catch (e) {
      console.error(e, new Error().stack)
      res.status(500)
      res.set('Content-Type', 'application/json')
      res.send({ error: '' })
      return
    }

    res.status(200)
    res.set('Content-Type', 'application/json')
    res.send({ code: validationCode })
  })

/*
 * Route to execute when agent answers the call. Ask for the validation code
 */
router.route('/gather_code')
  .post(async function (req, res) {
    const getData = req.query

    if (getData.attempt) {
      getData.attempt++
    } else {
      getData.attempt = 1
    }

    const twiml = new Twilio.twiml.VoiceResponse()

    if (getData.attempt > 3) {
      twiml.say('You have made too many invalid attempts.  Please hang up and try again.')
      twiml.hangup()
      res.status(200)
      res.set('Content-Type', 'text/xml')
      res.send(twiml.toString())
      return
    }

    const queryString = querystring.stringify(getData, '&', '=')
    const gatherCallback = `${config.call_url}validate_agent_number/validate_code?${queryString}`
    const urlCallback = `${config.call_url}validate_agent_number/gather_code?${queryString}`
    const gather = twiml.gather({ numDigits: 3, timeout: 10, action: gatherCallback })

    gather.say('Please enter your Agent Verification Code')
    twiml.redirect(urlCallback)
    res.status(200)
    res.set('Content-Type', 'text/xml')
    res.send(twiml.toString())
  })

/*
 * Route to validate agent response and validate the code
 */
router.route('/validate_code')
  .post(async function (req, res) {
    const getData = req.query
    const postData = req.body

    const twiml = new Twilio.twiml.VoiceResponse()

    if (getData.attempt > 3) {
      twiml.say('You have made too many invalid attempts.  Please hang up and try again.')
      twiml.hangup()
      res.status(200)
      res.set('Content-Type', 'text/xml')
      res.send(twiml.toString())
      return
    }

    const redisClient = redis.createClient({'url': `redis://${config.redis.host}`})
    await redisClient.connect();
    const callKey = 'agentValidationCall-' + postData.CallSid
    const callKeyValue = await redisClient.get(callKey)

    if (postData.Digits && callKeyValue && parseInt(postData.Digits, 10) === parseInt(callKeyValue.slice(1, -1), 10)) {
      twiml.say('You have entered a valid code.  Thank you')
      twiml.hangup()

      const twilioAccount = await getAccountDetail(postData.AccountSid)
      const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken)

      await twilio.taskrouter.workspaces(twilioAccount.workspace_sid)
        .workers(getData.workerSid).fetch()
        .then(async (worker) => {
          const updateAttributes = { phoneVerified: 1 }
          await worker.update({ attributes: JSON.stringify({ ...JSON.parse(worker.attributes), ...updateAttributes }) })
        })
        .catch((e) => {
          console.error(e, new Error().stack)
        })

      res.status(200)
      res.set('Content-Type', 'text/xml')
      res.send(twiml.toString())
    } else {
      twiml.say('You have entered an invalid agent code.')
      const queryString = querystring.stringify(getData, '&', '=')
      const urlCallback = `${config.call_url}validate_agent_number/gather_code?${queryString}`
      twiml.redirect(urlCallback)
      res.status(200)
      res.set('Content-Type', 'text/xml')
      res.send(twiml.toString())
    }
  })

module.exports = router
