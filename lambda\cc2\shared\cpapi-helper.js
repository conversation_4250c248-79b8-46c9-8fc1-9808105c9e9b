const cpapiClient = require("../cpapi_client");
const config = require('../config');
const _ = require('lodash');
const moment = require('moment-timezone');

async function getUser(params, userId) {
  try {
    const coreClient = new cpapiClient.coreClient(
      config.db.serviceTokens.readWrite
    );
    const data = await coreClient.getData(`user/${userId}`);
    return data;
  } catch (e) {
    console.error("ERROR obtaning User data", e);
    return {};
  }
}

async function createAuditLog(params, callData) {
  // console.log('CREATE AUDIT LOG', params, callData);

  // Audit log for inbound call is already being added from call log model
  // https://github.com/callpotential/cpapi-call/blob/master/lambda/models/call-log.js#L382
  // prevent duplicates
  if (callData.call_type.includes("inbound")) {
    return Promise.resolve();
  }

  const intClient = new cpapiClient.intClient(
    config.db.serviceTokens.readWrite
  );
  const locClient = new cpapiClient.locClient(
    config.db.serviceTokens.readWrite
  );

  const leadCallType = ["outbound_followup", "outbound_lead_payment"];

  const customerCallType = ["outbound_collection", "outbound_payment", "outbound_callback"];

  try {
    let objectType;
    let esLeadId = "";
    let esCustomerId = "";

    if (callData.es_tenant_id && callData.es_tenant_id.length > 0) {
      esCustomerId = callData.es_tenant_id;
      objectType = "customer";
    } else if (callData.es_lead_id && callData.es_lead_id.length > 0) {
      esLeadId = callData.es_lead_id;
      objectType = "lead";
    } else if (customerCallType.includes(callData.call_type)) {
      if (callData.es_tenant_id) {
        esCustomerId = callData.es_tenant_id;
      } else {
        let mysqlEsConvertPayload = {
          account_id: callData.account_id,
          location_id: callData.location_id,
          mysql_id: callData.customer_id,
          entity_type: "customer",
        };
        let esID = await intClient.postData(
          `convertmysqlesid`,
          mysqlEsConvertPayload
        );

        esCustomerId = esID.data.es_id;
      }
      objectType = "customer";
    } else if (leadCallType.includes(callData.call_type)) {
      if (callData.es_lead_id) {
        esLeadId = callData.es_lead_id;
      } else {
        let mysqlEsConvertPayload = {
          account_id: callData.account_id,
          location_id: callData.location_id,
          mysql_id: callData.fk_lead_id,
          entity_type: "lead",
        };
        let esID = await intClient.postData(
          `convertmysqlesid`,
          mysqlEsConvertPayload
        );

        esLeadId = esID.data.es_id;
      }
      objectType = "lead";
    } else if (callData.fk_lead_id && callData.fk_lead_id > 0) {
      let mysqlEsConvertPayload = {
        account_id: callData.account_id,
        location_id: callData.location_id,
        mysql_id: callData.fk_lead_id,
        entity_type: "lead",
      };

      let esID = await intClient.postData(
        `convertmysqlesid`,
        mysqlEsConvertPayload
      );

      esLeadId = esID.data.es_id;
      objectType = "lead";
    }

    let paymentCall = false;

    if (callData.call_type.includes("payment")) {
      paymentCall = true;
    }

    if (!objectType) return Promise.resolve();

    /*
      CPAPI-1756
      `callData.employee_id` is not present in the call data. I'm not sure
      if there is a case where the call data contains `employee_id`.  If
      the employee_id is not found in the callData, use the AgentId param.
    */
    let employeeId = callData.employee_id || params.AgentId;
    const employeeDetail = await getUser(params, employeeId);

    // console.log('EMPLOYEE DETAIL', employeeDetail);

    let callType;
    if (callData.call_type) {
      callType = `${callData.call_type}_call`;
    }

    if (
      ![
        "outbound_call",
        "outbound_collection_call",
        "outbound_followup_call",
      ].includes(callType)
    ) {
      callType = "outbound_call";
    }

    let dateStamp;
    if (callData.datestamp) {
      dateStamp = callData.datestamp;
    }

    const employeeName = `${employeeDetail.firstname} ${employeeDetail.lastname}`;
    const message = `Call to ${callData.call_number} with ${employeeName}`;

    const auditLogParams = {
      logType: callType,
      logMessage: message,
      logOrigin: "cp_manual",
      entityType: objectType,
      esLeadId: esLeadId,
      esCustomerId: esCustomerId,
      esLedgerId: "",
      esUnitId: "",
      employeeId: employeeId,
      employeeName: employeeName,
      phoneNumber: callData.call_number,
      callDuration: params.callDuration,
      callOutcome: "",
      linkText: "",
      message: "",
      accountId: callData.account_id,
      locationId: callData.location_id,
      date: dateStamp,
      meta: { callSid: callData.twilio_id, paymentCall: paymentCall },
    };

    if ("recordingUrl" in params && params.recordingUrl) {
      auditLogParams.link = `${config.API_CALL_URL}/recording/${callData.twilio_id}`;
      auditLogParams.linkType = "recording";
    }

    const locData = await locClient.getLocationConfiguration(
      callData.location_id
    );

    if (paymentCall && locData.record_payment_calls == 0) {
      delete auditLogParams.link;
      delete auditLogParams.linkType;
    }

    // console.log('AUDIT LOG PARAMS', auditLogParams);

    const acctClient = new cpapiClient.acctClient(
      config.db.serviceTokens.readWrite
    );
    await acctClient.postData("auditlognew", auditLogParams);

    const callClient = new cpapiClient.callClient(
      config.db.serviceTokens.readWrite
    );
    let callNotePayload = { addOutboundNote: true };
    if (callData.workflow_step && callData.workflow_step === "true") {
      callNotePayload.workflow_step = "true";
    }

    await callClient // mysql endpoint
      .putData(`calldetail/${callData.db_log_id}`, callNotePayload);

    return Promise.resolve();
  } catch (e) {
    console.error("ERROR Creating Audit", e);
    return {};
  }
}

async function updateReport(params) {
  if (!_.isEmpty(params.report_id)) {
    if (!_.isEmpty(params.leadId)) await updateFollowupReport(params);
    if (!_.isEmpty(params.customerId)) await updateCollectionReport(params);
  }
}

async function updateOutboundCallLogs(params, callSid) {
  const callClient = new cpapiClient.callClient(
    config.db.serviceTokens.readWrite
  );

  const payload = {
    location_id: params.locationId,
    twilio_id: callSid,
    duration: parseInt(params.callDuration, 10),
    call_duration: parseInt(params.callDuration, 10),
    is_route_complete: 1,
  };

  if ("accountId" in params) {
    payload.account_id = parseInt(params.accountId, 10);
  }

  if ("RecordingUrl" in params && params.RecordingUrl !== "") {
    payload.recording_url = params.RecordingUrl;
  }

  if ("RecordingSid" in params) {
    payload.recording_sid = params.RecordingSid;
  }

  if ("billingDuration" in params) {
    payload.billingDuration = params.billingDuration;
  }

  // console.log('CALL LOG PAYLOAD', payload);

  try {
    const data = await callClient.getData(
      `call/${params.locationId}/${callSid}`
    );

    // console.log('CALL CLIENT DATA', data);

    /**
     * It is possible that the call type has been updated due to payment
     * scenarios.  We need to update the call_type in the call details
     * data to match the call_type of the call log data.
     */
    if (data.call_type) {
      payload.call_type = data.call_type;
    }

    // update call info on api
    await callClient // mysql endpoint
      .putData(`calldetail/${data.db_log_id}`, payload);

    delete payload.duration;
    await callClient // dynamodb endpoint
      .putData(`call/${params.locationId}/${callSid}`, payload);

    return true;
  } catch (e) {
    console.error("ERROR updating call record!", e);
    return 0;
  }
}

async function updateRecordingDetails(params) {
  // console.log('UPDATE RECORDING DETAILS');

  const callClient = new cpapiClient.callClient(
    config.db.serviceTokens.readWrite
  );
  const customerCallSid = params.Customer;

  const payload = {
    location_id: params.locationId,
    twilio_id: customerCallSid,
  };
  if (params.RecordingUrl !== "") {
    payload.recording_url = params.RecordingUrl;
  }

  if ("RecordingSid" in params) {
    payload.recording_sid = params.RecordingSid;
  }

  if ("callDuration" in params) {
    payload.duration = params.callDuration;
  }

  if ("billingDuration" in params) {
    payload.billingDuration = params.billingDuration;
  }

  // console.log('CALL LOG PAYLOAD', payload);

  try {
    /*
      CPAPI-1756
      The RecordingSid and RecordingUrl is saved to the MySQL database
      for reporting purposes, etc.  It does not live in the live call log.
      However, in order to properly push this information to the MySQL,
      it is necessary to push it to the DynamoDB call log using the endpoint
      below.  The reason for this is that the endpoint below uses logic that
      will check if the call is complete and push the data to MySQL when
      `is_route_complete = 1`.
    */
    await callClient // dynamodb endpoint
      .putData(`call/${params.locationId}/${customerCallSid}`, payload);

    return true;
  } catch (e) {
    console.error("ERROR updating call record!", e);
    return 0;
  }
}

async function updateFollowupReport(params) {
  try {
    const payload = {};
    let isUpdate = false;

    if (!_.isEmpty(params.RecordingUrl)) {
      if (!_.isEmpty(params.Customer)) {
        payload.details = `${config.API_CALL_URL}/recording/${params.Customer}`;
      } else {
        payload.details = `${config.API_CALL_URL}/recording/${params.callSid}`;
      }

      isUpdate = true;
    }

    if ("reached_to_customer" in params) {
      const dateCompleted = moment.tz(config.TZ).format("YYYY-MM-DD HH:mm:ss");
      payload.reached_to_customer = 1;
      payload.date_completed = dateCompleted;
      isUpdate = true;
    }

    if (!isUpdate) {
      return 0;
    }

    const intClient = new cpapiClient.intClient(params.authToken);

    if (_.isEmpty(params.leadESId)) {
      let mysqlEsConvertPayload = {
        account_id: params.accountId,
        location_id: params.locationId,
        mysql_id: params.leadId,
        entity_type: "lead",
      };
      let esID = await intClient.postData(
        `convertmysqlesid`,
        mysqlEsConvertPayload
      );

      params.leadESId = esID.data.es_id;
    }

    const data = await intClient.putData(
      `lead/${params.leadESId}/followup/${params.report_id}`,
      payload
    );
    return data.report_id;
  } catch (e) {
    console.error("ERROR updating followup report", e);
    return 0;
  }
}

async function updateCollectionReport(params) {
  try {
    const payload = {};
    let isUpdate = false;

    if (params.record_outgoing != 1 || params.overideRecCheck) {
      payload.length = params.callDuration;
      isUpdate = true;
    }

    if (!_.isEmpty(params.RecordingUrl)) {
      payload.length = params.callDuration;

      if (!_.isEmpty(params.Customer)) {
        payload.details = `${config.API_CALL_URL}/recording/${params.Customer}`;
      } else {
        payload.details = `${config.API_CALL_URL}/recording/${params.callSid}`;
      }

      isUpdate = true;
    }

    if ("reached_to_customer" in params) {
      const dateCompleted = moment.tz(config.TZ).format("YYYY-MM-DD HH:mm:ss");
      payload.outcome = "connected";
      payload.date_completed = dateCompleted;
      payload.call_number = params.toPhone;
      isUpdate = true;
    }

    if (!isUpdate) return 0;

    const intClient = new cpapiClient.intClient(params.authToken);

    if (_.isEmpty(params.customerESId)) {
      let mysqlEsConvertPayload = {
        account_id: params.accountId,
        location_id: params.locationId,
        mysql_id: params.customerId,
        entity_type: "customer",
      };
      let esID = await intClient.postData(
        `convertmysqlesid`,
        mysqlEsConvertPayload
      );

      params.customerESId = esID.data.es_id;
    }

    const data = await intClient.putData(
      `tenant/${params.customerESId}/collection/${params.report_id}`,
      payload
    );
    return data.report_id;
  } catch (e) {
    console.error("ERROR adding collection report", e);
    return 0;
  }
}

module.exports = {
  updateOutboundCallLogs,
  updateRecordingDetails,
  updateFollowupReport,
  updateCollectionReport,
  createAuditLog,
  getUser,
  updateReport,
};
