<?php

use Tests\Unit\AbstractUnitTest;

include_once './app/models/LeadHistory.php';

class LeadHistoryTest extends AbstractUnitTest
{
    public function test_GivenLeadHistoryWithExistingId_WhenFindByIdCalled_ItShouldReturnLeadDetails(): void
    {
        // Arrange
        $leadHistory = $this->getMockBuilder(LeadHistory::class)
                            ->setMethods(['search'])
                            ->getMock();

        $expectedResult = ['id' => '123', 'type' => 'example'];

        $leadHistory->expects($this->once())
                    ->method('search')
                    ->willReturn($expectedResult);
        // Act
        $result = $leadHistory->findById('123');

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenLeadHistoryWithNonExistentId_WhenFindByIdCalled_ItShouldReturnEmptyLeadDetails(): void
    {
        // Arrange
        $leadHistory = $this->getMockBuilder(LeadHistory::class)
                            ->setMethods(['search'])
                            ->getMock();

        $leadHistory->expects($this->once())
                    ->method('search')
                    ->willReturn([]);
        // Act
        $result = $leadHistory->findById('nonexistent_id');

        // Assert
        $this->assertEquals([], $result);
    }
}
