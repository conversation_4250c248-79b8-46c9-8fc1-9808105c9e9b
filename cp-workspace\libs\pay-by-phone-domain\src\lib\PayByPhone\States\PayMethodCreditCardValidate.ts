import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { BinCheckResponse, PaymentToken } from '@cp-workspace/shared';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodCreditCardValidate extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. If CC is invalid
     * 1a. Compose TwiML to ask for credit card input
     * 1b. Compose the TwiML that will gather credit card input
     * 1c. Transition to PayMethodCreditCardValidate
     * 2. If CC is valid
     * 2a. if surcharge flag is enabled at crm end
     * 2a1. do bin check to see if surcharge is enabled for the card and save the surchargeable in storage
     * 2b. Compose TwiML to ask for confirmation
     * 2c. Compose TwiML to gather the input
     * 2d. Transition to PayMethodCreditCardConfirm
     */

    const { request, twilioResponse, storage } = context;
    const coreClient = this.services.coreService;
    const enteredNumber = request.Digits;
    const allowedDigitRange = [15,19];

    if (!enteredNumber || enteredNumber.length < allowedDigitRange[0] || enteredNumber.length > allowedDigitRange[1]) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
    }

    const paymentToken: PaymentToken = {
      cardNumber: enteredNumber,
    };

    const encodedToken = await coreClient.encodePaymentToken(paymentToken);

    storage.paymentToken = encodedToken;

    if(storage.matchedTenants && storage.matchedTenants?.length > 0) {
      const locationDetails = await this.services.locationService.getLocationDetails(storage.locationId);
      if (locationDetails?.api_type && storage.surchargeDetails?.surchargeEnabledAPITypes && (storage.surchargeDetails?.surchargeEnabledAPITypes).includes(locationDetails?.api_type) && locationDetails?.user_id) {
        const binCheckResponse: BinCheckResponse = await this.services.integrationService.doBinCheck(enteredNumber, storage.locationId);
        storage.surchargeDetails.isSurchargeEnabledForCard = binCheckResponse?.surchargeable ?? false;
      }
    }
    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      numDigits: 1,
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.cc-confirm',
      locale: storage.locale,
      i18nOptions: { args: [{ enteredNumber: enteredNumber.split('').join(' ') }] }
    }]);


    return { nextState: PayByPhoneState.PayMethodCreditCardConfirm };
  }
}
