import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { UnitsToPayPrompt } from './UnitsToPayPrompt';
import { Locale } from '@cp-workspace/shared';

describe('UnitsToPayPrompt', () => {
  let unitsToPayPrompt: UnitsToPayPrompt;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UnitsToPayPrompt],
    }).compile();

    unitsToPayPrompt = module.get<UnitsToPayPrompt>(UnitsToPayPrompt);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.UnitsToPayPrompt,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        matchedTenants: [
          { first_name: '<PERSON>', last_name: '<PERSON><PERSON>', ledgers: [{ unit_name: 'A101' }] as any } as any,
          { first_name: 'Jane', last_name: 'Smith', ledgers: [{ unit_name: 'B202' }] as any } as any,
        ],
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should transfer to agent if no matched tenants', async () => {
      context.storage.matchedTenants = [];

      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrompt.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.TransferToAgent);
    });

    it('should prompt user with multiple accounts and gather response', async () => {
      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrompt.handler(context);

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
        numDigits: 1,
        method: 'POST',
        timeout: 10,
      }, [
        { messageId: 'pay-by-phone.multiple-accounts', locale: context.storage.locale },
        { messageId: 'pay-by-phone.pay-all', locale: context.storage.locale },
        { messageId: 'pay-by-phone.account-select', locale: context.storage.locale, i18nOptions: { args: [{ keyPress: '2' }, { tenantName: 'John Doe' }, { unitName: 'A101' }] } },
        { messageId: 'pay-by-phone.account-select', locale: context.storage.locale, i18nOptions: { args: [{ keyPress: '3' }, { tenantName: 'Jane Smith' }, { unitName: 'B202' }] } },
        { messageId: 'pay-by-phone.start-over', locale: context.storage.locale },
      ]);
      expect(response.nextState).toBe(PayByPhoneState.UnitsToPaySelection);
    });

    it('should limit the number of units to maximumUnits', async () => {
      context.storage.matchedTenants = Array.from({ length: 12 }, (_, i) => ({
        first_name: `First${i}`,
        last_name: `Last${i}`,
        ledgers: [{ unit_name: `Unit${i}` }],
      })) as any;

      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrompt.handler(context);

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
        numDigits: 1,
        method: 'POST',
        timeout: 10,
      }, [
        { messageId: 'pay-by-phone.multiple-accounts', locale: context.storage.locale },
        { messageId: 'pay-by-phone.pay-all', locale: context.storage.locale },
        ...Array.from({ length: 7 }, (_, i) => ({
          messageId: 'pay-by-phone.account-select',
          locale: context.storage.locale,
          i18nOptions: { args: [{ keyPress: (i + 2).toString() }, { tenantName: `First${i} Last${i}` }, { unitName: `Unit${i}` }] }
        })),
        { messageId: 'pay-by-phone.start-over', locale: context.storage.locale },
      ]);
      expect(response.nextState).toBe(PayByPhoneState.UnitsToPaySelection);
    });
  });
});
