import { Injectable } from '@nestjs/common';
import { PayByPhoneState } from './Generated/PayByPhoneState.generated';
import {
  PayByPhoneResponse,
  PayByPhoneRequestBody,
  PayByPhoneStateHandlerResponseBase,
  ExitPayByPhoneStateHandlerResponse,
  LoopbackPayByPhoneStateHandlerResponse,
  PayByPhoneStorage,
  storageKey,
  PayByPhoneLongRunningState,
  PayByPhoneLongRunningResult,
  ApiType,
  surchargeDetails
} from './PayByPhone.model';
import { PayByPhoneTransitions } from "./Generated/PayByPhoneTransitions.generated";
import { PayByPhoneContextService } from './PayByPhoneContext.service';
import { PayByPhoneStorageService } from './PayByPhoneStorage.service';
import { Locale, DataDogService, DomainEventsService, LocationService, AsyncWorkersService, AsyncWorkersIntegrationService, BugsnagService } from '@cp-workspace/shared';
import { PayByPhoneStateHandlerService } from './PayByPhoneStateHandler.service';
import { ConfigService, PathImpl2 } from '@nestjs/config';
import { I18nTranslations } from './Generated/i18n.generated';

@Injectable()
export class PayByPhoneService {
  private defaultEntryState: PayByPhoneState = PayByPhoneState.CustomerByPhoneSearch;
  private defaultEntryLocale: Locale = Locale.English;
  public stateHandlers: PayByPhoneStateHandlerService;
  private surchargeEnabledAPITypes: ApiType[] = [ApiType.STOREDGE];

  constructor(
    public context: PayByPhoneContextService,
    public storageService: PayByPhoneStorageService,
    private dataDogService: DataDogService,
    private configService: ConfigService,
    private domainEventService: DomainEventsService,
    private locationService: LocationService,
    private workerService: AsyncWorkersService,
    private bugsnagService: BugsnagService,
  ) {
    this.stateHandlers = new PayByPhoneStateHandlerService(context);
  }

  public async isFirstRequest(storageKey: string): Promise<boolean> {
    return await this.storageService.getStorage(storageKey) === undefined;
  }

  public async handleRequest(request: PayByPhoneRequestBody): Promise<string> {
    const twilioResponse = new PayByPhoneResponse(this.context.i18n);
    let storage = await this.storageService.getStorage(storageKey(request));
    const callDisconnected = request.CallStatus === 'completed';
    if(callDisconnected) {
      if(storage) {
        await this.logPayByPhoneEnd(request, 'hangup');
        await this.storageService.deleteStorage(storageKey(request));
      }
      return twilioResponse.toString();
    }

    if (!storage) {
      storage = await this.determineEntryStorageState(request);
      await this.domainEventService.publish({
        source: 'comms-api.pay-by-phone.storage',
        detailType: 'PayByPhoneNewStorage',
        detail: {
          callSid: request.CallSid, storage,
        },
      });
      this.bugsnagService.leaveBreadcrumb('PayByPhoneNewStorage', {
        source: 'comms-api.pay-by-phone.storage',
        callSid: request.CallSid,
        storage,
      });
    }

    const handlerResponse = await this.handleState(request, twilioResponse, storage);
    return await this.redirect(handlerResponse, request, storage, twilioResponse);
  }

  public async handleState(request: PayByPhoneRequestBody, twilioResponse: PayByPhoneResponse, storage: PayByPhoneStorage): Promise<PayByPhoneStateHandlerResponseBase> {
    this.dataDogService.incrementCounter('pay_by_phone.state.usage', [`state:${storage.state}`]);

    const state = await this.stateHandlers.getStateHandler(storage.state);
    const longRunningState = state as PayByPhoneLongRunningState;

    let handlerResponse;
    if (longRunningState.longRunningState) {
      if (request.Source === 'async-invocation' || AsyncWorkersIntegrationService.options?.enabled === false) {
        handlerResponse = await longRunningState.handler({ request, twilioResponse, storage });
      } else if (longRunningState.isWatchingForCompletion(storage)) {
        const workerStorage = await this.storageService.getStorage(`in_progress_${request.CallSid}`) || {} as PayByPhoneStorage;
        handlerResponse = await longRunningState.checkForCompletion(workerStorage, storage);
      } else {
        handlerResponse = await longRunningState.invokeLongRunningProcess();
      }
    } else {
      handlerResponse = await state.handler({ request, twilioResponse, storage });
    }

    if(handlerResponse.nextState) {
      const isValidTransition = this.isValidTransitionState(storage.state, handlerResponse.nextState);
      if(!isValidTransition) {
        throw new Error(`Invalid state transition from ${storage.state} to ${handlerResponse.nextState}`);
      }
    }
    return handlerResponse;
  }

  public async redirect(handlerResponse: PayByPhoneStateHandlerResponseBase, request: PayByPhoneRequestBody, storage: PayByPhoneStorage, twilioResponse: PayByPhoneResponse): Promise<string> {
    let redirectURL = '';
    const exitHandlerResponse = handlerResponse as ExitPayByPhoneStateHandlerResponse;
    const loopbackHandlerResponse = handlerResponse as LoopbackPayByPhoneStateHandlerResponse;
    let twimlResponse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response></Response>";
    if(loopbackHandlerResponse.loopback) {
      if (loopbackHandlerResponse.doReturnLongRunningResult) {
        delete storage.loopbackCount;
        delete storage.stateInProgress;
        await this.storageService.saveStorage(storageKey(request), { ...storage, ...loopbackHandlerResponse.newStorage || {} });
        const result = loopbackHandlerResponse.longRunningResult || {} as PayByPhoneLongRunningResult;
        if (result.error) {
          const err = new Error(result.error.message || 'Unknown error occurred');
          err.stack = result.error.stack;
          throw err;
        }
        twimlResponse = result.twimlResponse || '';
      } else if (loopbackHandlerResponse.doInvokeLongRunningProcess) {
        this.dataDogService.incrementCounter('pay_by_phone.state.invoked_long_running_process', [`state:${storage.state}`]);
        await this.invokeAsyncStateHandler(request, storage);
        twimlResponse = this.loopbackRedirectTwiml(twilioResponse);
      } else if(loopbackHandlerResponse.nextState) {
        storage.state = loopbackHandlerResponse.nextState;
        delete storage.loopbackCount;
        delete storage.stateInProgress;
        await this.storageService.saveStorage(storageKey(request), storage);
        twimlResponse = this.loopbackRedirectTwiml(twilioResponse);
      } else {
        storage.loopbackCount = storage.loopbackCount ? storage.loopbackCount + 1 : 1;
        await this.storageService.saveStorage(storageKey(request), storage);
        if (storage.state === PayByPhoneState.FinalPayAmountSubmitted) {
          twimlResponse = this.loopbackRedirectTwiml(twilioResponse, 'pay-by-phone.payment-under-process', storage.locale);
        } else {
          twimlResponse = this.loopbackRedirectTwiml(twilioResponse);
        }
      }
    } else {
      if (exitHandlerResponse.redirectUrl) {
        redirectURL = exitHandlerResponse.redirectUrl;
        const wasSuccessfulPayment = storage.state === PayByPhoneState.PaymentSuccessful;
        const durationTags = wasSuccessfulPayment ? [`payment:success`] : [];
        await this.logPayByPhoneEnd(request, 'exit', durationTags);
        await this.storageService.deleteStorage(request.CallSid);
        await this.storageService.deleteStorage(`in_progress_${request.CallSid}`);
      }
      else if(handlerResponse.nextState) {
        redirectURL = this.configService.get<string>('comms_api_url') + 'pay-by-phone';
        storage.state = handlerResponse.nextState;
        await this.storageService.saveStorage(storageKey(request), storage);
      }
      if(redirectURL !== '') {
        twilioResponse.redirect(redirectURL);
      }
      twimlResponse = twilioResponse.toString();
    }
    return twimlResponse;
  }

  async invokeAsyncStateHandler(request: PayByPhoneRequestBody, storage: PayByPhoneStorage) {
    /**
     * When processing a state handler asynchronously, we need to:
     * - save a copy of the current storage to a key formatted like `in_progress_{callSid}`
     * - set the `stateInProgress` property to the current state and save the storage
     * - invoke the state handler asynchronously
     */
    await this.storageService.saveStorage(`in_progress_${request.CallSid}`, storage);
    storage.stateInProgress = storage.state;
    await this.storageService.saveStorage(storageKey(request), storage);
    await this.workerService.invokeAsyncWorker({
      source: 'async-invocation',
      body: request
    });
  }


  public isValidTransitionState(currentState: PayByPhoneState, nextState: PayByPhoneState): boolean {
    if(!PayByPhoneTransitions.has(currentState))
    {
      return false;
    }

    const nextValidTransitionStatesMap = PayByPhoneTransitions.get(currentState)!;
    return nextValidTransitionStatesMap.has(nextState);
  }

  private determineEntryState(request: PayByPhoneRequestBody): PayByPhoneState {
    if (
      request.OverrideEntryState &&
      Object.values(PayByPhoneState).includes(request.OverrideEntryState)
    ) {
      return request.OverrideEntryState;
    }
    return this.defaultEntryState;
  }

  private determineEntryLocale(request: PayByPhoneRequestBody): Locale {
    const legacyString = request.OverrideLocale as string;
    if(legacyString === 'en') {
      return Locale.English;
    }
    if(legacyString === 'es') {
      return Locale.Spanish;
    }
    if(legacyString === 'fr') {
      return Locale.French;
    }

    const locale = request.OverrideLocale as Locale;
    if (locale && Object.values(Locale).includes(locale)) {
      return locale;
    }
    return this.defaultEntryLocale;
  }

  private async determineEntryStorageState(
    request: PayByPhoneRequestBody
  ): Promise<PayByPhoneStorage> {
    const locationConfig = await this.locationService.getLocationConfiguration(request.LocationId);
    const defaultSurchargeDetails: surchargeDetails = {}
    defaultSurchargeDetails.surchargeEnabledAPITypes = this.surchargeEnabledAPITypes;

    return {
      state: this.determineEntryState(request),
      locale: this.determineEntryLocale(request),
      transferToAgentUrl: request.TransferToAgentUrl,
      locationId: request.LocationId,
      phoneNumber: request.From,
      toNumber: request.To,
      paymentSuccessRedirectUrl: request.PaymentSuccessRedirectUrl,
      paymentFailureRedirectUrl: request.PaymentFailureRedirectUrl,
      tenantId: request.TenantId,
      payByPhoneStartTime: Date.now(),
      origin: request.Origin,
      convenienceFee: Number(locationConfig.convenience_fee_phone),
      totalAmountDue: 0,
      totalBalance: 0,
      absoluteUrl: (this.configService.get<string>('comms_api_url') ?? '') + 'pay-by-phone',
      surchargeDetails: defaultSurchargeDetails
    };
  }

  public async logPayByPhoneEnd(request: PayByPhoneRequestBody, reason: 'hangup' | 'exit' | 'runtime_error', tags?: string[]): Promise<void> {
    const storage = await this.storageService.getStorage(storageKey(request));
    if(storage === undefined) return;

    if(storage.payByPhoneStartTime !== undefined)
    {
      const totalDuration = Date.now() - storage.payByPhoneStartTime;
      this.dataDogService.recordDistribution('pay_by_phone.session.duration', totalDuration, tags);
      this.dataDogService.incrementCounter('pay_by_phone.session.total', [
        `locale:${storage.locale}`,
        `end_reason:${reason}`,
        `origin:${storage.origin ?? 'unknown'}`,
        `state:${storage.state}`,
        `pbp_not_allowed:${!storage.payByPhoneAllowed}`,
      ]);
      this.bugsnagService.leaveBreadcrumb('PayByPhoneSessionEnd', {
        totalDuration,
        tags,
        storage,
      });
    }
  }

  private loopbackRedirectTwiml(
    twilioResponse: PayByPhoneResponse,
    messageId?: PathImpl2<I18nTranslations>,
    locale?: Locale
  ): string {
    if (messageId && locale) {
      twilioResponse.sayInLocale({
        messageId,
        locale,
      });
    }
    this.bugsnagService.leaveBreadcrumb('PayByPhoneLoopbackRedirect', {
      twilioResponse: twilioResponse.toString(),
    });
    twilioResponse.redirect(this.configService.get<string>('comms_api_url') + 'pay-by-phone');
    return twilioResponse.toString();
  }

}
