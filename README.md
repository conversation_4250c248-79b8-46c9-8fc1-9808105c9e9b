# cpapi-call
cpapi-call provides a common API for call data and call configuration.  

This service is a Phalcon 3 project, expecting PHP7. Dependencies are managed via Composer.

## Installing composer
Services rely on composer to install 3rd party and Call Potential dependencies.  See https://getcomposer.org/doc/00-intro.md#installation-linux-unix-osx for a guide on installing composer.

## Installing Phalcon
See the reference at https://docs.phalconphp.com/en/latest/reference/install.html for Phalcon installation instructions.  Phalcon is an MVC model implemented as a PHP module, not just a framework, so installation requires sudo access on the OS.

The repository contains .htaccess files which handle phalcon's routing mechanism, but instead of using .htaccess, a more performant method is defined in the template for an Apache vhost: 

```apacheconf
  Listen __PORT__
  
  <VirtualHost *:__PORT__>
  	ServerAdmin <EMAIL>
  	DocumentRoot /var/www/__DOCROOT__/public
  	<Directory />
  		Options FollowSymLinks
  		AllowOverride None
  	</Directory>
      <IfModule mod_rewrite.c>
          <Directory "/var/www/__DOCROOT__">
              RewriteEngine on
              RewriteRule  ^$ public/    [L]
              RewriteRule  ((?s).*) public/$1 [L]
          </Directory>
  
          <Directory "/var/www/__DOCROOT__/public">
              RewriteEngine On
              RewriteCond %{REQUEST_FILENAME} !-d
              RewriteCond %{REQUEST_FILENAME} !-f
              RewriteRule ^((?s).*)$ index.php?_url=/$1 [QSA,L]
          </Directory>
      </IfModule>
  
  	ErrorLog ${APACHE_LOG_DIR}/__DOCROOT__-error.log
  
  	# Possible values include: debug, info, notice, warn, error, crit,
  	# alert, emerg.
  	LogLevel warn
  	CustomLog ${APACHE_LOG_DIR}/access.log combined
  
      Header set Access-Control-Allow-Origin "*"
      Header set Access-Control-Allow-Methods "GET,PUT,POST,DELETE,OPTIONS"
      Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Authentication"
  </VirtualHost>
  ```
  
To install project dependencies, use composer:
```bash
cd /var/www/__DOCROOT__
composer install
```

Sample configuration in app/config/config.php:
```php
<?php
defined('BASE_PATH') || define('BASE_PATH', getenv('BASE_PATH') ?: realpath(dirname(__FILE__) . '/../..'));
defined('APP_PATH') || define('APP_PATH', BASE_PATH . '/app');

return new \Phalcon\Config([
   'elasticsearch' => [
       'hosts' => [
           'http://es:9200'
       ],
       'indexPrefix' => ''
   ],
    'database' => [
        'adapter'     => 'Mysql',
        'host'        => '*************',
        'username'    => 'root',
        'password'    => '',
        'dbname'      => 'cp_call',
        'charset'     => 'utf8',
    ],
    'replica' => [
        'adapter'     => 'Mysql',
        'host'        => '*************',
        'username'    => 'root',
        'password'    => '',
        'dbname'      => 'cp_call',
        'charset'     => 'utf8',
    ],
   'loglevel' => 4,
   'serviceName' => 'call',
   'application' => [
       'appDir'         => APP_PATH . '/',
       'controllersDir' => APP_PATH . '/controllers/',
       'modelsDir'      => APP_PATH . '/models/',
       'migrationsDir'  => APP_PATH . '/migrations/',
       'pluginsDir'     => APP_PATH . '/plugins/',
       'libraryDir'     => APP_PATH . '/library/',
       'cacheDir'       => BASE_PATH . '/cache/',
       'mappingDir'     => BASE_PATH . '/mapping/',
       'baseUri'        => '/',
       'debug'          => 1,
       'logLevel'       => 6,
       'logFile'        => null,
       'services'       => [
           'core'  => 'http://core',
           'loc'   => 'http://loc',
           'acct'  => 'http://acct',
           'int'   => 'http://int'
       ],
       'staticToken' => [
           'readOnly' => '',
           'readWrite' => ''
       ]
   ],
   'twilio' => [
       "credentials" => [
           "account_sid" => "",
           "auth_token" => ""
       ]
   ]
]);
```
