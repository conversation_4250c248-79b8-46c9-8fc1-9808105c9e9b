import { Injectable } from '@nestjs/common';
import {
  AccountService,
  LocationService,
  CoreService,
  IntegrationService,
  DataDogService,
  BugsnagService,
} from '@cp-workspace/shared';
import { I18nTranslationService } from './I18nTranslation.service';
import { ModuleRef } from '@nestjs/core';

@Injectable()
export class PayByPhoneContextService {
  constructor(
    public i18n: I18nTranslationService,
    public accountService: AccountService,
    public locationService: LocationService,
    public coreService: CoreService,
    public integrationService: IntegrationService,
    public dataDogService: DataDogService,
    public bugsnagService: BugsnagService,
    public readonly moduleRef: ModuleRef,
  ) { }
}
