const path = require('path');
const { NestFactory } = require('@nestjs/core');
const { I18nModule } = require('nestjs-i18n');

async function generate() {
  const app = await NestFactory.createApplicationContext(I18nModule.forRoot({
    loaderOptions: {
      path: path.join(__dirname, '../../../../libs/pay-by-phone-domain/src/i18n/')
    },
    typesOutputPath: path.join(__dirname, '../../../../libs/pay-by-phone-domain/src/lib/PayByPhone/Generated/i18n.generated.ts'),
  }));

  await app.close();
}

generate(); //node generate-types.js