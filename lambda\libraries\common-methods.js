"use strict";
var _ = require('lodash');
var main_config = require('../config/config');
const console_c = require('../config/logger').console;
var miscellaneous = require('../config/miscellaneous');
const moment = require('moment-timezone');
const cpapiClient = require('./cpapi-client');

/**
 * CommonMethod Class
 *
 *
 * @package    Callpotential
 * @subpackage Application
 * @category   Libraries
 * <AUTHOR>
 * @link       void
 */
var commonMethod = class CommonMethod {
  async send_email_notification(email_body, subject, addresses, from_emails) {
    if (!addresses)
      addresses = [];
    if (!from_emails)
      from_emails = {};

    var from_email = miscellaneous.EMAIL_DETAILS.NOTIFICATION_EMAIL;
    var from_name = 'CallPotential';

    if (from_emails['email']) {
      from_email = from_emails['email'];
    }

    if (from_emails['name']) {
      from_name = from_emails['name'];
    }

    var toEmails = [];
    if (addresses['to_email']) {
      _.each(addresses['to_email'], function(to_value) {
          toEmails.push({"email" : to_value, "name" : ""});
      });
    }

    var bccEmails = [];
    if (addresses['bcc_email']) {
      _.each(addresses['bcc_email'], function(bcc_value) {
        bccEmails.push({"email" : bcc_value, "name" : ""});
      });
    }

    var ccEmails = [];
    if (addresses['cc_email']) {
      _.each(addresses['cc_email'], function(cc_value) {
        ccEmails.push({"email" : cc_value, "name" : ""});
      });
    }

    const emailClient = new cpapiClient.emailClient(main_config.db.serviceTokens.readWrite);
    let emailPayload = {
        "systemEmail": 1,
        "from": {"email" : from_email, "name" : from_name},
        "to": toEmails,
        "subject": subject,
        "body": email_body,
        "bcc": bccEmails,
        "cc": ccEmails,
        "isHtml": 1
      };

    return await emailClient.postData('email', emailPayload);
  }

  async get_custom_variables_mapping(data) {
    const coreClient = new cpapiClient.coreClient(main_config.db.serviceTokens.readWrite);
    const user = await coreClient.cache.getData(`user/${data['parent_id']}`);
    let custom_variables = user.custom_variables;
    let loc_custom_variables = {};
    let final_custom_variables = {};

    if (custom_variables) {
      custom_variables = JSON.parse(custom_variables);

      if (custom_variables['custom_variables']) {
        custom_variables = custom_variables['custom_variables'];
      }
    }

    let location_variables = data.destination_data.custom_variables;

    if (location_variables && location_variables.length > 0)
    {
      loc_custom_variables = JSON.parse(location_variables);
    }

    if (Object.keys(custom_variables).length > 0) {
      let locVars = [];
      if (Object.keys(loc_custom_variables).length > 0 && loc_custom_variables['custom_variables']) {
        locVars = loc_custom_variables['custom_variables'];
      }

      const localOverrideMap = locVars.reduce((acc, item) => {
        acc[item.name] = item.value;
        return acc;
      }, {});

      // check each default for a location specific override and apply
      custom_variables.forEach((defaultVar) => {

        // eslint-disable-next-line no-prototype-builtins
        if (localOverrideMap.hasOwnProperty(defaultVar.name)){
          final_custom_variables[defaultVar.name] = localOverrideMap[defaultVar.name];  
        }else{
          final_custom_variables[defaultVar.name] = defaultVar.value;  
        }

      });

      // check each location specific override for a default and add if missing
      Object.getOwnPropertyNames(localOverrideMap).forEach((varName) => {
        // eslint-disable-next-line no-prototype-builtins
        if (!final_custom_variables.hasOwnProperty(varName)){
          final_custom_variables[varName] = localOverrideMap[varName];
        }
      });
    }
    return final_custom_variables;
  }

  isLocationOpen(business_days, timeZone, isCallcenterHours) {

    const hours_availability = JSON.parse(business_days);
    let isOpen = false;
  
    console_c.log("LOCATION TIMEZONE:", timeZone);

    const now = moment().tz(timeZone);
    let hoursList = hours_availability.office_hours.filter((d) => now.format("dddd") === d.weekday);
    if (isCallcenterHours) {
      hoursList = hours_availability.callcenter_hours.filter((d) => now.format("dddd") === d.weekday);
    }

    if (hoursList.length > 0) {
  
      const hours = hoursList[0];
      if (hours.closed) {
        console_c.log("CLOSED ON THIS DAY!");
      } else {
  
        /*
          get office hours for day
          open time = day + open office hour
          close time = day + close office hour
          get current time
          is current time between open and close time?
        */
        const fmt = "dddd, MMMM Do YYYY, h:mm:ss a";
  
        const setExplicitTime = (dateMoment, timeMoment) => moment(dateMoment).hour(timeMoment.hour()).minute(timeMoment.minute()).second(timeMoment.second());
  
        const officeOpen = hours.from_hour;
        const officeClose = hours.to_hour;
        const timeFmt = "hh:mmA";
        const localTime = moment.utc();
        const timeCurrent = moment(localTime).tz(timeZone);
        const hourOpen = moment(officeOpen, timeFmt);
        const hourClose = moment(officeClose, timeFmt);
        const timeOpen = setExplicitTime(timeCurrent, hourOpen);
        const timeClose = setExplicitTime(timeCurrent, hourClose);
  
        const printIt = (lbl, time) => console_c.log(`${lbl} ${time.format(fmt)} (${time.tz()})`);
  
        printIt("UTC TIME: ", localTime);
        printIt("ZONE TIME : ", timeCurrent);
        console_c.log("---");
        printIt("OPENS at  : ", timeOpen);
        printIt("CLOSES at : ", timeClose);
        console_c.log("---");
  
        // https://momentjs.com/docs/#/query/is-between/
        isOpen = timeCurrent.isBetween(timeOpen, timeClose, undefined, '[)');
  
      }
  
    }
  
    console_c.log(`LOCATION IS ${isOpen ? "OPEN" : "CLOSED"}`);
  
    return isOpen;
  
  }

  escapeTwilioChars(string) {
    if (typeof string === 'string') {
      return string.replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;")
        .replace(/%/g, "&#37;");
    } else {
      return string;
    }
  }


  async invokeLambdaFunction(payload, async_path) {

    let restPayload = {
      "resource": '/{proxy+}',
      "path": `${main_config.env_url}${async_path}`,
      "httpMethod": 'POST',
      "headers": {},
      "multiValueHeaders": {
        'Content-Type': [ 'application/json; charset=UTF-8' ],
      },
      "queryStringParameters": null,
      "multiValueQueryStringParameters": null,
      "pathParameters": { proxy: `v1${async_path}` },
      "requestContext": {
        "resourcePath": '/{proxy+}',
        "httpMethod": 'POST',
        "path": `${main_config.env_url}${async_path}`,
      },
      "asyncBody": payload,
      "isBase64Encoded": false,
      "asyncInvoke": true
    }

    var AWS = require("aws-sdk");
    let lambda = new AWS.Lambda({
      region: process.env.AWS_REGION || 'us-west-2'
    });

    let params = {
      FunctionName: process.env.AWS_LAMBDA_FUNCTION_NAME,
      InvocationType: 'Event',
      LogType: 'Tail',
      Payload: JSON.stringify(restPayload)
    };

    return await lambda.invoke(params).promise()
      .catch((e) => {
        console.error(e, new Error().stack);
      });
  }
}

/*
  CPIC-1051
  Get workflow SID by friendlyName
*/
async function fetchWorkflow(client, workspaceSid, friendlyName){

  const workflows = await client.taskrouter.workspaces(workspaceSid)
  .workflows
  .list({limit: 1, friendlyName });

  if (workflows.length > 0){
    return workflows[0];
  }else{
    throw new Error(`Missing Workflow: "${friendlyName}"`);
  }

}

function getTwilioClient(accountSid, authToken){
  return require('twilio')(accountSid, authToken);
}

/*
  CPIC-1051
  Retrieve a list of available workers in a task queue.
*/
async function getWorkers(client, workspaceSid, taskQueueSid) {

  const findWorkersExpression = `cc_queues HAS '${taskQueueSid}'`;

  console_c.log('FIND WORKERS:', findWorkersExpression);

  const workers = await client.taskrouter.workspaces(workspaceSid)
    .workers
    .list({
      targetWorkersExpression: findWorkersExpression
    });

  const free = workers.filter((worker) => worker.activityName === 'Ready');

  console_c.log('AVAILABLE WORKERS', free.length);

  if (free.length > 0) {

    console_c.log('WORKER DETAILS', free);

    return free;

  }

  return [];

}

/*
  CPIC-1051
  Ensure that a given set of workers has the required worker task
  channel capacity.
*/
async function ensureWorkerTaskChannelCapacity(client, workspaceSid, taskQueueSid, taskChannelUniqueName, minCapacity, newCapacity) {

  const workers = await getWorkers(client, workspaceSid, taskQueueSid);

  for (let i = 0; i < workers.length; i++) {

    const worker = workers[i];

    console_c.log("WORKER", worker.sid);

    const channels = await client.taskrouter.workspaces(workspaceSid)
      .workers(worker.sid)
      .workerChannels
      .list();

    const filtered = channels.filter(ch => ch.taskChannelUniqueName === taskChannelUniqueName);
    const tc = filtered.length > 0 ? filtered[0] : null;
    if (tc && tc.configuredCapacity <= minCapacity) {
      console_c.log(`TASK CHANNEL, "${taskChannelUniqueName}", CAPACITY = ${tc.configuredCapacity}, UPDATE TO CAPACITY = ${newCapacity}`);
      const updatedChannel = await updateWorkerTaskChannelCapacity(client, workspaceSid, worker.sid, tc.sid, newCapacity);
      console_c.log(`CHANNEL UPDATED, CAPACITY = ${updatedChannel.configuredCapacity}`);
    } else {
      console_c.log(`TASK CHANNEL, "${taskChannelUniqueName}", CAPACITY = ${tc.configuredCapacity}`);
    }

  }

}

function updateWorkerTaskChannelCapacity(client, workspaceSid, workerSid, workerChannelSid, newCapacity) {

  return client.taskrouter.workspaces(workspaceSid)
    .workers(workerSid)
    .workerChannels(workerChannelSid)
    .update({ capacity: newCapacity });

}

async function fetchTaskQueue(client, workspaceSid, taskQueueName) {

  const queues = await client.taskrouter.workspaces(workspaceSid)
  .taskQueues
  .list({limit: 1, friendlyName: taskQueueName });

  if (queues.length === 1){
    return queues[0];
  }else{
    return null;
  }

}

module.exports = {
  'CommonMethod': commonMethod,
  fetchWorkflow, getTwilioClient,
  ensureWorkerTaskChannelCapacity,
  fetchTaskQueue
}
