import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class ConfirmCustomerInfo extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { request, storage } = context;

    const customerInput = request.Digits;
    const noResponseFromCustomer = !customerInput || customerInput.length === 0;
    const transferToAgentResponse = customerInput && customerInput !== '1';
    const shouldTransferToAgent = transferToAgentResponse || noResponseFromCustomer || !storage.matchedTenants;
    if (shouldTransferToAgent) {
      return { nextState: PayByPhoneState.TransferToAgent };
    }
    if (storage.isDelinquentPaymentFlow) {
      return { nextState: PayByPhoneState.SayAmountDue };
    }

    return { nextState: PayByPhoneState.UnitsToPayPrepayMonthsPrompt };
  }
}
