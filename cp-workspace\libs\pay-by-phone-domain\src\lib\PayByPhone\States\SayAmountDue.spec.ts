import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { SayAmountDue } from './SayAmountDue';
import { Locale } from '@cp-workspace/shared';

describe('SayAmountDue', () => {
  let sayAmountDue: SayAmountDue;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SayAmountDue],
    }).compile();

    sayAmountDue = module.get<SayAmountDue>(SayAmountDue);
    sayAmountDue.services = {
      integrationService: {
        calculateAmountDue: jest.fn().mockResolvedValue(500.58)
      } as any
    } as any;

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.SayAmountDue,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        selectedUnits: [
          { ledger_id: 101, unit_id: 'unit101', tenant_id: 'tenant101', amount_owed: 250.29 } as any,
          { ledger_id: 102, unit_id: 'unit102', tenant_id: 'tenant102', amount_owed: 250.29 } as any,
        ],
        prepayMonths: undefined,
        convenienceFee: 0,
        totalBalance: 0, // 500.58,
        totalAmountDue: 0, // 500.58
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should calculate total amount due and announce it', async () => {
      const response: PayByPhoneStateHandlerResponse = await sayAmountDue.handler(context);

      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({
        messageId: 'pay-by-phone.amount-due',
        locale: Locale.English,
        i18nOptions: { args: [{ paymentAmount: "500.58" }] }
      });
      expect(response.nextState).toBe(PayByPhoneState.GetSavedCards);
    });

    it('should store the total amount due in storage.totalAmountDue', async () => {
      await sayAmountDue.handler(context);

      expect(context.storage.totalAmountDue).toBe(500.58);
    });

    it('should ensure paymentAmount is always 2 decimal places', async () => {
      context.storage.convenienceFee = 0.321;

      await sayAmountDue.handler(context);

      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({
        i18nOptions: { args: [{ paymentAmount: "500.90" }] },
        locale: "en-US",
        messageId: "pay-by-phone.amount-due",
      });
    });
  });
});
