/*
  This configuration needs to be read from a configuration service.  For demonstration
  purposes, this file is used to quickly provide configuration setup for the following scenarios:
  - location only
  - call center only
  - location first, call center next
  - call center first, location next

  NOTE: Since its not clear how to run LC from a web browser, I will simulate the 4 types of routing above
  by routing messages to a specific worker considered to be "location", a worker considered to be "call center",
  and a worker considered to be "existing conversation".
*/
const messagingServiceSid = "MG680765415513df917b43becb49196359";
const ccQueueSid = "WQ66cd3d08aaa4fa04692159415d287b49";

const CHAT_CONFIG_LOC = {
  "messaging_service_sid": messagingServiceSid,
  "contact_center_queue_sid": ccQueueSid,
  "location_after": 5,
  "updated": "2021-01-27 12:46:31",
  "conversation_auto_reply": "Thanks for contacting the {system.LocName}. A team member will reply as soon as they are available.",
  "call_center_after": 5,
  "unavailable_auto_reply": "Unfortunately, all team members are currently unavailable.",
  "created": "2021-01-27 12:46:31",
  "account_id": 880,
  "routing_type": "loc"
};

const CHAT_CONFIG_CC = {
  "messaging_service_sid": messagingServiceSid,
  "contact_center_queue_sid": ccQueueSid,
  "location_after": 5,
  "updated": "2021-01-27 12:46:31",
  "conversation_auto_reply": "Thanks for contacting the {system.LocName}. A team member will reply as soon as they are available.",
  "call_center_after": 5,
  "unavailable_auto_reply": "Unfortunately, all team members are currently unavailable.",
  "created": "2021-01-27 12:46:31",
  "account_id": 880,
  "routing_type": "cc"
};

const CHAT_CONFIG_LOC_FIRST = {
  "messaging_service_sid": messagingServiceSid,
  "contact_center_queue_sid": ccQueueSid,
  "location_after": 5,
  "updated": "2021-01-27 12:46:31",
  "conversation_auto_reply": "Thanks for contacting the {system.LocName}. A team member will reply as soon as they are available.",
  "call_center_after": 5,
  "unavailable_auto_reply": "Unfortunately, all team members are currently unavailable.",
  "created": "2021-01-27 12:46:31",
  "account_id": 880,
  "routing_type": "loc-first"
};

const CHAT_CONFIG_CC_FIRST = {
  "messaging_service_sid": messagingServiceSid,
  "contact_center_queue_sid": ccQueueSid,
  "location_after": 5,
  "updated": "2021-01-27 12:46:31",
  "conversation_auto_reply": "Thanks for contacting the {system.LocName}. A team member will reply as soon as they are available.",
  "call_center_after": 5,
  "unavailable_auto_reply": "Unfortunately, all team members are currently unavailable.",
  "created": "2021-01-27 12:46:31",
  "account_id": 880,
  "routing_type": "cc-first"
};

module.exports = CHAT_CONFIG_LOC;