<?php
/**
 * Client factory library
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 8/31/17
 * Time: 2:28 PM
 *
 * @category ClientFactory
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\CoreClient;
use Phalcon\DI;

/**
 * Client factory library
 *
 * @category ClientFactory
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class ClientFactory
{
    /**
     * Retrieve core client object
     *
     * @param mixed $token auth token
     *
     * @return CoreClient
     */
    public static function getCoreClient($token = null): CoreClient
    {
        $di = Di::getDefault();
        $config = $di->getShared('config');
        if (is_null($token)) {
            $token = $config->application['staticToken']['readWrite'];
        }

        return new CoreClient((array) $config, $token, $config->application['services']['core']);
    }
}
