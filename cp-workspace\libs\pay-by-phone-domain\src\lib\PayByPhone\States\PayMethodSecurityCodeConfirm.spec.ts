import { PayMethodSecurityCodeConfirm } from './PayMethodSecurityCodeConfirm';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodSecurityCodeConfirm', () => {
  let service: PayMethodSecurityCodeConfirm;
  let mockLocationService: any;

  beforeEach(() => {
    mockLocationService = {
      getLocationDetails: jest.fn().mockImplementation(() => Promise.resolve({ country: 'US' })),
    };

    service = new PayMethodSecurityCodeConfirm();
    service.services = {
      locationService: mockLocationService,
    } as any;
  });

  it('should transition to PayMethodPostalCodePrompt when confirmation selection is 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
      services: {
        locationService: mockLocationService,
      },
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodPostalCodePrompt);
  });

  it('should transition to PayMethodSecurityCodePrompt when confirmation selection is not 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '2',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
      services: {
        locationService: mockLocationService,
      },
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodSecurityCodePrompt);
  });

  it('should transition to FinalPayAmountPrompt when location is not in the US', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en', locationId: 880 } as any,
      services: {
        locationService: mockLocationService,
      },
    } as any;

    mockLocationService.getLocationDetails.mockResolvedValue({ country: 'CA' });

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.FinalPayAmountPrompt);
  });
});
