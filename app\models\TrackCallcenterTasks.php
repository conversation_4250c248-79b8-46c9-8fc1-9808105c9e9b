<?php
/**
 * TrackCallcenterTasks model
 *
 * @category Trackcallcentertasks
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Model
 *
 * @category Trackcallcentertasks
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Trackcallcentertasks")
 */
class TrackCallcenterTasks extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * Model Property
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $id;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $task_sid;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $log_id;

    /**
     * Model Property Dynamo call_logs table log_id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $dynamo_log_id;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $queue_id;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $config_step_uid;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $reservation_created;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $reservation_accepted;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $reservation_rejected;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $reservation_timeout;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $agent_id;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $outcome;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $retry_attempt;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $date_created;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $date_modified;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $is_rolled_over;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $is_abandoned;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $is_task;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $is_task_complete;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $queue_time;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $queue_sid;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $channel;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $workflow;

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Method to set the value of field id
     *
     * @param integer $id id
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field task_sid
     *
     * @return string
     */
    public function getTaskSid(): string
    {
        return $this->task_sid;
    }

    /**
     * Method to set the value of field task_sid
     *
     * @param string $taskSid
     *
     * @return $this
     */
    public function setTaskSid(string $taskSid)
    {
        $this->task_sid = $taskSid;

        return $this;
    }

    /**
     * Method to set the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Method to set the value of field log_id
     *
     * @param integer $logId
     *
     * @return $this
     */
    public function setLogId(int $logId)
    {
        $this->log_id = $logId;

        return $this;
    }

    /**
     * Method to set the value of field queue_id
     *
     * @return integer
     */
    public function getQueueId(): int
    {
        return $this->queue_id;
    }

    /**
     * Method to set the value of field queue_id
     *
     * @param integer $queueId
     *
     * @return $this
     */
    public function setQueueId(int $queueId)
    {
        $this->queue_id = $queueId;

        return $this;
    }

    /**
     * Method to set the value of field config_step_uid
     *
     * @return string
     */
    public function getConfigStepUid(): string
    {
        return $this->config_step_uid;
    }

    /**
     * Method to set the value of field config_step_uid
     *
     * @param string $configStepUid
     *
     * @return $this
     */
    public function setConfigStepUid(string $configStepUid)
    {
        $this->config_step_uid = $configStepUid;

        return $this;
    }

    /**
     * Method to set the value of field reservation_created
     *
     * @return string
     */
    public function getReservationCreated(): string
    {
        return $this->reservation_created;
    }

    /**
     * Method to set the value of field reservation_created
     *
     * @param string $reservationCreated
     *
     * @return $this
     */
    public function setReservationCreated(string $reservationCreated)
    {
        $this->reservation_created = $reservationCreated;

        return $this;
    }

    /**
     * Method to set the value of field reservation_accepted
     *
     * @return string
     */
    public function getReservationAccepted(): string
    {
        return $this->reservation_accepted;
    }

    /**
     * Method to set the value of field reservation_accepted
     *
     * @param string $reservationAccepted
     *
     * @return $this
     */
    public function setReservationAccepted(string $reservationAccepted)
    {
        $this->reservation_accepted = $reservationAccepted;

        return $this;
    }

    /**
     * Method to set the value of field reservation_rejected
     *
     * @return string
     */
    public function getReservationRejected(): string
    {
        return $this->reservation_rejected;
    }

    /**
     * Method to set the value of field reservation_rejected
     *
     * @param string $reservationRejected
     *
     * @return $this
     */
    public function setReservationRejected(string $reservationRejected)
    {
        $this->reservation_rejected = $reservationRejected;

        return $this;
    }

    /**
     * Method to set the value of field reservation_timeout
     *
     * @return string
     */
    public function getReservationTimeout(): string
    {
        return $this->reservation_timeout;
    }

    /**
     * Method to set the value of field reservation_timeout
     *
     * @param string $reservationTimeout
     *
     * @return $this
     */
    public function setReservationTimeout(string $reservationTimeout)
    {
        $this->reservation_timeout = $reservationTimeout;

        return $this;
    }

    /**
     * Method to set the value of field agent_id
     *
     * @return integer
     */
    public function getAgentId(): int
    {
        return $this->agent_id;
    }

    /**
     * Method to set the value of field agent_id
     *
     * @param integer $agentId
     *
     * @return $this
     */
    public function setAgentId(int $agentId)
    {
        $this->agent_id = $agentId;

        return $this;
    }

    /**
     * Method to set the value of field outcome
     *
     * @return integer
     */
    public function getOutcome(): int
    {
        return $this->outcome;
    }

    /**
     * Method to set the value of field outcome
     *
     * @param integer $outcome
     *
     * @return $this
     */
    public function setOutcome(int $outcome)
    {
        $this->outcome = $outcome;

        return $this;
    }

    /**
     * Method to set the value of field retry_attempt
     *
     * @return integer
     */
    public function getRetryAttempt(): int
    {
        return $this->retry_attempt;
    }

    /**
     * Method to set the value of field retry_attempt
     *
     * @param integer $retryAttempt
     *
     * @return $this
     */
    public function setRetryAttempt(int $retryAttempt)
    {
        $this->retry_attempt = $retryAttempt;

        return $this;
    }

    /**
     * Method to set the value of field date_created
     *
     * @return string
     */
    public function getDateCreated(): string
    {
        return $this->date_created;
    }

    /**
     * Method to set the value of field date_created
     *
     * @param string $dateCreated
     *
     * @return $this
     */
    public function setDateCreated(string $dateCreated)
    {
        $this->date_created = $dateCreated;

        return $this;
    }

    /**
     * Method to set the value of field date_modified
     *
     * @return string
     */
    public function getDateModified(): string
    {
        return $this->date_modified;
    }

    /**
     * Method to set the value of field date_modified
     *
     * @param string $dateModified
     *
     * @return $this
     */
    public function setDateModified(string $dateModified)
    {
        $this->date_modified = $dateModified;

        return $this;
    }

    /**
     * Method to set the value of field is_rolled_over
     *
     * @return integer
     */
    public function getIsRolledOver(): int
    {
        return $this->is_rolled_over;
    }

    /**
     * Method to set the value of field is_rolled_over
     *
     * @param integer $isRolledOver
     *
     * @return $this
     */
    public function setIsRolledOver(int $isRolledOver)
    {
        $this->is_rolled_over = $isRolledOver;

        return $this;
    }

    /**
     * Method to set the value of field is_abandoned
     *
     * @return integer
     */
    public function getIsAbandoned(): int
    {
        return $this->is_abandoned;
    }

    /**
     * Method to set the value of field is_abandoned
     *
     * @param integer $isAbandoned
     *
     * @return $this
     */
    public function setIsAbandoned(int $isAbandoned)
    {
        $this->is_abandoned = $isAbandoned;

        return $this;
    }

    /**
     * Method to set the value of field is_task
     *
     * @return integer
     */
    public function getIsTask(): int
    {
        return $this->is_task;
    }

    /**
     * Method to set the value of field is_task
     *
     * @param integer $isTask
     *
     * @return $this
     */
    public function setIsTask(int $isTask)
    {
        $this->is_task = $isTask;

        return $this;
    }

    /**
     * Method to set the value of field is_task_complete
     *
     * @return integer
     */
    public function getIsTaskComplete(): int
    {
        return $this->is_task_complete;
    }

    /**
     * Method to set the value of field is_task_complete
     *
     * @param string $isTaskComplete
     *
     * @return $this
     */
    public function setIsTaskComplete(int $isTaskComplete)
    {
        $this->is_task_complete = $isTaskComplete;

        return $this;
    }

    /**
     * Method to set the value of field queue_time
     *
     * @return integer
     */
    public function getQueueTime(): int
    {
        return $this->queue_time;
    }

    /**
     * Method to set the value of field queue_time
     *
     * @param integer $queueTime
     *
     * @return $this
     */
    public function setQueueTime(int $queueTime)
    {
        $this->queue_time = $queueTime;

        return $this;
    }

    /**
     * Method to set the value of field queue_sid
     *
     * @return string
     */
    public function getQueueSid(): string
    {
        return $this->queue_sid;
    }

    /**
     * Method to set the value of field queue_sid
     *
     * @param string $queueSid
     *
     * @return $this
     */
    public function setQueueSid(string $queueSid)
    {
        $this->queue_sid = $queueSid;

        return $this;
    }

    /**
     * Method to set the value of field channel
     *
     * @return string
     */
    public function getChannel(): string
    {
        return $this->channel;
    }

    /**
     * Method to set the value of field channel
     *
     * @param string $channel
     *
     * @return $this
     */
    public function setChannel(string $channel)
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * Method to set the value of field workflow
     *
     * @return string
     */
    public function getWorkflow(): string
    {
        return $this->workflow;
    }

    /**
     * Method to set the value of field workflow
     *
     * @param string $workflow
     *
     * @return $this
     */
    public function setWorkflow(string $workflow)
    {
        $this->workflow = $workflow;

        return $this;
    }

    /**
     * Method to set the value of field dynamo_log_id
     *
     * @return integer
     */
    public function getDynamoLogId(): int
    {
        return $this->dynamo_log_id;
    }

    /**
     * Method to set the value of field dynamo_log_id
     *
     * @param integer $dynamoLogId
     *
     * @return $this
     */
    public function setDynamoLogId(int $dynamoLogId)
    {
        $this->dynamo_log_id = $dynamoLogId;

        return $this;
    }

    /**
     * Initialize function
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setConnectionService('dbLegacy');
        $this->setSource("track_callcenter_tasks");
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters Parameters
     *
     * @return Ads[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters
     *
     * @return Ads
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
