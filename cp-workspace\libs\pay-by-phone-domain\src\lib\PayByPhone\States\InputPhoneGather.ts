import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class InputPhoneGather extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { twilioResponse, storage } = context;    

    twilioResponse.gatherWithLocaleSay({
      numDigits: 10,
      method: 'POST',
      timeout: 10,
      finishOnKey: '#',
    }, [{
      messageId: 'pay-by-phone.ask-for-number',
      locale: storage.locale
    }]);
    
    return { nextState: PayByPhoneState.InputPhoneValidate };
  }
}
