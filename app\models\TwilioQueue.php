<?php
/**
 * TwilioQueue model
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 8/1/17
 * Time: 1:29 PM
 *
 * @category TwilioQueue
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */

use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\Util;

use Aws\DynamoDb\Exception\DynamoDbException;
use Aws\DynamoDb\Marshaler;

/**
 * TwilioQueue model
 *
 * @category TwilioQueue
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="TwilioQueueDynamo")
 */
class TwilioQueue extends CallPotential\CPCommon\RestModel
{
    use JsonModelTrait, CallPotential\CPCommon\Models\DynamoModel;

    /**
     * Table name
     *
     * @var string
     */
    protected $table = 'twilio_queues';

    /**
     * Account Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     *
     * @Column(type="integer", length=20, nullable=true)
     *
     * @SWG\Property(description="account ID (user_id/parent_id from legacy user table)")
     */
    protected $account_id;

    /**
     * Queue SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     *
     * @SWG\Property(description="twilio queue SID")
     */
    public $queue_sid;

    /**
     * Friendly name
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     *
     * @SWG\Property(description="Twilio queue name")
     */
    protected $FriendlyName;

    /**
     * Delay for agent
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     *
     * @SWG\Property(description="Delay between agent calls")
     */
    protected $delay_for_agent;

    /**
     * Number of agent
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     *
     * @SWG\Property(description="Number of agents to ring")
     */
    protected $number_of_agent;

    /**
     * Hold music
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     *
     * @SWG\Property(description="Hold music")
     */
    protected $hold_music;

    /**
     * Notify caller status
     *
     * @var int
     *
     * @Column(type="integer")
     *
     * @SWG\Property()
     */
    protected $notify_caller_status;

    //setters
    /**
     * Method to set the value of field account_id
     *
     * @param integer $account_id value to set
     *
     * @return $this
     */
    public function setAccountId(int $account_id)
    {
        $this->account_id = $account_id;

        return $this;
    }

    /**
     * Method to set the value of field queue_sid
     *
     * @param string $queue_sid value to set
     *
     * @return $this
     */
    public function setQueueDid(string $queue_sid)
    {
        $this->queue_sid = $queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field FriendlyName
     *
     * @param string $FriendlyName value to set
     *
     * @return $this
     */
    public function setFriendlyName(string $FriendlyName)
    {
        $this->FriendlyName = $FriendlyName;

        return $this;
    }

    /**
     * Method to set the value of field delay_for_agent
     *
     * @param integer $delay_for_agent value to set
     *
     * @return $this
     */
    public function setDelayForAgent(int $delay_for_agent)
    {
        $this->delay_for_agent = $delay_for_agent;

        return $this;
    }

    /**
     * Method to set the value of field number_of_agent
     *
     * @param integer $number_of_agent value to set
     *
     * @return $this
     */
    public function setNumberOfAgent(int $number_of_agent)
    {
        $this->number_of_agent = $number_of_agent;

        return $this;
    }

    /**
     * Method to set the value of field hold_music
     *
     * @param string $hold_music value to set
     *
     * @return $this
     */
    public function setHoldMusic(string $hold_music)
    {
        $this->hold_music = $hold_music;

        return $this;
    }

    /**
     * Method to set the value of field notify_caller_status
     *
     * @param integer $notify_caller_status value to set
     *
     * @return $this
     */
    public function setNotifyCallerStatus(int $notify_caller_status)
    {
        $this->notify_caller_status = $notify_caller_status;

        return $this;
    }

    //getters
    /**
     * Returns the value of field account_id
     *
     * @return integer
     */
    public function getAccountId(): int
    {
        return $this->account_id;
    }

    /**
     * Returns the value of field queue_sid
     *
     * @return string
     */
    public function getQueueDid(): string
    {
        return $this->queue_sid;
    }

    /**
     * Returns the value of field FriendlyName
     *
     * @return string
     */
    public function getFriendlyName(): string
    {
        return $this->FriendlyName;
    }

    /**
     * Returns the value of field delay_for_agent
     *
     * @return integer
     */
    public function getDelayForAgent(): int
    {
        return $this->delay_for_agent;
    }

    /**
     * Returns the value of field number_of_agent
     *
     * @return integer
     */
    public function getNumberOfAgent(): int
    {
        return $this->number_of_agent;
    }

    /**
     * Returns the value of field hold_music
     *
     * @return string
     */
    public function getHoldMusic(): string
    {
        return $this->hold_music;
    }

    /**
     * Returns the value of field notify_caller_status
     *
     * @return integer
     */
    public function getNotifyCallerStatus(): int
    {
        return $this->notify_caller_status;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setConnectionService('dbLegacy');
        $this->setSource("TwilioQueue");
    }


    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallDetail[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallDetail
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
