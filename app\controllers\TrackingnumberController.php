<?php
/**
 * Manipulates data with tracking numbers
 *
 * @category TrackingnumberController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use \Phalcon\Mvc\Model\Query\Builder;
use Phalcon\Paginator\Adapter\QueryBuilder as PaginatorQueryBuilder;
use CallPotential\CPCommon\LoggerTrait;
use Phalcon\Mvc\Model\Resultset;

use \CallPotential\CPCommon\Controllers\MysqlControllerTrait;
use CallPotential\CPCommon\Controllers\SessionTrait;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="TrackingnumberListItem",
 * @SWG\Property(property="trackingnumber",ref="#/definitions/Trackingnumber"),
 * @SWG\Property(property="ad_name",type="string"),
 * @SWG\Property(property="location_name",type="string"),
 * @SWG\Property(property="location_phone",type="string")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="TrackingnumberList",
 * @SWG\Property(
 *     property="items",
 *     type="array",
 * @SWG\Items(ref="#/definitions/TrackingnumberListItem")
 *   ),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="TrackingnumberBulkResultItem",
 * @SWG\Property(property="message",type="string"),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/Trackingnumber"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="TrackingnumberBulkResult",
 * @SWG\Property(property="success",type="array",@SWG\Items(type="number")),
 * @SWG\Property(property="error",type="array",@SWG\Items(ref="#/definitions/TrackingnumberBulkResultItem"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="TrackingnumberBulkResponse",
 * @SWG\Property(property="status",type="string",enum={"OK","ERROR","MIXED"}),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/TrackingnumberBulkResult"))
 * )
 */

/**
 * Manipulates data with tracking numbers
 *
 * @category TrackingnumberController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class TrackingnumberController extends CallPotential\CPCommon\Controllers\BaseController
{
    use SessionTrait, MysqlControllerTrait;

    /**
     * Filters to apply while querying for data that typecast fields values passed in GET action
     *
     * @var array
     */
    public $listFilters = [
        "active"      => "bool",
        "call_number" => "string",
    ];

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($parentId);
        if (!$this->userHasWriteAccess($data)) {
            return $this->sendAccessDeniedResponse();
        }

        $data = $this->preProcessPostData($data);
        $modelName = $this->getModelName();
        $model = new $modelName();
        $model->assign($data);
        if ($model->save()) {
            return $model->formatJson();
        }

        return false;
    }

    /**
     * Intermediate function to prepare data for update action
     *
     * @param mixed $id       primary key value of the record
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($id, $parentId);
        if (!$this->userHasWriteAccess($data)) {
            return $this->sendAccessDeniedResponse();
        }

        $modelName = $this->getModelName();
        $model = new $modelName();
        $model->assign($data);
        if ($model->save()) {
            return $model->formatJson();
        }

        return false;
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        $this->id = (int) $id;
        if ($id) {
            $modelName = $this->getModelName();
            $data = $modelName::find('trackingnumber_id = ' . $this->id);
            if ($data->count() > 0) {
                $data->setHydrateMode(Resultset::HYDRATE_ARRAYS);
                $result = $this->extractData($data);

                return $result;
            }
        }

        return false;
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Tracking Number"},
     *     path="/trackingnumber/{id}",
     *     description="Returns an tracking number based on a single ID",
     *     summary="get tracking number",
     *     operationId="TrackingnumberGetById",
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of tracking number to fetch",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="integer"
     *     ),
     *     produces={
     *         "application/json"
     *     },
     * @SWG\Response(
     *         response=200,
     *         description="tracking number response",
     * @SWG\Schema(ref="#/definitions/Trackingnumber")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }

        return parent::getAction();
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Tracking Number"},
     *     path="/trackingnumber",
     *     description="Returns all tracking numbers from the system that the user has access to",
     *     summary="list tracking numbers",
     *     operationId="ListTrackingnumbers",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="Page Number",
     *         format="int64",
     *         in="query",
     *         name="page",
     *         required=false,
     *         type="integer"
     *     ),
     * @SWG\Parameter(
     *         name="perpage",
     *         in="query",
     *         description="maximum number of results to return",
     *         required=false,
     *         type="integer",
     *         format="int32"
     *     ),
     * @SWG\Parameter(
     *         name="filterActive",
     *         in="query",
     *         description="filter on active boolean field",
     *         required=false,
     *         type="string",
     *         enum={"true","false"}
     *     ),
     * @SWG\Parameter(
     *         name="filterCall_number",
     *         in="query",
     *         description="match number",
     *         required=false,
     *         type="string"
     *     ),
     * @SWG\Response(
     *         response=200,
     *         description="tracking number response",
     * @SWG\Schema(ref="#/definitions/TrackingnumberList")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response=401,
     *         description="Not Authorized",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response=500,
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }
        $userId = $this->getCurrentAccountId();
        if ((int) $userId) {
            $this->addListCondition('Trackingnumber.user_id', $userId);

            return parent::listAction();
        } else {
            $this->sendForbidden();
        }
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/trackingnumber",
     *   tags={"Tracking Number"},
     *   summary="Create a new tracking number",
     *   description="create new tracking number",
     *   summary="create tracking number",
     *   operationId="CreateTrackingnumber",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Trackingnumber record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/Trackingnumber")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/Trackingnumber")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *       response=500,
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Swagger
     *
     * @SWG\Put(path="/trackingnumber/{id}",
     *   tags={"Tracking Number"},
     *   summary="Update an existing tracking number",
     *   description="Update existing tracking number",
     *   operationId="UpdateTrackingnumber",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *   ),
     * @SWG\Parameter(
     *     description="ID of Ad type",
     *     in="path",
     *     name="id",
     *     required=true,
     *     type="integer",
     *     format="int64"
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Tracking number record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/Trackingnumber")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/Trackingnumber")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Swagger
     *
     * @SWG\Put(path="/trackingnumber",
     *   tags={"Tracking Number"},
     *   summary="Bulk update of existing tracking numbers",
     *   description="Bulk update of existing tracking numbers",
     *   operationId="UpdateTrackingnumberBulk",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="array of tracking number records",
     *     required=false,
     * @SWG\Schema(type="array",@SWG\Items(ref="#/definitions/Trackingnumber"))
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/TrackingnumberBulkResponse")
     *   ),@SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }

        return parent::saveAction();
    }

    /**
     * Swagger
     *
     * @SWG\Delete(
     *     tags={"Tracking Number"},
     *     path="/trackingnumber/{id}",
     *     description="deletes a single tracking number based on the ID supplied",
     *     summary="delete tracking number",
     *     operationId="DeleteTrackingnumber",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of tracking number to delete",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="integer"
     *     ),
     * @SWG\Response(
     *         response=204,
     *         description="tracking number deleted",
     * @SWG\Schema(type="null")
     *     ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        if (!strlen($this->authToken)) {
            return $this->sendNotAuthorized();
        }
        if (!$this->validSession()) {
            return $this->sendForbidden();
        }

        return parent::deleteAction();
    }

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    protected function getModelName(): string
    {
        return "Trackingnumber"; //model
    }

    /**
     * This is called from controller file for validation purpose
     *
     * @return array
     */
    protected function requiredRequestParams(): array
    {
        return array();
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        $accountId = $this->getCurrentAccountId();

        return (array_key_exists('user_id', $data) &&
            (int) $data['user_id'] === $accountId && $accountId > 0
        );
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        $accountId = $this->getCurrentAccountId();

        return (array_key_exists('user_id', $data) &&
            (int) $data['user_id'] === $accountId && $accountId > 0
        );
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        $accountId = $this->getCurrentAccountId();

        return (array_key_exists('user_id', $data) &&
            (int) $data['user_id'] === $accountId && $accountId > 0
        );
    }

    /**
     * List action condition
     *
     * @param string $field field name
     * @param string $value value
     * @param string $op    operator
     * @param string $bool  and or or condition
     *
     * @return void
     */
    protected function addListCondition(
        string $field,
        string $value,
        string $op = '=',
        string $bool = "and"
    ) {
        //prefix field names with the tracking number table if there isn't already a prefix
        if (strpos($field, '.') === false) {
            $field = 'Trackingnumber.'.$field;
        }

        return parent::addListCondition($field, $value, $op, $bool);
    }

    /**
     * Format GET action response before sending to std o/p
     *
     * @param mixed $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatGetResponse($data)
    {
        $data = parent::formatGetResponse($data);
        if (is_null($data['dateRemoved'])) {
            $data['dateRemoved'] = '';
        }

        return $data;
    }

    /**
     * Format List Response change o/p of each item in list
     *
     * @param mixed $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatListResponse($data)
    {
        for ($x = 0; $x < count($data); $x++) {
            $data[$x]['trackingnumber'] = $this->formatGetResponse($data[$x]['trackingnumber']);
        }

        return parent::formatListResponse($data);
    }

    /**
     * Change POST data before sending to Data insert flow
     *
     * @param mixed $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatPostResponse($data)
    {
        $data = parent::formatPostResponse($data);
        if (is_null($data['dateRemoved'])) {
            $data['dateRemoved'] = '';
        }

        return $data;
    }

    /**
     * QueryBuilder
     *
     * @param array $params parameters
     *
     * @return \Phalcon\Mvc\Model\Query\Builder
     */
    protected function getQueryBuilder(array $params): \Phalcon\Mvc\Model\Query\Builder
    {
        $queryBuilder = new Builder($params);
        $queryBuilder = $this->setListConditions($queryBuilder);
        $queryBuilder = $this->setOrderByClause($queryBuilder);
        $queryBuilder->leftJoin('Ad', 'a.ad_id = Trackingnumber.ad_id', 'a');
        $queryBuilder->leftJoin('Location', 'loc.location_id = Trackingnumber.location_id', 'loc');
        $queryBuilder->columns(
            [
            'Trackingnumber.*',
            'a.ad_name as ad_name',
            'loc.location_name as location_name',
            'loc.phone as location_phone',
            ]
        );

        return $queryBuilder;
    }

    /**
     * Any preprocessing of post data is done here
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    protected function preProcessPostData(array $data): array
    {
        $data['user_id'] = $this->getCurrentAccountId();
        if (array_key_exists('active', $data) && $data['active'] === '0') {
            $data['dateRemoved'] = gmdate('Y-m-d H:i:s');
        }

        return $data;
    }

    /**
     * Any preprocessing of put data is done here
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param array $original original record data
     *
     * @return array
     */
    protected function preProcessPutData(array $data, array $original = []): array
    {
        /*When archive tracking number then also set dateRemoved for cron to drop phone numbers*/
        $data['user_id'] = $this->getCurrentAccountId();

        if (!empty($original)) {
            $data = array_merge($original, $data);
        }
        if (array_key_exists('active', $data) && $data['active'] === '0') {
            $data['dateRemoved'] = gmdate('Y-m-d H:i:s');
        }

        return $data;
    }
}
