<?php

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

include BASE_PATH.'/vendor/autoload.php';


use Phalcon\Loader;
use \Elasticsearch\ClientBuilder;
use Phalcon\Cli\Console as ConsoleApp;
use Phalcon\Di\FactoryDefault\Cli as CliDI;
use Phalcon\Cache\AdapterFactory;
use Phalcon\Storage\SerializerFactory;
use Bugsnag\Client as BugsnagClient;
use Bugsnag\Handler as BugsnagHandler;


// Using the CLI factory default services container
$di = new CliDI();

/**
 * Register the autoloader and tell it to register the tasks directory
 */
$loader = new Loader();

$loader->registerDirs(
    array(
        APP_PATH . '/tasks',
        APP_PATH . '/models',
        APP_PATH . '/library',
        APP_PATH . '/events',
    )
);

$loader->register();

echo APP_PATH;

// Load the configuration file (if any)

$configFile = APP_PATH . "/config/config.php";

if (is_readable($configFile)) {
    $config = include $configFile;

    $di->set("config", $config);
}

/**
 * Database connection is created based in the parameters defined in the configuration file
 */
$di->setShared('elasticsearch', function () {
    $config = $this->getConfig();
    $hosts = array();
    foreach ($config->elasticsearch->hosts as $host) {
        $hosts[] = (string) $host;
    }
    $connectionPool = '\Elasticsearch\ConnectionPool\StaticNoPingConnectionPool';
    $client = ClientBuilder::create()
        ->setHosts($hosts)
        ->setConnectionPool($connectionPool)
        ->build();

    return $client;
});

$di->setShared('dbReplica', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host'     => $config->replica->host,
        'username' => $config->replica->username,
        'password' => $config->replica->password,
        'dbname'   => $config->replica->dbname,
        'charset'  => $config->replica->charset,
        'persistent' => true,
        "options"    => [\PDO::ATTR_PERSISTENT => 1],
    ]);

    return $connection;
});

/**
 * Database connection is created based in the parameters defined in the configuration file
 */
$di->setShared('db', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host'     => $config->database->host,
        'username' => $config->database->username,
        'password' => $config->database->password,
        'dbname'   => $config->database->dbname,
        'charset'  => $config->database->charset,
    ]);

    return $connection;
});

/**
 * Database connection is created based in the parameters defined in the configuration file
 */
$di->setShared('database', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host'     => $config->database->host,
        'username' => $config->database->username,
        'password' => $config->database->password,
        'dbname'   => $config->database->dbname,
        'charset'  => $config->database->charset,
    ]);

    return $connection;
});

$di->setShared('replica', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->replica->adapter;
    $connection = new $class([
        'host'     => $config->replica->host,
        'username' => $config->replica->username,
        'password' => $config->replica->password,
        'dbname'   => $config->replica->dbname,
        'charset'  => $config->replica->charset,
    ]);

    return $connection;
});

$di->setShared("dynamoDB", function () {
    $config = $this->getConfig();
    $dynamoConfig = [
        'version' => $config->dynamodb['version'],
        'region' => $config->dynamodb['region'],
        'endpoint' => $config->dynamodb['endpoint'],
        'credentials' => [
            'key' => $config->dynamodb['key'],
            'secret' => $config->dynamodb['secret'],
        ],
    ];

    $sdk = new \Aws\Sdk($dynamoConfig);

    return $sdk->createDynamoDb();
});

/**
 * Legacy database (read only) connection is created based in the parameters defined in the configuration file
 */
$di->setShared(
    'dbLegacy',
    function () {
        $config = $this->getConfig();

        $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->legacy->adapter;
        $connection = new $class(
            [
            'host'     => $config->legacy->host,
            'username' => $config->legacy->username,
            'password' => $config->legacy->password,
            'dbname'   => $config->legacy->dbname,
            'charset'  => $config->legacy->charset,
            ]
        );

        return $connection;
    }
);

// Memcache for badge counter
$di->setShared(
    'badgeCache',
    function () {
        $config = $this->getConfig();
        $serializerFactory = new SerializerFactory();
        $adapterFactory = new AdapterFactory($serializerFactory);

        $options = [
            'defaultSerializer' => 'Json',
            'lifetime'          => 7200,
            'servers'           => [
                0 => [
                    'host'   => $config->application->memcache->memcacheHost,
                    'port'   => $config->application->memcache->memcachePort,
                ],
            ],
        ];

        $adapter = $adapterFactory->newInstance('libmemcached', $options);

        return new Phalcon\Cache($adapter);
    }
);

$di->setShared(
    BugsnagClient::class,
    function () {
        $config = $this->getConfig();
        $bugsnag = BugsnagClient::make($config->bugsnag['apiKey']);

        BugsnagHandler::register($bugsnag);

        return $bugsnag;
    }
);

// Create a console application
$console = new ConsoleApp();

$console->setDI($di);



/**
 * Process the console arguments
 */
$arguments = [];

foreach ($argv as $k => $arg) {
    if ($k === 1) {
        $arguments["task"] = $arg;
    } elseif ($k === 2) {
        $arguments["action"] = $arg;
    } elseif ($k >= 3) {
        $arguments["params"][] = $arg;
    }
}
echo "CLI Args : ".json_encode($arguments).PHP_EOL;
try {
    // Handle incoming arguments
    $console->handle($arguments);
} catch (\Phalcon\Exception $e) {
    // Bugsnag error reporting
    $di->getShared(BugsnagClient::class)->notifyException($e);
    echo $e->getMessage();

    exit(255);
}
