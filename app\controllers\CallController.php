<?php
/**
 * CallController
 *
 * @category CallController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\Controllers;
use CallPotential\CPCommon\HttpStatusCode;
use Phalcon\Validation;
use Phalcon\Validation\Validator;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\EventRouter;

/**
 * Swagger
 *
 * @SWG\Definition(definition="CallItem",type="object",
 *    allOf={@SWG\Schema(ref="#/definitions/Call")},
 * @SWG\Property(property="location_name",type="string"),
 * @SWG\Property(property="location_id",type="integer"),
 * @SWG\Property(property="lead_first_name",type="string"),
 * @SWG\Property(property="lead_last_name",type="string"),
 * @SWG\Property(property="lead_phone",type="string"),
 * @SWG\Property(property="lead_email",type="string"),
 * @SWG\Property(property="lead_qt_rental_type",type="integer"),
 * @SWG\Property(property="lead_inquiry_type",type="integer")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallList",
 * @SWG\Property(
 *     property="items",
 *     type="array",
 * @SWG\Items(ref="#/definitions/CallItem")
 *   ),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData")
 * )
 */


/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallBulkResultItem",
 * @SWG\Property(property="message",type="string"),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/Call"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallBulkResult",
 * @SWG\Property(property="success",type="array",@SWG\Items(type="number")),
 * @SWG\Property(property="error",type="array",@SWG\Items(ref="#/definitions/CallBulkResultItem"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallBulkResponse",
 * @SWG\Property(property="status",type="string",enum={"OK","ERROR","MIXED"}),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallBulkResult"))
 * )
 */

/**
 * CallController
 *
 * @category CallController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class CallController extends Controllers\BaseController
{
    use Controllers\DynamoTrait, Controllers\SessionTrait;

    /**
     * Twilio Id
     *
     * @var string
     */
    private $twilioId;

    /**
     * Location Id
     *
     * @var int
     */
    private $locationId;

    /**
     * Array of numeric fields in dynamo db
     *
     * @var array
     */
    private $dynamoNumericFields = [
      'log_id', 'employee_id', 'is_auto_call', 'neighbor_location_id',
      'is_route_complete', 'call_duration', 'ad_id', 'location_id',
      'user_id', 'fk_lead_id', 'db_log_id', 'account_id', 'timetolive',
    ];

    /**
     * Array of string fields in dynamo db
     *
     * @var array
     */
    private $dynamoStringFields = [
      'call_status', 'customer_type', 'customer_name', 'route_config',
      'current_route_step', 'caller_name', 'call_destination', 'recording_url',
      'call_processed_by', 'call_type', 'datestamp', 'call_name', 'call_number',
      'twilio_id', 'recording_sid', 'es_lead_id', 'es_tenant_id', 'customer_id',
      'is_voicemail',
    ];

    private $eventData = [];

    /**
     * Constructor to initialize data
     *
     * @return void
     */
    public function initialize()
    {
        $this->twilioId = $this->dispatcher->getParam("twilio_id"); //Primary partition key
        $this->locationId = $this->dispatcher->getParam("location_id"); //Primary sort key
        parent::initialize();
    }

    /**
     * Intermediate function to prepare data for list action response
     *
     * @return mixed
     */
    public function getListContent()
    {
        //not implemented
    }


    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/call/{location_id}/{twilio_id}",
     *     description="Returns a Call based on a single ID",
     *     summary="get Call",
     *     operationId="CallGetById",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *       description="Authorization token",
     *       type="string",
     *       name="Authorization",
     *       in="header",
     *       required=true
     *     ),
     * @SWG\Parameter(
     *       description="Twilio ID of Call to fetch",
     *       in="path",
     *       name="twilio_id",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Parameter(
     *       description="Location ID of Call to fetch",
     *       in="path",
     *       name="location_id",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Response(
     *       response=200,
     *       description="call detail response",
     * @SWG\Schema(ref="#/definitions/Call")
     *     ),@SWG\Response(
     *       response="403",
     *       description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="404",
     *       description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="500",
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }

        try {
            if (!$this->twilioId) {
                return $this->sendBadRequest();
            }

            $data = $this->getItem(
                [
                'twilio_id'     => $this->twilioId,
                'location_id'   => (int) $this->locationId,
                ]
            );

            if (!is_array($data)) {
                return $this->sendNotFound();
            }

            $result = $this->formatGetResponse($data);

            if ($this->userHasReadAccess($result)) {
                $this->response->setJsonContent($result);

                return $this->response;
            } else {
                return $this->sendForbidden();
            }
        } catch (\Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/call",
     *     description="Returns call detail listing",
     *     summary="list call details",
     *     operationId="ListCalls",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *        description="Authorization token",
     *        type="string",
     *        name="Authorization",
     *        in="header",
     *        required=true
     *     ),
     * @SWG\Response(
     *        response=200,
     *        description="Call response",
     * @SWG\Schema(ref="#/definitions/CallList")
     *     ),@SWG\Response(
     *        response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *        description="Not Authorized Invalid or missing Authorization header"
     *     ),
     @SWG\Response(
     *        response=401,
     *        description="Not Authorized",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *        response=500,
     *        description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        return $this->sendNotImplemented();
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/call",
     *   tags={"Call Data"},
     *   summary="Create a call",
     *   description="create new call",
     *   summary="create call",
     *   operationId="CreateCall",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Call record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/Call")
     *   ),@SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/CallItem")
     *   ),@SWG\Response(
     *     response="400",
     *     description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response=500,
     *     description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }
            $this->infoMessage('create request begin', __METHOD__ . __LINE__);
            $result = parent::createAction();
            $this->infoMessage('create request end', __METHOD__ . __LINE__);

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Put(path="/call/{location_id}/{twilio_id}",
     *   tags={"Call Data"},
     *   summary="Update an existing call",
     *   description="Update existing call",
     *   operationId="UpdateCall",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     description="Twilio ID of call",
     *     in="path",
     *     name="twilio_id",
     *     required=true,
     *     type="string"
     *   ),
     * @SWG\Parameter(
     *     description="Loacation ID of call",
     *     in="path",
     *     name="location_id",
     *     required=true,
     *     type="string"
     *   ),
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Call record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/CallItem")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/CallItem")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }
            $this->infoMessage('save request begin', __METHOD__ . __LINE__);

            $data = Util::objectToArray($this->request->getJsonRawBody());

            if (empty($data) || !is_array($data)) {
                return $this->sendBadRequest('Invalid Request Body');
            }

            //verify if exist more than one element
            if (Util::existSubArray($data, true)) {
                return $this->bulkSave();
            }
            $this->infoMessage('update request begin', __METHOD__ . ":" . __LINE__);
            $id = [
                    'twilio_id'     => $this->twilioId,
                    'location_id'   => (int) $this->locationId,
                ];
            if (empty($this->twilioId)) {
                return $this->sendBadRequest();
            }

            $this->debugMessage(
                'Update request for id: ' .$this->twilioId,
                __METHOD__ . ":" . __LINE__
            );

            $current = $this->getItem($id);
            if (is_array($current) && count($current) > 0) {
                $data = $this->preProcessPutData($data, $current);
                $combinedData = array_merge($current, $data);

                // Check user has write access or not
                if (!$this->userHasWriteAccess($combinedData)) {
                    return $this->sendAccessDeniedResponse('Write Access Denied');
                }
                $validate = $this->validateSave(array_merge($combinedData), $id);
                if (!empty($validate)) {
                    return $this->sendBadRequest($validate);
                }

                try {
                    $response = $this->updateItem($id, $data);
                    $this->eventData['oldData'] = $current;
                    $this->eventData['newData'] = $response;
                    $this->eventData['staticToken'] = $this->config->application
                    ->staticToken->readWrite;
                    $this->eventData['callUrl'] = $this->config->application->services->call;
                    $this->eventData['locUrl'] = $this->config->application->services->loc;
                    $this->eventData['coreUrl'] = $this->config->application->services->core;
                    $this->eventData['recordingBucket'] = $this->config->aws->buckets->recordings;
                    $this->eventData['env'] = $this->config->application->env;

                    // If db log id is set then only update mysql data
                    if (!empty($combinedData['db_log_id'])) {
                        $this->eventData['updateMysql'] = true;

                        if (isset($combinedData['reservation_payment_id'])) {
                            $this->eventData['updateReservationFee'] = true;
                        }

                        if (isset($combinedData['customer_payment_id'])) {
                            $this->eventData['updateCustomerPayment'] = true;
                        }
                    }

                    $this->auditMessage(
                        'event data for call update ' . $this->twilioId . ': ' .
                        var_export($this->eventData, true),
                        __METHOD__ . ":" . __LINE__
                    );

                    // Execute it synchrously to propogate data into mysql

                    $this->unsetEventDataFields(['convIntelApi']);
                    EventRouter::route('Call', $this->eventData);

                    // Convintel call will remain async
                    $this->unsetEventDataFields(['updateMysql']);

                    $this->eventData['convIntelApi'] = $this->config->conv_intel_api;
                    EventRouter::route('Call', $this->eventData, true);

                    //Save video recording in S3
                    $this->unsetEventDataFields(['convIntelApi', 'updateMysql']);

                    if (substr($this->twilioId, 0, 2) === 'RM') {
                        $this->eventData['saveVideoRecording'] = true;
                        EventRouter::route('Call', $this->eventData, true);
                    }


                    if (is_array($response)) {
                        $this->debugMessage(
                            'update response for ' . $this->twilioId . ': ' .
                            var_export($response, true),
                            __METHOD__ . ":" . __LINE__
                        );
                    } else {
                        return $this->sendErrorResponse($response);
                    }
                } catch (Exception $e) {
                    $this->errorMessage(
                        'Update request for id: ' . $this->twilioId .
                        ' Error: ' . $e->getMessage() .
                        ' Data: ' . print_r($this->request->getJsonRawBody(), true)
                    );

                    if ($e->getMessage() === 'UpdateExpression condition can not be empty') {
                        return $this->sendBadRequest($e->getMessage());
                    } else {
                        return $this->sendErrorResponse($e);
                    }
                }
            } else {
                // There is possibility that call might exist
                // in mysql and not in dynamo so update mysql when entry
                // not found in dynamo db
                $this->eventData = [];
                $this->eventData['oldData'] = [];
                $this->eventData['newData'] = array_merge($data, $id);
                $this->eventData['updateMysql'] = true;

                EventRouter::route('Call', $this->eventData, true);

                $this->debugMessage(
                    'Save request end id ' . $this->twilioId . ' not found.',
                    __METHOD__ . ':' . __LINE__
                );

                return $this->sendNotFound();
            }
            $this->response->setStatusCode(
                HttpStatusCode::HTTP_OK,
                HttpStatusCode::getMessageForCode(HttpStatusCode::HTTP_OK)
            );
            $this->response->setJsonContent($this->formatPutResponse($response));
            $this->infoMessage(
                'update request end. result: ' . var_export($response, true),
                __METHOD__ . ":" . __LINE__
            );

            return $this->response;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Delete(
     *     tags={"Call Data"},
     *     path="/call/{location_id}/{twilio_id}",
     *     description="deletes a Call based on the ID supplied",
     *     summary="delete Call",
     *     operationId="DeleteCall",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *         description="Authorization token",
     *         type="string",
     *         name="Authorization",
     *         in="header",
     *         required=true
     *     ),
     * @SWG\Parameter(
     *         description="Location ID of Call to delete",
     *         format="int64",
     *         in="path",
     *         name="location_id",
     *         required=true,
     *         type="integer"
     *     ),
     * @SWG\Parameter(
     *         description="Twilio ID of Call to delete",
     *         format="int64",
     *         in="path",
     *         name="twilio_id",
     *         required=true,
     *         type="integer"
     *     ),
     * @SWG\Response(
     *         response=204,
     *         description="call queue deleted",
     * @SWG\Schema(type="null")
     *     ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        try {
            $this->infoMessage('delete request begin for id '.$this->twilioId, __METHOD__.__LINE__);
            if (!strlen($this->authToken)) {
                return $this->sendNotAuthorized();
            }
            if (!$this->validSession()) {
                return $this->sendForbidden();
            }
            $id = [
                'twilio_id'     => $this->twilioId,
                'location_id'   => (int) $this->locationId,
            ];

            $model = $this->getItem($id);

            //delete if exists the object
            if (empty($model)) {
                return $this->sendNotFound();
            }
            if (!$this->userHasDeleteAccess($model)) {
                return $this->sendForbidden();
            }

            $result = $this->deleteItem($id);
            if ($result === true) {
                $this->response->setStatusCode(
                    HttpStatusCode::HTTP_NO_CONTENT,
                    HttpStatusCode::getMessageForCode(HttpStatusCode::HTTP_NO_CONTENT)
                );
                $this->response->setJsonContent(array('status' => "OK"));
            } else {
                $this->response->setStatusCode(
                    HttpStatusCode::HTTP_CONFLICT,
                    HttpStatusCode::getMessageForCode(HttpStatusCode::HTTP_CONFLICT)
                );
                $this->response->setJsonContent(
                    array('status' => "ERROR", 'messages' => $result)
                );
            }

            return $this->response;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Validate data before create record for create action
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function validateCreate(array $data)
    {
        $validation = new Validation();
        $validation->add(
            'location_id',
            new Validator\PresenceOf(['message' => ':field is required'])
        );
        $validation->add(
            'location_id',
            new Validator\Numericality(
                [
                    'message' => 'Please enter valid :field only numeric accepted.',
                ]
            )
        );
        $validation->add(
            'twilio_id',
            new Validator\PresenceOf(['message' => ':field is required'])
        );
        $validationMessages = $validation->validate($data);
        $messages = [];
        foreach ($validationMessages as $message) {
            $messages[] = $message->getMessage();
        }

        if (0 < count($messages)) {
            $this->validationMessages = $messages;
        }

        return $messages;
    }

    /**
     * Validate data before save record for save action
     *
     * @param array $data array of POST body / query params in key value pair
     * @param mixed $id   primary key value of the record to update
     *
     * @return mixed
     */
    protected function validateSave(array $data, $id = 0)
    {
        unset($id);
        $validation = new Validation();
        $validation->add(
            'location_id',
            new Validator\PresenceOf(['message' => ':field is required'])
        );
        $validation->add(
            'location_id',
            new Validator\Numericality(
                [
                    'message' => 'Please enter valid :field only numeric accepted.',
                ]
            )
        );
        $validation->add(
            'twilio_id',
            new Validator\PresenceOf(['message' => ':field is required'])
        );
        $validationMessages = $validation->validate($data);
        $messages = [];
        foreach ($validationMessages as $message) {
            $messages[] = $message->getMessage();
        }

        if (0 < count($messages)) {
            $this->validationMessages = $messages;
        }

        return $messages;
    }

    /**
     * Validate data before delete record for delete action
     *
     * @param array   $data array of POST body / query params in key value pair
     * @param integer $id   primary key value of the record to delete
     *
     * @return array
     */
    protected function validateDelete(array $data, int $id = 0): array
    {
        unset($id);
        $validation = new Validation();
        $validation->add(
            'location_id',
            new Validator\PresenceOf(['message' => ':field is required'])
        );
        $validation->add(
            'location_id',
            new Validator\Numericality(
                [
                    'message' => 'Please enter valid :field only numeric accepted.',
                ]
            )
        );
        $validation->add(
            'twilio_id',
            new Validator\PresenceOf(['message' => ':field is required'])
        );
        $validationMessages = $validation->validate($data);
        $messages = [];
        foreach ($validationMessages as $message) {
            $messages[] = $message->getMessage();
        }

        if (0 < count($messages)) {
            $this->validationMessages = $messages;
        }

        return $messages;
    }

    /**
     * Any preprocessing of put data is done here
     *
     * @param array $data     request data
     * @param array $original original data
     *
     * @return array
     */
    protected function preProcessPutData(array $data, array $original): array
    {
        // We do not allow the web app to update these fields but we need to allow
        // lambda function to be able to update them, so check for staticToken
        if (!$this->isStaticToken($this->authToken)) {
            unset(
                $data['log_id'],
                $data['current_route_step'],
                $data['db_log_id'],
                $data['is_route_complete'],
                $original
            );
        }

        foreach ($this->dynamoNumericFields as $value) {
            if (isset($data[$value])) {
                $data[$value] = (int) $data[$value];
            }
        }

        foreach ($this->dynamoStringFields as $value) {
            if (isset($data[$value])) {
                $data[$value] = (string) $data[$value];
            }
        }

        return $data;
    }

    /**
     * Any preprocessing of post data is done here
     *
     * @param array $data request data
     *
     * @return array
     */
    protected function preProcessPostData(array $data): array
    {
        foreach ($this->dynamoNumericFields as $value) {
            if (isset($data[$value])) {
                $data[$value] = (int) $data[$value];
            }
        }

        foreach ($this->dynamoStringFields as $value) {
            if (isset($data[$value])) {
                $data[$value] = (string) $data[$value];
            }
        }

        return $data;
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    protected function doBulkSave(): array
    {
        //not implemented
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    protected function doBulkCreate()
    {
        //not implemented
    }

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    protected function getModelName(): string
    {
        return "Call"; //model
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Format List Response change o/p of each item in list
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatListResponse($data)
    {
        unset($data);
    }

    /**
     * function to unset not required fields for Calldetail event handler
     * @param array $fields
     */
    protected function unsetEventDataFields($fields)
    {
        foreach ($fields as $value) {
            unset($this->eventData[$value]);
        }
    }
}
