stateDiagram-v2
    direction TB

    %% TERMINAL STATES
    TransferToAgent --> [*]
    DisconnectCall --> [*] : Call is ended

    %% LOCALE SELECTION
    [*] --> LocalePrompt : Coming from Collections Workflow
    LocalePrompt --> LocaleConfirm : More than one language selection
    LocalePrompt --> CollectionsPrompt: Only one language selection

    LocaleConfirm --> LocalePrompt : Invalid selection
    LocaleConfirm --> CollectionsPrompt

    %% COLLECTIONS PROMPT
    CollectionsPrompt --> CollectionsConfirm

    CollectionsConfirm --> SayAmountDue : Make payment
    CollectionsConfirm --> TransferToAgent : Speak with manager
    CollectionsConfirm --> CustomerOptOut : Opt out
    CollectionsConfirm --> DisconnectCall : Too many incorrect attempts
    CollectionsConfirm --> CollectionsPrompt : Invalid input or no response

    %% OPT OUT
    CustomerOptOut --> DisconnectCall

    %% PHONE SEARCH
    InputPhoneGather --> InputPhoneValidate

    InputPhoneValidate --> InputPhoneGather: Phone is invalid, retries < max
    InputPhoneValidate --> TransferToAgent: Phone is invalid, retries >= max or speak to manager
    InputPhoneValidate --> CustomerByPhoneSearch: Phone is valid

    %% CUSTOMER_SEARCH
    [*] --> CustomerByPhoneSearch : Coming from Call Route
    CustomerByPhoneSearch --> InputPhoneGather : Customer not found, no response, or PBP not allowed
    CustomerByPhoneSearch --> UnitsToPayPrompt : Multiple units found
    CustomerByPhoneSearch --> ConfirmCustomerInfo : Single unit found

    %% CONFIRM_PAYMENTS
    UnitsToPayPrepayMonthsPrompt --> UnitsToPayPrepayMonthsSelection

    GetSavedCards --> PayMethodPrompt : Has saved cards
    GetSavedCards --> PayMethodCreditCardPrompt : No saved cards

    SayAmountDue --> GetSavedCards

    ConfirmCustomerInfo --> SayAmountDue : Delinquent payment flow
    ConfirmCustomerInfo --> TransferToAgent : No response or invalid input
    ConfirmCustomerInfo --> UnitsToPayPrepayMonthsPrompt : Non-delinquent payment flow

    %% UNIT SELECTION
    UnitsToPayPrompt --> UnitsToPaySelection
    UnitsToPayPrompt --> TransferToAgent : No matched tenants

    UnitsToPaySelection --> UnitsToPayPrompt : No response, start over, or invalid selection
    UnitsToPaySelection --> ConfirmCustomerInfo : Chose to pay single unit

    UnitsToPayPrepayMonthsSelection --> UnitsToPayPrepayMonthsPrompt : No response or invalid input
    UnitsToPayPrepayMonthsSelection --> UnitsToPayPrepayMonthsConfirm : Valid selection

    UnitsToPayPrepayMonthsConfirm --> UnitsToPayPrepayMonthsPrompt : Change selection or no response
    UnitsToPayPrepayMonthsConfirm --> GetSavedCards : Confirm selection

    %% GATHER_PAYMENT
    PayMethodPrompt --> PayMethodSelection

    PayMethodSelection --> ExistingPayMethodVerify : Verify existing method
    PayMethodSelection --> PayMethodCreditCardPrompt : Add new pay method
    PayMethodSelection --> PayMethodPrompt : No digits provided or invalid digits provided
    PayMethodSelection --> TransferToAgent : Too many retries

    ExistingPayMethodVerify --> PayMethodSecurityCodePrompt : Passes verification
    ExistingPayMethodVerify --> PayMethodCreditCardPrompt : Fails verification

    PayMethodCreditCardPrompt --> PayMethodCreditCardValidate

    PayMethodCreditCardValidate --> PayMethodCreditCardPrompt : Is invalid
    PayMethodCreditCardValidate --> PayMethodCreditCardConfirm : Is valid

    PayMethodCreditCardConfirm --> PayMethodExpirationPrompt : Customer confirms
    PayMethodCreditCardConfirm --> PayMethodCreditCardPrompt : Customer rejects

    PayMethodExpirationPrompt --> PayMethodExpirationValidate

    PayMethodExpirationValidate --> PayMethodExpirationPrompt : Is invalid
    PayMethodExpirationValidate --> PayMethodExpirationConfirm : Is valid

    PayMethodExpirationConfirm --> PayMethodSecurityCodePrompt : Customer confirms
    PayMethodExpirationConfirm --> PayMethodExpirationPrompt : Customer rejects

    PayMethodSecurityCodePrompt --> PayMethodSecurityCodeValidate

    PayMethodSecurityCodeValidate --> PayMethodSecurityCodePrompt : Is invalid
    PayMethodSecurityCodeValidate --> PayMethodSecurityCodeConfirm : Is valid

    PayMethodSecurityCodeConfirm --> PayMethodPostalCodePrompt : Customer confirms (U.S. Location)
    PayMethodSecurityCodeConfirm --> FinalPayAmountPrompt : Customer confirms (Non U.S. Location)
    PayMethodSecurityCodeConfirm --> PayMethodSecurityCodePrompt : Customer rejects

    PayMethodPostalCodePrompt --> PayMethodPostalCodeValidate

    PayMethodPostalCodeValidate --> PayMethodPostalCodePrompt : Is invalid
    PayMethodPostalCodeValidate --> PayMethodPostalCodeConfirm : Is valid

    PayMethodPostalCodeConfirm --> FinalPayAmountPrompt : Customer confirms
    PayMethodPostalCodeConfirm --> PayMethodPostalCodePrompt : Customer rejects

    %% PROCESS_PAYMENT
    FinalPayAmountPrompt --> FinalPayAmountConfirm

    FinalPayAmountConfirm --> FinalPayAmountSubmitted : Customer confirms
    FinalPayAmountConfirm --> FinalPayAmountPrompt : No response or customer rejects, retries < max
    FinalPayAmountConfirm --> TransferToAgent : No response, retries >= max

    FinalPayAmountSubmitted --> PaymentSuccessful : Payment successful
    FinalPayAmountSubmitted --> PaymentFailure : Payment failed

    PaymentSuccessful --> DisconnectCall
    PaymentFailure --> TransferToAgent
