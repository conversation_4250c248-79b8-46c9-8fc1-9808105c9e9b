import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { PayByPhoneStateRetryHandler } from './_Utils';

@Injectable()
export class PayMethodSelection extends PayByPhoneStateBase {
  @PayByPhoneStateRetryHandler(
    3,
    PayByPhoneState.PayMethodPrompt,
    [PayByPhoneState.PayMethodCreditCardPrompt, PayByPhoneState.ExistingPayMethodVerify],
    PayByPhoneState.TransferToAgent,
    'pay-by-phone.max-retry'
  )
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. If customer wishes to use existing pay method, transition to ExistingPayMethodVerify
     * 2. If customer wishes to use new CC, transition to PayMethodCreditCardPrompt
     * 3. Otherwise, re-prompt for input
     */

    const { request, twilioResponse, storage } = context;

    const customerSelection = request.Digits;
    const useNewCard = customerSelection === '0';
    const useSavedCard = customerSelection?.length === 4;

    if (useNewCard) {
      return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
    } else if (useSavedCard) {
      storage.lastFourDigits = customerSelection;
      return { nextState: PayByPhoneState.ExistingPayMethodVerify };
    } else {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale,
      });
      return { nextState: PayByPhoneState.PayMethodPrompt };
    }
  }
}
