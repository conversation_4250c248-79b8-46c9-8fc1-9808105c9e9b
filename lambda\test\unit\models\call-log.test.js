const CallLogModel = require("../../../models/call-log");
const cpapiClient = require('../../../libraries/cpapi-client');

describe('call log test', () => {
    let callLogModel;
    let callClient;
    let coreClient;

    beforeEach(() => {
        callLogModel = new CallLogModel();
        callClient = {
            getData: jest.fn(),
            putData: jest.fn(),
            postData: jest.fn(),
        };
        coreClient = {
            getData: jest.fn(),
            putData: jest.fn(),
            postData: jest.fn(),
        };

        jest.spyOn(cpapiClient, 'callClient').mockImplementation(() => callClient)
        jest.spyOn(cpapiClient, 'coreClient').mockImplementation(() => coreClient)
    });

    afterAll(() => {
        jest.clearAllMocks()
    })

    it('it should create a call log record when create_call_log is triggered', async () => {
        const log_info = { call_status: 'completed', log_id: '4585785'};
        callClient.postData.mockResolvedValue({ 'db_log_id': 123 });
         const result = await callLogModel.create_call_log(log_info);
        expect(callClient.postData).toHaveBeenCalledWith('calldetail', log_info);
        expect(result).toEqual({ 'db_log_id': 123 });
    })
    
    it('it should throw error when create_call_log fails to create a record', async () => {
        const log_info = { call_status: 'completed', log_id: '4585785'};
        callClient.postData.mockRejectedValue(new Error("Something went wrong not able to create a record."));
        await expect(callLogModel.create_call_log(log_info)).rejects.toThrow("Something went wrong not able to create a calldetail record.");
        expect(callClient.postData).toHaveBeenCalledWith('calldetail', log_info);
    })

    it('it should update a call log record when update_call_log is triggered', async () => {
        const log_info = { twilio_id: 1234, log_id: 4585785};
        callClient.putData.mockResolvedValue({ 'db_log_id': 123 });
        const result = await callLogModel.update_call_log(log_info);
        expect(callClient.putData).toHaveBeenCalledWith('calldetail/'+log_info['twilio_id'], log_info);
        expect(result).toEqual({ 'db_log_id': 123 });
    })
    
    it('it should throw error when update_call_log fails to update a record', async () => {
        const log_info = { twilio_id: 1234, log_id: 4585785};
        callClient.putData.mockRejectedValue(new Error("Something went wrong not able to update a record."));
        await expect(callLogModel.update_call_log(log_info)).rejects.toThrow("Something went wrong not able to update a calldetail record.");
        expect(callClient.putData).toHaveBeenCalledWith('calldetail/'+log_info['twilio_id'], log_info);
    })

    it('should call update_call_log and update calldetails', async () => {
        const log_info = { twilio_id: 123 };
        jest.spyOn(callLogModel, 'update_call_log').mockResolvedValue({ 'db_log_id': 123 });
        jest.spyOn(callLogModel, 'update_dynamodb').mockResolvedValue({ 'db_log_id': 3433 });
        CallLogModel.prototype.list_dynamodb = jest.fn().mockResolvedValue({ 'log_id': 34343 });
        await callLogModel.save_call_log_from_dynamo_db(log_info);
        expect(callLogModel.update_call_log).toHaveBeenCalledWith(log_info);
    })
});