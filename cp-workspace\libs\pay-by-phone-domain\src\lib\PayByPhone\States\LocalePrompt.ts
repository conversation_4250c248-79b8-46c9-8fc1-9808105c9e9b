import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase, PayByPhoneSayAttributes } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class LocalePrompt extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { twilioResponse, storage } = context;

    const locationDetails = await this.services.locationService.getLocationDetails(storage.locationId);
    if (locationDetails.locales.length > 1) {
      const messages = locationDetails.locales.map((locale, index) => ({
        messageId: 'pay-by-phone.locale-select',
        locale: locale,
        i18nOptions: { args: [{ optionNumber: index + 1 }] }
      } as PayByPhoneSayAttributes));
      
      twilioResponse.gatherWithLocaleSay({
        numDigits: 1,
        method: 'POST',
        timeout: 10,
        action: storage.absoluteUrl!,
      }, messages);
    
      return { nextState: PayByPhoneState.LocaleConfirm };
    }
    
    return { nextState: PayByPhoneState.CollectionsPrompt };
  }
}
