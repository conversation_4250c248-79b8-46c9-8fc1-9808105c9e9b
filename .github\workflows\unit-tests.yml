name: Unit Test & Code Coverage
on:
  pull_request:
    branches:
      - "**"
jobs:
  Pipeline:
    env:
      php-version: 7.4
      extensions: json, openssl, fileinfo, zip, gd, psr-0.7.0, phalcon-4.0.6
      key: cpapi-call-cache
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup cache environment
        id: extcache
        uses: shivammathur/cache-extensions@v1
        with:
          php-version: ${{ env.php-version }}
          extensions: ${{ env.extensions }}
          key: ${{ env.key }}

      - name: Cache extensions
        uses: actions/cache@v3
        with:
          path: ${{ steps.extcache.outputs.dir }}
          key: ${{ steps.extcache.outputs.key }}
          restore-keys: ${{ steps.extcache.outputs.key }}

      - name: PHP Setup
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.php-version}}
          extensions: ${{ env.extensions }}

      - name: Set up Composer authentication
        env:
          COMPOSER_AUTH: |
            {
              "github-oauth": {
                "github.com": "${{ secrets.COMPOSER_GITHUB_PAT }}"
              }
            }
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Unit Tests
        run: vendor/bin/phpunit --configuration phpunit_unit.xml --coverage-clover clover.xml

      - name: Coveralls
        env:
          COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          composer global require php-coveralls/php-coveralls
          php-coveralls --coverage_clover=clover.xml -v --json_path=upload.json

      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: 16

      - name: ESLint
        run: |
          cd lambda
          cp deployment/.npmrc .npmrc
          npx eslint@8.21.0 . --ext .js
        env:
          NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}

      - name: JestUnitTest
        run: |
          cd lambda
          npm install jest
          npm install jest-junit
          npm run test:unit
          cd ..
          cd cp-workspace
          npm install
          npm run unit-test:all
        env:
          NPM_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}

      - name: Coveralls
        uses: coverallsapp/github-action@v2
        with:
          path-to-lcov: |
            "lambda/coverage/lcov.info"
          parallel: true

      - name: CP-Workspace Pay-By-Phone-Domain Library Coveralls
        uses: coverallsapp/github-action@v2
        with:
          path-to-lcov: |
            "cp-workspace/coverage/pay-by-phone-domain/lcov.info"
          parallel: true

      - name: CP-Workspace Shared Library Coveralls
        uses: coverallsapp/github-action@v2
        with:
          path-to-lcov: |
            "cp-workspace/coverage/shared/lcov.info"
          parallel: true

      - name: CP-Workspace Lambda Functions Coveralls
        uses: coverallsapp/github-action@v2
        with:
          path-to-lcov: |
            "cp-workspace/coverage/lambda-functions/lcov.info"
          parallel: true
          