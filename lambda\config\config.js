var debug_mode = false;
var current_env = 'qa-1675';
var set_current_env = 'false';

var redis_host = 'qa-cp.7xwsax.0001.usw2.cache.amazonaws.com';
var memcache_host = 'qa.7xwsax.cfg.usw2.cache.amazonaws.com:11211';

var site_endpoint = 'https://qa-1675.callpotential.com/';



module.exports = {
  port: 3001,
  env: current_env,
  site_url: site_endpoint, // endpoint
  env_url: '/qa-1675/v1',
  env_prefix_url: '/qa/v1',
  call_url: 'https://call-api.callpdev.com/qa-1675/v1/',
  'es': {
    'prefix': (set_current_env == "false") ? '' : current_env + '-',
    'host': 'https://v2-qa55-es.callpdev.com:443',
    'httpAuth': null,
    'log': debug_mode ? 'trace' : null // 'trace' or null
  },
  's3': {
    'bucket': 'cp-qa-1675-pipeline',
    'read': {},
    'write': {
      'call_es_write_prefix': 'calllog-trigger/write'
    }
  },
  'sqs': {
    'QueueName': 'qa-1675-callPipelineDLQ',
    'lambdaDLQErrors': 'https://sqs.us-west-2.amazonaws.com/923874182572/qa-1675-callPipelineDLQ'
  },
  db: {
    client: 'mysql',
    connection: {
      host: 'rds-qa.callpotential.com',
      user: 'qa',
      password: 'CallPotential1',
      database: 'qa_1675',
      charset: 'utf8',
    },
    serviceTokens: {
      readOnly: 'VYZQ3XScyB9LDryvjqxDi6nF3Hw349JB3QsTN1hatxorEu1VMqq9QUEFSrTVyGS6',
      readWrite: 'ye1w2iqF3BXihixGzw6efFF3W9edRmoKbwMqOhfBuNDLzsyWz6vxiPgyzKhMjRDr',
    },
    pool: {
      min: 0,
      max: 1,
      requestTimeout: 250000,
    },
    acquireConnectionTimeout: 300000, // 5 minute
    debug: false
  },
  db_ro: {
    client: 'mysql',
    connection: {
      host: 'rds-qa.callpotential.com',
      user: 'qaReadOnly',
      password: 'CallPotential1',
      database: 'qa_1675',
      charset: 'utf8',
    },
    serviceTokens: {
      readOnly: 'VYZQ3XScyB9LDryvjqxDi6nF3Hw349JB3QsTN1hatxorEu1VMqq9QUEFSrTVyGS6',
      readWrite: 'ye1w2iqF3BXihixGzw6efFF3W9edRmoKbwMqOhfBuNDLzsyWz6vxiPgyzKhMjRDr',
    },
    pool: {
      min: 0,
      max: 1,
      requestTimeout: 250000,
    },
    acquireConnectionTimeout: 300000, // 5 minute
    debug: false,
  },
  redis: {
    host: redis_host,
    port: '6379',
    db: 0,
  },
  memcache: memcache_host,
  locations: {
    SL: '1', // SiteLink
    CP: '0', // CallPotential
    WSS: '2', // Web Self Storage
    CS: '3', // CenterShift
    QS: '4', // QuikStor
    DI: '5', // Domico
    DS: '6', // DoorStep
    SC: '7', // Space control universal
    ES: '8', // Extra Space
  },
  dynamodb: {
    prefix: 'qa-1675',
    acctTable: ('' === 'qa-1675') ? 'twilio_accounts' : 'qa-1675-twilio_accounts',
    callTable: ('' === 'qa-1675') ? 'call_logs' : 'qa-1675-call_logs',
    queueTable: ('' === 'qa-1675') ? 'queue_calls' : 'qa-1675-queue_calls',
    twilioQueueTable: ('' === 'qa-1675') ? 'twilio_queues' : 'qa-1675-twilio_queues'
  },
  twilio: {
    statDuration: '1440',
    agent_status_map: 'agent_status',
    task_list_map: 'task_list',
    twiml_app_sid: 'AP7665881a11172df848332a8f6a4e86a7',
    stats_document: 'workspace_stats',
    agent_stats_map: 'agent_stats',
  },
  CP_CDN_URL: 'https://cp-qa-1675-cdn.s3.amazonaws.com/',
  ES_URL: 'https://v2-qa55-es.callpdev.com:443',
  API_CORE_URL: 'https://qa-1675-core.callpotential.com',
  API_LOC_URL: 'https://qa-1675-loc.callpotential.com',
  API_ACCT_URL: 'https://qa-1675-acct.callpotential.com',
  API_INT_URL: 'https://qa-1675-int.callpotential.com',
  API_CALL_URL: 'https://qa-1675-call.callpotential.com',
  API_MCC_URL: 'https://qa-1675-mcc.callpotential.com',
  API_SMS_URL: 'https://qa-1675-sms.callpotential.com',
  API_EMAIL_URL: 'https://qa-1675-email.callpotential.com',
  API_CHAT_URL: 'https://qa-1675-chat.callpotential.com',
  API_EVAL_URL: 'https://qa-1675-eval.callpotential.com',
  TZ: 'America/Chicago',
  payment_call_types: [
    'inbound_payment',
    'inbound_lead_payment',
    'inbound_lead_payment_fail',
    'inbound_lead_payment_success',
    'outbound_lead_payment',
    'outbound_lead_payment_fail',
    'outbound_lead_payment_success',
    'outbound_payment',
  ],
  conv_intel_api: 'https://qa.conv-intel.callpotential.com/api/v1/intelligence/voicebase',
  recording_bucket: 'cp-stage-recordings',
};
