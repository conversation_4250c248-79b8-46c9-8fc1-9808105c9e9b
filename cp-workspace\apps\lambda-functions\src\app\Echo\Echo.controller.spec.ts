import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { EchoController } from './Echo.controller';
import { PayByPhoneRequestInterceptor } from '../Interceptors/PayByPhoneRequestInterceptor';
import { DomainEventsService } from '@cp-workspace/shared';

describe('EchoController', () => {
  let controller: EchoController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EchoController],
      providers: [PayByPhoneRequestInterceptor, {provide: DomainEventsService, useValue: {}}],
    }).compile();

    controller = module.get<EchoController>(EchoController);
  });

  describe('handleEchoRequest', () => {
    it('should throw BadRequestException if request body is empty', async () => {
      const mockRequest = {
        body: null,
      };
      await expect(controller.handleEchoRequest(mockRequest as any))
        .rejects
        .toThrow(BadRequestException);
    });

    it('should return the request body on a successful request', async () => {
      const mockRequest = {
        body: { message: 'Hello, World!' },
      };
      const result = await controller.handleEchoRequest(mockRequest as any);
      expect(result).toEqual(mockRequest.body);
    });
  });
});
