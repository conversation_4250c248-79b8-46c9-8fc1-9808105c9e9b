<?php
/**
 * PbxaccountController
 *
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: cwalker
 * Date: 12/7/17
 * Time: 12:04 AM
 *
 * @category PbxaccountController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Controllers\BaseController;
use CallPotential\CPCommon\Controllers\SessionTrait;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="PbxAccountCreate",
 * @SWG\Property(property="number",type="string"),
 * @SWG\Property(property="password",type="string",description="Plain text password")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="PbxAccountEdit",
 * @SWG\Property(property="password",type="string",description="Plain text password")
 * )
 */

/**
 * PbxaccountController
 *
 * @category PbxaccountController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class PbxaccountController extends BaseController
{
    use SessionTrait;

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($parentId);
        $pbxAccount = $this->di->getShared('pbxAccount');

        return $pbxAccount->addAccount($data['number'], $data['password']);
    }

    /**
     * Intermediate function to delete data for delete action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function deleteItem($id)
    {
        $pbxAccount = $this->di->getShared('pbxAccount');

        return $pbxAccount->deleteAccount($id);
    }

    /**
     * Intermediate function to prepare data for update action
     *
     * @param mixed $id       primary key value of the record
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($parentId);
        $pbxAccount = $this->di->getShared('pbxAccount');

        return $pbxAccount->addAccount($id, $data['password']);
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/pbxaccount",
     *   tags={"PBX"},
     *   summary="Create a pbx account",
     *   description="create new pbx account",
     *   operationId="CreatePbxAccount",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="New PBX Account record",
     *     required=true,
     * @SWG\Schema(ref="#/definitions/PbxAccountCreate")
     *   ),@SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/PbxAccountCreate")
     *   ),
     * @SWG\Response(
     *     response="400",
     *     description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response=500,
     *     description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }

        return parent::createAction();
    }

    /**
     * Swagger
     *
     * @SWG\Put(path="/pbxaccount/{id}",
     *   tags={"PBX"},
     *   summary="Update an existing PBXAccount",
     *   description="Update existing PBXAccount",
     *   operationId="UpdatePbxAccount",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     description="ID of Account (number)",
     *     in="path",
     *     name="id",
     *     required=true,
     *     type="string"
     *   ),
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="PBX Password record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/PbxAccountEdit")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/PbxAccountCreate")
     *   ),@SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }

        return parent::saveAction();
    }

    /**
     * Swagger
     *
     * @SWG\Delete(
     *     tags={"PBX"},
     *     path="/pbxaccount/{id}",
     *     description="deletes a Pbx Account based on the ID supplied",
     *     summary="delete Pbx Account",
     *     operationId="DeletePbxAccount",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *         description="Authorization token",
     *         type="string",
     *         name="Authorization",
     *         in="header",
     *         required=true
     *     ),
     * @SWG\Parameter(
     *         description="number of Pbx Account to delete",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="string"
     *     ),
     * @SWG\Response(
     *         response=204,
     *         description="call queue deleted",
     * @SWG\Schema(type="null")
     *     ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }

        return parent::deleteAction();
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        return $this->sendNotImplemented();
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    public function doBulkSave(): array
    {
        return $this->sendNotImplemented();
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    public function doBulkCreate()
    {
        return $this->sendNotImplemented();
    }

    /**
     * Handles bulk delete requests
     *
     * @return \Phalcon\Http\Response
     */
    public function doBulkDelete(): \Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        unset($id);

        return false;
    }

    /**
     * Intermediate function to prepare data for list action response
     *
     * @return mixed
     */
    public function getListContent()
    {
        return false;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);

        return ($this->isStaticToken($this->authToken));
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return ($this->isStaticToken($this->authToken));
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return ($this->isStaticToken($this->authToken));
    }
}
