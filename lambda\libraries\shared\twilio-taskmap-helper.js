const TwUtils = require("../../routes/utils/twillio");

async function syncTaskMap(twilioAccount, data) {
  try {
    return await TwUtils.sync_task_map(twilioAccount, data);
  } catch (error) {
    console.error("Error syncing task map:", error);
    throw error;
  }
}

async function syncTaskMapAndRespond(twilioAccount, data) {
  return await TwUtils.sync_task_map(twilioAccount, data);
}

async function removeSyncTaskMapAndRespond(twilioAccount, data) {
  await TwUtils.remove_sync_task_map(twilioAccount, data);
}


module.exports = {
  syncTaskMap,
  syncTaskMapAndRespond,
  removeSyncTaskMapAndRespond
};