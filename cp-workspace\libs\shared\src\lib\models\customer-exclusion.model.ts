export type CustomerExclusion = {
    location_id: number;
    contact_type: 'collection' | 'followup' | 'employee';
    exclusion_type: 'call' | 'sms' | 'email';
    excluded_contact: string;
    reason: string | 'Auto: Received Stop Request';
    request_sid: string;
    entity_type: string | 'customer';
};

export interface CustomerExclusionResponse extends CustomerExclusion {
    id: number;
    entity_id: number;
    exclusion_date: string;
}