/*import { Redis } from 'ioredis';
import { Cacheable, RequiresRedis } from './cacheable.decorator';
import { jest } from '@jest/globals';

describe('Cacheable Decorator', () => {
  let redis: Redis;

  beforeEach(() => {
    redis = new Redis();
  });

  it('should return the cached result if it exists', async () => {
    const cacheKey = 'testMethod-[1,"arg2",{"key":"value"}]';
    const mockResult = 'Mock Result';

    jest.spyOn(redis, 'get').mockResolvedValue(JSON.stringify(mockResult));
    jest.spyOn(redis, 'set');

    class TestClass implements RequiresRedis {
      redis: Redis = redis;

      @Cacheable(60)
      async testMethod(...args: any[]): Promise<any> {
        return 'This should not be returned';
      }
    }

    const instance = new TestClass();
    const result = await instance.testMethod(1, 'arg2', { key: 'value' });

    expect(redis.get).toHaveBeenCalledWith(cacheKey);
    expect(redis.set).not.toHaveBeenCalled();
    expect(result).toEqual(mockResult);
  });

  it('should call the original method and cache the result if cache is empty', async () => {
    const cacheKey = 'testMethod-[1,"arg2",{"key":"value"}]';
    const mockResult = 'Mock Result';

    jest.spyOn(redis, 'get').mockResolvedValue(null);
    jest.spyOn(redis, 'set').mockResolvedValue('OK');

    class TestClass implements RequiresRedis {
      redis: Redis = redis;

      @Cacheable(60)
      async testMethod(...args: any[]): Promise<any> {
        return mockResult;
      }
    }

    const instance = new TestClass();
    const result = await instance.testMethod(1, 'arg2', { key: 'value' });

    expect(redis.get).toHaveBeenCalledWith(cacheKey);
    expect(redis.set).toHaveBeenCalledWith(cacheKey, JSON.stringify(mockResult), 'EX', 60);
    expect(result).toEqual(mockResult);
  });

  it('should handle errors when accessing Redis', async () => {
    const cacheKey = 'testMethod-[1,"arg2",{"key":"value"}]';
    const mockResult = 'Mock Result';

    jest.spyOn(redis, 'get').mockRejectedValue(new Error('Redis error'));
    jest.spyOn(redis, 'set').mockResolvedValue('OK');
    jest.spyOn(console, 'error');

    class TestClass implements RequiresRedis {
      redis: Redis = redis;

      @Cacheable(60)
      async testMethod(...args: any[]): Promise<any> {
        return mockResult;
      }
    }

    const instance = new TestClass();
    const result = await instance.testMethod(1, 'arg2', { key: 'value' });

    expect(redis.get).toHaveBeenCalledWith(cacheKey);
    expect(console.error).toHaveBeenCalledWith('Error accessing Redis', expect.any(Error));
    expect(redis.set).toHaveBeenCalledWith(cacheKey, JSON.stringify(mockResult), 'EX', 60);
    expect(result).toEqual(mockResult);
  });

  it('should handle errors when saving data to Redis', async () => {
    const cacheKey = 'testMethod-[1,"arg2",{"key":"value"}]';
    const mockResult = 'Mock Result';

    jest.spyOn(redis, 'get').mockResolvedValue(null);
    jest.spyOn(redis, 'set').mockRejectedValue(new Error('Redis error'));
    jest.spyOn(console, 'error');

    class TestClass implements RequiresRedis {
      redis: Redis = redis;

      @Cacheable(60)
      async testMethod(...args: any[]): Promise<any> {
        return mockResult;
      }
    }

    const instance = new TestClass();
    const result = await instance.testMethod(1, 'arg2', { key: 'value' });

    expect(redis.get).toHaveBeenCalledWith(cacheKey);
    expect(redis.set).toHaveBeenCalledWith(cacheKey, JSON.stringify(mockResult), 'EX', 60);
    expect(console.error).toHaveBeenCalledWith('Error saving data to Redis', expect.any(Error));
    expect(result).toEqual(mockResult);
  });
});*/