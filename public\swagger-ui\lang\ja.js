'use strict';

/* jshint quotmark: double */
window.SwaggerTranslator.learn({
    "Warning: Deprecated":"警告: 廃止予定",
    "Implementation Notes":"実装メモ",
    "Response Class":"レスポンスクラス",
    "Status":"ステータス",
    "Parameters":"パラメータ群",
    "Parameter":"パラメータ",
    "Value":"値",
    "Description":"説明",
    "Parameter Type":"パラメータタイプ",
    "Data Type":"データタイプ",
    "Response Messages":"レスポンスメッセージ",
    "HTTP Status Code":"HTTPステータスコード",
    "Reason":"理由",
    "Response Model":"レスポンスモデル",
    "Request URL":"リクエストURL",
    "Response Body":"レスポンスボディ",
    "Response Code":"レスポンスコード",
    "Response Headers":"レスポンスヘッダ",
    "Hide Response":"レスポンスを隠す",
    "Headers":"ヘッダ",
    "Try it out!":"実際に実行!",
    "Show/Hide":"表示/非表示",
    "List Operations":"操作一覧",
    "Expand Operations":"操作の展開",
    "Raw":"未加工",
    "can't parse JSON.  Raw result":"JSONへ解釈できません.  未加工の結果",
    "Example Value":"値の例",
    "Model Schema":"モデルスキーマ",
    "Model":"モデル",
    "Click to set as parameter value":"パラメータ値と設定するにはクリック",
    "apply":"実行",
    "Username":"ユーザ名",
    "Password":"パスワード",
    "Terms of service":"サービス利用規約",
    "Created by":"Created by",
    "See more at":"詳細を見る",
    "Contact the developer":"開発者に連絡",
    "api version":"APIバージョン",
    "Response Content Type":"レスポンス コンテンツタイプ",
    "Parameter content type:":"パラメータコンテンツタイプ:",
    "fetching resource":"リソースの取得",
    "fetching resource list":"リソース一覧の取得",
    "Explore":"調査",
    "Show Swagger Petstore Example Apis":"SwaggerペットストアAPIの表示",
    "Can't read from server.  It may not have the appropriate access-control-origin settings.":"サーバから読み込めません.  適切なaccess-control-origin設定を持っていない可能性があります.",
    "Please specify the protocol for":"プロトコルを指定してください",
    "Can't read swagger JSON from":"次からswagger JSONを読み込めません",
    "Finished Loading Resource Information. Rendering Swagger UI":"リソース情報の読み込みが完了しました. Swagger UIを描画しています",
    "Unable to read api":"APIを読み込めません",
    "from path":"次のパスから",
    "server returned":"サーバからの返答"
});
