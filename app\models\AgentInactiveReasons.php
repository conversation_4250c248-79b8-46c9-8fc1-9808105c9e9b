<?php
/**
 * AgentInactiveReasons model
 *
 * @category AgentInactiveReasons
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * AgentInactiveReasons model
 *
 * @category AgentInactiveReasons
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class AgentInactiveReasons extends \CallPotential\CPCommon\RestModel
{
    /**
     * Primary key Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $id;

    /**
     * Description
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     */
    protected $description;

    /**
     * User Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $user_id;

    /**
     * Is default
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $is_default;

    /**
     * Activity SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $activity_sid;

    /**
     * Method to set the value of field id
     *
     * @param integer $id primary key Id
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field description
     *
     * @param string $description description
     *
     * @return $this
     */
    public function setDescription(string $description)
    {
        $this->description = $description;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $user_id User Id
     *
     * @return $this
     */
    public function setUserId(int $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field is_default
     *
     * @param integer $is_default is Default boolean value to set
     *
     * @return $this
     */
    public function setIsDefault(int $is_default)
    {
        $this->is_default = $is_default;

        return $this;
    }

    /**
     * Method to set the value of field activity_sid
     *
     * @param string $activity_sid activity SID
     *
     * @return $this
     */
    public function setActivitySid(string $activity_sid)
    {
        $this->activity_sid = $activity_sid;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns the value of field description
     *
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * Returns the value of field user_id
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field is_default
     *
     * @return integer
     */
    public function getIsDefault(): int
    {
        return $this->is_default;
    }

    /**
     * Returns the value of field activity_sid
     *
     * @return string
     */
    public function getActivitySid(): string
    {
        return $this->activity_sid;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setSource("agent_inactive_reasons");
        $this->setConnectionService('dbLegacy');
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return AgentInactiveReasons[]|AgentInactiveReasons
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return AgentInactiveReasons
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
