const Twilio = require('twilio');
const AWS = require('aws-sdk');
const express = require('express');
const config = require('../config/config');
const router = express.Router();
const TwUtils = require('./utils/twillio');
const redis = require('redis');
const { LocationModel } = require('../models/location');
const cpapiClient = require('../libraries/cpapi-client');
const got = require('got');
const bugsnagService = require('../libraries/bugsnag-service');

AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });

const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
const tableName = config.dynamodb.acctTable;

Array.prototype.forEachAsync = async function (fn) {
    for (let t of this) { await fn(t) }
};

async function getAccountDetail(accountSid) {
  const params = {
    TableName: tableName,
    IndexName: 'account_sid-index',
    KeyConditionExpression: "account_sid = :sid",
    ExpressionAttributeValues: {
      ":sid": accountSid
    }
  };

  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

async function cleanupLocationTask(client, task, workspaceSid) {
  if (task.assignmentStatus === 'assigned' || task.assignmentStatus === 'wrapping') {

    try {
      /*
      Try to complete the task. if race condition exists, this will fail and we'll
      attempt to cancel the task.
      */
      await client.taskrouter
        .workspaces(workspaceSid)
        .tasks(task.sid)
        .update({
          assignmentStatus: 'completed',
          reason: 'moving to new call step',
        });
    } catch (err) {
      await client.taskrouter
        .workspaces(workspaceSid)
        .tasks(task.sid)
        .update({
          assignmentStatus: 'canceled',
          reason: 'moving to new call step',
        });
    }
  } else if (task.assignmentStatus === 'pending' || task.assignmentStatus === 'reserved') {
    try {
      /*
      Try to cancel the task. if race condition exists, this will fail and we'll
      attempt to complete the task.
      */
      await client.taskrouter
        .workspaces(workspaceSid)
        .tasks(task.sid)
        .update({
          assignmentStatus: 'canceled',
          reason: 'moving to new call step',
        });
    } catch (err) {
      await client.taskrouter
        .workspaces(workspaceSid)
        .tasks(task.sid)
        .update({
          assignmentStatus: 'completed',
          reason: 'moving to new call step',
        });
    }

  }
}

async function moveLocationNextStep(callSid, req_q_data, req_p_data, twilioClient) {
  const CallRoute = require('../libraries/call-route').CallRoute;
  const CallLogModel = require('../models/call-log');
  let log_info = await new CallLogModel().list_dynamodb(callSid, 'twilio_id');
  const callRoute = new CallRoute({ log_id: log_info.log_id }, req_q_data, req_p_data);

  if (callRoute.exit === true) {
    await callRoute.chk_or_exit_update_call_log(req_q_data, log_info.log_id, log_info, req_p_data);
    return;
  }

  await TwUtils.make_location_leave_queue_request(log_info.log_id);

  var redirect_url = config.call_url +
    'twilio/process_next_step/' + log_info.log_id + "?next_step";

  let updateInstruction = new Twilio.twiml.VoiceResponse();
  updateInstruction.redirect(redirect_url);

  await twilioClient
    .calls(callSid)
    .fetch()
    .then(async (call) => {
      if (call.status == 'in-progress') {
        await twilioClient.calls(callSid)
        .update({
          twiml: updateInstruction.toString(),
        }).catch((e) => {
          console.log(e, new Error().stack);
        });
      }
    })
    .catch((e) => {
      console.log(e, new Error().stack);
    });
}

router.route('/location_enqueue/:log_id/')
  .post(async function (req, res) {
    const req_p_data = {};
    if (req.body) {
      Object.assign(req_p_data, req.body);
    }
    req_p_data.req_url = req.url;
    res.set('Content-Type', 'text/xml');

    /*
      DETECT CALLER HANGUP: check CallStatus === 'in-progress' && QueueResult === 'hangup'
      If this is the case, we want to remove the task from the task queue
      */

    let acct = await getAccountDetail(req.body.AccountSid);
    let twilioClient;
    
    if (req_p_data['CallStatus'] === 'in-progress' && req_p_data['QueueResult'] === 'hangup') {
      twilioClient = new Twilio(acct.account_sid, acct.authtoken);
      
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();

      // Key being set from task_created event in workspace-event-handler
      let taskSid = await redisClient.get(`locationCall_${req_p_data['CallSid']}`);

      try {
        let locationsCallSids = JSON.parse(
          await redisClient.get(
            "location_call_sids_" + req.body.CallSid
          ));
          
        //completes all location calls when customer hangup
        if (locationsCallSids) {
          await locationsCallSids.forEachAsync(async (callSid) => {
            try {
                await twilioClient.calls(callSid)
                  .update({status: 'completed'})
                  .catch((e) => {
                    console.error(e, new Error().stack);
                  });
            } catch (error) {
              console.debug("Participant call end error", error)
            }
          });
        }
      } catch (error) {
        console.error(error, new Error().stack);
      }

      await redisClient.disconnect();


      const task = await twilioClient.taskrouter.workspaces(acct.workspace_sid).tasks(taskSid).fetch();
      if (task && task.sid) {
        await cleanupLocationTask(twilioClient, task, acct.workspace_sid);
      }
    }

    res.send('<Response/>');
  })

/*
  CCC-164: Added this route as a way to serve TwiML
    that will play a normal telephone ringtone when
    an enqueued location caller is waiting to be
    connected to the location.
*/
router.route('/location_wait/:log_id/:wait_time')
  .post(async function (req, res){

    const redis_client = redis.createClient({'url': `redis://${config.redis.host}`});
    await redis_client.connect();
    let task_sid = await redis_client.get('locationCall_'+req.body.CallSid);
    let webhookKey = await redis_client.get('webhook_key_'+req.body.CallSid);

    let webhookCallData = {};
    if (webhookKey) {
      webhookCallData = await redis_client.get(webhookKey);
    }

    //CP-17503 if wait-time of callroute step exceeds then redirect to next step
    if (req.params.wait_time < req.body.QueueTime 
      && Object.keys(webhookCallData).length !== 0) {
      webhookCallData = JSON.parse(webhookCallData)
      if (task_sid && webhookCallData.redirected === 'false'
        && webhookCallData.moveLocationNextStep === 'false') {
        let acct = await getAccountDetail(req.body.AccountSid);
        var twilioClient = new Twilio(acct.account_sid, acct.authtoken);

        webhookCallData.moveLocationNextStep = 'true';
        await redis_client.set(webhookKey,
          JSON.stringify(webhookCallData), {EX: 300});

        let otherLocationsCallSids = JSON.parse(
          await redis_client.get(
            "location_call_sids_" + webhookCallData.customer_call_sid
            )
          );

        if (otherLocationsCallSids) {
          await otherLocationsCallSids.forEachAsync(async (callSid) => {
            try {
                await twilioClient.calls(callSid)
                  .update({status: 'completed'})
                  .catch((e) => {
                    console.error(e, new Error().stack);
                  });
            } catch (error) {
              console.debug("Participant remove error", error)
            }
          })
        }

        await moveLocationNextStep(req.body.CallSid, {'locationTaskSid': task_sid}, {}, twilioClient);
      }
      await redis_client.disconnect();

      res.set('Content-Type', 'text/xml'); //CP_CDN_URL
      return res.send(`<Response><Play loop="1">${config.CP_CDN_URL}uploads/ringtones/cp_inbound_ring.mp3</Play></Response>`);

    } else {

      let remainingWaitTime = req.params.wait_time - req.body.QueueTime;

      // 24s is the lenght of the recording audio
      if (remainingWaitTime < 24) {
        var CommonMethod = require('../libraries/common-methods').CommonMethod;
        var common_method = new CommonMethod();

        let payload = {
          'accoundSid': req.body.AccountSid,
          'callSid': req.body.CallSid,
          'remainingWaitTime': remainingWaitTime,
        };

        await common_method.invokeLambdaFunction(
          payload,
          `/location_call/location_redirect_wait`
          );
      }

      res.set('Content-Type', 'text/xml'); //CP_CDN_URL
      return res.send(`<Response><Play loop="1">${config.CP_CDN_URL}uploads/ringtones/cp_inbound_ring.mp3</Play></Response>`);
    }
  })

/**
 * This route will get executed as lambda function invoke event (Not as rest api call from twilio)
 * It will wait for x seconds and then rediret the call to next step.
 */
router.route('/location_redirect_wait')
  .post(async function (req, res) {

    try {
      const { getCurrentInvoke } = require('@vendia/serverless-express');
      const currentInvoke = getCurrentInvoke();

      let {
        accoundSid,
        callSid,
        remainingWaitTime
      } = currentInvoke.event.asyncBody;

      remainingWaitTime = remainingWaitTime * 1000;
      await new Promise((resolve) => {
        setTimeout(resolve, remainingWaitTime);
      });

      const redis_client = redis.createClient({'url': `redis://${config.redis.host}`});
      await redis_client.connect();
      let task_sid = await redis_client.get('locationCall_'+callSid);
      let webhookKey = await redis_client.get('webhook_key_'+callSid);

      let webhookCallData = {};
      if (webhookKey) {
        webhookCallData = await redis_client.get(webhookKey);
        webhookCallData = JSON.parse(webhookCallData)
      }

      //check if any location call is in progress call
      let isLocationCallInProgress = await redis_client.get("location_call_in_progress_"+callSid);

      if (task_sid && Object.keys(webhookCallData).length !== 0 
        && webhookCallData.redirected === 'false'
        && webhookCallData.moveLocationNextStep === 'false' && !isLocationCallInProgress) {

        webhookCallData.moveLocationNextStep = 'true';
        await redis_client.set(webhookKey,
          JSON.stringify(webhookCallData), {EX: 300});

        let acct = await getAccountDetail(accoundSid);
        var twilioClient = new Twilio(acct.account_sid, acct.authtoken);
        let otherLocationsCallSids = JSON.parse(
          await redis_client.get(
            "location_call_sids_" + webhookCallData.customer_call_sid
            )
          );

        if (otherLocationsCallSids) {
          await otherLocationsCallSids.forEachAsync(async (callSid) => {
            try {
                await twilioClient.calls(callSid)
                  .update({status: 'completed'})
                  .catch((e) => {
                    console.error(e, new Error().stack);
                  });
            } catch (error) {
              console.debug("Participant remove error", error)
            }
          })
        }

        await moveLocationNextStep(callSid, {'locationTaskSid': task_sid}, {}, twilioClient);
      }

      await redis_client.disconnect();
    } catch (e) {
      console.error(e, new Error().stack);
    }

    res.send();
    return;
  })

router.route('/location_next_step/:log_id/')
  .post(async function (req, res) {

    const req_q_data = req.query;
    const req_p_data = {};
    if (req.body) {
      Object.assign(req_p_data, req.body);
    }

    req_p_data.req_url = req.url;

    global.query_data = {};
    global.query_data[req.body.CallSid] = {...req_q_data, ...req_p_data}

    let acct = await getAccountDetail(req.body.AccountSid);
    var twilioClient = new Twilio(acct.account_sid, acct.authtoken);

    /*
     We only move the call route forward when a location call
     times out before an agent answers.
     */
    if (! ['failed', 'busy', 'no-answer'].includes(req.body.DialCallStatus)) {
      const cpapiClient = require('../libraries/cpapi-client');
      const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);

      const payload = {
        location_id: req_q_data.locationId,
        twilio_id: req.body.CallSid
      };

      if (typeof req.body.RecordingUrl !== 'undefined' && req.body.RecordingUrl) {
        payload.recording_url = req.body.RecordingUrl;

        if (req.body.RecordingSid) {
          payload.recording_sid = req.body.RecordingSid;
        }
      }

      if (req.body.RecordingDuration) {
        payload.duration = req.body.RecordingDuration;
      }

      await callClient // dynamodb endpoint
        .putData(`call/${req_q_data.locationId}/${req.body.CallSid}`, payload);

    } else {
      await moveLocationNextStep(req.body.CallSid, req_q_data, req_p_data, twilioClient);
    }

    /*
      - Tasks with assignment status = "assigned" or "wrapping" must move to "completed".
      - Tasks with assignment status = "pending" or "reserved" must move to "canceled".
    */

    await twilioClient.taskrouter.workspaces(req_q_data.WorkspaceSid)
      .tasks(req_q_data.locationTaskSid)
      .fetch()
      .then(async (task) => {
        await cleanupLocationTask(twilioClient, task, req_q_data.WorkspaceSid);
      })
      .catch((e) => {
        console.log(e, new Error().stack);
      });

    res.set('Content-Type', 'text/xml');
    return res.send('<Response/>');

  })

router.route('/call/connect')
  .get(async function(req, res) {
    console.debug('Fonality webhook payload', req.query)
    var req_q_data = req.query;

    if (!req_q_data.phone || !req_q_data.callNumber) {
      res.set('Content-Type', 'text/xml');
      res.status(400);
      res.send('<?xml version="1.0" encoding="UTF-8"?><Response>Required Parameters not supplied.</Response>');
      return;
    }

    let resposne = await new LocationModel().webhook_call_redirect(req_q_data);
    let xml_res = resposne.toString();
    res.set('Content-Type', 'text/xml');
    res.send(xml_res);
  })
  .post(async function(req, res) {
    console.debug('Fonality webhook payload', req.query, req.body)
    var req_q_data = req.body;

    if (!req_q_data.callNumber && !req_q_data.phone) {
      req_q_data = req.query;
    }

    if (!req_q_data.phone || !req_q_data.callNumber) {
      res.set('Content-Type', 'text/xml');
      res.status(400);
      res.send('<?xml version="1.0" encoding="UTF-8"?><Response>Required Parameters not supplied.</Response>');
      return;
    }

    let resposne = await new LocationModel().webhook_call_redirect(req_q_data);
    let xml_res = resposne.toString();
    res.set('Content-Type', 'text/xml');
    res.send(xml_res);   
  })

router.route('/conference_call/:conf_name')
  .post(async function(req, res) {
    // console.log("conf event callbacks")

    let acct = await getAccountDetail(req.body.AccountSid);
    var twilioClient = new Twilio(acct.account_sid, acct.authtoken);
    
    if (req.body.StatusCallbackEvent === "participant-leave") {
      const conferences = await twilioClient.conferences.list({
        friendlyName: req.params.conf_name
      });
      const participants = await twilioClient.conferences(conferences[0].sid).participants.list();

      if (participants.length == 1) {
        //here conf_name is customer call sid
        let customerCallSid = req.params.conf_name;
        if (participants[0].callSid == customerCallSid) {
          try {
            await twilioClient.calls(customerCallSid)
              .update({status: 'completed'})
              .catch((e) => {
                console.error(e, new Error().stack);
              });
          } catch (error) {
            console.debug("Participant call update error", error)
          }
        }
      }
    }

    //on conf end event complete the task
    if (req.body.StatusCallbackEvent === "conference-end") {
      try {
          const task = await twilioClient.taskrouter.workspaces(acct.workspace_sid).tasks(req.query.task_sid).fetch();
          if (task && task.sid) {
            await cleanupLocationTask(twilioClient, task, acct.workspace_sid);
          }
      } catch (error) {
        console.error("Error while updating task status to completed");
        console.error(error);
      }
    }

    res.send();
  })

router.route('/customer_connect/:conf_name')
  .post(function(req, res) {
    // console.log("POST customer_connect callbacks")
    // console.log(req.body)

    const recordingStatusUrl = config.call_url + `location_call/recording/${req.params.conf_name}/?customer_call_sid=${req.params.conf_name}`;

    const conferenceParams = {
      startConferenceOnEnter: true,
      endConferenceOnExit: true,
    }

    const recordOption = req.query.record_option;
    
    const confTwiml = new Twilio.twiml.VoiceResponse();
    const dial = confTwiml.dial({
      record: recordOption == 'record-from-answer-dual' ? 'record-from-answer-dual' : 0,
      recordingStatusCallback: recordingStatusUrl
    });
    dial.conference(conferenceParams, req.params.conf_name);
    var xml_res = confTwiml.toString();
    res.set('Content-Type', 'text/xml');

    res.send(xml_res);
  })

router.route('/recording/:conf_name')
  .post(async function(req, res) {
    let data = req.body
    let queryParam = req.query
    let callSid = queryParam.customer_call_sid

    // Add Bugsnag breadcrumb for recording webhook
    bugsnagService.leaveBreadcrumb('Location call recording webhook received', {
      callSid: callSid,
      conferenceName: req.params.conf_name,
      hasRecordingUrl: !!data.RecordingUrl,
      hasRecordingSid: !!data.RecordingSid,
      hasRecordingDuration: !!data.RecordingDuration,
      recordingStatus: data.RecordingStatus,
      postDataKeys: Object.keys(data)
    });

    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
    var CallLogModel = require('../models/call-log');
    let callLogInfo = await new CallLogModel().list_dynamodb(callSid, 'twilio_id');

    if (!callLogInfo || Object.keys(callLogInfo).length === 0) {
      // Add Bugsnag error tracking for missing call info
      bugsnagService.notify('Call log info not found for recording webhook', {
        severity: 'error',
        metadata: {
          call: {
            callSid: callSid,
            conferenceName: req.params.conf_name,
            recordingData: data
          }
        }
      });
      return res.send();
    }

    const payload = {
      recording_url : data.RecordingUrl,
      recording_sid : data.RecordingSid,
      call_duration : data.RecordingDuration,
    };

    // Add Bugsnag breadcrumb for successful recording update
    bugsnagService.leaveBreadcrumb('Location call recording updated', {
      callSid: callSid,
      locationId: callLogInfo.location_id,
      recordingUrl: data.RecordingUrl,
      recordingSid: data.RecordingSid,
      recordingDuration: data.RecordingDuration
    });

    try {
      await callClient // dynamodb endpoint
        .putData(`call/${callLogInfo.location_id}/${callSid}`, payload);
    } catch (error) {
      // Add Bugsnag error tracking for failed recording update
      bugsnagService.notify(error, {
        metadata: {
          call: {
            callSid: callSid,
            locationId: callLogInfo.location_id,
            recordingData: data,
            error: 'Failed to update recording in DynamoDB'
          }
        }
      });
    }

    res.send();
  })

router.route('/fonality_call_status/:conf_name')
  .post(async function(req, res) {
    let moveNextStep = false;

    const redis_client = redis.createClient({'url': `redis://${config.redis.host}`});
    await redis_client.connect();
    let conferenceName = req.params.conf_name

    let acct = await getAccountDetail(req.body.AccountSid);
    const twilioClient = new Twilio(acct.account_sid, acct.authtoken);

    let redis_data = await redis_client.get(req.query.redis_key);

    if (redis_data && Object.keys(redis_data).length !== 0) {
      redis_data =  JSON.parse(redis_data);
      if (redis_data.redirected === 'false') {
        moveNextStep = true;
      }
    }

    if (['failed', 'busy', 'no-answer'].includes(req.body.CallStatus)) {
      moveNextStep = true;
    }

    if (redis_data.moveLocationNextStep === 'true') {
      moveNextStep = false;
    }

    try {
      let locationsCallSids = JSON.parse(await redis_client.get("location_call_sids_"+conferenceName));
      //let conferenceSid = await redis_client.get("conference_sid_"+conferenceName);
      
      const conferences = await twilioClient.conferences.list({
        friendlyName: conferenceName
      });

      console.debug('location conference participant fetch', conferences);
      const participants = await twilioClient.conferences(conferences[0].sid).participants.list();
      let webhookParticipant = participants.filter(
        participant => participant.label == 'webhookParticipant');

      if (moveNextStep && participants.length == 0) {
        redis_data.moveLocationNextStep = 'true';
        await redis_client.set(req.query.redis_key,
          JSON.stringify(redis_data), {EX: 300});
        await moveLocationNextStep(req.params.conf_name, 
          {'locationTaskSid': req.query.task_sid}, {}, twilioClient);
      }

      // Do not process this if status callback is for fonality/ringcentral call
      if (webhookParticipant.callSid != req.body.CallSid
        && participants.length > 0 && locationsCallSids.length != 0 
        && 'in-progress' === req.body.CallStatus) {

        if (redis_data.redirected === 'false') {
          await participants.forEachAsync(async (participant) => {
            //remove other particpaints, exclude customer call sid and current call sid
            if (! [conferenceName, req.body.CallSid].includes(participant.callSid)) {
              try {
                await twilioClient.calls(participant.callSid)
                  .update({status: 'completed'})
                  .catch((e) => {
                    console.error(e, new Error().stack);
                  });
                } catch (error) {
                  console.debug("Participant call update error", error)
                }
              }
            })

          await redis_client.set("location_call_in_progress_"+conferenceName,req.body.CallSid, {EX: 300});

          await twilioClient.calls(conferenceName)
            .update({
              url: redis_data.customer_connect_url,
              method: "POST"
            })
            .catch((e) => {
              console.error(e, new Error().stack);
            });
        } else {
          await twilioClient.calls(req.body.CallSid)
            .update({status: 'completed'})
            .catch((e) => {
              console.error("Error call update",e, new Error().stack);
            });
        }
      }
    } catch (error) {
      console.log("Error", error)
    }

    await redis_client.disconnect();

    res.set('Content-Type', 'text/xml');
    return res.send('<Response/>');
  })

router.route('/ringcentral_webhook')
  .post(async function(req, res) {
    let validation_token = req.header('Validation-token');

    // If Validation Token is sent in headers then it is a validation request
    // from RingCentral, it is a way to authenticate webhook url when we
    // subscribe to extension events
    if (typeof validation_token !== 'undefined' &&  validation_token !== null && validation_token !== '') {
      res.set('Validation-token', validation_token)
      res.send(validation_token)
      return;
    }

    let event_data = null;

    // If validation token is not set, which means it is an
    // webhook call for some event, thus process event data
    try {
      event_data = JSON.parse(req.body);
    } catch(e) {
      event_data = req.body;
    }

    // console.log('ringcentral_webhook event_data ', event_data, req.query);

    if (event_data === null && event_data === '') {
      res.send('Invalid request');
      return;
    }

    if (!event_data.body) {
      res.send('Empty Payload');
      return;
    }

    if (!event_data.body.activeCalls) {
      res.send('Empty Payload Active calls');
      return;
    }

    let active_calls = event_data.body.activeCalls;

    await active_calls.forEachAsync(async item => {
      // console.log('ringcentral_webhook event_item', item);
      let from_number = item.from.trim().replace(/[^\d]+/, '');
      let to_number = item.to.trim().replace(/[^\d]+/, '').slice(-10);

      if (item.telephonyStatus === 'CallConnected' && item.direction === 'Inbound') {
          let endpoint = `${config.call_url}location_call/call/connect?phone=${to_number}&callNumber=${from_number}`;
          try {
            var response = await got.get(endpoint);
            if (response && response.statusCode === 200) {
              console.log('ringcentral_webhook call success ', endpoint);
            }
          } catch (error) {
            if (error) {
              console.log('ringcentral_webhook call error ', endpoint, error, response.body); 
            }
          }
      }
    });

    res.send();
    return;
});

module.exports = router
