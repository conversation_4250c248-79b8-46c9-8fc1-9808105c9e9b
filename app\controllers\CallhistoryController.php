<?php
/**
 * CallhistoryController
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 3/30/17
 * Time: 4:38 PM
 *
 * @category CallhistoryController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\EventRouter;
use CallPotential\CPCommon\HttpStatusCode;
use Phalcon\Db\Enum;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryLead",
 * @SWG\Property(property="first_name",type="string"),
 * @SWG\Property(property="last_name",type="string"),
 * @SWG\Property(property="phone",type="string"),
 * @SWG\Property(property="email",type="string"),
 * @SWG\Property(property="qt_rental_type",type="integer"),
 * @SWG\Property(property="inquiry_type",type="integer"),
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryTenant",
 * @SWG\Property(property="first_name",type="string"),
 * @SWG\Property(property="last_name",type="string"),
 * @SWG\Property(property="phone",type="string"),
 * @SWG\Property(property="email",type="string")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryQueue",
 * @SWG\Property(property="agent_id",type="integer"),
 * @SWG\Property(property="agent_name",type="string"),
 * @SWG\Property(property="queue_id",type="integer"),
 * @SWG\Property(property="created_date",type="string"),
 * @SWG\Property(property="accepted_date",type="string"),
 * @SWG\Property(property="agent_call_duration",type="integer"),
 * @SWG\Property(property="is_timed_out",type="integer"),
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryItem",
 *     allOf = {
 * @SWG\Schema(ref="#/definitions/CallHistory"),
 * @SWG\Schema(
 *      @SWG\Property(property="num_rollovers",type="integer"),
 *      @SWG\Property(property="_lastmodified",type="integer"),
 *      @SWG\Property(property="agent_queue",type="integer"),
 *      @SWG\Property(property="_lastupdate",type="integer"),
 *      @SWG\Property(property="call_duration",type="integer"),
 *      @SWG\Property(property="agent_answered",type="integer"),
 *      @SWG\Property(property="version_id",type="integer"),
 *      @SWG\Property(property="ad_name",type="string"),
 *      @SWG\Property(property="recording_duration",type="integer")
 *       )
 *     }
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryList",
 * @SWG\Property(
 *     property="items",
 *     type="array",
 * @SWG\Items(ref="#/definitions/CallHistoryItem")
 *   ),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData"),
 * @SWG\Property(property="status",type="string")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryBulkResultItem",
 * @SWG\Property(property="message",type="string"),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallHistoryItem"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryBulkResult",
 * @SWG\Property(property="success",type="array",@SWG\Items(type="number")),
 * @SWG\Property(property="error",type="array",@SWG\Items(ref="#/definitions/CallHistoryBulkResultItem"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryBulkResponse",
 * @SWG\Property(property="status",type="string",enum={"OK","ERROR","MIXED"}),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallHistoryBulkResult"))
 * )
 */

/**
 * CallhistoryController
 *
 * @category CallhistoryController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class CallhistoryController extends \CallPotential\CPCommon\Controllers\BaseController
{
    use \CallPotential\CPCommon\Controllers\SessionTrait;
    use \CallPotential\CPCommon\Controllers\ElasticSearchTrait {
        getItem as protected esTraitGetItem;
    }

    protected $outputFormat = '';
    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    public function getModelName(): string
    {
        return "\\CallHistory"; //model
    }

    /**
     * Constructor to initialize data
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->outputFormat = $this->request->getHeader('Accept');
        if (array_key_exists('of', $_GET)) {
            $this->outputFormat = $_GET['of'];
        }
    }

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($parentId);
        $callArchive = CallArchive::getInstance();
        $callArchive->setBulkCount(0);
        $callArchive->processIndexPrefix($data["datestamp"]);
        try {
            $callArchive->deleteByLogId($data);
        } catch (Exception $e) {
            $this->debugMessage('Id not found' . __LINE__);
        }
        $record = $callArchive->processRecord($data);
        $callArchive->processIndexPrefix(null);

        return $record;
    }

    /**
     * Change in data before sending to update flow
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    public function preUpdateCallHistory(array $data): array
    {
        if (0 < count($data)) {
            $newData = [];
            $newData['log_id'] = $data['log_id'];
            $newData['twilio_id'] = $data['twilio_id'];
            $newData['recording_sid'] = Util::array_get('recording_sid', $data, "");
            $newData['call_number'] = $data['call_number'];
            $newData['caller_name'] = $data['caller_name'];
            $newData['call_name'] = $data['call_name'];
            $newData['datestamp'] = $data['datestamp'];
            $newData['call_type'] = $data['call_type'];
            $newData['call_processed_by'] = $data['call_processed_by'];
            $newData['recording_url'] = $data['recording_url'];
            $newData['duration'] = $data['recording_duration'] ??
            (int) Util::array_get('call_duration', $data, 0);
            $newData['call_destination'] = $data['call_destination'];
            $newData['answered_by'] = $data['answered_by'];
            $newData['ad_id'] = $data['ad_id'];
            $newData['grade'] = $data['grade'];
            $newData['halloffame'] = $data['halloffame'];
            $newData['gradesheet'] = $data['gradesheet'];
            $newData['gradesheet_id'] = $data['gradesheet_id'];
            $newData['customer_card'] = $data['customer_card'];
            $newData['call_status'] = $data['call_status'];
            $newData['gradesheet_points_appointed'] = (int) $data['gradesheet_points_appointed'];
            $newData['gradesheet_points_possible'] = (int) $data['gradesheet_points_possible'];
            $newData['manager_score'] = $data['manager_score'];
            $newData['confirm_action'] = $data['confirm_action'];
            $newData['confirmed_by'] = $data['confirmed_by'];
            $newData['is_excluded'] = $data['is_excluded'];
            $newData['customer_type'] = $data['customer_type'];
            $newData['customer_name'] = $data['customer_name'];
            $newData['customer_id'] = $data['customer_id'];
            $newData['is_auto_call'] = $data['is_auto_call'];
            $newData['neighbor_location_id'] = $data['neighbor_location_id'];
            $newData['employee_id'] = $data['employee_id'];
            $newData['fk_lead_id'] = (int) Util::array_get('lead_id', $data, 0);

            return $newData;
        } else {
            return array();
        }
    }

    /**
     * Update call history record
     *
     * @param mixed $id   primary key value of the record
     * @param array $data array of POST body / query params in key value pair
     *
     * @return void
     */
    public function updateCallHistory($id, array $data)
    {
        unset($id);
        $data = $this->preUpdateCallHistory($data);
        $callDetailData = CallDetail::findFirst(
            [
            "conditions" => "log_id = ".$data['log_id'],
            ]
        );

        if ($callDetailData) {
            /* Updating MySql data start */
            foreach ($data as $k => $v) {
                if ($k === "call_type"
                    && $callDetailData->$k !== "inbound"
                    && $v === "inbound"
                ) {
                    continue;
                }
                $callDetailData->$k = $v;
            }
            $callDetailData->save();

            /* Sleep for 50ms for replica lag */
            usleep(50000);

            /* Retrieve updated record from MySql */
            $CallArchive = CallArchive::getInstance();
            $result = $CallArchive->buildCallRecord(
                Util::array_get('log_id', $data, 0),
                Util::array_get('twilio_id', $data, '')
            );

            return $result;
        }

        return [];
    }

    /**
     * Intermediate function to delete data for delete action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function deleteItem($id)
    {
        $modelName = $this->getModelName();
        $model = new $modelName();
        $resp = $model->deleteById($id);
        if ($resp['result'] === "deleted" || $resp['result'] === 'noop') {
            return true;
        }

        return false;
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        $result = [];
        if (!empty($this->listType) && $this->listType === 'ElasticSearch') {
            $result = $this->getItemCurrentPreviousMonthIndex($id);
            if (isset($result['0']['neighbor_location_id']) &&
                $result['0']['neighbor_location_id'] > 0) {
                $intClient = ClientFactory::getIntClient($this->getRequestAuthToken());
                $accountId = Util::array_get('customer_id', $result[0], '');
                $requestData = [
                    'mysql_id' => $accountId,
                    'location_id' => $result['0']['neighbor_location_id'],
                    'account_id' => $accountId,
                    'entity_type' => 'customer',
                ];
                try {
                    $esIdRecord = Util::objectToArray($intClient->convertMysqlEsId($requestData));
                    $result[0]['es_tenant_id'] = (string) Util::array_get(
                        'es_id',
                        $esIdRecord['data'],
                        ''
                    );
                } catch (Exception $e) {
                }
            }
        } else {
            $modelName = $this->getModelName();
            $model     = new $modelName();
            $result    = $model->findByCurrentPreviousMonthIndex($id);
            if (isset($result['hits']['total']) && $result['hits']['total'] > 0) {
                $result = Util::array_get('_source', $result['hits']['hits'][0], []);
            } else {
                $result = [];
            }
        }

        return $result;
    }

    /**
     * Get record from ES
     *
     * @param mixed $log_id call log Id
     *
     * @return array
     */
    public function getItemFromEs($log_id): array
    {
        $modelName = $this->getModelName();
        $model = new $modelName();
        $model->indexSuffix = "-*";
        $objData = new \stdClass();
        $objData->log_id = $log_id;
        $result = $model->searchAll(['match' => $objData]);

        return $result;
    }

    /**
     * Get record from ES current & previous mont index
     *
     * @param mixed $log_id call log Id
     *
     * @return array
     */
    public function getItemCurrentPreviousMonthIndex($log_id): array
    {
        $modelName = $this->getModelName();
        $model = new $modelName();
        $getInitialIndex = $model->getIndex();
        $startDate = date("Y-m-d H:i:s", strtotime("-1 Months"));
        $endDate   = date("Y-m-d H:i:s");
        $model->setMultiIndex($startDate, $endDate);
        $objData = new \stdClass();
        $objData->log_id = $log_id;
        $result = $model->searchAll(['match' => $objData]);
        if (empty($result)) {
            $model->setIndex = $getInitialIndex;
            $result = $this->getItemFromEs($log_id);
        }

        return $result;
    }

    /**
     * GetListContent
     *
     * @return mixed
     */
    public function getListContent()
    {
        $model = $this->getModelObject();
        $startDate =  $this->request->getQuery('startDate', null, '2016-01-01 00:00:00');
        $endDate =  $this->request->getQuery('endDate', null, date('Y-m-d')).' 23:59:59';
        $locId = $this->request->getQuery('locationId', null, null);
        if ((int) $locId === 0) {
            $locId = $this->getLocationIDs();
        } elseif (strpos($locId, ',') !== false) {
            $locId = explode(',', $locId);
        }

        $includeNeighborLocations = $this->request->getQuery(
            'includeNeighborLocations',
            null,
            false
        );

        if ($includeNeighborLocations === "false") {
            $includeNeighborLocations = false;
        }

        $callType = $this->request->getQuery('callType', null, null);
        if (strpos($callType, ',')) {
            $callType = explode(',', $callType);
        }
        $logId = $this->request->getQuery('logId', null, null);
        if (strpos($logId, ',')) {
            $logId = explode(',', $logId);
        }
        $leadId = $this->request->getQuery('leadId', null, null);
        if (strpos($leadId, ',')) {
            $leadId = explode(',', $leadId);
        }
        $employeeId = $this->request->getQuery('employeeId', null, null);
        if (strpos($employeeId, ',')) {
            $employeeId = explode(',', $employeeId);
        }
        $adId = $this->request->getQuery('adId', null, null);
        if (strpos($adId, ',')) {
            $adId = explode(',', $adId);
        }
        $customerId = $this->request->getQuery('customerId', null, null);
        if (strpos($customerId, ',')) {
            $customerId = explode(',', $customerId);
        }
        $customerName = $this->request->getQuery('customerName', null, null);

        $customerEmail = $this->request->getQuery('customerEmail', null, null);

        $duration =  $this->request->getQuery('duration', null, null);
        $durationVal =  $this->request->getQuery('durationVal', null, 0);

        $callDuration =  $this->request->getQuery('callDuration', null, null);
        $callDurationVal =  $this->request->getQuery('callDurationVal', null, 0);

        $listType =  $this->request->getQuery('listType', null, null);

        $channelType =  $this->request->getQuery('channel', null, '');

        $recordingDuration = null;
        $recordingDurationVal = null;
        $recordingUrlCheck = false;
        $hallOfFameCheck = false;

        if ($listType === 'unprocessed') {
            $callType = 'inbound';
            $recordingUrlCheck = true;
            $recordingDuration = '>';
            $recordingDurationVal = 0;
        } elseif ('calloffame' === $listType) {
            $hallOfFameCheck = true;
        }

        $callNumber = $this->request->getQuery('callNumber', null, null);
        if (strpos($callNumber, ',')) {
            $callNumber = explode(',', $callNumber);
        }

        $page = $this->request->getQuery('page', 'absint', 0);
        $perPage = $this->request->getQuery('perPage', null, 999);
        $orderBy = $this->request->getQuery('orderBy', null, '');
        $order = $this->request->getQuery('order', null, 'ASC');

        if ($page === 0) {
            $paging = false;
            $page = 1;
        }

        $result = $model->getAccountCalls(
            $this->getCurrentAccountId(),
            $startDate,
            $endDate,
            $page,
            $perPage,
            $orderBy,
            $order,
            $locId,
            $callType,
            $leadId,
            $employeeId,
            $adId,
            $customerId,
            $customerName,
            $callNumber,
            $duration,
            $durationVal,
            $callDuration,
            $callDurationVal,
            $recordingDuration,
            $recordingDurationVal,
            $customerEmail,
            $logId,
            $includeNeighborLocations,
            $recordingUrlCheck,
            $hallOfFameCheck,
            $channelType
        );
        if ($page > 0) {
            $totalItems = (int) $result['hits']['total'];
            $prevPage = $page -1;
            if ($prevPage < 1) {
                $prevPage = null;
            }
            if ($totalItems % $perPage === 0) {
                $totalPages = (int) ($totalItems / $perPage);
            } else {
                $totalPages = (int) ($totalItems / $perPage)+1;
            }
            if ($page < $totalPages) {
                $nextPage = $page + 1;
            } else {
                $nextPage = null;
            }
            $paging = [
                "lastPage" => $totalPages,
                "perPage" => $perPage,
                "nextPage" => $nextPage,
                "totalItems" => $totalItems,
                "totalPages" => $totalPages,
                "prevPage" => $prevPage,
                "currentPage" => $page,
                "firstPage" => 1,
            ];
        }
        $items = [];
        foreach ($result['hits']['hits'] as $item) {
            $item["_source"]['id'] = $item["_id"];
            $items[] = $item["_source"];
        }

        return [
            'items' => $items,
            'paging' => $paging,
        ];
    }

    /**
     * Retrieve location client object
     *
     * @return mixed
     */
    public function getLocationClient()
    {
        try {
            return new CallPotential\CPCommon\LocationClient(
                [],
                $this->getRequestAuthToken(),
                $this->config->application->services['loc']
            );
        } catch (GuzzleHttp\Exception\ClientException $e) {
            return null;
        }
    }

    /**
     * BulkSave
     *
     * @override bulksave method to overcome error from BaseController
     * as it is not implemented
     *
     * @return \Phalcon\Http\Response
     */
    public function bulkSave(): \Phalcon\Http\Response
    {
         return $this->sendNotImplemented();
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    public function doBulkSave(): array
    {
        return $this->sendNotImplemented();
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    public function doBulkCreate()
    {
        // TODO: Implement doBulkCreate() method.
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/callhistory/{id}",
     *     description="Returns a call record based on a single ID",
     *     summary="get callhistory",
     *     operationId="CallhistoryGetById",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *       description="Authorization token",
     *       type="string",
     *       name="Authorization",
     *       in="header",
     *       required=true
     *     ),
     * @SWG\Parameter(
     *       description="ID of callhistory to fetch",
     *       in="path",
     *       name="id",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Parameter(
     *        description="list type",
     *        type="string",
     *        name="listType",
     *        in="query",
     *        enum={"mysql","ElasticSearch"},
     *        required=true
     *     ),
     * @SWG\Response(
     *       response=200,
     *       description="call history response",
     * @SWG\Schema(ref="#/definitions/CallHistoryItem")
     *     ),@SWG\Response(
     *       response="403",
     *       description="Not Authorized Invalid or missing Authorization header"
     *     ),@SWG\Response(
     *       response="404",
     *       description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="500",
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }
        $this->listType = $this->request->getQuery('listType');

        return parent::getAction();
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/callhistory",
     *     description="Returns callhistory listing",
     *     summary="list callhistory",
     *     operationId="ListCallHistorys",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *        description="Authorization token",
     *        type="string",
     *        name="Authorization",
     *        in="header",
     *        required=true
     *     ),
     * @SWG\Parameter(
     *        description="Start Date (yyyy-mm-dd)",
     *        type="string",
     *        name="startDate",
     *        in="query",
     *        required=true
     *     ),
     * @SWG\Parameter(
     *        description="End Date (yyyy-mm-dd)",
     *        type="string",
     *        name="endDate",
     *        in="query",
     *        required=true
     *     ),
     * @SWG\Parameter(
     *         description="Page Number or false for all items",
     *         format="int64",
     *         in="query",
     *         name="page",
     *         required=true,
     *         type="string"
     *     ),
     * @SWG\Parameter(
     *         name="perPage",
     *         in="query",
     *         description="maximum number of results to return",
     *         required=false,
     *         type="integer",
     *         format="int32"
     *     ),
     * @SWG\Parameter(
     *        description="Order By",
     *        type="string",
     *        name="orderBy",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="Order",
     *        type="string",
     *        enum={"ASC", "DESC"},
     *        name="order",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A Location ID, or comma separated list of location IDs",
     *        type="string",
     *        name="locationId",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A Neighbor Location ID, or comma separated list
     *        of neighbor location IDs",
     *        type="boolean",
     *        name="includeNeighborLocations",
     *        in="query",
     *        enum={true, false},
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="list type",
     *        type="string",
     *        name="listType",
     *        in="query",
     *        enum={"unprocessed", "calloffame"},
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A Call Type, or a comma separated list of call types",
     *        type="string",
     *        name="callType",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="Call logs Log ID, or comma separated list of log IDs",
     *        type="string",
     *        name="logId",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A lead ID, or comma separated list of lead IDs",
     *        type="string",
     *        name="leadId",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A employee ID, or comma separated list of employee IDs",
     *        type="string",
     *        name="employeeId",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="An ad ID, or comma separated list of ad IDs",
     *        type="string",
     *        name="adId",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A customer ID, or comma separated list of customer IDs",
     *        type="string",
     *        name="customerId",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A customer name",
     *        type="string",
     *        name="customerName",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A customer email",
     *        type="string",
     *        name="customerEmail",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="duration compare",
     *        type="string",
     *        name="duration",
     *        in="query",
     *        enum={">","<","="},
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="duration value",
     *        type="string",
     *        name="durationVal",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="call_duration compare",
     *        type="string",
     *        name="callDuration",
     *        in="query",
     *        enum={">","<","="},
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="call duration value",
     *        type="string",
     *        name="callDurationVal",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="A customer number, or comma separated list of customer numbers",
     *        type="string",
     *        name="callNumber",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *     name="channel",
     *     in="query",
     *     description="filter for channel type",
     *     required=false,
     *     type="string",
     *     enum={"voice", "video"}
     * ),
     * @SWG\Response(
     *        response=200,
     *        description="call history response",
     *        @SWG\Schema(ref="#/definitions/CallHistoryList")
     *     ),
     * @SWG\Response(
     *        response="403",
     *        @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *        description="Not Authorized Invalid or missing Authorization header"
     *     ),
     * @SWG\Response(
     *        response="401",
     *        @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *        description="Not Authorized"
     *     ),
     * @SWG\Response(
     *        response=500,
     *        description="unexpected error",
     *        @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        if (!$this->validSession()) {
             return $this->sendAccessDeniedResponse();
        }
        $this->infoMessage('list request begin', 'CallHistoryController::listAction:'.__LINE__);
        $data = $this->getListContent();
        $this->debugMessage(
            'Load Complete: ' . count($data) . " records",
            'CallHistoryController::listAction:' . __LINE__
        );
        $this->response->setStatusCode(
            CallPotential\CPCommon\HttpStatusCode::HTTP_OK,
            CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                CallPotential\CPCommon\HttpStatusCode::HTTP_OK
            )
        );
        $this->response->setJsonContent(
            array('status' => "OK", 'items' => $data['items'], 'paging' => $data['paging'])
        );
        $this->infoMessage(
            'request end',
            'CallHistoryController::listAction:' . __LINE__
        );

        return $this->response;
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/callhistory",
     *   tags={"Call Data"},
     *   summary="Create a callhistory",
     *   description="create new callhistory",
     *   summary="create callhistory",
     *   operationId="CreateCallHistory",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Ad record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/CallHistory")
     *   ),@SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/CallHistoryItem")
     *   ),@SWG\Response(
     *     response="400",
     *     description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response=500,
     *     description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }
            $this->infoMessage(
                'create request begin',
                'CallHistoryController::saveAction:' . __LINE__
            );
            $result = parent::createAction();
            $this->infoMessage(
                'create request end',
                'CallHistoryController::saveAction:' . __LINE__
            );

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Put(path="/callhistory/{id}",
     *   tags={"Call Data"},
     *   summary="Update an existing callhistory",
     *   description="Update existing callhistory",
     *   operationId="UpdateCallHistory",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     description="ID of callhistory",
     *     in="path",
     *     name="id",
     *     required=true,
     *     type="string"
     *   ),
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="CallHistory record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/CallHistoryItem")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/CallHistoryItem")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Swagger
     *
     * @SWG\Put(path="/callhistory",
     *   tags={"Call Data"},
     *   summary="Bulk update of existing callhistory",
     *   description="Bulk update of existing callhistory",
     *   operationId="UpdateCallHistoryBulk",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="CallHistory type record",
     *     required=false,
     * @SWG\Schema(type="array",@SWG\Items(ref="#/definitions/CallHistoryItem"))
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/CallHistoryBulkResponse")
     *   ),@SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="501",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     **/

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        try {
            if ($this->validSession()) {
                $this->infoMessage(
                    'save request begin',
                    'CallHistoryController::saveAction:' . __LINE__
                );
                $data = Util::objectToArray($this->request->getJsonRawBody());

                if (is_null($data) || (is_array($data) && count($data) === 0)) {
                    return $this->sendBadRequest('Invalid Request Body');
                }

                if (Util::existSubArray($data, true)) {
                    return $this->bulkSave();
                }

                if (!is_array($data)) {
                    return $this->sendBadRequest();
                }

                $id = $this->getRelatedItem() ?? $this->getParamID();

                if ($id) {
                    $this->debugMessage(
                        'update request for id: ' . $id,
                        __METHOD__ . ":" . __LINE__
                    );
                    $current = $this->getItem($id);
                    if (is_array($current) && count($current) > 0) {
                        if (!$this->userHasWriteAccess($data)) {
                            return $this->sendAccessDeniedResponse('Write Access Denied');
                        }

                        /* Retrieve Mysql LeadId based on ES Lead Id */
                        $data = $this->getMysqlLeadId($data);
                        /* Retrieve Mysql LeadId based on ES Lead Id */

                        /* Save & retrieve record from MySql */
                        $callDetailData = $this->updateCallHistory($id, $data);

                        //Update Employee Grade data
                        $this->updateEmployeeGrade($data);
                        if (!empty($callDetailData)) {
                            $callDetailData['fk_lead_id'] = Util::array_get('lead_id', $data, null);
                            $callDetailData['customer_id'] = Util::array_get(
                                'customer_id',
                                $data,
                                null
                            );
                            $callDetailData['token'] = $this->getRequestAuthToken();
                            $callDetailData['es_lead_id'] = Util::array_get(
                                'es_lead_id',
                                $data,
                                null
                            );
                            $callDetailData['es_tenant_id'] = Util::array_get(
                                'es_tenant_id',
                                $data,
                                null
                            );
                            $callService = $this->config->application->services->call;
                            $callDetailData['callService'] = $callService;
                        }
                        EventRouter::route(
                            'CallHistoryUpdate',
                            [
                                'before' => $current,
                                'after' => $callDetailData,
                            ]
                        );

                        if (!empty($callDetailData) && !empty($callDetailData["version_id"])) {
                            unset($callDetailData['token']);
                            $this->addOtherContact($data);
                            $this->response->setStatusCode(
                                HttpStatusCode::HTTP_OK,
                                HttpStatusCode::getMessageForCode(
                                    HttpStatusCode::HTTP_OK
                                )
                            );
                            $this->response->setJsonContent(
                                $this->formatPutResponse($callDetailData)
                            );

                            return $this->response;
                        } else {
                            return $this->sendNotFound();
                        }
                    } else {
                        $this->debugMessage(
                            'Save request end id ' . $this->getParamID() . ' not found.',
                            __METHOD__ . ':' . __LINE__
                        );

                        return $this->sendNotFound();
                    }
                } else {
                    return $this->sendBadRequest();
                }
            } else {
                return $this->sendAccessDeniedResponse();
            }
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Delete(
     *     tags={"Call Data"},
     *     path="/callhistory/{id}",
     *     description="deletes a callhistory based on the ID supplied",
     *     summary="delete callhistory",
     *     operationId="DeleteCallHistory",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *         description="Authorization token",
     *         type="string",
     *         name="Authorization",
     *         in="header",
     *         required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of callhistory to delete",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="integer"
     *     ),
     * @SWG\Response(
     *         response=204,
     *         description="call queue deleted",
     * @SWG\Schema(type="null")
     *     ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        try {
            $this->infoMessage(
                'delete request begin for id '.$this->getParamID(),
                'CallHistoryController::deleteAction:'.__LINE__
            );
            if (!strlen($this->authToken)) {
                return $this->sendNotAuthorized();
            }
            if (!$this->validSession()) {
                return $this->sendForbidden();
            }
            $result =  parent::deleteAction();
            $this->infoMessage(
                'save request end for id '.$this->getParamID(),
                'CallHistoryController::deleteAction:' . __LINE__
            );

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * This is called from controller file for validation purpose
     *
     * @return array
     */
    protected function requiredRequestParams(): array
    {
        return array();
    }

    /**
     * Extract ids from locations details
     *
     * @return array
     */
    protected function getLocationIDs(): array
    {
        $activeLocationIds = [];
        if ($this->isStaticToken($this->authToken)) {
            $locClient = $this->getLocationClient();
            $locations = (array) $locClient->getActiveLocations();
            if (is_array($locations) && count($locations) > 0) {
                foreach ($locations["items"] as $loc) {
                    $activeLocationIds[] = (int) $loc->location_id;
                }
            }
        } else {
            $sessionUserData = unserialize($this->currentSession->user_data);

            if (!empty($sessionUserData['locations'])) {
                $activeLocationIds = array_keys($sessionUserData['locations']);
            }
        }

        return $activeLocationIds;
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Format GET action response before sending to std o/p
     *
     * @param mixed $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatGetResponse($data)
    {
        if (array_key_exists('_source', $data)) {
            $result = $data['_source'];
            if (array_key_exists('_id', $data)) {
                $result['id'] = $data['_id'];
            }

            return $result;
        }

        return $data;
    }

    /**
     * Change POST data before sending to Data insert flow
     *
     * @param mixed $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatPostResponse($data)
    {
        return $this->formatGetResponse($data);
    }

    /**
     * Change PUT data before sending to Data update flow
     *
     * @param mixed $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatPutResponse($data)
    {
        return $this->formatGetResponse($data);
    }

    /**
     * Any preprocessing of post data is done here
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    protected function preProcessPostData(array $data): array
    {
        if (!array_key_exists('datestamp', $data)) {
            $data['datestamp'] = gmdate('Y-m-d H:i:s');
        }
        $data['account_id'] = $this->getCurrentAccountId();

        return parent::preProcessPostData($data);
    }

    /**
     * Get MySql leadId from ES lead Id
     *
     * @param array $data call record
     *
     * @return array
     */
    private function getMysqlLeadId(array $data)
    {
        $esLeadId = Util::array_get('es_lead_id', $data, '');
        if (empty($esLeadId)) {
            $esLeadId = Util::array_get('fk_lead_id', $data, '');
        }
        $leadId = Util::array_get('lead_id', $data, '');
        if ((!empty($esLeadId) && (int) $leadId === 0)) {
            $intClient = ClientFactory::getIntClient(
                $this->getRequestAuthToken()
            );
            $leadData = Util::objectToArray($intClient->getLead($esLeadId));
            $data["lead_id"] = Util::array_get('lead_id', $leadData, "");
        }

        return $data;
    }

    /**
     * Add Other contact to Lead and Customer
     *
     * @param array $data Param data
     *
     * @return void
     */
    private function addOtherContact(array $data)
    {
        $intClient = ClientFactory::getIntClient($this->getRequestAuthToken());
        $tenantId = Util::array_get('es_tenant_id', $data, '');
        $leadId = Util::array_get('es_lead_id', $data, '');
        if (empty($leadId)) {
            $leadId = Util::array_get('fk_lead_id', $data, '');
        }
        $callType = Util::array_get('call_type', $data, '');

        if (!empty($data["call_number"])) {
            if (!empty($leadId) && $callType === 'inbound_lead') {
                $leadData = [];
                $leadData["phone_number"] = $data["call_number"];
                $leadData["phone_type"] = "other";
                $intClient->addLeadPhone($leadId, $leadData);
            } elseif (!empty($tenantId) && $callType === 'inbound_customer') {
                $tenantData = [];
                $tenantData["phone"] = $data["call_number"];
                $tenantData["phone_type"] = "other";
                $tenantData["is_primary"] = 0;
                $intClient->addTenantPhone($tenantId, $tenantData);
            }
        }
    }

    /**
     * Update employee grade
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return void
     */
    private function updateEmployeeGrade(array $data)
    {
        $updateData = [];
        $evalClient = ClientFactory::getEvalClient($this->getRequestAuthToken());
        if (!empty($data['log_id'])) {
            $params = [
                'logId' => $data['log_id'],
                'page'   => false,
            ];
            $employeeGrades = (array) $evalClient->getEmployeeGradeByLogId($params);

            if (!empty($employeeGrades['items'])) {
                foreach ($employeeGrades['items'] as $grade) {
                    $grade = Util::objectToArray($grade);
                    if ((int) $grade['employee_id'] === (int) $data['employee_id']) {
                        continue;
                    }
                    $grade['employee_id'] = $data['employee_id'];
                    $updateData[] = $grade;
                }
            }
        }
        if (!empty($updateData)) {
            $evalClient->updateEmployeeGrade($updateData);
        }
    }
}
