import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PayByPhoneController } from './PayByPhone/PayByPhone.controller';
import { PayByPhoneServicesModule, PayByPhoneServicesModuleOptions } from '@cp-workspace/pay-by-phone-domain';
import { RedisModuleOptions } from '@nestjs-modules/ioredis';
import { AsyncWorkersClientType, DomainEventsClientType, IoRedisClientType, IoRedisModuleOptions, SharedServicesModule, SharedServicesModuleOptions } from '@cp-workspace/shared';
import * as config from '../config.js';
import { EchoController } from './Echo/Echo.controller';
import { PayByPhoneRequestInterceptor } from './Interceptors/PayByPhoneRequestInterceptor';
import { AsyncTestController } from './AsyncTest/AsyncTest.controller';
import { EventLoggerController } from './EventLogger/EventLogger.controller';
import { BugsnagService } from '@cp-workspace/shared';

const sharedServicesOptions: SharedServicesModuleOptions = {
  redisOptions: {
    clientType: IoRedisClientType.EXTERNAL,
    options: {
      type: 'single',
      url: `redis://${config['redis']['host']}`,
      options: {
        db: config['redis']['db'],
        /**
         * Causes the client to wait until the first
         * connection is made before it is used.
         */
        lazyConnect: true,
      }
    } as RedisModuleOptions
  } as IoRedisModuleOptions,
  domainEventsOptions: {
    clientType: DomainEventsClientType.EXTERNAL,
    options: { enabled: config['domainEventsEnabled'] },
  },
  asyncWorkersOptions: {
    clientType: AsyncWorkersClientType.EXTERNAL,
    options: { enabled: true },
  }
};

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [() => ({ ...config })],
    }),
    PayByPhoneServicesModule.forRoot({ sharedServicesOptions } as PayByPhoneServicesModuleOptions),
    SharedServicesModule.forRoot(sharedServicesOptions),
  ],
  providers: [PayByPhoneRequestInterceptor, BugsnagService],
  controllers: [PayByPhoneController, EchoController, AsyncTestController, EventLoggerController],
})
export class AppModule {}

