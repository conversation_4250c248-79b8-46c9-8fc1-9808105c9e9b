import { PayMethodExpirationValidate } from './PayMethodExpirationValidate';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import * as CardValidator from 'card-validator';

jest.mock('card-validator');

describe('PayMethodExpirationValidate', () => {
  let service: PayMethodExpirationValidate;

  beforeEach(() => {
    service = new PayMethodExpirationValidate();
    service.services = {
      coreService: {
        encodePaymentToken: jest
          .fn()
          .mockImplementation((token) => Promise.resolve(token)),
        decodePaymentToken: jest
          .fn()
          .mockImplementation((token) => Promise.resolve(token)),
      },
    } as any;
  });

  it('should return nextState as PayMethodExpirationPrompt when expiration date is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en', paymentToken: {} } as any,
    } as any;

    (CardValidator.expirationDate as jest.Mock).mockReturnValue({
      isValid: false,
    });

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodExpirationPrompt);
  });

  it('should return nextState as PayMethodExpirationConfirm when expiration date is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1122',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en', paymentToken: {} } as any,
    } as any;

    (CardValidator.expirationDate as jest.Mock).mockReturnValue({
      isValid: true,
    });

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodExpirationConfirm);
  });

  it('should throw an error when paymentToken is not found in storage', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1122',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    (CardValidator.expirationDate as jest.Mock).mockReturnValue({
      isValid: true,
    });

    await expect(service.handler(context)).rejects.toThrow();
  });
});
