import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { UnitsToPayPrepayMonthsSelection } from './UnitsToPayPrepayMonthsSelection';
import { Locale } from '@cp-workspace/shared';

jest.mock('@cp-workspace/shared', () => {
  const original = jest.requireActual('@cp-workspace/shared');
  return {
    ...original,
    Customer: {
      ...original.Customer,
      getPrepayAmountDue: jest.fn().mockResolvedValue(1200)
    }
  };
});

describe('UnitsToPayPrepayMonthsSelection', () => {
  let unitsToPayPrepayMonthsSelection: UnitsToPayPrepayMonthsSelection;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UnitsToPayPrepayMonthsSelection],
    }).compile();

    unitsToPayPrepayMonthsSelection = module.get<UnitsToPayPrepayMonthsSelection>(UnitsToPayPrepayMonthsSelection);
    unitsToPayPrepayMonthsSelection.services = {
      integrationService: {
        calculateAmountDue: jest.fn().mockResolvedValue(1200)
      }
    } as any;

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.UnitsToPayPrepayMonthsSelection,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        selectedUnits: [{ unit_id: 101, tenant_id: 'tenant101' } as any],
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should calculate prepay amount and move to confirmation if valid response', async () => {
      context.request.Digits = '3'; // Assume user selects 3 months prepay

      await unitsToPayPrepayMonthsSelection.handler(context);

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(
        {
          numDigits: 1,
          method: 'POST',
          timeout: 10,
        }, [
        {
          messageId: 'pay-by-phone.prepay-selection',
          locale: context.storage.locale,
          i18nOptions: { args: [{ numPayments: '3' }, { prepayAmountDue: "1200.00" }] }
        }
        ]
      );
    });
  });
});
