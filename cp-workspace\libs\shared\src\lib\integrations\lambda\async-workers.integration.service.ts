import { Inject, Injectable } from '@nestjs/common';
import { AsyncWorkerEvent, AsyncWorkersServiceOptions, IAsyncWorkersInvoker, IAsyncWorkersProvider } from '../../models/async-workers.model';

@Injectable()
export class AsyncWorkersIntegrationService implements IAsyncWorkersInvoker {

  public static options: AsyncWorkersServiceOptions;
  public static provider: IAsyncWorkersProvider;
  public static client: IAsyncWorkersInvoker;
  public static workerFunctionName: string;

  get isEnabled(): boolean {
    const options = AsyncWorkersIntegrationService.options ?? { enabled: false };
    return options.enabled ?? false;
  }
  private initialized = false;
  get isInitialized(): boolean {
    return this.initialized;
  }

  constructor(@Inject('IAsycnWorkersProvider') private readonly provider: IAsyncWorkersProvider) {}

  initialize(): unknown {
    this.initialized = true;
    if (this.isEnabled) {
      AsyncWorkersIntegrationService.provider = this.provider;
      AsyncWorkersIntegrationService.client = this.provider.getClient();
      return AsyncWorkersIntegrationService.client;
    }
    return undefined;
  }

  private checkInitialized() {
    if (!this.isInitialized) {
      this.initialize();
    }
  }

  public async invokeAsyncWorker(event: AsyncWorkerEvent): Promise<void> {
    this.checkInitialized();
    if (this.isEnabled) {
      await AsyncWorkersIntegrationService.client.invokeAsyncWorker(event);
    }
  }
}
