const AWS = require('aws-sdk');
const Twilio = require('twilio');
const jwt = require('jsonwebtoken');
const got = require('got');
const redis = require('redis');
const moment = require('moment-timezone');

const config = require('./config');
const cpapiClient = require('./cpapi_client');

const taskChannel = 'custom1';
const direction = 'outbound';

AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });

Array.prototype.forEachAsync = async function (fn) { 
  for (let t of this) { await fn(t) }
}

async function handler(event, context, callback) {
  if (event.source === 'aws.events') {
    // keep warm!
    return context.done();
  }

  if (!('queryStringParameters' in event)) {
    return callback(null, response(400, { error: 'A required parameter was not specified.' }));
  }

  let params = (typeof event.queryStringParameters === 'string')
    ? JSON.parse(event.queryStringParameters)
    : event.queryStringParameters;

  const {
    authToken, // Call Potential Authorization Token
    Token: token, // Twilio Capability Token
    From: from,
    To: to,
    outboundCardLinkId
  } = params;


  const twilioAccount = await twilioCredentials(authToken);
  params.locationId = params.fromLocationId;
  params.accountId = twilioAccount.id;
  params.agentId = twilioAccount.userId;
  if (Object.keys(twilioAccount).length === 0) return response(401);

  const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);

  try {
    await jwt.verify(token, twilioAccount.authtoken);
  } catch (e) {
    console.error(e);
    return callback(null, response(401, e));
  }

  /*
    CPIC-1051
    Get SID for the workflow referred to as:
    - "OutboundVoice" (in Twilio)
    - "outbound_workflow" (in CP Account record --  -- referred to here as `twilioAccount`)
  */

  try {
    const workspace = twilio.taskrouter.workspaces(twilioAccount.workspace_sid);

    /*
      CCC-286
      Since a single agent cannot perform multiple outbound calls at once,
      ensure that, before attempting to create a new outbound call task,
      any existing outbound call tasks are closed.
    */
    await closeOrphanedOutboundTasks(workspace, twilioAccount);
    let logData = await logOutboundCall(params);
    params.log_id = logData.log_id;

    const workflowSid = twilioAccount.outbound_workflow;
    const taskOptions = {
      workflowSid: workflowSid,
      taskChannel,
      attributes: JSON.stringify({
        to,
        from,
        direction,
        taskchannel: taskChannel,
        agent_id: twilioAccount.userId,
        outboundCardLinkId,
        dbLogId: logData.log_id
      }),
    };
    const task = await workspace.tasks.create(taskOptions);

    const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
    await redisClient.connect();
    await redisClient.set(`outboundTask_${task.sid}`, logData.log_id, {EX: 300});
    await redisClient.disconnect();

    await updateTaskLogIds(params, task.sid);
    return callback(null, response(200, { sid: task.sid }));
  } catch (e) {
    console.error('ERROR while creating the task', e);
    return callback(e);
  }
}

async function logOutboundCall(params) {
  const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);

  let fkLeadId = 0;
  if (params.leadId) {
    fkLeadId = parseInt(params.leadId);
  }
  let customerId = 0;
  if (params.customerId) {
    customerId = params.customerId;
  }

  let ledgerId = 0;
  if (params.ledgerId) {
    ledgerId = params.ledgerId;
  }

  params.callType = params.callType || 'outbound';
  params.workflowStep = params.workflowStep || false;

  const payload = {
    employee_id: params.agentId,
    call_number: params.To,
    call_name: params.From,
    datestamp: moment.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss'),
    call_type: params.callType,
    location_id: params.locationId,
    account_id: params.accountId,
    is_auto_call: 0,
    twilio_id: null,
    fk_lead_id: fkLeadId,
    call_duration: 0,
    duration: 0,
    recording_url: '',
    customer_id: customerId,
    ledger_id: ledgerId,
    workflow_step: params.workflowStep
  };

  if (!payload.fk_lead_id){
    delete payload.fk_lead_id;
  }

  if (payload.customer_id) {
    delete payload.fk_lead_id;
  }

  // console.log('LOG OUTBOUND CALL', payload);

  // log to mysql
  return await callClient.postData('calldetail', payload);
}

async function updateTaskLogIds(params) {

  const callClient = new cpapiClient.callClient(params.authToken);
  // Update track_callcenter_tasks entries with log id
  try {
    //CCC-1743 update log_id in track_callcenter_tasks for callback related tasks corresponds to current outboud call 
    if (params.callback_task_sid) {
      let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${params.callback_task_sid}`);
      if (taskList && taskList.items && taskList.items.length > 0) {
        await taskList.items.forEachAsync(async (element) => {
          if (element.workflow === 'CallBackWorkflow') {
            element.log_id = params.log_id
            await callClient.putData(`trackcallcentertasks/${element.id}`, element);
          }
        });
      }
    }

  } catch (e) {
    console.error('Error in updateing track_callcenter_tasks record', e);
  }
}

/*
  CCC-286
  It is possible for unexpected situations to interrupt the normal flow
  of an outbound call.  One example, seen in CCC-286, relates to an edge
  case where a Twilio auth token may become invalid while an outbound
  call workflow is in flight.  Situations like this will leave behind
  orphaned tasks.  I.e. Tasks that are "stuck" in an "Assigned" state.

  Since a single agent cannot perform multiple outbound calls at once,
  this function will ensure that, before attempting to create a new
  outbound call task, any existing outbound call tasks are closed.
*/
async function closeOrphanedOutboundTasks(workspace, twilioAccount) {

  try {

    let reason = "Orphaned outbound call task";
    await workspace.workers(twilioAccount.workerSid)
      .reservations
      .list({limit: 20})
      .then(reservations => {
        reservations.forEach(async (reservation) => {
          let task = await workspace.tasks(reservation.taskSid).fetch();
          if (task.workflowFriendlyName === 'OutboundVoice') {
            try {
              if (task.assignmentStatus === 'assigned' || task.assignmentStatus === 'wrapping') {
                try {
                  await workspace.tasks(task.sid).update({
                    assignmentStatus: 'completed',
                    reason
                  });
                } catch (err) {
                  await workspace.tasks(task.sid).update({
                    assignmentStatus: 'canceled',
                    reason
                  });
                }
              } else if (task.assignmentStatus === 'pending' || task.assignmentStatus === 'reserved') {
                try {
                  await workspace.tasks(task.sid).update({
                    assignmentStatus: 'canceled',
                    reason
                  });
                } catch (err) {
                  await workspace.tasks(task.sid).update({
                    assignmentStatus: 'completed',
                    reason
                  });
                }
              }
            } catch (err) {
              console.error('ERROR while closing task', err, task);
            }
          }
        })
      });
  } catch (e) {
    console.error('ERROR while checking for tasks', e);
  }

}

async function twilioCredentials(authToken) {
  try {
    const url = `https://${config.env}-core.callpotential.com/session`;
    const options = {
      headers: { Authorization: authToken },
    };
    const res = await got.get(url, options);
    const data = JSON.parse(res.body).user;
    const account = await dynamoQuery(data.sid);
    account.userId = data.user_id;
    account.workerSid = data.v2_worker_sid;

    return account;
  } catch (e) {
    console.error('ERROR obtaning twilio credentials', e);
    return {};
  }
}

async function dynamoQuery(accountSid) {
  const params = {
    TableName: config.dynamodb.acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: 'account_sid = :sid',
    ExpressionAttributeValues: { ':sid': accountSid },
  };

  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}


function response(statusCode = 200, data = {}) {
  const body = (typeof data === 'string' || data instanceof String)
    ? data
    : JSON.stringify(data);

  return {
    statusCode,
    body,
  };
}

function noOp(event, context) {
  // console.log(event);
  context.done();
}

module.exports = {
  handler,
  no_op: noOp,
};
