import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { CallPotentialHttpApiService } from './callpotential-http-api.service';
import { Cacheable } from '../decorators/cacheable.decorator';
import { LocationId, LocationConfiguration, LocationConfigurationRequest, LocationDetailsRequest, LocationDetails } from '../models/location-configuration.model';
import { RedisService } from './redis.service';
import { Locale } from '../models/i18n.model';
import { DomainEventsService } from './domain-events.service';

@Injectable()
export class LocationService extends CallPotentialHttpApiService {
    constructor(
        protected override httpService: HttpService,
        protected override configService: ConfigService,
        protected override domainEventService: DomainEventsService,
        public redisService: RedisService,
    ) {
        super(httpService, configService, domainEventService, 'API_LOC_URL');
    }

    @Cacheable(5 * 60)
    public async getLocationConfiguration(locationId: LocationId, request: LocationConfigurationRequest = {}): Promise<LocationConfiguration> {
        const endpoint = `/locationconfiguration/${locationId}`;
        return await this.get(endpoint, request) as LocationConfiguration;
    }

    @Cacheable(5 * 60)
    public async getLocationDetails(locationId: LocationId, request: LocationDetailsRequest = {}): Promise<LocationDetails> {
        const endpoint = `/location/${locationId}`;
        const response = await this.get(endpoint, request) as LocationDetails;
        
        response.locales = [Locale.English];
        if(response.language) {
            const languageMap = new Map<string, Locale[]>([
                ['English', [Locale.English]],
                ['French - English', [Locale.French, Locale.English]],
                ['English - French', [Locale.English, Locale.French]],
                ['Spanish - English', [Locale.Spanish, Locale.English]],
                ['English - Spanish', [Locale.English, Locale.Spanish]],
            ]);
            response.locales = languageMap.get(response.language) ?? [Locale.English];
        }
        return response;
    }
}
