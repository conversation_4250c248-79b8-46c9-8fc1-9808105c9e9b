<?php
/**
 * CallRouteConfig model
 *
 * @category CallRouteConfig
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * CallRouteConfig model
 *
 * @category CallRouteConfig
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="CallRouteConfig")
 */

class CallRouteConfig extends \Phalcon\Di\Injectable
{
    use CallPotential\CPCommon\JsonModelTrait;
    use CallPotential\CPCommon\LoggerTrait;
    use CallPotential\CPCommon\Models\ElasticSearchModel;
    use CallPotential\CPCommon\Models\S3DataTrait;

    /**
     * _id
     *
     * @var string
     *
     * @Primary
     * @Identity
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $_id;

    /**
     * User Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $user_id;

    /**
     * Config
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $config;

    /**
     * Name
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $name;

    /**
     * Cascade time
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $cascade_time;

    /**
     * Is active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_active;

    /**
     * Is default
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_default;

    /**
     * Workflow SID
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $workflow_sid;

    /**
     * Created date time
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $created_date;

    /**
     * Updated date time
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $updated_date;

    /**
     * Constructor to initialize data
     */
    public function __construct()
    {
        $this->index = 'call-data';
        $this->type = 'call-route-config';
    }

    /**
     * Clear model object values
     *
     * @return void
     */
    public function clear()
    {
        $this->config_id = null;
        $this->user_id = null;
        $this->id = null;
        $this->properties = null;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setSource("call_route_config");
        $this->fieldsToValidate = [
            [
                'name, config, cascade_time' => ['required' => true],
            ],
        ];

        $this->jsonExcludeFields = [
            'workflow_sid',
        ];
    }
}
