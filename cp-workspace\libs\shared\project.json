{"name": "shared", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared", "main": "libs/shared/src/index.ts", "tsConfig": "libs/shared/tsconfig.lib.json", "assets": ["libs/shared/*.md"]}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "libs/shared/jest.config.ts", "passWithNoTests": true, "codeCoverage": true, "testPathPattern": ["libs/shared/src/lib"], "coverageDirectory": "{workspaceRoot}/coverage/shared", "coverageReporters": ["lcov", "text"]}, "dependsOn": ["shared:build"]}}}