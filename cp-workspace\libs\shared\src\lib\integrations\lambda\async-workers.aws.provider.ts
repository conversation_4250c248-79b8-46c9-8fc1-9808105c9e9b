import { Injectable } from '@nestjs/common';
import { LambdaClient, InvokeCommand, InvocationRequest } from "@aws-sdk/client-lambda";

import { AsyncWorkerEvent, IAsyncWorkersProvider, IAsyncWorkersInvoker } from '../../models/async-workers.model';
import { AsyncWorkersIntegrationService } from './async-workers.integration.service';

@Injectable()
export class AsyncWorkersClientProvider
  implements IAsyncWorkersProvider
{
  getClient(): IAsyncWorkersInvoker {
    return new LambdaClientService().getClient();
  }
}

class LambdaClientService extends IAsync<PERSON>orkersProvider implements IAsyncWorkersInvoker {
  client!: LambdaClient;

  override getClient() {
    this.client = new LambdaClient();
    return this;
  }

  async invokeAsyncWorker(event: AsyncWorkerEvent): Promise<void> {
    try {
      const input = {
        FunctionName: AsyncWorkersIntegrationService.workerFunctionName,
        InvocationType: "Event",
        Payload: Buffer.from(JSON.stringify(event)),
      } as InvocationRequest;
      const command = new InvokeCommand(input);
      const response = await this.client.send(command);
      console.log('Lambda invocation response:', response);
    } catch (error) {
      console.error('Error invoking async worker lambda function:', error);
    }
  }
}
