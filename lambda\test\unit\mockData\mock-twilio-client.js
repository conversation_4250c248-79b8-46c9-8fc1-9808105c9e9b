
const twilioMockData = require('./twilio-mock-data.json');
const mapData = {
    EventType: 'task.created',
    EventDescription : "Task created",
    TaskAttributes: twilioMockData.task_data.task_attributes,
    TaskSid: twilioMockData.task_data.task_sid,
    TaskChannelUniqueName: twilioMockData.task_data.voice_task_channel,
    TaskQueueName: twilioMockData.task_data.task_queue_name,
    TaskAssignmentStatus: twilioMockData.task_data.task_assignment,
}

class twilioModule {
    constructor(accountSid, authtoken) {
        this.twilioAccount = {
            account_sid: accountSid,
            authtoken: authtoken
        };
    }

    sync = {
        v1 : {
            services : (sync_service_sid) => {
                return new servicesModule(sync_service_sid)
            }
        }
    }

    video = {
        v1 : {
            rooms : (room_sid) => {
                return new videoRoomModule(room_sid)
            }
        }
    }
}

class videoRoomModule {
    constructor(room_sid) {
        this.room_sid = room_sid;
    }
    update = (updateroomData) => {
        return Promise.resolve({updateroomData});
    }
    fetch = () => {
        return Promise.resolve(this.room_sid);
    }
}

class servicesModule {
    constructor(sync_service_sid) {
        this.sync_service_sid = sync_service_sid;
    }
    
    syncMaps =  (sync_map) => {
        return new syncMapsModule(sync_map)
    }
}

class syncMapsModule {
    constructor(map) {
        this.map = map;
    }
    
    syncMapItems =  (task_sid) => {
        return new syncMapItemsModule(task_sid)
    }
}

class syncMapItemsModule {
    constructor(taskSid) {
        this.taskSid = taskSid;
    }
    
    update =  (data) => {
        return  new syncMapUpdateModule(data)
    }

    remove =  () => {
        return  true
    }
}

class syncMapUpdateModule {
    constructor(mapData) {
        this.mapData = mapData;
    }
    then =  () => {
        return  {
            key: twilioMockData.task_data.task_sid,
            accountSid: twilioMockData.twilio_account.account_sid,
            serviceSid: twilioMockData.twilio_account.sync_service_sid,
            mapSid: twilioMockData.twilio_account.map_sid,
            url: `https://sync.twilio.com/v1/Services/${ twilioMockData.twilio_account.sync_service_sid}/Maps/${twilioMockData.twilio_account.sync_map_sid}/Items/${twilioMockData.task_data.task_sid}`,
            revision: '1',
            data: mapData,
            dateExpires: null,
            dateCreated: '2023-05-31T14:56:18.000Z',
            dateUpdated: '2023-05-31T14:56:18.000Z',
            createdBy: 'system'
        }
    }
    
    catch =  (error) => {

        return  {
            key: twilioMockData.task_data.task_sid,
            accountSid: twilioMockData.twilio_account.account_sid,
            serviceSid: twilioMockData.twilio_account.sync_service_sid,
            mapSid: 'MP9450f9e69eb8fcd9b344599c87a97724',
            url: `https://sync.twilio.com/v1/Services/${ twilioMockData.twilio_account.sync_service_sid}/Maps/${twilioMockData.twilio_account.sync_map_sid}/Items/${twilioMockData.task_data.task_sid}`,
            revision: '1',
            data: mapData,
            dateExpires: null,
            dateCreated: '2023-05-31T14:56:18.000Z',
            dateUpdated: '2023-05-31T14:56:18.000Z',
            createdBy: 'system'
        }
    }
}

const setup = (issetTwilio) => {
    jest.doMock('../../../libraries/common-methods', () => {
        const originalModule = jest.requireActual('../../../libraries/common-methods');

        return {
        __esModule: true,
        ...originalModule,
            getTwilioClient: jest.fn().mockImplementation( () => {
                if (issetTwilio) {
                    return new twilioModule(twilioMockData.twilio_account.account_sid,twilioMockData.twilio_account.authtoken)
                }
                return {};
            }),
        };
    });
}

module.exports = {
    setup: setup
};