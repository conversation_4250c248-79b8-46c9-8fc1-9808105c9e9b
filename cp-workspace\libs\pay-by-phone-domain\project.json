{"name": "pay-by-phone-domain", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/pay-by-phone-domain/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/pay-by-phone-domain", "tsConfig": "libs/pay-by-phone-domain/tsconfig.lib.json", "packageJson": "libs/pay-by-phone-domain/package.json", "main": "libs/pay-by-phone-domain/src/index.ts", "assets": ["libs/pay-by-phone-domain/*.md", {"glob": "**/*", "input": "libs/pay-by-phone-domain/src/i18n", "output": "src/i18n"}]}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "libs/pay-by-phone-domain/jest.config.ts", "passWithNoTests": true, "codeCoverage": true, "testPathPattern": ["libs/pay-by-phone-domain/src/lib"], "coverageDirectory": "{workspaceRoot}/coverage/pay-by-phone-domain", "coverageReporters": ["lcov", "text"]}, "dependsOn": ["pay-by-phone-domain:build"]}}}