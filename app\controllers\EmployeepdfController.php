<?php
/**
 * EmployeepdfController
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 3/30/17
 * Time: 4:38 PM
 *
 * @category EmployeepdfController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Dompdf\Dompdf;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\Controllers\SessionTrait;

/**
 * EmployeepdfController
 *
 * @category EmployeepdfController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class EmployeepdfController extends CallhistoryController
{
    use  SessionTrait;

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/employeepdf/{id}",
     *     description="Returns a call record based on a single ID",
     *     summary="get callhistory",
     *     operationId="EmployeePdf",
     *     produces={"application/pdf"},
     * @SWG\Parameter(
     *       description="Authorization token",
     *       type="string",
     *       name="Authorization",
     *       in="header",
     *       required=true
     *     ),
     * @SWG\Parameter(
     *       description="ID of callhistory to fetch",
     *       in="path",
     *       name="id",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Parameter(
     *        description="list type",
     *        type="string",
     *        name="listType",
     *        in="query",
     *        enum={"ElasticSearch"},
     *        required=true
     *     ),
     * @SWG\Response(
     *       response=200,
     *       description="Employee grade response",
     *     ),@SWG\Response(
     *       response="403",
     *       description="Not Authorized Invalid or missing Authorization header"
     *     ),@SWG\Response(
     *       response="404",
     *       description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="500",
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }
        $this->listType = $this->request->getQuery('listType');
        try {
            $id = $this->getParamID();
            if (!$id) {
                return $this->sendBadRequest();
            }
            $data = $this->getItem($id);

            if (!is_array($data) || empty($data)) {
                return $this->sendNotFound();
            }
            $result = $this->formatGetResponse($data);
            if ($this->userHasReadAccess($result)) {
            } else {
                return $this->sendForbidden();
            }
        } catch (\Exception $e) {
            return $this->sendErrorResponse($e);
        }

        $location = $this->getLocationName($result[0]['location_id']);
        $result[0]['location_name'] = $location;
        $getHtmlContent = $this->getHtmlContent($result[0]);

        $this->generatePdf($getHtmlContent);
    }


    /**
     * Generate pdf
     * @param stirng $html
     * @return mixed
    */
    private function generatePdf($html)
    {
        // instantiate and use the dompdf class
        $dompdf = new Dompdf();
        $dompdf->loadHtml($html);
        // Render the HTML as PDF
        $dompdf->render();

        $dompdf->stream('EmployeeGradesheet', array('Attachment' => 1, 'compress' => 1));
        exit(0);
    }

    /**
     * Generate Html
     *
     * @param array $data
     *
     * @return string
     *
    */
    private function getHtmlContent($data)
    {
        $view = new \Phalcon\Mvc\View();
        $view->setViewsDir(APP_PATH . '/views/');
        $view->setVars($data);
        $view->start();
        $view->render('gradesheet', 'employee');
        $view->finish();
        $html = $view->getContent();

        return $html;
    }

    /**
     * Get Locations Details
     *
     * @param string $locationId
     *
     * @return string
    */
    private function getLocationName($locationId)
    {
        $location_name  = "";
        if ($this->isStaticToken($this->authToken)) {
            $locClient = ClientFactory::getLocClient($this->getRequestAuthToken());
            $location = $locClient->getLocationDetails($locationId);
            if (isset($location) && !empty($location)) {
                $location_name = $location->location_name;
            }
        } else {
            $sessionUserData = unserialize($this->currentSession->user_data);
            if (!empty($sessionUserData['locations'])) {
                if (isset($sessionUserData['locations'][$locationId])) {
                    $location_name = $sessionUserData['locations'][$locationId];
                }
            }
        }

        return $location_name;
    }
}
