import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { Customer } from "@cp-workspace/shared";

@Injectable()
export class UnitsToPaySelection extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse, request } = context;
    const customerResponse = request.Digits;
    const noResponseFromCustomer = !customerResponse || customerResponse.length === 0;
    const startOverResponse = customerResponse && customerResponse === '9';
    const shouldStartOver = noResponseFromCustomer || startOverResponse;

    if (shouldStartOver) {
      return { nextState: PayByPhoneState.UnitsToPayPrompt };
    }

    storage.payingAllUnits = false;

    const payAllUnitResponse = customerResponse && customerResponse === '1';
    if (payAllUnitResponse) {
      storage.payingAllUnits = true;
      storage.selectedUnits = Customer.getDelinquentUnits(storage.matchedTenants!);
      storage.isDelinquentPaymentFlow = storage.selectedUnits.length > 0;
      if(!storage.isDelinquentPaymentFlow) {
        storage.selectedUnits = Customer.getCurrentUnits(storage.matchedTenants!);
      }
      storage.totalBalance = Customer.calculateTotalBalance(storage.matchedTenants!);
      twilioResponse.sayTotalBalance(storage);

      return { nextState: PayByPhoneState.ConfirmCustomerInfo };
    }

    const unitSelectionIndex = parseInt(customerResponse) - 2; //-2 to account for 1-based index and pay-all option
    const unitAndTenantByIndex = Customer.getUnitAndTenantByIndex(storage.matchedTenants!, unitSelectionIndex);
    if(!unitAndTenantByIndex) {
      return { nextState: PayByPhoneState.UnitsToPayPrompt };
    }

    const { selectedUnit, selectedTenant } = unitAndTenantByIndex;
    if (!selectedUnit || !selectedTenant) {
      return { nextState: PayByPhoneState.UnitsToPayPrompt };
    }

    storage.selectedUnits = [ selectedUnit ];
    storage.selectedUnit = selectedUnit;
    storage.selectedTenant = selectedTenant;
    storage.isDelinquentPaymentFlow = Customer.isDelinquentUnit(selectedUnit);

    twilioResponse.sayAccountFoundDetails(storage.selectedUnit, storage.selectedTenant, storage);

    return { nextState: PayByPhoneState.ConfirmCustomerInfo };
  }
}
