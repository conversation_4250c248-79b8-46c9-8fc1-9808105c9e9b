var PORT = 3001;

module.exports = {
  'port': PORT,
  'site_url': 'https://qa-384.callpotential.com/', // endpoint
  'call_url': 'https://call-api.callpotential.com/qa-384/v1/',
  'env_url': '/qa-384/v1', // current environment
  'db': {
    client: 'mysql',
    connection: {
      host: 'rds-qa.callpotential.com',
      user: 'qa',
      password: 'CallPotential1',
      database: 'qa_384',
      charset: 'utf8',
    },
    serviceTokens: {
      readOnly: 'VYZQ3XScyB9LDryvjqxDi6nF3Hw349JB3QsTN1hatxorEu1VMqq9QUEFSrTVyGS6',
      readWrite: 'ye1w2iqF3BXihixGzw6efFF3W9edRmoKbwMqOhfBuNDLzsyWz6vxiPgyzKhMjRDr',
    },
    pool: {
      min: 0,
      max: 1,
      requestTimeout: 250000,
    },
    acquireConnectionTimeout: 300000, // 5 minute
    debug: true,
  },
  db_ro: { // read only
    client: 'mysql',
    connection: {
      host: 'rds-qa.callpotential.com',
      user: 'qa',
      password: 'CallPotential1',
      database: 'qa_384',
      charset: 'utf8',
    },
    serviceTokens: {
      readOnly: 'VYZQ3XScyB9LDryvjqxDi6nF3Hw349JB3QsTN1hatxorEu1VMqq9QUEFSrTVyGS6',
      readWrite: 'ye1w2iqF3BXihixGzw6efFF3W9edRmoKbwMqOhfBuNDLzsyWz6vxiPgyzKhMjRDr',
    },
    pool: {
      min: 0,
      max: 1,
      requestTimeout: 250000,
    },
    acquireConnectionTimeout: 300000, // 5 minute
    debug: false,
  },
  env: '__ENV__',
  dynamodb: {
    acctTable: 'dev-twilio_accounts',
    callTable: '__APPNAME__-call_logs',
  },
  twilio: {
    statDuration: '1440',
    agent_status_map: 'agent_status',
    task_list_map: 'task_list',
    twiml_app_sid: 'AP7665881a11172df848332a8f6a4e86a7',
    stats_document: 'workspace_stats',
    agent_stats_map: 'agent_stats',
  },
  redis: {
    host: 'cp.7xwsax.ng.0001.usw2.cache.amazonaws.com',
    port: '6379',
    db: 0,
  },
  memcache: 'callcache.7xwsax.cfg.usw2.cache.amazonaws.com:11211',
  locations: {
    SL: '1', // SiteLink
    CP: '0', // CallPotential
    WSS: '2', // Web Self Storage
    CS: '3', // CenterShift
    QS: '4', // QuikStor
    DI: '5', // Domico
    DS: '6', // DoorStep
    SC: '7', // Space control universal
    ES: '8', // Extra Space
  },
  'CP_CDN_URL': 'https://cp-cdn.s3.amazonaws.com/',
  'TZ': 'America/Chicago',
  'API_CORE_URL': 'https://qa-384-core.callpotential.com',
  'API_LOC_URL': 'https://qa-384-loc.callpotential.com',
  'API_ACCT_URL': 'https://qa-384-acct.callpotential.com',
  'API_INT_URL': 'https://qa-384-int.callpotential.com',
  'ES_URL': 'https://e7cf70a9d4c5621274ba:<EMAIL>:30780',
  'API_CALL_URL': 'https://qa-384-call.callpotential.com'
};
