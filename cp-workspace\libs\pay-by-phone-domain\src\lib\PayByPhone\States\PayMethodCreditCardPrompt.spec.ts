import { PayMethodCreditCardPrompt } from './PayMethodCreditCardPrompt';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodCreditCardPrompt', () => {
  let service: PayMethodCreditCardPrompt;

  beforeEach(() => {
    service = new PayMethodCreditCardPrompt();
  });

  it('should prompt for credit card input and transition to PayMethodCreditCardValidate', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234567812345678',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      finishOnKey: "*",
      timeout: 10
    }, [{
      messageId: 'pay-by-phone.enter-cc',
      locale: context.storage.locale
    }]);
    expect(result.nextState).toBe(PayByPhoneState.PayMethodCreditCardValidate);
  });
});
