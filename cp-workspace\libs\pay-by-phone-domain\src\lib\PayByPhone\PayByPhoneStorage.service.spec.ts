import { PayByPhoneStorageService } from './PayByPhoneStorage.service';
import { PayByPhoneStorage } from './PayByPhone.model';
import { PayByPhoneState } from './Generated/PayByPhoneState.generated';
import { RedisService } from '@cp-workspace/shared';
import { PayByPhoneTest } from './PayByPhoneTest.module';

describe('PayByPhoneStorageService', () => {
  let service: PayByPhoneStorageService;
  let redisService: RedisService;

  beforeEach(async () => {
    process.env['BUGSNAG_API_KEY'] = '616b106fc4dba0412968d8c0e91995be';
    const module = await PayByPhoneTest.createTestingModule();
    service = module.get<PayByPhoneStorageService>(PayByPhoneStorageService);
    redisService = module.get<RedisService>(RedisService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getStorage', () => {
    it('should return the storage for a given callSid', async () => {
      const callSid = 'testCallSid';
      const storage = {
        state: PayByPhoneState.LocalePrompt,
        locationId: 456,
        locale: 'en-US',
        transferToAgentUrl: '/transfer-to-agent',
        convenienceFee: 0.25
      } as PayByPhoneStorage;

      /**
       * Call the initialize method to initialize the Redis client
       * with some data.
       */
      redisService.initialize({
        data: { [callSid]: storage }
      });
      
      const redisGetSpy = jest.spyOn(redisService, 'get').mockResolvedValue(JSON.stringify(storage));

      const result = await service.getStorage(callSid);

      expect(redisGetSpy).toHaveBeenCalledWith(callSid);
      expect(result).toEqual(storage);
    }, 1000 * 60 * 10);

    it('should return undefined if storage is not found for a given callSid', async () => {
      const callSid = 'testCallSid';
      const redisGetSpy = jest.spyOn(redisService, 'get').mockResolvedValue(null);
      redisService.initialize();

      await expect(service.getStorage(callSid)).resolves.toBeUndefined();
      expect(redisGetSpy).toHaveBeenCalledWith(callSid);
    });
  });

  describe('saveStorage', () => {
    it('should save the storage for a given callSid', async () => {
      const callSid = 'testCallSid';
      const storage = {
        state: PayByPhoneState.LocalePrompt,
        locationId: 456,
        locale: 'en-US',
        transferToAgentUrl: '/transfer-to-agent',
        convenienceFee: 0.25
      } as PayByPhoneStorage;
      redisService.initialize();
      const redisSetSpy = jest.spyOn(redisService, 'set').mockResolvedValue(undefined as any);

      await service.saveStorage(callSid, storage);

      expect(redisSetSpy).toHaveBeenCalledWith(callSid, JSON.stringify(storage), 'EX', service['expirationTime']);
    });
  });

  describe('deleteStorage', () => {
    it('should delete the storage for a given callSid', async () => {
      const callSid = 'testCallSid';
      const redisDelSpy = jest.spyOn(redisService, 'del').mockResolvedValue(undefined as any);
      redisService.initialize();
      await service.deleteStorage(callSid);

      expect(redisDelSpy).toHaveBeenCalledWith(callSid);
    });
  });
});