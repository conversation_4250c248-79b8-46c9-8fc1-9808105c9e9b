const cpapiClient = require('../../libraries/cpapi-client');
const config = require('../config');

class FeatureToggles {
    static async isFeatureEnabled(accountId, featureName) {
        const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
        const featureToggle = await coreClient.cache.getData(`featuretoggle?filterAccount_id=${accountId}&filterFeature_name=${featureName}`).catch(error => console.log(error, new Error().stack));
        return featureToggle?.items?.[0]?.feature_value === 1;
    }
}

module.exports = FeatureToggles;