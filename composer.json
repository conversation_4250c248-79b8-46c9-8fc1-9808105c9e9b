{"name": "callpotential/cp-call", "repositories": [{"type": "vcs", "url": "https://github.com/callpotential/cpapi-integration.git", "use-api": false}, {"type": "vcs", "url": "https://github.com/callpotential/cpapi-common.git", "use-api": false}], "autoload": {"psr-4": {"App\\Services\\": "app/services"}}, "require": {"whoops/phalcon": "^2.0", "twilio/sdk": "^5.4", "aws/aws-sdk-php": "^3.19", "zircote/swagger-php": "^2.0", "callpotential/cpcommon": "^4.0", "callpotential/test_common": "dev-master", "elasticsearch/elasticsearch": "~6.0", "ongr/elasticsearch-dsl": "~6.0", "dompdf/dompdf": "^2.0", "bugsnag/bugsnag": "^3.0"}, "require-dev": {"phpunit/phpunit": "^9.6", "karen-nalbandian/phalcon-coding-standard": "dev-master", "codeception/module-phalcon4": "^1.0", "phalcon/incubator-test": "^v1.0.0-alpha.1", "mockery/mockery": "^1.0"}, "scripts": {"post-update-cmd": "vendor/bin/phpcs --config-set installed_paths vendor/karen-nalbandian/phalcon-coding-standard,vendor/slevomat/coding-standard"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"process-timeout": 0, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}