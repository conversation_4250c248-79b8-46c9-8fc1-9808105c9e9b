import { PayMethodPostalCodeValidate } from './PayMethodPostalCodeValidate';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { PaymentToken } from '@cp-workspace/shared';


describe('PayMethodPostalCodeValidate', () => {
  let service: PayMethodPostalCodeValidate;

  beforeEach(() => {
    service = new PayMethodPostalCodeValidate();
    service.services = {
      coreService: {
        encodePaymentToken: jest
          .fn()
          .mockImplementation((token) => Promise.resolve(token)),
        decodePaymentToken: jest
          .fn()
          .mockImplementation((token) => Promise.resolve(token)),
      }
    } as any;
  });

  it('should return nextState as PayMethodPostalCodePrompt when postal code is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en', paymentToken: {} } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodPostalCodePrompt);
  });

  it('should return nextState as PayMethodPostalCodeConfirm when postal code is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '90210',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en', paymentToken: {} } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodPostalCodeConfirm);
  });

  it('should throw an error when paymentToken is not found in storage', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '90210',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    await expect(service.handler(context)).rejects.toThrow();
  });

  it('should update paymentToken with postal code when postal code is valid', async () => {
    const paymentToken: PaymentToken = {
      cardNumber: '****************',
      securityCode: '123',
      expiration: '1122',
      postalCode: '',
    };

    const context: PayByPhoneStateContext = {
      request: {
        Digits: '90210',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: {
        locale: 'en',
        paymentToken,
      } as any,
    } as any;

    const requestToken: PaymentToken = {
      postalCode: context.request.Digits,
    };

    // Mock encodePaymentToken to return a promise that resolves to an object with the updated postalCode
    (service.services.coreService.encodePaymentToken as jest.Mock).mockResolvedValue({
      ...paymentToken,
      postalCode: requestToken.postalCode,
    });

    const result = await service.handler(context);

    expect(
      service.services.coreService.encodePaymentToken
    ).toHaveBeenCalledWith(requestToken);
    expect(result.nextState).toBe(PayByPhoneState.PayMethodPostalCodeConfirm);
    expect(paymentToken.postalCode).toBe(context.request.Digits);
  });
});
