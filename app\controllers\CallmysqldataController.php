<?php
/**
 * Retrieve call log MySql record to store to ES/S3
 *
 * @category CallController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use \CallPotential\CPCommon\Controllers\BaseController;
use \CallPotential\CPCommon\Controllers\SessionTrait;
use \CallPotential\CPCommon\Util;
use Phalcon\Db\Enum;

/**
 * Swagger content
 *
 * @SWG\Definition(definition="CallMysqlDataRequest", type="object",
 * @SWG\Property(property="twilio_id",                type="string"),
 * @SWG\Property(property="log_id",                   type="integer")
 * )
 */

/**
 * Retrieve call log MySql record to store to ES/S3
 *
 * @category CallmysqldataController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class CallmysqldataController extends BaseController
{
    use SessionTrait;

    /**
     * Returns model name
     *
     * @return string
     */
    public function getModelName() : string
    {
        return "\\CallHistory"; //model
    }

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param null  $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return bool
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($data, $parentId);

        return false;
    }

    /**
     * Intermediate function to prepare data for update action
     *
     * @param mixed $id       primary key value of the record
     * @param array $data     array of POST body / query params in key value pair
     * @param null  $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return bool
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($id, $data, $parentId);

        return false;
    }

    /**
     * Intermediate function to prepare data for list action response
     *
     * @return mixed
     */
    public function getListContent()
    {
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return void
     */
    public function doBulkCreate()
    {
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return void
     */
    public function doBulkSave() : array
    {
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        unset($id);
    }

    /**
     * Intermediate function to delete data for delete action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function deleteItem($id)
    {
        unset($id);
    }

    /**
     * Method Http accept: GET
     *
     * @return \Phalcon\Http\Response
     */
    public function getAction(): \Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Method Http accept: GET
     *
     * @return \Phalcon\Http\Response
     */
    public function listAction()
    {
        return $this->sendNotImplemented();
    }

    /**
     * Method Http accept: PUT (update)
     *
     * @return \Phalcon\Http\Response
     */
    public function saveAction(): \Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/callmysqldata",
     *     tags={"Call Data"},
     *     summary="Create a call mysql data record for ES",
     *     description="create new call history record",
     *     summary="create mysql call history record",
     *     operationId="CreateCallMySQLData",
     *     consumes={"application/json"},
     *     produces={"application/json"},
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Ad record",
     *     required=false, @SWG\Schema(ref="#/definitions/CallMysqlDataRequest")
     * ),
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     * ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation", @SWG\Schema(ref="#/definitions/CallHistory")
     * ),
     * @SWG\Response(
     *     response="400",
     *     description="Invalid data supplied", @SWG\Schema(ref="#/definitions/ErrorResponse")
     * ),
     * @SWG\Response(
     *     response=500,
     *     description="unexpected error", @SWG\Schema(ref="#/definitions/ErrorResponse")
     * ),
     * @SWG\Response(
     *     response="403", @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return \Phalcon\Http\Response
     */
    public function createAction():\Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }

            $this->infoMessage(
                'create request begin',
                'CallMysqlDataController::createAction:' . __LINE__
            );
            $data = Util::objectToArray($this->request->getJsonRawBody());

            if (!$this->isValidRequest($data)) {
                return $this->sendBadRequest('Invalid Request Body');
            }
            $CallArchive = CallArchive::getInstance();
            $result = $CallArchive->buildCallRecord(
                Util::array_get('log_id', $data, 0),
                Util::array_get('twilio_id', $data, '')
            );
            if (count($result) === 0) {
                return $this->sendNotFound();
            }
            $this->infoMessage(
                'create request end',
                'CallMysqlDataController::createAction:' . __LINE__
            );

            return $this->sendSuccessResponse($result);
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Determines if user has read access to data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Determines if user has write access to data record
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Determines if user has delete access to data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);

        return false;
    }

    /**
     * Check call log record contains twilio id or log_id field
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return boolean
     */
    private function isValidRequest($data) : bool
    {
        if (is_array($data) &&
        (
            array_key_exists('twilio_id', $data) ||
            array_key_exists('log_id', $data))
        ) {
            return true;
        }

        return false;
    }
}
