import { Modu<PERSON> } from '@nestjs/common';
import { IoRedisMockClientProvider } from './redis-ioredis-mock.client';
import { IoRedisClientProvider } from './redis-ioredis.client';
import { IoRedisProvider } from './redis-ioredis.provider';
import { RedisOptions as InMemoryRedisModuleOptions } from "ioredis-mock";
import { RedisModule, RedisModuleOptions } from '@nestjs-modules/ioredis';

export enum IoRedisClientType {
  EXTERNAL = 'EXTERNAL',
  IN_MEMORY = 'IN_MEMORY',
}

export interface IoRedisModuleOptions {
  clientType: IoRedisClientType;
  options?: InMemoryRedisModuleOptions | RedisModuleOptions;
}

@Module({})
export class IoRedisModule {
  static forRoot(options: IoRedisModuleOptions) {
    if (options.clientType === IoRedisClientType.IN_MEMORY) {
      return {
        module: IoRedisModule,
        providers: [
          IoRedisProvider,
          IoRedisMockClientProvider,
          {
            provide: 'IoRedisProvider',
            useClass: IoRedisMockClientProvider,
          },
        ],
        exports: [
          IoRedisProvider,
          IoRedisMockClientProvider,
          'IoRedisProvider',
        ],
      };
    }
    if (options.clientType === IoRedisClientType.EXTERNAL) {
      return {
        module: IoRedisModule,
        imports: [
          RedisModule.forRoot(options.options as RedisModuleOptions),
        ],
        providers: [
          IoRedisProvider,
          IoRedisClientProvider,
          {
            provide: 'IoRedisProvider',
            useClass: IoRedisClientProvider,
          },
        ],
        exports: [
          IoRedisProvider,
          IoRedisClientProvider,
          'IoRedisProvider',
        ],
      };
    }
    return {
      module: IoRedisModule,
      providers: [
        IoRedisProvider,
        IoRedisMockClientProvider,
        IoRedisClientProvider,
        {
          provide: 'IoRedisProvider',
          useClass: IoRedisProvider,
        },
      ],
      exports: [
        IoRedisProvider,
        IoRedisMockClientProvider,
        IoRedisClientProvider,
        'IoRedisProvider',
      ],
    };
  }
}