import { Injectable } from '@nestjs/common';
import {
  DomainEvent,
  IPublishDomainEvents,
  IPublishDomainEventsProvider,
} from '../../models/domain-event.model';
import { EventBridgeClient, PutEventsCommand } from '@aws-sdk/client-eventbridge';

@Injectable()
export class DomainEventsClient<PERSON>rovider
  implements IPublishDomainEventsProvider
{
  getClient(): IPublishDomainEvents {
    return new EventBridgeClientService().getClient();
  }
}

class EventBridgeClientService implements IPublishDomainEvents {
  client!: EventBridgeClient;

  getClient() {
    this.client = new EventBridgeClient();
    return this;
  }

  async publish(event: DomainEvent): Promise<DomainEvent> {
    try {
      await this.client.send(new PutEventsCommand({
        Entries: [
          {
            Source: event.source,
            DetailType: event.detailType,
            Detail: JSON.stringify(event.detail),
            EventBusName: 'default',
          },
        ],
      }));
    } catch (error) {
      console.error('Error sending domain event:', error);
    }
    return event;
  }
}
