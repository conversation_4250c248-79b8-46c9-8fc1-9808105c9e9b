<?php
/**
 * JobController
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 12/17/17
 * Time: 10:21 PM
 *
 * @category JobController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use \CallPotential\CPCommon\Controllers\BaseController;
use \CallPotential\CPCommon\Controllers\SessionTrait;
use CallPotential\CPCommon\Util;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="InvokeAsyncJob",
 * @SWG\Property(property="task",type="string"),
 * @SWG\Property(property="action",type="string"),
 * @SWG\Property(property="description",type="string"),
 * @SWG\Property(property="callback_url",type="string"),
 * @SWG\Property(property="timeout",type="integer"),
 * @SWG\Property(property="flags",type="array",@SWG\Items(type="string")),
 * @SWG\Property(property="params",type="array",@SWG\Items(type="string")),
 * @SWG\Property(property="args",type="array",@SWG\Items(ref="#/definitions/KeyValuePair"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="AsyncJob",
 * @SWG\Property(property="service",type="string",
 * description="name of service creating task"),
 * @SWG\Property(property="cmd",type="string",
 * description="cli command line or lambda function"),
 * @SWG\Property(property="description",type="string"),
 * @SWG\Property(property="callback_url",type="string",
 * description="url to call when status changes happen"),
 * @SWG\Property(property="timeout_seconds",type="integer",
 * description="amount of time to keep task in database"),
 * @SWG\Property(property="progress",type="integer",description=""),
 * @SWG\Property(property="created",type="string",description=""),
 * @SWG\Property(property="updated",type="string",description=""),
 * @SWG\Property(property="status",type="integer",description="")
 * )
 */

/**
 * JobController
 *
 * @category JobController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class JobController extends BaseController
{
    use SessionTrait;

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    public function getModelName(): string
    {
        return ""; //model
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        unset($id);
    }

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($parentId);
        $job = \CallPotential\CPCommon\Cli\CliTaskRunner::executeAsync(
            $data['task'],
            $data['action'],
            $data['description'],
            Util::array_get('callback_url', $data, ''),
            Util::array_get('timeout', $data, 0),
            Util::array_get('flags', $data, []),
            Util::array_get('args', $data, []),
            Util::array_get('params', $data, [])
        );

        return $job;
    }

    /**
     * Intermediate function to prepare data for update action
     *
     * @param mixed $id       primary key value of the record
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($id, $data, $parentId);

        return [];
    }

    /**
     * Intermediate function to delete data for delete action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function deleteItem($id)
    {
        unset($id);

        return false;
    }

    /**
     * GetListContent
     *
     * @return mixed
     */
    public function getListContent()
    {
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    public function doBulkCreate()
    {
    }
    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    public function doBulkSave(): array
    {
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        return $this->sendNotImplemented();
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/job",
     *   tags={"System"},
     *   summary="Execute an async task",
     *   description="Execute an async task",
     *   summary="Execute an async task",
     *   operationId="CreateJob",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="New Job record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/InvokeAsyncJob")
     *   ),@SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/AsyncJob")
     *   ),@SWG\Response(
     *     response="400",
     *     description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response=500,
     *     description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }

            $this->infoMessage(
                'create request begin',
                'JobController::createAction:' . __LINE__
            );
            $result = parent::createAction();
            $this->infoMessage('create request end', 'JobController::createAction:' . __LINE__);

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }

        return false;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }

        return false;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }

        return false;
    }

    /**
     * This is called from controller file for validation purpose
     *
     * @return array
     */
    protected function requiredRequestParams(): array
    {
        return array();
    }
}
