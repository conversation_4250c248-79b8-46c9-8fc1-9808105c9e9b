import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { Customer } from "@cp-workspace/shared";

@Injectable()
export class UnitsToPayPrepayMonthsConfirm extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { request } = context;
    const customerResponse = request.Digits;
    const noResponseFromCustomer = !customerResponse || customerResponse.length === 0;
    const processPaymentResponse = customerResponse && customerResponse === '1';
    const changeSelectionResponse = customerResponse && customerResponse === '2';
    const shouldChangeSelection = noResponseFromCustomer || changeSelectionResponse || !processPaymentResponse;
    if(shouldChangeSelection) {
      return { nextState: PayByPhoneState.UnitsToPayPrepayMonthsPrompt };
    }

    return { nextState: PayByPhoneState.GetSavedCards };
  }
}
