import { DomainEvent, DomainEventsService } from '@cp-workspace/shared';
import { Controller, Post, Req, BadRequestException } from '@nestjs/common';
import { Request } from 'express';

@Controller('log')
export class EventLoggerController {
  constructor(public readonly logger: DomainEventsService) {}

  @Post()
  async handleEventLoggerRequest(@Req() request: Request) {
    if (this.logger.isEnabled) {
      if (!request.body) {
        throw new BadRequestException('Bad Request');
      }
      if (!this.logger.isDomainEvent(request.body)) {
        throw new BadRequestException('Bad Request');
      }
      await this.logger.publish(request.body as DomainEvent, true);
    }
  }
}
