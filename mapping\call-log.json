{"settings": {"index": {"number_of_shards": 3, "number_of_replicas": 2, "max_result_window": "15000"}, "analysis": {"filter": {"autocomplete_filter": {"type": "edge_ngram", "min_gram": 1, "max_gram": 50}}, "tokenizer": {"phone_tokenizer": {"type": "edge_ngram", "min_gram": 1, "max_gram": 15}}, "char_filter": {"phone_char_filter": {"type": "pattern_replace", "pattern": "[^\\d]"}}, "analyzer": {"autocomplete": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "autocomplete_filter"]}, "autocomplete_email": {"type": "custom", "tokenizer": "uax_url_email", "filter": ["lowercase", "autocomplete_filter"]}, "autocomplete_phone": {"type": "custom", "tokenizer": "phone_tokenizer", "char_filter": "phone_char_filter"}, "search_email": {"type": "custom", "tokenizer": "uax_url_email", "filter": ["lowercase"]}}, "string_filter": {"quote": {"type": "mapping", "mappings": ["« => \"", "» => \""]}}, "normalizer": {"lowercase_normalizer": {"type": "custom", "string_filter": ["quote"], "filter": ["lowercase", "asciifolding"]}}}}, "mappings": {"call-history": {"_source": {"enabled": true}, "properties": {"_lastupdate": {"type": "long"}, "log_id": {"type": "long"}, "lead_id": {"type": "long"}, "location_id": {"type": "long"}, "account_id": {"type": "long"}, "twilio_id": {"type": "text", "analyzer": "standard"}, "recording_sid": {"type": "text", "analyzer": "standard"}, "call_number": {"type": "text", "analyzer": "standard"}, "caller_name": {"type": "text", "analyzer": "standard"}, "call_type": {"type": "text", "analyzer": "standard", "fielddata": true}, "datestamp": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "call_processed_by": {"type": "text", "analyzer": "standard"}, "recording_url": {"type": "keyword"}, "recording_duration": {"type": "long"}, "call_duration": {"type": "long"}, "call_destination": {"type": "text", "analyzer": "standard"}, "answered_by": {"type": "long"}, "ad_id": {"type": "long"}, "employee_id": {"type": "long"}, "grade": {"type": "text", "analyzer": "standard"}, "halloffame": {"type": "integer"}, "gradesheet": {"type": "text", "analyzer": "standard"}, "gradesheet_id": {"type": "long"}, "customer_card": {"type": "text", "analyzer": "standard"}, "gradesheet_points_appointed": {"type": "integer"}, "gradesheet_points_possible": {"type": "integer"}, "manager_score": {"type": "integer"}, "confirm_action": {"type": "text", "analyzer": "standard"}, "confirmed_by": {"type": "long"}, "is_excluded": {"type": "integer"}, "customer_type": {"type": "text", "analyzer": "standard"}, "customer_name": {"type": "text", "analyzer": "standard"}, "customer_id": {"type": "text"}, "is_auto_call": {"type": "integer"}, "neighbor_location_id": {"type": "long"}, "channel": {"type": "text", "analyzer": "standard"}, "lead": {"type": "nested", "properties": {"inquiry_type": {"type": "integer"}, "qt_rental_type": {"type": "integer"}, "first_name": {"type": "text", "analyzer": "autocomplete", "fields": {"raw": {"type": "text", "analyzer": "standard"}}}, "last_name": {"type": "text", "analyzer": "autocomplete", "fields": {"raw": {"type": "text", "analyzer": "standard"}}}, "phone": {"type": "text", "fielddata": true, "analyzer": "autocomplete_phone", "fields": {"raw": {"type": "text", "analyzer": "standard"}}}, "email": {"type": "text", "analyzer": "autocomplete_email", "fields": {"raw": {"type": "text", "analyzer": "search_email"}}}}}, "tenant": {"type": "nested", "properties": {"inquiry_type": {"type": "integer"}, "qt_rental_type": {"type": "integer"}, "first_name": {"type": "text", "analyzer": "autocomplete", "fields": {"raw": {"type": "text", "analyzer": "standard"}}}, "last_name": {"type": "text", "analyzer": "autocomplete", "fields": {"raw": {"type": "text", "analyzer": "standard"}}}, "phone": {"type": "text", "fielddata": true, "analyzer": "autocomplete_phone", "fields": {"raw": {"type": "text", "analyzer": "standard"}}}, "email": {"type": "text", "analyzer": "autocomplete_email", "fields": {"raw": {"type": "text", "analyzer": "search_email"}}}}}, "queue": {"type": "nested", "properties": {"agent_id": {"type": "integer"}, "queue_id": {"type": "integer"}, "wait_time": {"type": "integer"}, "created_date": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "accepted_date": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "agent_call_duration": {"type": "integer"}, "is_timed_out": {"type": "integer"}}}, "tasks": {"type": "nested", "properties": {"agent_id": {"type": "long"}, "config_step_uid": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 50}}}, "date_created": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "date_modified": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd"}, "event_id": {"type": "long"}, "is_abandoned": {"type": "long"}, "is_rolled_over": {"type": "long"}, "is_task": {"type": "long"}, "is_task_complete": {"type": "long"}, "outcome": {"type": "long"}, "queue_id": {"type": "long"}, "queue_time": {"type": "long"}, "reservation_accepted": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 50}}}, "reservation_created": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 50}}}, "reservation_rejected": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 50}}}, "reservation_timeout": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 50}}}, "retry_attempt": {"type": "long"}, "task_sid": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 50}}}}}}}}}