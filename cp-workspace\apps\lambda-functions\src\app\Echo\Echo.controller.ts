import { <PERSON>, Post, Req, BadRequestException, UseInterceptors } from '@nestjs/common';
import { Request } from 'express';
import { PayByPhoneRequestInterceptor } from '../Interceptors/PayByPhoneRequestInterceptor';

@Controller('echo')
@UseInterceptors(PayByPhoneRequestInterceptor)
export class EchoController {
  @Post()
  async handleEchoRequest(@Req() request: Request) {
    if (!request.body) {
      throw new BadRequestException('Bad Request');
    }
    return request.body;
  }
}
