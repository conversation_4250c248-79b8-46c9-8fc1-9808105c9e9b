<?php
/**
 * Library to manage call data
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 10/10/17
 * Time: 3:10 PM
 *
 * @category CallArchive
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\Libraries\EsSync;
use Ph<PERSON>con\Db\Enum;

/**
 * Library to manage call data
 *
 * @category CallArchive
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class CallArchive extends EsSync
{
    /**
     * Location List
     *
     * @var array
     */
    private $locAccount = [];

    /**
     * User names
     *
     * @var array
     */
    private $users = [];

    /**
     * Ad names
     *
     * @var array
     */
    private $ads = [];

    /**
     * Fields to remove from call records after formatting
     *
     * @var array
     */
    private $deletedFields = [
        'rollover_index',
        'current_route_step',
        'is_route_complete',
        'is_assigned',
        'read_status',
        'imported',
        'user_id',
        'route_config',
        'lead_inquiry_type',
        'lead_qt_rental_type',
        'lead_first_name',
        'lead_last_name',
        'lead_phone',
        'lead_email',
        'queue',
        'queue_id',
        'agent_id',
        'queue_wait_time',
        'call_queue_created_date',
        'agent_call_duration',
        'is_timed_out',
        'queue_calls_accepted_date',
        'fk_lead_id',
        'duration',
    ];

    /**
     * Replica Database
     *
     * @var Phalcon\Db\Adapter\Pdo\Mysql
     */
    private $replica;

    /**
     * CallArchive instance
     *
     * @var CallArchive
     */
    private static $instance = null;

    /**
     * GetInstance
     *
     * @return CallArchive
     */
    public static function getInstance()
    {
        if (!is_object(self::$instance)) {
            self::$instance = new CallArchive();
        }

        return self::$instance;
    }

    /**
     * Compile and format call log record for ES/S3 storage
     *
     * @param array $data Call log mysql data
     *
     * @return array
     */
    public function shapeRecord(array $data)
    {
        $data['ad_id'] = (int) Util::array_get('ad_id', $data, 0);
        $data['ad_name'] = $this->getAdName($data['ad_id']);
        $data['employee_id'] = (int) Util::array_get('employee_id', $data, 0);
        $data['employee_name'] = $this->getUserName($data['employee_id']);
        $data['datestamp'] = date('Y-m-d H:i:s', strtotime($data['datestamp']));
        if (array_key_exists('version_id', $data)) {
            $data['version_id'] = (int) $data['version_id'];
        }

        $data['answered_by'] = (int) Util::array_get('answered_by', $data, 0);
        $data['call_processed_by'] = (int) Util::array_get('call_processed_by', $data, 0);
        $data['confirmed_by'] = (int) Util::array_get('confirmed_by', $data, 0);
        $data['customer_id'] = Util::array_get('customer_id', $data, 0);
        $duration = (int) Util::array_get('duration', $data, 0);
        $recording_duration = (int) Util::array_get(
            'recording_duration',
            $data,
            Util::array_get('duration', $data, 0)
        );
        $data['recording_duration'] = ($recording_duration > 0) ? $recording_duration : $duration;
        $data['call_duration'] = (int) Util::array_get('call_duration', $data, 0);

        $data['gradesheet_id'] = (int) Util::array_get('gradesheet_id', $data, 0);
        $data['gradesheet_points_appointed'] = (int) Util::array_get(
            'gradesheet_points_appointed',
            $data,
            0
        );
        $data['gradesheet_points_possible'] = (int) Util::array_get(
            'gradesheet_points_possible',
            $data,
            0
        );
        $data['halloffame'] = (int) Util::array_get('halloffame', $data, 0);
        $data['is_auto_call'] = (int) Util::array_get('is_auto_call', $data, 0);
        $data['is_excluded'] = (int) Util::array_get('is_excluded', $data, 0);
        $data['customer_card'] = (string) Util::array_get('customer_card', $data, '');
        $data['call_type'] = (string) Util::array_get('call_type', $data, '');
        $data['_lastupdate'] = time();
        $data['channel'] = (string) Util::array_get('channel', $data, 'voice');

        $events = $this->getEventSummary($data['log_id']);
        $data = array_merge($data, $events);

        $data['location_id'] = (int) $data['location_id'];
        $data['log_id'] = (int) $data['log_id'];
        $data['manager_score'] = (int) Util::array_get('manager_score', $data, 0);
        $data['neighbor_location_id'] = (int) Util::array_get('neighbor_location_id', $data, 0);
        $data['account_id'] = $this->getRecordAccount(
            util::array_get('account_id', $data, 0),
            util::array_get('location_id', $data, 0)
        );

        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $config = $di->getShared('config');
        $intClient = new \CallPotential\CPCommon\IntClient(
            [],
            $config->application->staticToken->readWrite,
            $config->application->services->int
        );
        $data = $this->preapreData($intClient, $data);

        $data['es_lead_id'] = (string) Util::array_get('es_lead_id', $data, '');
        $data['es_tenant_id'] = (string) Util::array_get('es_tenant_id', $data, '');
        if (empty($data['lead_id']) || empty($data['customer_id']) ||
            empty($data['es_lead_id']) || empty($data['es_tenant_id'])
        ) {
            $dialingCode = Util::getDialingCode(Util::DIALING_CODES, $data['location_id']);
            $call_number = Util::removeDialingCode($data["call_number"], $dialingCode);
            if (!empty($call_number) && strpos($data['call_type'], "inbound_nolead") === false) {
                try {
                    $requestData = [
                        'phone' => $call_number,
                        'location_id' => $data['location_id'],
                    ];

                    /**CPAPI-3832 get disposition data from redis, not present fetch from disposition endpoint*/
                    $result = $this->getDispositionData($intClient, $requestData, $data);

                    $data['lead_id'] = (int) Util::array_get('lead_id', $result, 0);
                    $data['es_lead_id'] = (string) Util::array_get('es_lead_id', $result, '');
                    $data['customer_id'] = Util::array_get('tenant_id', $result, 0);
                    $data['es_tenant_id'] = (string) Util::array_get('es_tenant_id', $result, '');
                } catch (GuzzleHttp\Exception\ClientException $e) {
                    $this->errorMessage('Error while getting disposition data ' .
                    $e->getMessage());
                }
            }
        }

        if ($data['lead_id'] > 0) {
            $data['fk_lead_id'] = (int) $data['lead_id'];
            $data['lead'] = $this->getLeadRecord($data['lead_id']);
        }

        if (!empty($data['customer_id']) && $data['customer_id'] !== -1) {
            $locationID = $data['location_id'];
            if (isset($data['neighbor_location_id']) && !empty($data['neighbor_location_id'])) {
                $locationID = $data['neighbor_location_id'];
            }
            $location = $this->getLocationAPIType($locationID);
            $api_type = (int) Util::array_get('api_type', $location, 0);
            $data['tenant'] = $this->getTenantRecord($data['customer_id'], $api_type);
        }

        $data['tasks'] = $this->getTaskRecords($data['log_id']);
        $data['is_callcenter_call'] = in_array(
            1,
            array_column($data['tasks'], 'is_callcenter_call')
        ) ? 1 : 0;
        $data['is_location_call'] = in_array(
            1,
            array_column($data['tasks'], 'is_location_call')
        ) ? 1 : 0;
        if (count($data['tasks']) === 0) {
            unset($data['tasks']);
        }

        foreach ($this->deletedFields as $field) {
            $data = Util::array_del($field, $data);
        }

        return $data;
    }

    /**
     * Method log information messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function infoMessage(string $msg, $src = null)
    {
        unset($src);
        echo '[call-history] '.$msg."\n";
    }

    /**
     * Method log debug messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function debugMessage(string $msg, $src = null)
    {
        unset($src);
        echo '[call-history] '.$msg."\n";
    }

    /**
     * Retrieve call details from mysql database to store ES/S3
     *
     * @param integer $logId    unique log Id
     * @param string  $twilioId call log twilio Id
     *
     * @return array
     */
    public function buildCallRecord($logId = 0, $twilioId = ''): array
    {
        $CallRec = [];
        if ($logId) {
            $CallRec = $this->getCallRecord('log_id', (int) $logId);
        } elseif ($twilioId) {
            $CallRec = $this->getCallRecord('twilio_id', $twilioId);
        }
        if (count($CallRec) > 0) {
            $CallRec = $this->shapeRecord($CallRec);
        }

        return $CallRec;
    }

    /**
     * Construct ES/S3 ID field
     *
     * @param array $data call log record
     *
     * @return string
     */
    protected function getItemID(array $data)
    {
        $itemId = $data['twilio_id'];
        if (!strlen($itemId)) {
            $accountId = $this->getRecordAccount(
                util::array_get('account_id', $data, 0),
                util::array_get('location_id', $data, 0)
            );
            $itemId = md5(
                $accountId . '-' . $data['location_id'] . '-' .
                $data['datestamp'] . '-' . $data['log_id']
            );
        }

        return $itemId;
    }

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return CallHistory
     */
    protected function getModel()
    {
        return new \CallHistory();
    }

    /**
     * Retrieve account Id from call log record
     *
     * @param array $rec Call log record
     *
     * @return mixed
     */
    protected function getItemAccountID(array $rec)
    {
        return Util::array_get('account_id', $rec, Util::array_get('user_id', $rec, 0));
    }

    /**
     * ExtractParent
     *
     * @param array $data request data
     *
     * @return array
     */
    protected function extractParent(&$data)
    {
        unset($data);

        return [];
    }

    /**
     * Get locations for account or all locations
     *
     * @param int $accountId account Id
     *
     * @return array
     */
    private function getLocations(int $accountId = 0) : array
    {
        if (count($this->locAccount) === 0) {
            $this->debugMessage("Querying location list");
            $sql = 'SELECT location_id, user_id FROM locations';
            if ($accountId) {
                $sql .= " and user_id=".(int) $accountId;
            }
            $locResult = $this->replica->query($sql);
            $locData = $locResult->fetchAll();
            $this->locAccount = [];
            foreach ($locData as $loc) {
                $this->locAccount[$loc['location_id']] = $loc['user_id'];
            }
            $this->debugMessage(count($this->locAccount) . " Locations loaded");
        }

        return $this->locAccount;
    }

    /**
     * Read event summary data from MySQL for call
     *
     * @param int $logId call log Id
     *
     * @return array
     */
    private function getEventSummary(int $logId) : array
    {
        $sql = "select IF(SUM(is_abandoned) IS NOT NULL, 1,
        IF(SUM(outcome) IS NOT NULL, 2, 3)) call_status,
            SUM(queue_time) as queue_time, count(*) as num_events,
            SUM(is_rolled_over) as num_rollovers,
                count(distinct agent_id) as num_agents,
            count(distinct queue_id) as num_queues,
            count(distinct log_id)  as agent_queue,
            count(distinct case when reservation_accepted != ''
            then log_id end) as agent_answered
            FROM track_callcenter_tasks where log_id=$logId";
        $result = $this->replica->query($sql);
        $data = $result->fetchAll();
        if (count($data) > 0) {
            $data = $data[0];
        }

        $eventData = [];
        if (count($data) > 0) {
            $eventData['call_status'] = (int) Util::array_get('call_status', $data, 0);
            $eventData['queue_time'] = (int) Util::array_get('queue_time', $data, 0);
            $eventData['num_events'] = (int) Util::array_get('num_events', $data, 0);
            $eventData['num_rollovers'] = (int) Util::array_get('num_rollovers', $data, 0);
            $eventData['num_agents'] = (int) Util::array_get('num_agents', $data, 0);
            $eventData['num_queues'] = (int) Util::array_get('num_queues', $data, 0);
            $eventData['agent_queue'] = (int) Util::array_get('agent_queue', $data, 0);
            $eventData['agent_answered'] = (int) Util::array_get('agent_answered', $data, 0);
        }

        return $eventData;
    }

    /**
     * Get lead record from MySQL
     *
     * @param int $leadId Lead Id
     *
     * @return array
     */
    private function getLeadRecord(int $leadId) : array
    {
        $sql = "select inquiry_type, qt_rental_type, first_name, last_name, phone, email
            FROM leads where lead_id=$leadId";
        $result = $this->replica->query($sql);
        $result->setFetchMode(
            Enum::FETCH_ASSOC
        );
        $data = $result->fetchAll();
        if (count($data) > 0) {
            $data = $data[0];
        }

        return $data;
    }

    /**
     * Get location type from MySQL
     *
     * @param int $locationId location Id
     *
     * @return array
     */
    private function getLocationAPIType(int $locationId) : array
    {
        $sql = "select api_type from locations where location_id=$locationId";
        $result = $this->replica->query($sql);
        $result->setFetchMode(
            Enum::FETCH_ASSOC
        );
        $data = $result->fetchAll();
        if (count($data) > 0) {
            $data = $data[0];
        }

        return $data;
    }

    /**
     * Get Tenant record from MySQL
     *
     * @param string $customerId customer Id
     * @param int $apiType    location api type
     *
     * @return array
     */
    private function getTenantRecord($customerId, $apiType) : array
    {
        if ((int) $apiType === 1) {
            $sql = "select sFName as first_name, sLName as last_name,
            sPhone as phone, sEmail as email from SiteLinkTenants where TenantID=$customerId";
        } else {
            return [];
        }

        $result = $this->replica->query($sql);
        $result->setFetchMode(
            Enum::FETCH_ASSOC
        );
        $data = $result->fetchAll();
        if (count($data) > 0) {
            $data = $data[0];
        }

        return $data;
    }

    /**
     * Read task records from MySQL for call
     *
     * @param int $logId call log Id
     *
     * @return array
     */
    private function getTaskRecords(int $logId) : array
    {
        $eventData = [];
        $sql = "select * from track_callcenter_tasks where log_id=$logId";
        $result = $this->replica->query($sql);
        $result->setFetchMode(Enum::FETCH_ASSOC);
        $events = $result->fetchAll();
        if (count($events) > 0) {
            $this->debugMessage('Loading ' . count($events) . ' task events for call ' . $logId);

            foreach ($events as $data) {
                $eventData[] = $this->shapeTaskRecord($data);
            }
        } else {
            $this->debugMessage('No task events found for call '.$logId);
        }

        return $eventData;
    }

    /**
     * Format task record for ES/S3 object
     *
     * @param array $taskRec task record
     *
     * @return array
     */
    private function shapeTaskRecord(array $taskRec)
    {
        $taskRec['event_id'] = (int) Util::array_get(
            'event_id',
            $taskRec,
            (int) Util::array_get('id', $taskRec, 0)
        );
        $taskRec['queue_id'] = (int) Util::array_get('queue_id', $taskRec, 0);
        $taskRec['agent_id'] = (int) Util::array_get('agent_id', $taskRec, 0);
        $taskRec['outcome'] = (int) Util::array_get('outcome', $taskRec, 0);
        $taskRec['retry_attempt'] = (int) Util::array_get('retry_attempt', $taskRec, 0);
        $taskRec['is_rolled_over'] = (int) Util::array_get('is_rolled_over', $taskRec, 0);
        $taskRec['is_abandoned'] = (int) Util::array_get('is_abandoned', $taskRec, 0);
        $taskRec['is_task'] = (int) Util::array_get('retry_attempt', $taskRec, 0);
        $taskRec['is_task_complete'] = (int) Util::array_get('is_rolled_over', $taskRec, 0);
        $taskRec['queue_time'] = (int) Util::array_get('queue_time', $taskRec, 0);

        $taskRec['task_sid'] = (string) Util::array_get('task_sid', $taskRec, '');
        $taskRec['config_step_uid'] = (string) Util::array_get('config_step_uid', $taskRec, '');

        $taskRec['reservation_created'] = (string) Util::array_get(
            'reservation_created',
            $taskRec,
            ''
        );
        $taskRec['reservation_accepted'] = (string) Util::array_get(
            'reservation_accepted',
            $taskRec,
            ''
        );
        $taskRec['reservation_rejected'] = (string) Util::array_get(
            'reservation_rejected',
            $taskRec,
            ''
        );
        $taskRec['reservation_timeout'] = (string) Util::array_get(
            'reservation_timeout',
            $taskRec,
            ''
        );

        if ($taskRec['queue_id'] > 0 && !array_key_exists('queue_sid', $taskRec)) {
            $taskRec['queue_sid'] = $this->getQueueSid($taskRec['queue_id']);
        }

        if (empty($taskRec['queue_sid'])) {
            util::array_del('queue_sid', $taskRec);
        }

        $taskRec['is_callcenter_call'] = 0;
        $taskRec['is_location_call'] = 0;
        if (!empty($taskRec['channel']) && !empty($taskRec['workflow'])) {
            $taskRec['is_callcenter_call'] = ($taskRec['channel'] === 'voice' &&
             $taskRec['workflow'] === 'InboundCallCenterVoice') ? 1 : 0;
        }

        if (!empty($taskRec['channel']) && !empty($taskRec['workflow'])) {
            $taskRec['is_location_call'] = ($taskRec['channel'] === 'voice' &&
             $taskRec['workflow'] === 'InboundLocationVoice') ? 1 : 0;
        }

        if ($taskRec['agent_id'] > 0
            && $taskRec['is_callcenter_call'] === 0
            && $taskRec['is_location_call'] === 0) {
            // Adding try catch because machine client user can be deleted
            // upton unregister
            try {
                $coreClient = ClientFactory::getCoreClient();
                $employee = $coreClient->getUser($taskRec['agent_id']);

                if (strpos($employee->email, '@tc.com') !== false) {
                    $taskRec['is_location_call'] = 1;
                }
            } catch (Exception $ex) {
                $this->errorMessage('There was an error looking for user: ' . $taskRec['agent_id']
                . ' for Call ES Sync at CallArchive.php, error: ' . $ex->getMessage());
            }
        }

        // Invalid date ('0000-00-00 00:00:00') causing issue in es replication
        if (strtotime($taskRec['date_modified']) <= 0) {
            unset($taskRec['date_modified']);
        }

        util::array_del('log_id', $taskRec);
        util::array_del('queue_id', $taskRec);

        return $taskRec;
    }

    /**
     * Get Queue twilio SID from MySQL
     *
     * @param int $queueId unique queue Id
     *
     * @return string
     */
    private function getQueueSid(int $queueId) : string
    {
        $sql = "select task_queue_sid from call_route_queue where queue_id=$queueId";
        $result = $this->replica->query($sql);
        if ($result) {
            $result->setFetchMode(Enum::FETCH_ASSOC);
            $data = $result->fetchAll();
            if (count($data) > 0) {
                $data = $data[0];

                return $data['task_queue_sid'];
            }
        }

        return '';
    }

    /**
     * Read user list from MySQL
     * @param int $userId user Id
     * @return array
     */
    private function getUsers($userId = null) : array
    {
        $this->users = [];

        if ($userId) {
            $this->debugMessage("Querying user list");

            $sql = 'SELECT user_id, CONCAT_WS(" ", firstname, lastname) AS "user_name"
            FROM users WHERE user_id = '.$userId;
            $result = $this->replica->query($sql);
            $data = $result->fetch();
            $this->users = $data;

            $this->debugMessage(count($this->users) . " user loaded");
        }

        return $this->users;
    }

    /**
     * Return user name from user list
     *
     * @param int $userId user Id
     *
     * @return array
     */
    private function getUserName(int $userId) : string
    {
        if ((int) $userId < 2) {
            // CP-7933 use name Default User for id < 2
            return 'Default User';
        }
        $user = $this->getUsers($userId);

        return isset($user['user_name']) ? $user['user_name'] : '';
    }

    /**
     * Load ad names from MySQL
     *
     * @return array
     */
    private function getAds() : array
    {
        if (count($this->ads) === 0) {
            $this->debugMessage("Querying Ad list");
            $sql = 'SELECT ad_id, ad_name FROM ads';
            $adResult = $this->replica->query($sql);
            $adData = $adResult->fetchAll();
            $this->ads = [];
            foreach ($adData as $ad) {
                $this->ads[$ad['ad_id']] = $ad['ad_name'];
            }
            $this->debugMessage(count($this->ads) . " ads loaded");
        }

        return $this->ads;
    }

    /**
     * Return ad name from ad list
     *
     * @param int $adId unique ad Id
     *
     * @return string
     */
    private function getAdName(int $adId) : string
    {
        if ((int) $adId === 0) {
            return '';
        }
        $ads = $this->getAds();
        if (isset($ads[$adId])) {
            return $ads[$adId];
        } else {
            return '';
        }
    }

    /**
     * Determine account ID for call record based on existing account ID or location ID
     *
     * @param int $accountId  account Id
     * @param int $locationId location Id
     *
     * @return int
     */
    private function getRecordAccount(int $accountId = 0, int $locationId = 0) : int
    {
        if (!$accountId) {
            $locAccount = $this->getLocations();
            if (isset($locAccount[$locationId])) {
                $accountId = (int) $locAccount[$locationId];
            } else {
                $accountId = 0;
            }
        }

        return $accountId;
    }

    /**
     * Constructor to initialize data
     */
    private function __construct()
    {
        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $this->replica =  $di->getShared('dbLegacy');

        $this->locAccount = [];
        $this->debugMessage('Loading users');
        $this->debugMessage('Loading Ads');
        $this->ads = $this->getAds();

        parent::initialize();
    }

    /**
     * Retrieve call details from mysql database
     *
     * @param string $field field name
     * @param mixed  $value twlilio Id / log Id value
     *
     * @return array
     */
    private function getCallRecord($field, $value) : array
    {
        if ($field === 'twilio_id') {
            $where =  "call_logs.$field='$value'";
        } elseif ($field === 'log_id') {
            $where =  "call_logs.$field=$value";
        } else {
            return [];
        }

        $sql = "select call_logs.*,
                    leads.inquiry_type as lead_inquiry_type,
                    leads.qt_rental_type as lead_qt_rental_type,
                    leads.first_name as lead_first_name,
                    leads.last_name as lead_last_name,
                    leads.phone as lead_phone, leads.email as lead_email,
                    call_billing.call_duration
                    from call_logs
                    LEFT JOIN leads ON (call_logs.fk_lead_id=leads.lead_id)
                    LEFT JOIN call_billing ON (call_logs.log_id=call_billing.log_id)
                    where $where";
        $result = $this->replica->query($sql);
        $result->setFetchMode(Enum::FETCH_ASSOC);
        $data = $result->fetchAll();
        if (count($data) > 0) {
            $data = $data[0];
        }

        return $data;
    }

    /**
     * Prepare Data
     *
     * @param mixed $intClient IntClient Obj
     * @param array $data      Data
     *
     * @return array
     */
    private function preapreData($intClient, array $data = []) :array
    {
        if (!array_key_exists('lead_id', $data) && array_key_exists('fk_lead_id', $data)) {
            $data['lead_id'] = (int) $data['fk_lead_id'];
        } else {
            $data['lead_id'] = (int) Util::array_get('lead_id', $data, 0);
        }
        if ($data['lead_id'] > 0) {
            $requestData = [
                'mysql_id' => $data['lead_id'],
                'location_id' => $data['location_id'],
                'entity_type' => 'lead',
                'account_id' => $data['account_id'],
            ];
            try {
                $esIdRecord = Util::objectToArray($intClient->convertMysqlEsId($requestData));
                $data['es_lead_id'] = (string) Util::array_get('es_id', $esIdRecord['data'], '');
            } catch (Exception $e) {
            }
        }

        if (!empty($data['customer_id']) && $data['customer_id'] !== -1) {
            $requestData = [
                'mysql_id' => $data['customer_id'],
                'location_id' => $data['location_id'],
                'account_id' => $data['account_id'],
            ];

            try {
                $esIdRecord = Util::objectToArray($intClient->convertMysqlEsId($requestData));
                $data['es_tenant_id'] = (string) Util::array_get('es_id', $esIdRecord['data'], '');
            } catch (Exception $e) {
            }
        }

        return $data;
    }

    /**
     * Get disposition Data from redis or disposition endpoint
     * @param mixed $intClient   IntClient Obj
     * @param array $requestData
     * @param array $data
     * @return array $dispositionData
     */
    private function getDispositionData($intClient, $requestData, $data)
    {
        $dispositionData = [];

        try {
            $di = \Phalcon\DI\FactoryDefault::getDefault();
            $config = $di->getShared('config');
            $this->redis = new \Redis();
            $this->redis->pconnect(
                $config->application
                ->redis->redisHost,
                $config->application->redis->redisPort
            );

            $redisKey = "disposition_data_".$requestData['phone']."_".$requestData['location_id'];
            $result = json_decode($this->redis->get($redisKey), true);
            $this->redis->close();

            if (empty($result)) {
                $dispositionRecords = Util::objectToArray(
                    $intClient->searchdisposition($requestData)
                );
                $result = Util::array_get('items', $dispositionRecords, []);
                $result = $result[0] ?? [];
            }

            $dispositionData['lead_id'] = $data['lead_id']
            ?? (int) Util::array_get('lead_id', $result, 0);

            $dispositionData['es_lead_id'] = !empty($data['es_lead_id'])
                ? $data['es_lead_id']
                : (string) Util::array_get('es_lead_id', $result, '');

            $dispositionData['tenant_id'] = !empty($data['customer_id'])
                ? $data['customer_id']
                : Util::array_get('tenant_id', $result, 0);

            $dispositionData['es_tenant_id'] = !empty($data['es_tenant_id'])
                ? $data['es_tenant_id']
                : (string) Util::array_get('es_tenant_id', $result, '');

            return $dispositionData;
        } catch (\Exception $e) {
            $this->errorMessage('Error while getting disposition data ' .
            $e->getMessage(). ' for '. var_export($requestData, true), __METHOD__ . ":" . __LINE__);

            return $dispositionData;
        }
    }
}
