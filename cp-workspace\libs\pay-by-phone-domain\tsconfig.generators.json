{
  "compilerOptions": {
    "target": "es2018",         // Compiled JavaScript target.
    "module": "commonjs",       // Use commonjs module system, as it's the standard for Node.js.
    "outDir": "./dist",         // Redirect output structure to the directory.
    "rootDir": "./src",         // Specify the root directory of input files.
    "sourceMap": true,          // Generate source map files for debugging.
    "inlineSourceMap": false,   // Emit a single file with source maps instead of having a separate file.
    "inlineSources": true,      // Emit the source alongside the sourcemaps within a single file.
    "strict": true,             // Enable all strict type-checking options.
    "esModuleInterop": true,    // Enables emit interoperability between CommonJS and ES Modules.
    "skipLibCheck": true,       // Skip type checking of declaration files.
    "forceConsistentCasingInFileNames": true // Disallow inconsistently-cased references to the same file.
  },
  "include": [
    "src/**/*.ts"   // Include all TypeScript files in src directory.
  ],
  "exclude": [
    "node_modules", // Exclude the node_modules directory.
    "**/*.spec.ts"  // Exclude test files.
  ]
}
