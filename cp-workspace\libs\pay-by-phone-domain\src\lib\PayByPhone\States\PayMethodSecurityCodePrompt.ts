import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodSecurityCodePrompt extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Compose TwiML that will ask for the security code
     * 2. Compose TwiML that will gather the security code
     * 3. Transition to PayMethodSecurityCodeValidate
     */

    const { twilioResponse, storage } = context;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      timeout: 10,
      finishOnKey: '*',
    }, [{
      messageId: 'pay-by-phone.enter-ccv',
      locale: storage.locale
    }]);


    return { nextState: PayByPhoneState.PayMethodSecurityCodeValidate };
  }
}
