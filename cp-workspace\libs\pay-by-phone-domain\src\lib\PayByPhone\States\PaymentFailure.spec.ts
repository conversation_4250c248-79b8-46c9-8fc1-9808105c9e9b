import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, ExitPayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { PaymentFailure } from './PaymentFailure';
import { Locale } from '@cp-workspace/shared';

describe('PaymentFailure', () => {
  let paymentFailure: PaymentFailure;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PaymentFailure],
    }).compile();

    paymentFailure = module.get<PaymentFailure>(PaymentFailure);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.PaymentFailure,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        paymentFailureRedirectUrl: 'http://example.com/redirect',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should set the nextState as TransferToAgent and provide a redirect URL on payment failure', async () => {
      const response: ExitPayByPhoneStateHandlerResponse = await paymentFailure.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.TransferToAgent);
      expect(response.redirectUrl).toBe(context.storage.paymentFailureRedirectUrl);
    });
  });
});
