<?php
/**
 * Ad model
 *
 * @category Ad
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Ad model
 *
 * @category Ad
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Ad")
 */
class Ad extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * Ad Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $ad_id;

    /**
     * Ad name
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $ad_name;

    /**
     * Ad type
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $ad_type;

    /**
     * Start date
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $startdate;

    /**
     * End date
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $enddate;

    /**
     * Cost
     *
     * @var float
     *
     * @Column(type="double", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $cost;

    /**
     * Active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $active;

    /**
     * User Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $user_id;

    /**
     * Is editable
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_editable;

    /**
     * Is always visible
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_always_visible;

    /**
     * Read only
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $read_only;

    /**
     * Method to set the value of field adId
     *
     * @param int $adId ad Id
     *
     * @return $this
     */
    public function setAdId(int $adId)
    {
        $this->ad_id = $adId;

        return $this;
    }

    /**
     * Method to set the value of field adName
     *
     * @param string $adName ad name
     *
     * @return $this
     */
    public function setAdName(string $adName)
    {
        $this->ad_name = $adName;

        return $this;
    }

    /**
     * Method to set the value of field adType
     *
     * @param integer $adType ad type
     *
     * @return $this
     */
    public function setAdType(int $adType)
    {
        $this->ad_type = $adType;

        return $this;
    }

    /**
     * Method to set the value of field startdate
     *
     * @param string $startdate start date
     *
     * @return $this
     */
    public function setStartdate(string $startdate)
    {
        $this->startdate = $startdate;

        return $this;
    }

    /**
     * Method to set the value of field enddate
     *
     * @param string $enddate end date
     *
     * @return $this
     */
    public function setEnddate(string $enddate)
    {
        $this->enddate = $enddate;

        return $this;
    }

    /**
     * Method to set the value of field cost
     *
     * @param double $cost cost
     *
     * @return $this
     */
    public function setCost(double $cost)
    {
        $this->cost = $cost;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active active
     *
     * @return $this
     */
    public function setActive(int $active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field userId
     *
     * @param integer $userId user Id
     *
     * @return $this
     */
    public function setUserId(int $userId)
    {
        $this->user_id = $userId;

        return $this;
    }

    /**
     * Method to set the value of field isEditable
     *
     * @param integer $isEditable is editable boolean value to set
     *
     * @return $this
     */
    public function setIsEditable(int $isEditable)
    {
        $this->is_editable = $isEditable;

        return $this;
    }

    /**
     * Method to set the value of field isAlwaysVisible
     *
     * @param integer $isAlwaysVisible boolean value to set
     *
     * @return $this
     */
    public function setIsAlwaysVisible(int $isAlwaysVisible)
    {
        $this->is_always_visible = $isAlwaysVisible;

        return $this;
    }

    /**
     * Method to set the value of field readOnly
     *
     * @param integer $readOnly boolean value to set
     *
     * @return $this
     */
    public function setReadOnly(int $readOnly)
    {
        $this->read_only = $readOnly;

        return $this;
    }

    /**
     * Returns the value of field adId
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field adName
     *
     * @return string
     */
    public function getAdName(): string
    {
        return $this->ad_name;
    }

    /**
     * Returns the value of field adType
     *
     * @return integer
     */
    public function getAdType(): int
    {
        return $this->ad_type;
    }

    /**
     * Returns the value of field startdate
     *
     * @return string
     */
    public function getStartdate(): string
    {
        return $this->startdate;
    }

    /**
     * Returns the value of field enddate
     *
     * @return string
     */
    public function getEnddate(): string
    {
        return $this->enddate;
    }

    /**
     * Returns the value of field cost
     *
     * @return double
     */
    public function getCost(): double
    {
        return $this->cost;
    }

    /**
     * Returns the value of field active
     *
     * @return integer
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field userId
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field isEditable
     *
     * @return integer
     */
    public function getIsEditable(): int
    {
        return $this->is_editable;
    }

    /**
     * Returns the value of field isAlwaysVisible
     *
     * @return integer
     */
    public function getIsAlwaysVisible(): int
    {
        return $this->is_always_visible;
    }

    /**
     * Returns the value of field readOnly
     *
     * @return integer
     */
    public function getReadOnly(): int
    {
        return $this->read_only;
    }

    /**
     * Constructor to initialize data
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setSource("ads");
        $this->belongsTo('ad_type', 'Adtype', 'adtype_id', ['alias' => 'adtype']);
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Ads[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Ads
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
