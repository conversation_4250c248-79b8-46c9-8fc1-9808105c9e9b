<?php

use Bugsnag\Client as BugsnagClient;
use Phalcon\Di\FactoryDefault;
use Whoops\Provider\Phalcon\WhoopsServiceProvider;

error_reporting(E_ALL);

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

try {

    /**
     * The FactoryDefault Dependency Injector automatically registers
     * the services that provide a full stack framework.
     */
    $di = new FactoryDefault();


    /**
     * Read services
     */
    include APP_PATH . "/config/services.php";

    /**
     * Get config service for use in inline setup below
     */
    $config = $di->getConfig();

    /**
     * Include Autoloader
     */
    include APP_PATH . '/config/loader.php';

    //new Whoops\Provider\Phalcon\WhoopsServiceProvider($di);
    new ErrorServiceProvider($di);
    /**
     * Handle the request
     */
    $application = new \Phalcon\Mvc\Application($di);
    $request = new Phalcon\Http\Request();

    // New Relic APM transaction
    $service = $config->serviceName;

    echo $application->handle($request->getURI())->getContent();

} catch (Phalcon\Mvc\Dispatcher\Exception $e) {
    $di = Phalcon\Di::getDefault();
    $bugsnag = $di->getShared(BugsnagClient::class);
    $bugsnag->notifyException($e);
    header('Content-type: text/json');
    http_response_code(404);
    $response = [
        'ERROR' => $e->getMessage(),
        'CODE'  => $e->getCode(),
        'FILE'  => $e->getFile(),
        'LINE'  => $e->getLine(),
        'TRACE' => $e->getTrace()
    ];

    if ($config->application->debug == 0) {
        $response['FILE'] = [];
        $response['LINE'] = '';
        $response['TRACE'] = [];
    }
    echo json_encode($response);
} catch (\Exception $e) {
    $di = Phalcon\Di::getDefault();
    $bugsnag = $di->getShared(BugsnagClient::class);
    $bugsnag->notifyException($e);
    header('Content-type: text/json');
    http_response_code($e->getCode());
    CallPotential\CPCommon\CPLogger::errorMessage(var_export($request->getHeaders(), true)."\n".var_export($request->getJsonRawBody(), true));
    CallPotential\CPCommon\CPLogger::errorMessage($e->getMessage()."\n".$e->getTraceAsString());
    $response = [
        'ERROR' => $e->getMessage(),
        'CODE'  => $e->getCode(),
        'FILE'  => $e->getFile(),
        'LINE'  => $e->getLine(),
        'TRACE' => $e->getTrace()
    ];

    if ($config->application->debug == 0) {
        $response['FILE'] = [];
        $response['LINE'] = '';
        $response['TRACE'] = [];
    }
    echo json_encode($response);
}
