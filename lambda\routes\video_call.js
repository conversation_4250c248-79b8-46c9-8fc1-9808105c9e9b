const AWS = require('aws-sdk');
const express = require('express');
const router = express.Router();
const config = require('../config/config');
const CallLogModel = require('../models/call-log');
const Twilio = require('twilio');
const cpapiClient = require('../libraries/cpapi-client');
const DynamoHelper = require('./shared/dynamo-helper');

AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });
Array.prototype.forEachAsync = async function (fn) {
  for (let t of this) { await fn(t) }
}

router.route('/callback')
.post(async function (req, resp) {   
    const xmlResponse = () => resp.type('xml').send('<Response>Success</Response>');
    //post data
    let data = req.body;
    
    /**skip room-created event */
    if (data.StatusCallbackEvent== "room-created") {
      return xmlResponse()
    }

    const roomSid = data.RoomSid
    let call_info = await new CallLogModel().list_dynamodb(roomSid, 'twilio_id');
    let location_id = call_info.location_id;
    let log_info = {};
    let no_db_save = true;

    log_info.log_id = call_info.log_id;  

    let acct = await DynamoHelper.getAccountDetail(data.AccountSid);

    const twilio = new Twilio(acct.account_sid, acct.authtoken);

    if (data.StatusCallbackEvent == "participant-connected") { 
      const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
      const locData = await locClient.getLocationConfiguration(location_id);
      log_info = {}
      /**check if video recording is enabled */
      if (locData.record_video_call) {
        /** Start the recording when more than one participants joins */
        await twilio.video.v1.rooms(roomSid)
        .participants
        .list({status: 'connected', limit: 20})
        .then(async participants => {
            if (participants.length > 1) {
              await twilio.video.rooms(roomSid)
              .recordingRules
              .update({rules: [{"type": "include", "all": true}]})
            }
        });
      }
    }

    if (data.StatusCallbackEvent == "participant-disconnected") { 
      /** fetch actual call duration from agents identity */
      if (data.ParticipantIdentity.includes('agent_')) {
        log_info.duration = parseInt(data.ParticipantDuration);
      }

      /**once participant disconnects, complete the room */
      await twilio.video.v1.rooms(roomSid)
      .fetch()
      .then(room => {
        if (room.status != 'completed') {
          return twilio.video.v1.rooms(room.sid)
          .update({status: 'completed'})
          .catch((e) => {
            console.error(e, new Error().stack);
            console.error("Request data ", data);
          });
        }
      });

    }

    if (data.StatusCallbackEvent == "room-ended") {
      log_info.billingDuration = parseInt(data.RoomDuration);
      /**CCC-1864 If no particpiats were in video call. set duration to zero */
      await twilio.video.v1.rooms(roomSid)
      .participants
      .list({status: 'disconnected', limit: 20})
      .then(async participants => {
          if (participants.length < 2) {
            log_info.duration = 0;
          }
      });

      log_info.is_route_complete = 1;
      no_db_save = false;

      try {
        await twilio.taskrouter.workspaces(call_info.workspace_sid).tasks(call_info.task_id).fetch().then((task) => {
          //complete task if assigned
          if (task.assignmentStatus === 'assigned') {
            return twilio.taskrouter.workspaces(call_info.workspace_sid)
            .tasks(call_info.task_id)
            .update({ assignmentStatus: 'completed', reason: 'Video Call completed' });
          } else if (task.assignmentStatus === 'pending' || task.assignmentStatus === 'reserved') {
            //cancel task if pending or reserved
            return twilio.taskrouter
            .workspaces(call_info.workspace_sid)
            .tasks(call_info.task_id)
              .update({
                assignmentStatus: 'canceled',
                reason: 'Task is Canceled as Video room ended',
            });
          }
        });
      } catch (e) {
        // Skip log when task is already completed or task not assigned
        if (e.status !== 400 && !e.message.includes('not currently assigned')) {
          console.error("Request data ", data);
        }
      }

      /**create composition once room end*/
      await twilio.video.v1.rooms(data.RoomSid)
        .recordings
        .list({limit: 20})
        .then(recordings => {
          console.debug("Recording list",recordings);
          if (recordings.length != 0) {
            try {
              /**Create composition, use to create playable
                files by mixing Video Recordings  of a particular room */
              return twilio.video.v1.compositions
                .create({
                    audioSources: ['*'],
                    videoLayout: {
                      grid: {
                        video_sources: [
                          '*'
                        ]
                      }
                    },
                    statusCallback: config.video_composition_url,
                    roomSid: roomSid
                  });
            } catch (error) {
              console.error("Composition create Error",error)
            }
          }
        });

      const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
      const locData = await locClient.getLocationConfiguration(location_id);

      /**on room-end make audit log entry for recording disabled location, as it wont be handled when recording is not present*/
      if (locData.record_video_call == 0) {
        let call_info = await new CallLogModel().list_dynamodb(roomSid, 'twilio_id');
        addAuditLog(call_info)
      }
    }
    
    
    if (log_info && Object.keys(log_info).length !== 0) {
      await new CallLogModel().update_dynamodb({
        'twilio_id': roomSid,
        'location_id': location_id,
      }, log_info, no_db_save);
    }

    resp.send();

    return;
  })


router.route('/composition-callback')
  .post(async function (req, resp) {  
    console.debug("video composition callback",req.body);
    let data = req.body;

    if (data.StatusCallbackEvent === "composition-available") {
      let call_info = await new CallLogModel().list_dynamodb(data.RoomSid, 'twilio_id');
      let log_info = {};

      log_info = {
        recording_sid : data.CompositionSid,
        duration : parseInt(data.Duration),
        channel: 'video'
      };

      if (log_info && Object.keys(log_info).length !== 0) {
        await new CallLogModel().update_dynamodb({
          'twilio_id': data.RoomSid,
          'location_id': call_info.location_id,
        }, log_info);
      }

      if (call_info.call_type.includes('payment')) {
        let acct = await DynamoHelper.getAccountDetail(data.AccountSid);
        const twilio = new Twilio(acct.account_sid, acct.authtoken);

        try {
          console.debug("Delete video recordings for payment calls",data.RoomSid)
          await twilio.video.v1.compositions(data.CompositionSid).remove();
  
          await twilio.video.v1.rooms(data.RoomSid)
              .recordings
              .list({limit: 20})
              .then(recordings => recordings.forEachAsync(async recording => {
                  await twilio.video.v1.recordings(recording.sid).remove();
                })
              );
        } catch (error) {
          console.error("Error while deleting video recordings",error)
        }
      }
    }

    if (data.StatusCallbackEvent === "composition-failed") {
      // Add logging
      console.debug("composition-failed",data)
      console.error("The Video composition processing task failed.",data.ErrorMessage)
      
    }

    resp.send();

    return;
});


//Function to make entry in audit log event
async function addAuditLog(call_info) {
  let callTypesForAuditLog = [
    'inbound_lead',
    'inbound_customer',
    'inbound_collection',
    'inbound_autopay',
  ];
  const paymentCallTypes = config.payment_call_types;
  callTypesForAuditLog = callTypesForAuditLog.concat(paymentCallTypes);
  
  
  if (callTypesForAuditLog.includes(call_info.call_type)) {
    
    const leadCallTypes = [
      'inbound_lead',
      'inbound_lead_payment',
    ];
    const customerCallTypes = [
      'inbound_customer',
      'inbound_collection',
      'inbound_payment',
      'inbound_autopay',
    ];

    let entityType = '';
    let auditLog = true

    if (leadCallTypes.includes(call_info.call_type)) {
      entityType = 'lead';

      if (!call_info.es_lead_id) {
        console.debug("Unable to determine valid es lead id for audit log entry")
        auditLog = false;
      }
    } else if (customerCallTypes.includes(call_info.call_type)) {
        entityType = 'customer';
        
        if (!call_info.es_tenant_id) {
          console.debug("Unable to determine valid es tenant id for audit log entry")
          auditLog = false;
        }
    }
    if (auditLog) {
      const cpapiClient = require('../libraries/cpapi-client');
      const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
      
      const employeeDetail = await coreClient.cache.getData(`user/${call_info.employee_id}`);
      const employeeName = `${employeeDetail.firstname} ${employeeDetail.lastname}`;

      const auditLogData =  {
        logType : "inbound_video_call",
        logMessage : '',
        logOrigin : 'inbound',
        entityType : entityType,
        esLeadId : call_info.es_lead_id ?? '',
        esCustomerId : call_info.es_tenant_id ?? '',
        esLedgerId : '',
        esUnitId : '',
        employeeId : call_info.employee_id,
        employeeName : employeeName,
        phoneNumber : call_info.call_name,
        callDuration : call_info.call_duration,
        callOutcome : 'answered',
        link : '',
        linkText : '',
        linkType : '',
        message : '',
        emailBody : '',
        emailSubject : '',
        emailTo : '',
        emailFrom : '',
        accountId : call_info.account_id,
        locationId : call_info.location_id,
        meta: {'callSid': call_info.twilio_id},
        date : call_info.datestamp,
      };

      console.debug("Video Audit log payload",auditLogData)
      const acctClient = new cpapiClient.acctClient(config.db.serviceTokens.readWrite);
      await acctClient.postData('auditlognew', auditLogData);
    }

  }
}
  module.exports = router
