import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneLongRunningState, LoopbackPayByPhoneStateHandlerResponse } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { Customer } from "@cp-workspace/shared";

@Injectable()
export class GetSavedCards extends PayByPhoneLongRunningState {
  constructor() {
    super(PayByPhoneState.GetSavedCards, PayByPhoneState.PayMethodCreditCardPrompt);
  }
  override async handler(context: PayByPhoneStateContext): Promise<LoopbackPayByPhoneStateHandlerResponse> {
    const { twilioResponse, storage } = context;

    /*
    * 1. Fetch location configuration to check if saved cards are allowed
    * 2. If there is only one tenant matched and selected, fetch saved cards for that tenant
    * 3. If surcharge is enabled for the location at CRM end and the surcharge percentage is greater than 0, prompt the user with a message about card processing fees with percentage
    * 4. If saved cards are available, transition to PayMethodPrompt
    * 5. If no saved cards are available, transition to PayMethodCreditCardPrompt
    */

    const locationConfiguration = await this.services.locationService.getLocationConfiguration(storage.locationId!);

    let filterByLedgers: string[] = [];
    if(storage.selectedUnits) {
      filterByLedgers = storage.selectedUnits.map(unit => unit.ledger_id.toString());
    }

    storage.savedCards = [];
    if ((storage.matchedTenants && storage.matchedTenants?.length === 1) && storage.selectedTenant) {
      storage.savedCards = locationConfiguration.allow_prev_cc === 1 ? await Customer.getSavedCards([storage.selectedTenant], this.services.integrationService, filterByLedgers) : [];
      storage.savedCards.forEach(card => {
        card.card_number = card.card_number.slice(-4);
      });
    }

    if(storage.matchedTenants && storage.matchedTenants?.length > 0) {
      const locationDetails = await this.services.locationService.getLocationDetails(storage.locationId);
      if (locationDetails?.api_type && storage.surchargeDetails?.surchargeEnabledAPITypes && (storage.surchargeDetails?.surchargeEnabledAPITypes).includes(locationDetails?.api_type) && locationDetails?.user_id) {
        const [isSurchargeEnabledforLocation, surchargePercentage] = await Promise.all([
          this.services.coreService.getLocationSetting(this.services.coreService.payByPhoneSurchargeFullyQualifiedLocationSettingName, locationDetails.user_id, storage.locationId),
          this.services.integrationService.getSurchargePercentage(storage.locationId)
        ]);
        storage.surchargeDetails.isSurchargeEnabledForLocation = isSurchargeEnabledforLocation as boolean;
        storage.surchargeDetails.surchargePercentage = surchargePercentage as number;
        if (isSurchargeEnabledforLocation && surchargePercentage > 0) {
          twilioResponse.sayInLocale({
            messageId: 'pay-by-phone.location-has-card-processing-fee',
            locale: storage.locale,
            i18nOptions: { args: [{ surchargePercent: surchargePercentage }] }
          });
        }
      }
    }

    const hasSavedCards = storage.savedCards.length > 0;
    if (hasSavedCards) {
      return { nextState: PayByPhoneState.PayMethodPrompt };
    }

    return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
  }
}
