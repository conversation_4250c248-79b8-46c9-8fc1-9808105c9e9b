import { Test, TestingModule } from '@nestjs/testing';
import { ApiType, PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { FinalPayAmountPrompt } from './FinalPayAmountPrompt';
import { Ledger, Locale, LocationConfiguration } from '@cp-workspace/shared';

describe('FinalPayAmountPrompt', () => {
  let finalPayAmountPrompt: FinalPayAmountPrompt;
  let context: PayByPhoneStateContext;
  let mockLocationSettingEnabled: boolean = true;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FinalPayAmountPrompt],
    }).compile();

    finalPayAmountPrompt = module.get<FinalPayAmountPrompt>(FinalPayAmountPrompt);
    finalPayAmountPrompt.services = {
      locationService: {
        getLocationConfiguration: jest.fn().mockResolvedValue({ allow_prev_cc: 1 } as LocationConfiguration),
        getLocationDetails: jest.fn().mockResolvedValue({
          api_type: 9,
          user_id: 'user123',
        }),
      } as any,
      i18n: { t: jest.fn() } as any,
      accountService: {} as any,
      coreService: {
        getLocationSetting: jest.fn().mockResolvedValue(mockLocationSettingEnabled),
        payByPhoneSurchargeFullyQualifiedLocationSettingName: 'mockSettingName'
      } as any,
      integrationService: {
        getTotalWithSurcharge: jest.fn().mockResolvedValue(262.76),
        getSurchargePercentage: jest.fn()
      } as any,
      dataDogService: {} as any,
      bugsnagService: {} as any,
      moduleRef: {} as any,
    }

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.FinalPayAmountPrompt,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        selectedUnits: [{
          amount_owed: '100.12',
        } as Ledger,
          {
            amount_owed: '150.12',
          } as Ledger
        ],
        totalBalance: 250.24,
        totalAmountDue: 250.24,
        surchargeDetails: {
          surchargeEnabledAPITypes: [ApiType.STOREDGE],
        }
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 123,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should calculate total amount due and prompt for payment confirmation', async () => {
      const response: PayByPhoneStateHandlerResponse = await finalPayAmountPrompt.handler(context);

      const totalAmountDue = "250.24";
      const gatherOptions = {
        numDigits: 1,
        method: 'POST',
        timeout: 10,
      };

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(gatherOptions, [
        { messageId: "pay-by-phone.amount-due", locale: Locale.English, i18nOptions: { args: [{ paymentAmount: totalAmountDue }] } },
        {messageId: "pay-by-phone.confirm-payment", locale: Locale.English}
      ])

      expect(response.nextState).toBe(PayByPhoneState.FinalPayAmountConfirm);
    });

    describe('when matchedTenants length is greater than zero', () => {
      beforeEach(() => {
        context.storage.matchedTenants = [{ customer_id: '123' }] as any;
      });

      describe('when surcharge is enabled for the card', () => {
        beforeEach(() => {
          context.storage.surchargeDetails!.isSurchargeEnabledForCard = true
        });

        describe('when surcharge percentage is greater than 0', () => {
          beforeEach(() => {
            context.storage.surchargeDetails!.surchargePercentage = 5;
          });

          it('should call getLocationDetails with proper params', async () => {
            await finalPayAmountPrompt.handler(context);
            expect(finalPayAmountPrompt.services.locationService.getLocationDetails).toHaveBeenCalledWith(context.storage.locationId);
          });

          describe('when surcharge is enabled for the api type', () => {
            beforeEach(async () => {
              await finalPayAmountPrompt.handler(context);
            });
            
            it('should call getLocationSetting with proper params', async () => {
              expect(finalPayAmountPrompt.services.coreService.getLocationSetting).toHaveBeenCalledWith(
                finalPayAmountPrompt.services.coreService.payByPhoneSurchargeFullyQualifiedLocationSettingName,
                'user123',
                context.storage.locationId
              );
            });

            it('should set isSurchargeEnabledForLocation in storage', async () => {
              expect(context.storage.surchargeDetails?.isSurchargeEnabledForLocation).toBe(mockLocationSettingEnabled);
            });
          
            describe('when surcharge toggle is enabled at crm side', () => {
              beforeEach(() => {
                mockLocationSettingEnabled = true;
              });

              describe('when first matchedTenant has customer id', () => {
                beforeEach(() => {
                  context.storage.matchedTenants = [{ customer_id: '123' }] as any;
                });

                it('should call getTotalWithSurcharge API with proper params', async () => {
                  expect(finalPayAmountPrompt.services.integrationService.getTotalWithSurcharge).toHaveBeenCalledWith(
                    context.storage.matchedTenants?.[0]?.customer_id, 
                    context.storage.locationId, 
                    expect.any(String)
                  );
                });

                it('should play the amount-due-with-card-processing-fee script', async () => {
                  expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(
                    expect.any(Object),
                    expect.arrayContaining([
                      expect.objectContaining({ messageId : 'pay-by-phone.amount-due-with-card-processing-fee' })
                    ])
                  );
                });
              });
            });
            describe('when surcharge toggle is disabled at crm side', () => {
              beforeAll(() => {
                mockLocationSettingEnabled = false;
              });

              it('should not say the card processing fee message', async () => {
                expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(
                  expect.any(Object),
                  expect.arrayContaining([
                    expect.objectContaining({ messageId : 'pay-by-phone.confirm-payment' })
                  ])
                );
              });
            });
          });
        });
      });
    });
  });
});
