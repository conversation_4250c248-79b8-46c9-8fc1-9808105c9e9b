{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "module": "commonjs", "types": ["jest", "node"], "declaration": true, "emitDecoratorMetadata": true, "target": "es6", "composite": true, "baseUrl": ".", "paths": {"@cp-workspace/pay-by-phone-domain": ["../../libs/pay-by-phone-domain/src/index.ts"], "@cp-workspace/shared": ["../../libs/shared/src/index.ts"]}}, "include": ["jest.config.ts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.d.ts", "src/app/Logging/**/*.ts", "src/app/Interceptors/**/*.ts", "src/app/PayByPhone/**/*.ts", "src/app/Echo/**/*.ts", "src/app/EventLogger/**/*.ts", "src/app/AsyncTest/**/*.ts", "../../libs/pay-by-phone-domain/src/index.ts"]}