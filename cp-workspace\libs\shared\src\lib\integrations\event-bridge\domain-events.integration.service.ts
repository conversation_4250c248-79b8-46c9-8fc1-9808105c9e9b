import { Inject, Injectable } from '@nestjs/common';
import {
  DomainEvent,
  DomainEventServiceOptions,
  IPublishDomainEvents,
  IPublishDomainEventsProvider,
} from '../../models/domain-event.model';

@Injectable()
export class DomainEventsIntegrationService implements IPublishDomainEvents {
  public static options: DomainEventServiceOptions;
  public static provider: IPublishDomainEventsProvider;
  public static client: IPublishDomainEvents;
  get isEnabled(): boolean {
    return !!DomainEventsIntegrationService.options?.enabled;
  }
  private initialized = false;
  get isInitialized(): boolean {
    return this.initialized;
  }

  constructor(@Inject('IPublishDomainEventsProvider') private readonly provider: IPublishDomainEventsProvider) {}

  initialize(): unknown {
    this.initialized = true;
    if (this.isEnabled) {
      DomainEventsIntegrationService.provider = this.provider;
      DomainEventsIntegrationService.client = this.provider.getClient();
      return DomainEventsIntegrationService.client;
    }
    return undefined;
  }

  private checkInitialized() {
    if (!this.isInitialized) {
      this.initialize();
    }
  }

  public async publish(domainEvent: DomainEvent): Promise<DomainEvent> {
    this.checkInitialized();
    if (!this.isEnabled) {
      return domainEvent;
    }
    return DomainEventsIntegrationService.client.publish(domainEvent);
  }
}
