<?php
/**
 * Library to setup call center
 *
 * @category CcSetup
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\Libraries\DynamoSync;

/**
 * Library to setup call center
 *
 * @category CcSetup
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class CcSetup extends DynamoSync
{
    /**
     * Replica
     *
     * @var Phalcon\Db\Adapter\Pdo\Mysql
     */
    private $replica;

    /**
     * Table
     *
     * @var string
     */
    protected $table = 'callcenter_setup';

    /**
     * Instance
     *
     * @var CcSetup
     */
    private static $instance = null;

    /**
     * Get instance
     *
     * @return CcSetup
     */
    public static function getInstance(): CcSetup
    {
        if (!is_object(self::$instance)) {
            self::$instance = new CcSetup();
        }

        return self::$instance;
    }

    /**
     * Method log information messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function infoMessage(string $msg, $src = null)
    {
        unset($src);
        echo '[call-center-setup] '.$msg."\n";
    }

    /**
     * Method log debug messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function debugMessage(string $msg, $src = null)
    {
        unset($src);
        echo '[call-center-setup] '.$msg."\n";
    }

    /**
     * Returns item Id
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    protected function getItemID(array $data): array
    {
        $itemId = [
            'user_id' => (int) $data['user_id'],
        ];

        return $itemId;
    }

    /**
     * Create dynamo db table
     *
     * @return boolean
     */
    protected function createTable(): bool
    {
        $tableName = $this->getTableNameWithPrefix();
        try {
            $this->getConnection()->createTable(
                [
                'AttributeDefinitions' => [
                    [
                        'AttributeName' => 'user_id',
                        'AttributeType' => 'N',
                    ],
                ],
                'KeySchema' => [
                    [
                        'AttributeName' => 'user_id',
                        'KeyType' => 'HASH',
                    ],
                ],
                'ProvisionedThroughput' => [
                    'ReadCapacityUnits' => 5,
                    'WriteCapacityUnits' => 5,
                ],
                'TableName' => $tableName,
                ]
            );

            $this->getConnection()->waitUntil(
                'TableExists',
                array(
                'TableName' => $tableName,
                )
            );

            return true;
        } catch (Aws\DynamoDb\Exception\DynamoDbException $e) {
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Format record
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    protected function shapeRecord(array $data): array
    {
        $data['chat_reservation_timeout'] = 15;
        $data['chat_queue_sid'] = '';
        $data['sms_reservation_timeout'] = 15;
        $data['sms_queue_sid'] = '';

        return $data;
    }

    /**
     * Format update record
     *
     * @param array $data array of PUT body / query params in key value pair
     *
     * @return array
     */
    protected function shapePutRecord(array $data): array
    {
        if (array_key_exists('user_id', $data)) {
            unset($data['user_id']);
        }

        return $data;
    }

    /**
     * Returns model name
     *
     * @return CallCenter
     */
    protected function getModel(): \CallCenter
    {
        return new \CallCenter();
    }

    /**
     * Get item account Id
     *
     * @param array $rec record
     *
     * @return mixed
     */
    protected function getItemAccountID(array $rec)
    {
        return Util::array_get('account_id', $rec, Util::array_get('user_id', $rec, 0));
    }

    /**
     * Constructor to initialize data
     */
    private function __construct()
    {
        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $this->replica =  $di->getShared('dbLegacy');
        parent::initialize();
    }
}
