import { ExecutionContext } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { firstValueFrom, of } from 'rxjs';
import { DomainEvent, DomainEventsService } from '@cp-workspace/shared';
import { CallSidRequestInterceptor } from './CallSidRequestInterceptor';

describe('CallSidRequestInterceptor', () => {
  let interceptor: CallSidRequestInterceptor;
  let logger: DomainEventsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CallSidRequestInterceptor,
        {
          provide: DomainEventsService,
          useValue: {
            publish: jest.fn(),
          },
        },
      ],
    }).compile();

    interceptor = module.get<CallSidRequestInterceptor>(
      CallSidRequestInterceptor
    );
    logger = module.get<DomainEventsService>(DomainEventsService);
  });

  it('should intercept the request and response', async () => {
    const start = 0;
    const end = start + 1000;
    const duration = 1000;
    const method = 'GET';
    const originalUrl = '/test';
    const headers = {};
    const body = {};
    const responseBody = {};

    const context: ExecutionContext = {
      switchToHttp: () => ({
        getRequest: () => ({
          method,
          originalUrl,
          headers,
          body,
        }),
        getResponse: () => ({
          statusCode: 201,
          getHeaders: jest.fn(),
        }),
      }),
    } as any;

    jest
      .spyOn(logger, 'publish')
      .mockImplementation(async (event: DomainEvent) => {
        return event;
      });

    await firstValueFrom(
      interceptor.intercept(context, {
        handle: () => of(responseBody),
      })
    );

    expect(logger.publish).toHaveBeenCalledTimes(2);
  });
});
