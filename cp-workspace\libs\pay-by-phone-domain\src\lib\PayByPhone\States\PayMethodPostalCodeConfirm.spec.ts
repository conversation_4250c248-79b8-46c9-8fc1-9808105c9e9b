import { PayMethodPostalCodeConfirm } from './PayMethodPostalCodeConfirm';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodPostalCodeConfirm', () => {
  let service: PayMethodPostalCodeConfirm;

  beforeEach(() => {
    service = new PayMethodPostalCodeConfirm();
  });

  it('should transition to FinalPayAmountPrompt when confirmation selection is 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.FinalPayAmountPrompt);
  });

  it('should transition to PayMethodPostalCodePrompt when confirmation selection is not 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '2',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodPostalCodePrompt);
  });
});
