import { IntegrationService } from "../services/integration.service";
import { Tenant } from "./tenant.model";
import { AmountDueErrorResponse, AmountDueSuccessfulResponse, SavedCard } from "./payment.model";
import { Ledger } from "./ledger.model";

export class Customer {
  static async getSavedCards(tenants: Tenant[], integrationService: IntegrationService, filterByLedgers: string[] = []): Promise<SavedCard[]> {
    const cards: SavedCard[] = [];
    if (!tenants) {
      return cards;
    }
    for (const tenant of tenants) {
      const tenantCards = await integrationService.getSavedCards(tenant._id, filterByLedgers);
      if (tenantCards && tenantCards.items?.length > 0) {
        cards.push(...tenantCards.items);
      }
    }
    return cards;
  }

  static calculateTotalBalance(tenants: Tenant[]): number {
    return tenants.reduce((total, tenant) => {
      return total + Customer.calculateBalanceDue(tenant.ledgers);
    }, 0);
  }

  static calculateBalanceDue(ledgers: Ledger[]): number {
    return ledgers.reduce((total, ledger) => {
      return total + Number(ledger.amount_owed);
    }, 0);
  }

  static getDelinquentUnits(tenants: Tenant[]): Ledger[] {
    const delinquentUnits: Ledger[] = [];
    if (!tenants) {
      return delinquentUnits;
    }
    for (const tenant of tenants) {
      for (const ledger of tenant.ledgers) {
        if (ledger.status !== 'Current') {
          delinquentUnits.push(ledger);
        }
      }
    }
    return delinquentUnits;
  }

  static getCurrentUnits(tenants: Tenant[]): Ledger[] {
    const currentUnits: Ledger[] = [];
    if (!tenants) {
      return currentUnits;
    }
    for (const tenant of tenants) {
      for (const ledger of tenant.ledgers) {
        if (ledger.status === 'Current') {
          currentUnits.push(ledger);
        }
      }
    }
    return currentUnits;
  }

  static isDelinquentUnit(ledger: Ledger): boolean {
    return ledger.status !== 'Current';
  }

  static getUnitAndTenantByIndex(tenants: Tenant[], index: number): UnitAndTenant | undefined {
    let currentUnitsCount = 0;
    for (const tenant of tenants) {
      for (const ledger of tenant.ledgers) {
        if (currentUnitsCount === index) {
          return { selectedUnit: ledger, selectedTenant: tenant };
        }
        currentUnitsCount++;
      }
    }
    return undefined;
  }
}

export type UnitAndTenant = {
  selectedUnit: Ledger;
  selectedTenant: Tenant;
};