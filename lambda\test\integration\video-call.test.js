const app = require('../../app')
const supertest = require('supertest');
const requestWithSupertest = supertest(app);
const CallLogModel = require('../../models/call-log');
const { uniqueId } = require('lodash');
let testData = require('../testDataSet.json');
const moment = require('moment-timezone');
const config = require('../../config/config');
const { generateRowId } = require('../../routes/utils/twillio');

const cpapiClient = require('../../libraries/cpapi-client');
const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
const accountSid = testData.twilio_account.account_sid
const authToken = testData.twilio_account.authtoken
const client = require('twilio')(accountSid, authToken)
const locationId = testData.twilio_account.location_id
const workspaceSid = testData.twilio_account.workspace_sid;  
const videoWorkflowSid = testData.twilio_account.video_workflow_sid;
const videoQueueSid = testData.twilio_account.video_default_queue;

let roomSid
let taskData

beforeAll(async() => {
  // create room
  await client.video.v1.rooms.create({
    uniqueName: uniqueId,
    statusCallback :  "qa/v1/video/callback",
  }).then(room => roomSid = room.sid).catch(e => console.log(e));

  // create task
  taskData = await client.taskrouter.v1
    .workspaces(testData.twilio_account.workspace_sid)
    .tasks
    .create({
      attributes: JSON.stringify(
        {
          location_id : locationId,
          direction   : "inbound",
          taskchannel : "video",
          room_sid    : roomSid,
          call_sid    : roomSid,
          queue_sid   : videoQueueSid,
          queue_id    : videoQueueSid,
          log_id      : generateRowId(5),
        }),
      workflowSid : videoWorkflowSid,
      taskchannel : 'video',
      "timeout"   : 60,
    }).catch((e) => console.log(e));

  const datestamp = moment.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss');

  let callLogData =  {
    'twilio_id'     : roomSid,
    'task_id'       : taskData.sid,
    'workspace_sid' : workspaceSid,
    'location_id'   : locationId,
    'account_id'    : accountSid,
    'customer_name' : "Robert",
    'datestamp'     : datestamp,
    'call_name'     : "**********",
    'call_type'     : 'inbound',
    'channel'       : 'video',
  }

  await callClient.postData('call', callLogData);
}, 20000)

describe('Video room callback handler', () => {
  test('POST /video/callback participant disconnected', async () => {

    let payload = {
      RoomSid: roomSid,
      StatusCallbackEvent: 'participant-disconnected',
      AccountSid: accountSid,
      ParticipantIdentity: 'agent_{"roomName":"auto-room","pid":"1ad9721b-c71d-4a9e-98a4-f5d6f786b37f"}',
      ParticipantDuration: 7,
    }

    const res = await requestWithSupertest.post('/qa/v1/video/callback').send(payload);
    expect(res.status).toEqual(200);

    // check if room status updated to completed
    await client.video.v1.rooms(roomSid)
      .fetch()
      .then(room => expect(room.status).toEqual('completed'));
   

    let call_info = await new CallLogModel().list_dynamodb(roomSid, 'twilio_id');
  
    // check if call duration is set
    expect(call_info).toHaveProperty('call_duration');
    expect(parseInt(call_info.call_duration)).toBe(payload.ParticipantDuration);

  }, 20000);


  test('POST /video/callback room ended', async () => {
    let roomDuration = 300;
    let payload = {
      RoomSid: roomSid,
      StatusCallbackEvent: 'room-ended',
      AccountSid: accountSid,
      RoomDuration: roomDuration
    }

    const res = await requestWithSupertest.post('/qa/v1/video/callback').send(payload);
    expect(res.status).toEqual(200);

    let call_info = await new CallLogModel().list_dynamodb(roomSid, 'twilio_id');

    // check if is_route_complete is set
    expect(call_info).toHaveProperty('is_route_complete');
    expect(call_info.is_route_complete).toEqual(1);

    // check if billing duration is set
    expect(call_info).toHaveProperty('call_duration');
    expect(parseInt(call_info.billingDuration)).toBe(roomDuration);

  }, 40000);


  test('POST /video/composition-callback composition-available', async () => {
    let payload = {
      RoomSid: roomSid,
      CompositionSid: "CJe4912229de624ddef94f6817b018665k",
      Duration: 300,
      StatusCallbackEvent : 'composition-available',
    }

    const res = await requestWithSupertest.post('/qa/v1/video/composition-callback').send(payload);
    expect(res.status).toEqual(200);

    let call_info = await new CallLogModel().list_dynamodb(roomSid, 'twilio_id');

    // check if recording_sid is set
    expect(call_info).toHaveProperty('recording_sid');
    expect(call_info.recording_sid).not.toBe('');

    // check if call duration is set
    expect(call_info).toHaveProperty('call_duration');
    expect(parseInt(call_info.call_duration)).toBe(payload.Duration);

  }, 20000);

});