<?php
/**
 * PbxAccountInterface
 *
 * Created by <PERSON><PERSON>Stor<PERSON>.
 * User: cwalker
 * Date: 12/6/17
 * Time: 11:49 PM
 *
 * @category PbxAccountInterface
 * @package  Pbx
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

namespace Pbx;

/**
 * PbxAccountInterface
 *
 * @category PbxAccountInterface
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
interface PbxAccountInterface
{
    /**
     * Add account
     *
     * @param string $number   number
     * @param string $password password
     *
     * @return array
     */
    public function addAccount(string $number, string $password): array;

    /**
     * Update account
     *
     * @param string $number   number
     * @param string $password password
     *
     * @return array
     */
    public function editAccount(string $number, string $password): array;

    /**
     * Delete account
     *
     * @param string $number number
     *
     * @return bool
     */
    public function deleteAccount(string $number): bool;
}
