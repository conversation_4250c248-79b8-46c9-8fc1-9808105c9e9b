import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { I18nPath } from "../Generated/i18n.generated";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from "../PayByPhone.model";

export function PayByPhoneStateRetryHandler(
    maxRetries: number,
    promptState: PayByPhoneState,
    confirmState: PayByPhoneState[],
    maxRetryState: PayByPhoneState,
    maxRetryMessageKey?: I18nPath
  ) {
    return function (
      target: any,
      propertyKey: string,
      descriptor: PropertyDescriptor
    ) {
      const originalMethod = descriptor.value;
      descriptor.value = async function (
        context: PayByPhoneStateContext
      ): Promise<PayByPhoneStateHandlerResponse> {
        if (context.storage.retryCount && context.storage.retryCount >= maxRetries) {
          if (maxRetryMessageKey) {
            context.twilioResponse.sayInLocale({
              messageId: maxRetryMessageKey,
              locale: context.storage.locale
            });
          }
          return { nextState: maxRetryState };
        }
        const response: PayByPhoneStateHandlerResponse = await originalMethod.apply(this, [context]);
        const isPromptState = response.nextState === promptState;
        const isConfirmState = confirmState.includes(response.nextState);
        if (isPromptState) {
          context.storage.retryCount = (context.storage.retryCount ?? 0) + 1;
        } else if (!isConfirmState) {
          context.storage.retryCount = 0;
        }
        return response;
      };
    };
  }
