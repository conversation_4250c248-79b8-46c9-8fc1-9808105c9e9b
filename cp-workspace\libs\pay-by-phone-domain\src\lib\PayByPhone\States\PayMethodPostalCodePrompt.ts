import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodPostalCodePrompt extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Compose TwiML that will ask for postal code
     * 2. Compose TwiML that will gather the postal code
     * 3. Transition to PayMethodPostalCodeValidate
     */

    const { twilioResponse, storage } = context;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      timeout: 10,
      finishOnKey: '*',
    }, [{
      messageId: 'pay-by-phone.enter-zip',
      locale: storage.locale
    }]);


    return { nextState: PayByPhoneState.PayMethodPostalCodeValidate };
  }
}
