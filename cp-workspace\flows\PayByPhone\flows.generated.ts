export const flows = [
  {
    "startState": "LocalePrompt",
    "endState": "TransferToAgent",
    "flows": [
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Too many retries"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Speak with manager"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Too many retries"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Speak with manager"
        }
      ]
    ]
  },
  {
    "startState": "LocalePrompt",
    "endState": "DisconnectCall",
    "flows": [
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "CustomerOptOut",
          "when": "Opt out"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": "Too many incorrect attempts"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "CustomerOptOut",
          "when": "Opt out"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": "Too many incorrect attempts"
        }
      ]
    ]
  },
  {
    "startState": "CustomerByPhoneSearch",
    "endState": "TransferToAgent",
    "flows": [
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "InputPhoneGather",
          "when": "Customer not found, no response, or PBP not allowed"
        },
        {
          "state": "InputPhoneValidate",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Phone is invalid, retries >= max or speak to manager"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Too many retries"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "TransferToAgent",
          "when": "No response or invalid input"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Too many retries"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "TransferToAgent",
          "when": "No matched tenants"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Too many retries"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "TransferToAgent",
          "when": "No response or invalid input"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Too many retries"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": "Payment failed"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ]
    ]
  },
  {
    "startState": "CustomerByPhoneSearch",
    "endState": "DisconnectCall",
    "flows": [
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Chose to pay single unit"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "SayAmountDue",
          "when": "Delinquent payment flow"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms (U.S. Location)"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Single unit found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "Non-delinquent payment flow"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": "Valid selection"
        },
        {
          "state": "GetSavedCards",
          "when": "Confirm selection"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms (Non U.S. Location)"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": "Payment successful"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ]
    ]
  }
];