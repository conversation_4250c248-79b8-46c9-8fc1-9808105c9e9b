import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { CustomerExclusion } from "@cp-workspace/shared";

@Injectable()
export class CustomerOptOut extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, request, twilioResponse } = context;

    const exclusion: CustomerExclusion = {
      location_id: storage.locationId,
      contact_type: 'collection',
      exclusion_type: 'call',
      excluded_contact: request.From!,
      reason: 'Auto: Received Stop Request',
      request_sid: request.CallSid,
      entity_type: 'customer',
    };

    await this.services.accountService.createCustomerExclusion(exclusion);

    twilioResponse.sayInLocale({
      messageId: "pay-by-phone.opt-out-success",
      locale: context.storage.locale
    });

    return { nextState: PayByPhoneState.DisconnectCall };
  }
}
