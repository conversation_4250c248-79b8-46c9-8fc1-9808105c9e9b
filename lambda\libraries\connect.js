"use strict";
var moment = require('moment-timezone');
var AWS = require("aws-sdk");
var _ = require('lodash');
const mainconfig = require('../config/config');
const console_c = require('../config/logger').console;
const CommonMethod = require('../libraries/common-methods').CommonMethod;
const util = new CommonMethod();
const cpapiClient = require('../libraries/cpapi-client');

/**
 * LocationCall Class
 *
 * Handles call connect functionalities related to location
 *
 * @package    Callpotential
 * @subpackage Application
 * @category   Libraries
 * <AUTHOR>
 * @link       void
 */
var locationCall = class LocationCall {
  /**
   * Initializes required instance variables and starts a location call
   *
   * @access public
   * @param  array $config
   * @return void
   */
  constructor(config, req_q_data, req_p_data) {
    if (config) {
      this.queue_manager = config['queue_manager'];
      this.req_q_data = req_q_data;
      this.req_p_data = req_p_data;
      this.config = config;
    }
  }

  async process_location_call() {
    var self = this;
    if (self.config) {
      var config = self.config;
      // Get the caller name and status info
      var caller_name = config['customer_name'];

      if (!caller_name) {
        caller_name = 'Unknown';
      }

      var redirect_url_enqueue = mainconfig.call_url + 'location_call/location_enqueue/' + config['log_id'] + '?';
      var redirect_url = mainconfig.call_url + 'location_call/location_next_step/' + config['log_id'] + '?location_dial';
      var dial_options = {
        'timeout': self.queue_manager.timeout,
        'action': redirect_url,
        'record': (config['record'] === true || config['record'].toString().toLowerCase() === 'true' || config['record'].toString().toLowerCase() === '1') ? "record-from-answer-dual" : 0
      };

      // Check the from number. if that is SIP number than add callerID
      // param to dial verb since we need that for SIP and client dial
      var post_call_number = self.req_p_data['From'];

      var DialCall = require('./dial-call').DialCall;

      // Check if inbound call to sip or caller is using sip so it will add called id in the dial option
      if (config['call_destination'].indexOf('@') != -1 || post_call_number.indexOf('sip') != -1) {
        dial_options['callerId'] = config['call_number'];
        // Inbound is call from SIP user than assign a called id from any tracking number from location
        if (post_call_number.indexOf('sip') != -1) {
          let call_number = await new DialCall().get_caller_id(config['location_id']);
          dial_options['callerId'] = call_number;

        }
      }

      var dynamodb = new AWS.DynamoDB({
        region: process.env.AWS_REGION || 'us-west-2'
      });

      var params = {
        Key: {
          "id": {
            N: config['account_id'].toString()
          }
        },
        TableName: mainconfig.dynamodb.acctTable
      };

      try {
        let data = await dynamodb.getItem(params).promise();
        if (data && data.Item) {
          _.forEach(data.Item, function (value, key) {
            data.Item[key] = value[Object.keys(value)[0]];
          });

          let cardPopInfo = await self.queue_manager.getCardPopInfo(config['call_number'], config['location_id']);

          let wait_time = config['queue_manager']['current_step']['max_wait_time']['value'];

          let task_attributes = {
            'call_sid': config['twilio_call_sid'],
            'type': 'call',
            'queue_name': '_location-call',
            'caller_id': config['call_number'],
            'caller_name': util.escapeTwilioChars(config['caller_name']),
            'tracking_no': config['call_name'],
            'ad_name': util.escapeTwilioChars(config['ad_name']),
            'date_time': config['datestamp'],
            'call_route_id': config['config_id'],
            'call_route_event': 'call_center',
            'timeout': 120,
            'queue_id': data.Item['location_call_queue_sid'],
            'log_id': config['log_id'],
            'location_id': parseInt(config['location_id'], 10),
            'location_name': util.escapeTwilioChars(config['location_name']),
            'customer_name': util.escapeTwilioChars(config['customer_name']),
            'customer_type': config['customer_type'],
            'user_id': config['parent_user_id'],
            'config_step_uid': config['config_step_uid'],
            'is_sip_call': config['is_sip_call'],
            'queue_sid': data.Item['location_call_queue_sid'],
            'tenant_id': cardPopInfo['tenantId'],
            'lead_id': cardPopInfo['leadId'],
            'disposition': cardPopInfo['disposition'],
            'first_name': util.escapeTwilioChars(cardPopInfo['first_name']),
            'last_name': util.escapeTwilioChars(cardPopInfo['last_name']),
            'direction': 'inbound',
            'taskchannel': 'custom3',
            'max_wait_time' : wait_time
          };


          // add the task_queue_id to the querystring so we can use the value when Twilio hits our endpoint later.
          redirect_url_enqueue = redirect_url_enqueue + `task_queue_sid=${data.Item['location_call_queue_sid']}`;

          // Build attributes that will be used to dial the location on workflow.entered.
          const datts = { dial_options };

          var number_options = {};

          // Add the number verb inside dial verb with number options
          datts.number = {};
          datts.number.sip_call= false;
          let webhook_integration = false;


          if (config['primary_sip_setup'] !== null && config['primary_sip_setup'] !== '') {
            let primary_sip_setup = config['primary_sip_setup'];
            webhook_integration = primary_sip_setup['webhook_integration'];
          }

          if (config['call_destination'].indexOf('@') != -1 || webhook_integration) {
            datts.number.sip_call= true;

            if (config['primary_sip_setup'] !== null && config['primary_sip_setup'] !== '') {
              let primary_sip_setup = config['primary_sip_setup'];

              if (primary_sip_setup && primary_sip_setup['sip_username']) {
                number_options = {
                  'username': primary_sip_setup['sip_username'],
                  'password': primary_sip_setup['sip_password']
                };
              }
              datts.number.webhook_integration = primary_sip_setup['webhook_integration'];
              datts.number.sip_extension = primary_sip_setup['sip_extension'];
              datts.number.is_webhook_enabled = primary_sip_setup['is_webhook_enabled'];
              task_attributes.sip_extension = stripNonAscii(primary_sip_setup['sip_extension']);
              datts.number.call_destination = config['call_destination'];
              datts.number.number_options = number_options;
            } else {
              datts.number.call_destination = config['call_destination'];
              datts.number.number_options = number_options;
            }

            if (!datts.number.call_destination.toLowerCase().startsWith('sip:')){
              datts.number.call_destination = `sip:${datts.number.call_destination}`;
            }
            
          } else {
            datts.number.call_destination = config['call_destination'];
            datts.number.number_options = number_options;
          }

          // CP-10205: strip out any unicode characters from phone number
          datts.number.call_destination = stripNonAscii(datts.number.call_destination);

          //save the dial info for use on workflow.entered
          task_attributes.dial = datts;
          task_attributes.call_destination = stripNonAscii(config['call_destination']);

          /*
           Build a Twilio response containing <Enqueue><Task/></Enqueue> instructions
           */

          const response = self.queue_manager.twilio_response;
          response.enqueue({
            action: redirect_url_enqueue,
            workflowSid: data.Item["location_workflow"],
            waitUrl: mainconfig.call_url + 'location_call/location_wait/' + config['log_id'] + '/' + wait_time
          })
          .task({
            timeout: 120
          }, JSON.stringify(task_attributes));
        
          self.queue_manager.is_output_set = 1;
          return {'type': 'exit'};
        }
      } catch (e) {
        console.error(e, new Error().stack);
        self.queue_manager.is_output_set = 1;
        return {'type': 'exit'};
      }
    } else {
      return null;
    }
  }

}

/*
  CP-10205
  During QA testing, a scenario was encountered where a SIP phone number string
  contained an invisible unicode character. 
  
  For example, here's a SIP address:

    <EMAIL>

  Our code expects to give Twilio a number formatted like this:

    sip:<EMAIL>

  But, with the invisible unicode character, Twilio was seeing this:
  
    sip:?<EMAIL>
  
  This would generate an error about a bad SIP address.

  This is a symptom that speaks to the larger topic of input sanitization.  We
  will need additional input sanitization around the forms that are used to register
  SIP phones.

  The function below will strip out all non-ascii characters, including unicode, and 
  will be used to sanitize the task attributes for location calls (which are enqueued in this file).
*/
function stripNonAscii(str) {
  return !str ? str : str.toString().replace(/[^\x20-\x7E]/g, '');
}

var connect = class Connect {
  constructor(req_q_data, req_p_data) {
    this.name = null;
    this.connect_type = null;
    this.wait_time = null;
    this.lead_card_type = null;
    this.queue_manager = null;
    this.location_id = null;
    this.queue_id = null;
    this.phone = null;
    this.call_center = null;
    this.location_call = null;
    this.dial = null;
    this.req_q_data = req_q_data;
    this.req_p_data = req_p_data;
  }

  /**
   * Formats the business hour and returns array containing to and
   * from hour info in 24 hour min format
   *
   * @access private
   * @param  string $business_days
   * @return array/bool
   */
  get_business_hour(business_days) {
    var cur_weekday = moment.tz(mainconfig.TZ).format('dddd');
    business_days = JSON.parse(business_days);

    var ret_val = '';
    _.each(business_days['office_hours'], function(day) {
      if (day['weekday'] == cur_weekday) {
        ret_val = (day['closed']) ? false : ({
          'from': moment(day['from_hour'], "HH:mma"),
          'to': moment(day['to_hour'], "HH:mma")
        });
        return false;
      }
    });
    return ret_val;
  }

  /**
   * Executes the specific connect call step functionality
   *
   * @access public
   * @param  array $config
   * @return mixed
   */
  async process_step(config, call_info) {
    var self = this;
    if (config) {
      self.name = config['name'];
      self.connect_type = config['connect_type']['selected'];
      self.wait_time = config['max_wait_time']['value'];
      self.lead_card_type = config['lead_card']['selected'];

      // Add the inbound call to location primary queue
      self.queue_manager = call_info['queue_manager'];
      self.queue_manager.set_call_info(call_info['log_id'], self.wait_time);
      self.location_id = call_info['location_id'];
      self.account_id = call_info['account_id'];
    }

    console_c.log("CPBUG-351: process_step ", config, call_info);
    console.debug("Processing connect step:", self.connect_type);

    switch (self.connect_type) {
      case 'call_center': {
        const locClient = new cpapiClient.locClient(mainconfig.db.serviceTokens.readWrite);
        const locData = await locClient.getLocation(call_info.location_id)
        if (config['skip_outside_callcenterhr']
          && config['skip_outside_callcenterhr']['checked']
          && ! util.isLocationOpen(locData['hours_availability'], locData['timezone'], 1)) {
          return { 'result': false };
        }

        self.queue_id = config['queue_type']['selected'];
        var CallRouteQueueModel = require('../models/twilliocommon').CallRouteQueueModel;
        var queue = {};
        let list_queue_logs = await new CallRouteQueueModel().list_dynamodb(this.account_id, this.queue_id);

        queue['hold_music'] = `${self.account_id}/callcenter/hold_music/${self.queue_id}.mp3`;
        queue['notify_caller_status'] = 1;
        if (Object.keys(list_queue_logs).length !== 0) {
          queue['notify_caller_status'] = 0;
          if (list_queue_logs.hold_music) {
            queue['hold_music'] = list_queue_logs.hold_music.replace('/', '');
          }
          if (list_queue_logs.notify_caller_status && parseInt(list_queue_logs.notify_caller_status)) {
            queue['notify_caller_status'] = 1;
          }
        }
        if (queue['hold_music']) {
          self.queue_manager.wait_music_url = mainconfig.CP_CDN_URL + queue['hold_music'];
        }
        self.queue_manager.notify_caller_status = queue['notify_caller_status'];

        var post_input = self.req_p_data;
        var get_input = self.req_q_data;
        var leave_queue_gather = !!(post_input['Digits'] && 9 == post_input['Digits']);

        if ((post_input && post_input['QueueSid']) || ((get_input['leave_queue'] && post_input['QueueResult']) && ['error', 'system-error', 'hangup'].indexOf(post_input['QueueResult']) != -1)) {
          if (get_input && get_input['leave_queue']) {
            let t_client_resp = await self.queue_manager.leave_queue(post_input);
            return {
              'type': 'exit',
              'result': t_client_resp
            };
          } else if (leave_queue_gather) {

            let CallLogModel = require('../models/call-log');
            call_info.current_route_step = config;
            let next_step = await new CallLogModel().queue_save_next_step(call_info);
            await self.queue_manager.leave_call();
            return { 'result': next_step };
          } else if (get_input && get_input['wait_in_queue']) {
            let t_client_resp = await self.queue_manager.wait_in_queue(post_input);
            return {
              'type': 'exit',
              'result': t_client_resp
            };
          }
        }

        if (post_input['QueueSid']) {
          // Remove call from previous queue.
          let t_client_resp = await self.queue_manager.leave_call();
          return {
            'type': 'exit',
            'result': t_client_resp
          };
        }

        var CallCenter = require('./call-center').CallCenter;
        self.call_center = new CallCenter({
          'location_id': self.location_id
        });

        console.debug("Checking for available agents in queue:", self.queue_id, call_info);

        let available_count = await self.call_center.available_agents_count(self.queue_id, call_info);
        if (available_count) {
          console.debug("Available agents found in queue:", self.queue_id);
          console_c.log("# in queue #");
          call_info['lead_card_type'] = self.lead_card_type;

          // Unique id of step within the call route config.
          call_info['config_step_uid'] = config['uuid'];

          // Check if call is made to a sip number.
          // CC-985.
          call_info['is_sip_call'] = (0 === post_input['Called'].indexOf('sip'));

          // Transfer call from any previous queue to
          // call center queue.
          await self.queue_manager
            .queue_call(self.queue_id, call_info);
          return { 'result': 'continue' };
        } else {
          let CallLogModel = require('../models/call-log');
          call_info.current_route_step = config;
          let next_step = await new CallLogModel().queue_save_next_step(call_info);
          var redirect_url = mainconfig.call_url +
            'twilio/process_next_step/' + call_info['log_id'] + '?next_step';
          self.queue_manager.twilio_response.redirect(redirect_url);
          self.queue_manager.is_output_set = 1;
          return {
            'result': next_step
          };
        }
      }
      case 'dial': {
        let DialCall = require('./dial-call').DialCall;
        self.phone = config['dial_to']['value'];
        self.sip_username = (config && config['sip_username'] && config['sip_username']['value']) ? config['sip_username']['value'] : '';
        self.sip_password = (config && config['sip_password'] && config['sip_password']['value']) ? config['sip_password']['value'] : '';
        self.dial = new DialCall(call_info, self.req_q_data, self.req_p_data);
        let dial_res = await self.dial.call(call_info['log_id'], self.phone, self.sip_username, self.sip_password);
        return dial_res;
      }
      case 'location': {
        const locClient = new cpapiClient.locClient(mainconfig.db.serviceTokens.readWrite);
        const locData = await locClient.getLocation(call_info.location_id)
        if (config['skip_step']['checked'] 
          && ! util.isLocationOpen(locData['hours_availability'], locData['timezone'], 0)) {
          return { 'result': false };
        }

        call_info['lead_card_type'] = self.lead_card_type;
        let location_call = new locationCall(call_info, self.req_q_data, self.req_p_data);
        let loc_res = await location_call.process_location_call();
        self.location_call = location_call;
        return loc_res;
      }
      default:
        return { 'type': 'exit' };
    }
  }

}

module.exports = {
  'Connect': connect,
  'LocationCall': locationCall
}
