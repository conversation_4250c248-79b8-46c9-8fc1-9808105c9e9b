import { Module } from '@nestjs/common';
import {
  AcceptLanguageResolver,
  I18nAsyncOptions,
  I18nModule,
  I18nOptionsWithoutResolvers,
} from 'nestjs-i18n';
import * as path from 'path';
import { Locale } from '@cp-workspace/shared';
import { I18nTranslationService } from './I18nTranslation.service';

const I18N_DEFAULT_TRANSLATIONS_PATH =
  '../../libs/pay-by-phone-domain/src/i18n/';

@Module({
  imports: [
    I18nModule.forRootAsync({
      useFactory: () => {
        return {
          fallbackLanguage: Locale.English,
          loaderOptions: {
            /**
             * Running the application as a standalone server, lamba or local,
             * uses the `dist` library output. When running the e2e tests,
             * the translation files are in the `libs` folder. Because of this,
             * when running the e2e tests, the I18N_E2E_TRANSLATIONS_PATH environment 
             * variable is set to the path of the translation files relative to the 
             * workspace root.
             */
            path: path.join(
              __dirname,
              process.env['I18N_E2E_TRANSLATIONS_PATH'] ||
                I18N_DEFAULT_TRANSLATIONS_PATH
            ),
            watch: true,
          },
          resolvers: [
            AcceptLanguageResolver,
          ]
      } as I18nOptionsWithoutResolvers;
      },
    } as I18nAsyncOptions),
  ],
  controllers: [],
  providers: [I18nTranslationService],
  exports: [I18nTranslationService],
})
export class I18nServicesModule {}
