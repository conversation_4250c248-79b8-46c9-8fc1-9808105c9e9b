import { I18nService } from 'nestjs-i18n';
import { I18nTranslationService } from './I18nTranslation.service';
import { I18nTranslations } from './Generated/i18n.generated';

describe('I18nTranslationService', () => {
  let service: I18nTranslationService;
  let i18n: I18nService<I18nTranslations>;

  beforeEach(() => {
    i18n = { 
      translate: (_key: string, _options?: any) => {
        return 'Test Translation';
      }
     } as I18nService<I18nTranslations>;
    service = new I18nTranslationService(i18n);
  });

  describe('translate', () => {
    it('should call the i18n service translate method with the provided key and options', () => {
      const key = 'pay-by-phone.must-enter-ten-digits';
      const options = { lang: 'en-US' };

      jest.spyOn(i18n, 'translate');

      service.translate(key, options);

      expect(i18n.translate).toHaveBeenCalledWith(key, options);
    });
  });
});