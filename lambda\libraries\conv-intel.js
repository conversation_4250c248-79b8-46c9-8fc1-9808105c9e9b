"use strict";
const config = require('../config/config');
const cpapiClient = require('./cpapi-client');
const got = require('got');
const ci_api = `${config.conv_intel_api}`;
const call_url = `https://${config.env}-call.callpotential.com`;
const loc_url = `https://${config.env}-loc.callpotential.com`;
const core_url = `https://${config.env}-core.callpotential.com`;
const Twilio = require('twilio');

var convIntelClient = class convIntelClient {

  static async postData(postData) {
    try {
      let callSource = '';
      let ciEnabled = await this.checkFeatureToggle(postData.account_id);
      let url = ci_api;

      if (!ciEnabled) {
        return null;
      }

      let recDetails =  await this.fetchRecordingDetails(postData.account_id, postData.recording_sid);

      if (recDetails.source) {
        switch (recDetails.source) {
          case 'Conference':
            callSource = 'call_center';
            break;
          case 'RecordVerb':
            callSource = 'voicemail';
            break;
          case 'DialVerb':
            callSource = 'location';

            if (postData.recording_url.includes('external_connect')) {
              callSource = 'external_connect';
            }
            break;
        }
      }

      if (postData.call_type.includes('outbound')) {
        callSource = 'outbound';
      }

      let payload = {
          "RecordingUrl": postData.recording_url,
          "CallSid": postData.twilio_id,
          "RecordingSid": postData.recording_sid,
          "AccountSid": recDetails.accountSid,
          "UserId": postData.account_id,
          "RecordingDuration": recDetails.duration,
          "RecordingChannels": recDetails.channels,
          "RecordingSource": recDetails.source,
          "RecordingStatus": recDetails.status,
          "SourceEnv": config.env,
          "CallbackEndpoint": call_url,
          "CoreEndpoint": core_url,
          "LocationEndpoint": loc_url,
          "RecordingBucket": config.recording_bucket,
          "LocationId": postData.location_id,
          "CallType": postData.call_type,
          "CallerNumber": postData.call_number,
          "CallerName": postData.customer_name,
          "LeadId": postData.es_lead_id,
          "Version": "v2",
          "CallSource": callSource,
      }

      let options = {
        headers: { Authorization: config.db.serviceTokens.readWrite, 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      };

      let res = await got.post(url, options);
      let data = JSON.parse(res.body);
      return data;
    } catch (e) {
      console.error('ERROR posting conv-intel data', e, new Error().stack);
      return {};
    }
  }

  static async checkFeatureToggle(accountId) {

    const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
    let featureToggle = await coreClient.cache.getData(`featuretoggle?filterAccount_id=${accountId}&filterFeature_name=conversation_intelligence`).catch(error => console.log(error, new Error().stack));

    if (featureToggle
      && featureToggle.items
      && featureToggle.items[0]
      && featureToggle.items[0].feature_value
      && featureToggle.items[0].feature_value == 1) {
      return true;
    }

    return false
  }

  static async fetchRecordingDetails(accountId, recordingSid) {

    const mccClient = new cpapiClient.mccClient(config.db.serviceTokens.readWrite);
    let accountData = await mccClient.cache.getData(`twilio/account/${accountId}`).catch(error => console.log(error, new Error().stack));

    const twilioClient = new Twilio(accountData.account_sid, accountData.authtoken);

    return await twilioClient.recordings(recordingSid).fetch()
  }
}

module.exports = {
  convIntelClient
};

