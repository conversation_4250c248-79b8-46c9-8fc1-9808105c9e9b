const TwilioConstants = require("../../cc2/shared/twilio-constants");
const {
  handleParticipantJoin,
  handleConferenceStart,
  handleConferenceEnd,
  handleParticipantLeave,
  updateComputedCallDuration,
  getConferenceByFriendlyName,
  writeConferenceParticipantDetailsToTaskAttributes,
} = require("../../cc2/shared/conference-helper");

jest.mock("../../cc2/cpapi_client", () => ({
  callClient: jest.fn().mockImplementation(() => ({
    getData: jest.fn(),
  })),
}));

jest.mock("../../cc2/shared/cpapi-helper", () => ({
  createAuditLog: jest.fn(),
  updateReport: jest.fn().mockResolvedValue({}),
}));

jest.mock("../../cc2/shared/event-context-helper", () => ({
  twilioCredentials: jest.fn().mockResolvedValue({
    authtoken: "mockAuthToken",
  }),
  dynamoQuery: jest.fn().mockResolvedValue({}),
}));

const workerCallSid = "CALL123";
const customerCallSid = "CALL321";
const cooldownActivitySid = "ACT789";
const RecordingDuration = 400;
const conferenceSid = "CONF123";
const customerCallFrom = "+1234567890";
const customerCallTo = "+9876543210";
const workerCallFrom = "+1111111111";
const workerCallTo = "+2222222222";
const conferenceFriendlyName = "someFriendlyName";

function createBaseContext() {
  const mockContext = {
    task: {
      assignmentStatus: TwilioConstants.TaskAssignmentStatus.Pending,
      attributes: JSON.stringify({
        direction: "inbound",
        conference: {
          participants: {
            customer: {
              callSid: customerCallSid,
            },
            worker: {
              callSid: workerCallSid,
            },
          },
        },
      }),
      reason: "",
    },
    twilioClient: {
      conferences: jest.fn().mockReturnValue({
        participants: {
          list: jest.fn().mockResolvedValue([
            {
              callSid: customerCallSid,
            },
            {
              callSid: workerCallSid,
            },
          ]),
        },
        update: jest.fn().mockResolvedValue({}),
      }),
      update: jest.fn().mockReturnThis(),
      catch: jest.fn(),
      calls: jest.fn().mockReturnThis(),
      fetch: jest.fn().mockResolvedValue({ duration: "10" }),
      recordings: {
        list: jest
          .fn()
          .mockResolvedValue([
            { status: "completed", duration: RecordingDuration },
          ]),
      },
    },
    params: {
      ConferenceSid: conferenceSid,
      connectType: TwilioConstants.ConnectType.Phone,
      ParticipantLabel: "agent",
      WorkerSid: "WORKER123",
      ParticipantCallStatus: "in-progress",
    },
    workspace: {
      activities: {
        list: jest.fn().mockImplementation(({ friendlyName }) => {
          mockActivities = [
            { sid: "ACT123", friendlyName: "Offline" },
            { sid: "ACT456", friendlyName: "On-call" },
            { sid: cooldownActivitySid, friendlyName: "Cool-Down" },
          ];
          const filteredActivities = mockActivities.filter(
            (activity) => activity.friendlyName === friendlyName
          );
          return Promise.resolve(
            filteredActivities.length > 0
              ? filteredActivities
              : [{ sid: "ACT123", friendlyName: "Offline" }]
          );
        }),
      },
      workers: jest.fn().mockReturnValue({
        update: jest.fn().mockResolvedValue({}),
        fetch: jest.fn().mockResolvedValue({
          activityName: "On-call",
        }),
      }),
      fetch: jest.fn().mockResolvedValue(),
      tasks: jest.fn().mockReturnValue({
        fetch: jest.fn().mockResolvedValue({
          attributes: JSON.stringify({
            conference: {
              sid: conferenceSid,
              participants: {
                customer: {
                  callSid: customerCallSid,
                  from: customerCallFrom,
                  to: customerCallTo,
                },
                worker: {
                  callSid: workerCallSid,
                  from: workerCallFrom,
                  to: workerCallTo,
                },
              },
            },
          }),
        }),
        update: jest.fn().mockResolvedValue({}),
        reservations: jest.fn().mockReturnValue({
          update: jest.fn().mockResolvedValue({}),
        }),
      }),
    },
  };

  mockContext.twilioClient.conferences.list = jest
    .fn()
    .mockResolvedValue([
      { sid: conferenceSid, friendlyName: conferenceFriendlyName },
    ]);

  return mockContext;
}

describe("handleParticipantJoin", () => {
  let context;

  beforeEach(() => {
    context = createBaseContext();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should not complete the conference if task is not completed", async () => {
    context.task.assignmentStatus =
      TwilioConstants.TaskAssignmentStatus.Pending;

    await handleParticipantJoin(context);

    expect(context.twilioClient.conferences().update).not.toHaveBeenCalledWith({
      status: TwilioConstants.TaskAssignmentStatus.Completed,
    });
  });

  it("should accept reservation and dial customer in conference if agent is non-SIP", async () => {
    context.params.agentToType = "non-sip";
    context.params.reservationSid = "someReservationSid";
    context.params.customerCallSid = "someCustomerCallSid";
    context.params.FriendlyName = conferenceFriendlyName;

    await handleParticipantJoin(context);

    expect(context.twilioClient.calls).toHaveBeenCalledWith(
      context.params.customerCallSid
    );
    expect(context.twilioClient.calls().update).toHaveBeenCalledWith(
      expect.objectContaining({
        twiml: expect.stringContaining("<Dial><Conference"),
      })
    );
  });

  it("should not accept reservation if agent is external SIP", async () => {
    context.params.agentToType = "sip-external";

    await handleParticipantJoin(context);

    expect(context.twilioClient.calls).not.toHaveBeenCalled();
    expect(context.twilioClient.calls().update).not.toHaveBeenCalled();
  });
});

describe("handleConferenceEnd", () => {
  let context;

  beforeEach(() => {
    context = createBaseContext();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should complete the conference", async () => {
    await handleConferenceEnd(context);

    expect(context.twilioClient.conferences().update).toHaveBeenCalledWith({
      status: TwilioConstants.TaskAssignmentStatus.Completed,
    });
  });

  it("should complete the call if only one participant is left", async () => {
    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest.fn().mockResolvedValue([{ callSid: workerCallSid }]),
      },
      update: jest.fn().mockResolvedValue({}),
    });

    await handleConferenceEnd(context);

    expect(context.twilioClient.calls().update).toHaveBeenCalledWith({
      status: TwilioConstants.CallStatus.Completed,
    });
  });

  it("should not complete the call if more than one participant is left", async () => {
    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest
          .fn()
          .mockResolvedValue([
            { callSid: workerCallSid },
            { callSid: customerCallSid },
          ]),
      },
      update: jest.fn().mockResolvedValue({}),
    });

    await handleConferenceEnd(context);

    expect(context.twilioClient.calls().update).not.toHaveBeenCalledWith({
      status: TwilioConstants.CallStatus.Completed,
    });
  });

  it("should update worker cooldown status if on-call", async () => {
    (context.workspace.workers = jest.fn().mockReturnValue({
      update: jest.fn().mockResolvedValue({}),
      fetch: jest.fn().mockResolvedValue({
        activityName: "On-call",
      }),
    })),
      await handleConferenceEnd(context);

    expect(context.workspace.workers).toHaveBeenCalledWith(
      context.params.WorkerSid
    );
    expect(context.workspace.activities.list).toHaveBeenCalledWith({
      friendlyName: "Cool-Down",
    });
    expect(context.workspace.workers().update).toHaveBeenCalledWith({
      activitySid: cooldownActivitySid,
    });
  });

  it("should not update worker cooldown status if offline", async () => {
    (context.workspace.workers = jest.fn().mockReturnValue({
      update: jest.fn().mockResolvedValue({}),
      fetch: jest.fn().mockResolvedValue({
        activityName: "Offline",
      }),
    })),
      await handleConferenceEnd(context);

    expect(context.workspace.workers).toHaveBeenCalledWith(
      context.params.WorkerSid
    );
    expect(context.workspace.activities.list).not.toHaveBeenCalled();
    expect(context.workspace.workers().update).not.toHaveBeenCalled();
  });

  it("should not update worker if reason is Task TTL Exceeded or hangup or alreadyInCoolDown is set", async () => {
    (context.workspace.workers = jest.fn().mockReturnValue({
      update: jest.fn().mockResolvedValue({}),
      fetch: jest.fn().mockResolvedValue({
        activityName: "On-call",
      }),
    })),
      (context.task.reason = "Task TTL Exceeded");

    await handleConferenceEnd(context);

    expect(context.workspace.workers).toHaveBeenCalledWith(
      context.params.WorkerSid
    );
    expect(context.workspace.activities.list).not.toHaveBeenCalled();
    expect(context.workspace.workers().update).not.toHaveBeenCalled();

    context.task.reason = "hangup";

    await handleConferenceEnd(context);

    expect(context.workspace.workers).toHaveBeenCalledWith(
      context.params.WorkerSid
    );
    expect(context.workspace.activities.list).not.toHaveBeenCalled();
    expect(context.workspace.workers().update).not.toHaveBeenCalled();

    context.task.reason = "";
    context.task.attributes = JSON.stringify({ alreadyInCoolDown: true });

    await handleConferenceEnd(context);

    expect(context.workspace.workers).toHaveBeenCalledWith(
      context.params.WorkerSid
    );
    expect(context.workspace.activities.list).not.toHaveBeenCalled();
    expect(context.workspace.workers().update).not.toHaveBeenCalled();

    context.task.attributes = JSON.stringify({ alreadyInCoolDown: false });

    await handleConferenceEnd(context);

    expect(context.workspace.workers).toHaveBeenCalledWith(
      context.params.WorkerSid
    );
    expect(context.workspace.activities.list).toHaveBeenCalledWith({
      friendlyName: "Cool-Down",
    });
    expect(context.workspace.workers().update).toHaveBeenCalledWith({
      activitySid: cooldownActivitySid,
    });
  });

  it("should mark task as completed when ending an outbound conference", async () => {
    context.task.attributes = JSON.stringify({
      direction: "outbound",
      outboundCustomerCallSid: customerCallSid,
    });

    await handleConferenceEnd(context);

    expect(context.twilioClient.conferences().update).toHaveBeenCalledWith({
      status: TwilioConstants.TaskAssignmentStatus.Completed,
    });

    expect(context.workspace.tasks().update).toHaveBeenCalledWith(
      expect.objectContaining({
        assignmentStatus: TwilioConstants.TaskAssignmentStatus.Canceled,
      })
    );
  });

  it("should not update call status if more than one participant left in an outbound call conference", async () => {
    context.task.attributes = JSON.stringify({
      direction: "outbound",
      outboundCustomerCallSid: customerCallSid,
    });
    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest
          .fn()
          .mockResolvedValue([
            { callSid: workerCallSid },
            { callSid: customerCallSid },
          ]),
      },
      update: jest.fn().mockResolvedValue({}),
    });

    await handleConferenceEnd(context);

    expect(
      context.twilioClient.calls(customerCallSid).update
    ).not.toHaveBeenCalledWith({
      status: TwilioConstants.CallStatus.Completed,
    });
  });
});

describe("handleParticipantLeave", () => {
  let context;

  beforeEach(() => {
    context = createBaseContext();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should complete the call if only one participant is left and no transfer is in progress", async () => {
    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest.fn().mockResolvedValue([{ callSid: workerCallSid }]),
      },
      update: jest.fn().mockResolvedValue({}),
    });

    await handleParticipantLeave(context);

    expect(context.twilioClient.calls().update).toHaveBeenCalledWith({
      status: TwilioConstants.CallStatus.Completed,
    });
  });

  it("should not update worker activity if worker is still in the call", async () => {
    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest.fn().mockResolvedValue([{ callSid: workerCallSid }]),
      },
      update: jest.fn().mockResolvedValue({}),
    });

    await handleParticipantLeave(context);

    expect(context.workspace.workers().update).not.toHaveBeenCalled();
  });
});

describe("handleConferenceStart", () => {
  let context;

  beforeEach(() => {
    const mockCallInfo = {
      [customerCallSid]: {
        sid: customerCallSid,
        from: "FROM_CUSTOMER",
        to: "TO_CUSTOMER",
        status: "in-progress",
      },
      [workerCallSid]: {
        sid: workerCallSid,
        from: "FROM_WORKER",
        to: "TO_WORKER",
        status: "in-progress",
      },
    };
    context = createBaseContext();
    context.twilioClient.calls = jest.fn((callSid) => ({
      fetch: jest.fn().mockResolvedValue(mockCallInfo[callSid]),
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should update task attributes with conference details", async () => {
    await handleConferenceStart(context);

    const expectedAttributes = {
      direction: "inbound",
      conference: {
        sid: conferenceSid,
        participants: {
          customer: {
            callSid: "CALL321",
            from: "FROM_CUSTOMER",
            to: "TO_CUSTOMER",
          },
          worker: {
            callSid: "CALL123",
            from: "FROM_WORKER",
            to: "TO_WORKER",
          },
        },
      },
    };

    expect(context.workspace.tasks().update).toHaveBeenCalledWith({
      attributes: JSON.stringify(expectedAttributes),
    });
  });

  it("should update task attributes with conference details for outbound call with outboundCustomerCallSid", async () => {
    context.task.attributes = JSON.stringify({
      direction: "outbound",
      outboundCustomerCallSid: customerCallSid,
    });

    await handleConferenceStart(context);

    const expectedAttributes = {
      direction: "outbound",
      conference: {
        sid: conferenceSid,
        participants: {
          customer: {
            callSid: customerCallSid,
            from: "FROM_CUSTOMER",
            to: "TO_CUSTOMER",
          },
          worker: {
            callSid: workerCallSid,
            from: "FROM_WORKER",
            to: "TO_WORKER",
          },
        },
      },
      outboundCustomerCallSid: customerCallSid,
    };

    const actualAttributes = JSON.parse(
      context.workspace.tasks().update.mock.calls[0][0].attributes
    );
    expect(actualAttributes).toEqual(expectedAttributes);
  });

  it("should correctly identify the worker and customer participants", async () => {
    await handleConferenceStart(context);

    const callUpdateArgs = context.workspace.tasks().update.mock.calls[0][0];
    const updatedAttributes = JSON.parse(callUpdateArgs.attributes);

    expect(updatedAttributes.conference.participants.customer.callSid).toBe(
      "CALL321"
    );
    expect(updatedAttributes.conference.participants.worker.callSid).toBe(
      "CALL123"
    );
  });

  it("should not update task attributes if no customer call is found in task attributes", async () => {
    context.task.attributes = JSON.stringify({
      direction: "inbound",
      // Deliberately missing conference and outboundCustomerCallSid to trigger the error
    });

    await handleConferenceStart(context);

    expect(context.workspace.tasks().update).not.toHaveBeenCalled();
  });
});

describe("updateComputedCallDuration", () => {
  let context;
  const workerDuration = 300;
  const customerDuration = 200;

  beforeEach(() => {
    context = createBaseContext();
    context.twilioClient.calls = jest.fn().mockImplementation((callSid) => ({
      fetch: jest.fn().mockImplementation(() => ({
        duration: callSid === workerCallSid ? workerDuration : customerDuration,
      })),
    }));
  });

  it("should set the correct duration when the recording exists on twilioClient", async () => {
    await updateComputedCallDuration(
      context.twilioClient,
      context.params,
      JSON.parse(context.task.attributes)
    );
    expect(context.params.callDuration).toEqual(RecordingDuration);
  });

  it("should set the correct duration when both worker and customer calls exist and no recording exists on twilioClient", async () => {
    context.twilioClient.recordings.list = jest.fn().mockResolvedValue([]);
    await updateComputedCallDuration(
      context.twilioClient,
      context.params,
      JSON.parse(context.task.attributes)
    );
    expect(context.params.callDuration).toEqual(customerDuration);
  });

  it("should set the correct duration when only the worker call exists and no recording exists on twilioClient", async () => {
    context.twilioClient.recordings.list = jest.fn().mockResolvedValue([]);
    context.task.attributes = JSON.stringify({
      direction: "inbound",
      conference: {
        participants: {
          worker: {
            callSid: workerCallSid,
          },
        },
      },
    });
    await updateComputedCallDuration(
      context.twilioClient,
      context.params,
      JSON.parse(context.task.attributes)
    );
    expect(context.params.callDuration).toEqual(workerDuration);
  });

  it("should set the correct duration when only the customer call exists", async () => {
    context.twilioClient.recordings.list = jest.fn().mockResolvedValue([]);
    context.task.attributes = JSON.stringify({
      direction: "inbound",
      conference: {
        participants: {
          customer: {
            callSid: customerCallSid,
          },
        },
      },
    });
    await updateComputedCallDuration(
      context.twilioClient,
      context.params,
      JSON.parse(context.task.attributes)
    );
    expect(context.params.callDuration).toEqual(customerDuration);
  });

  it("should handle the case when neither worker nor customer call exist", async () => {
    context.twilioClient.recordings.list = jest.fn().mockResolvedValue([]);
    context.task.attributes = JSON.stringify({
      direction: "inbound",
      conference: {
        participants: {
          // No worker or customer call
        },
      },
    });
    await updateComputedCallDuration(
      context.twilioClient,
      context.params,
      JSON.parse(context.task.attributes)
    );
    expect(context.params.callDuration).toEqual(0);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
describe("getConferenceByFriendlyName", () => {
  let twilioClient;

  beforeEach(() => {
    twilioClient = {
      conferences: {
        list: jest.fn().mockResolvedValue([]),
      },
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return null if no conference found", async () => {
    const friendlyName = "TestConference";
    const result = await getConferenceByFriendlyName(
      twilioClient,
      friendlyName
    );
    expect(result).toBeNull();
    expect(twilioClient.conferences.list).toHaveBeenCalledWith({
      friendlyName,
      limit: 1,
    });
  });

  it("should return the first conference if found", async () => {
    const friendlyName = "TestConference";
    const conference = { sid: conferenceSid, friendlyName: "TestConference" };
    twilioClient.conferences.list.mockResolvedValueOnce([conference]);
    const result = await getConferenceByFriendlyName(
      twilioClient,
      friendlyName
    );
    expect(result).toEqual(conference);
    expect(twilioClient.conferences.list).toHaveBeenCalledWith({
      friendlyName,
      limit: 1,
    });
  });
});
describe("writeConferenceParticipantDetailsToTaskAttributes", () => {
  let context;

  beforeEach(() => {
    context = createBaseContext();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should update task attributes with conference details", async () => {
    context.params.FriendlyName = conferenceFriendlyName;
    context.params.direction = "inbound";
    context.params.call_sid = customerCallSid;

    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest
          .fn()
          .mockResolvedValue([
            { callSid: customerCallSid },
            { callSid: workerCallSid },
          ]),
      },
    });
    context.twilioClient.conferences.list = jest
      .fn()
      .mockResolvedValue([
        { sid: conferenceSid, friendlyName: conferenceFriendlyName },
      ]);

    context.twilioClient.calls = jest
      .fn()
      .mockImplementationOnce(() => ({
        fetch: jest.fn().mockResolvedValueOnce({
          sid: customerCallSid,
          from: customerCallFrom,
          to: customerCallTo,
        }),
      }))
      .mockImplementationOnce(() => ({
        fetch: jest.fn().mockResolvedValueOnce({
          sid: workerCallSid,
          from: workerCallFrom,
          to: workerCallTo,
        }),
      }));

    const updatedTaskAttributes =
      await writeConferenceParticipantDetailsToTaskAttributes(context);

    expect(context.twilioClient.calls).toHaveBeenCalledWith(customerCallSid);
    expect(context.twilioClient.calls).toHaveBeenCalledWith(workerCallSid);

    expect(updatedTaskAttributes).toEqual({
      conference: {
        sid: conferenceSid,
        participants: {
          customer: {
            callSid: customerCallSid,
            from: customerCallFrom,
            to: customerCallTo,
          },
          worker: {
            callSid: workerCallSid,
            from: workerCallFrom,
            to: workerCallTo,
          },
        },
      },
    });
  });

  it("should handle missing customer call in task attributes", async () => {
    context.params.FriendlyName = conferenceFriendlyName;
    context.params.direction = "inbound";
    context.task.attributes = JSON.stringify({});

    await expect(
      writeConferenceParticipantDetailsToTaskAttributes(context)
    ).rejects.toThrowError("No customer call found in task attributes");
  });

  it("should handle outbound call with outboundCustomerCallSid", async () => {
    context.params.FriendlyName = conferenceFriendlyName;
    context.params.outboundCustomerCallSid = customerCallSid;

    context.twilioClient.conferences = jest.fn().mockReturnValue({
      participants: {
        list: jest
          .fn()
          .mockResolvedValue([
            { callSid: customerCallSid },
            { callSid: workerCallSid },
          ]),
      },
    });
    context.twilioClient.conferences.list = jest
      .fn()
      .mockResolvedValue([
        { sid: conferenceSid, friendlyName: conferenceFriendlyName },
      ]);

    context.twilioClient.calls = jest
      .fn()
      .mockImplementationOnce(() => ({
        fetch: jest.fn().mockResolvedValueOnce({
          sid: customerCallSid,
          from: customerCallFrom,
          to: customerCallTo,
        }),
      }))
      .mockImplementationOnce(() => ({
        fetch: jest.fn().mockResolvedValueOnce({
          sid: workerCallSid,
          from: workerCallFrom,
          to: workerCallTo,
        }),
      }));

    const updatedTaskAttributes =
      await writeConferenceParticipantDetailsToTaskAttributes(context);

    expect(context.twilioClient.calls).toHaveBeenCalledWith(customerCallSid);
    expect(context.twilioClient.calls).toHaveBeenCalledWith(workerCallSid);

    expect(updatedTaskAttributes).toEqual({
      conference: {
        sid: conferenceSid,
        participants: {
          customer: {
            callSid: customerCallSid,
            from: customerCallFrom,
            to: customerCallTo,
          },
          worker: {
            callSid: workerCallSid,
            from: workerCallFrom,
            to: workerCallTo,
          },
        },
      },
    });
  });

  it("should handle missing customer call in outbound call scenario", async () => {
    context.params.FriendlyName = conferenceFriendlyName;
    context.task.attributes = JSON.stringify({});

    await expect(
      writeConferenceParticipantDetailsToTaskAttributes(context)
    ).rejects.toThrowError("No customer call found in task attributes");
  });
});
