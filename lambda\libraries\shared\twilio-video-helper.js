const TwUtils = require("../../routes/utils/twillio");

async function completeVideoRoom(twilio, taskAttribs, data) {
  const roomSid = taskAttribs.room_sid;
  if (roomSid) {
    const room = await twilio.video.v1.rooms(roomSid).fetch();
    if (room.status != "completed") {
      try {
        await twilio.video.v1.rooms(room.sid).update({ status: "completed" });
      } catch (e) {
        console.error(e, new Error().stack);
        console.error("Request data ", data);
      }
    }
  }
  await TwUtils.update_callcener_task(data);
}

module.exports = {
  completeVideoRoom
};