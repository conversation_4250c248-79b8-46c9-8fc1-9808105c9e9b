<?php

use Tests\Unit\AbstractUnitTest;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use Elasticsearch\Common\Exceptions\BadRequest400Exception;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;

include_once './app/models/CallHistory.php';

class CallHistoryTest extends AbstractUnitTest
{
    private $idsQueryMock;
    private $callHistoryMock;
    private $elasticsearchClientMock;
    private $getAccountCallsParams = [
            'accountId' => 1,
            'startDate' => '2023-01-01',
            'endDate' => '2023-12-31',
            'page' => 1,
            'perPage' => 20,
            'orderBy' => 'date',
            'order' => 'ASC',
            'locationId' => null,
            'callType' => null,
            'leadId' => null,
            'employeeId' => null,
            'adId' => null,
            'customerId' => null,
            'customerName' => null,
            'callNumber' => null,
            'duration' => null,
            'durationVal' => null,
            'callDuration' => null,
            'callDurationVal' => null,
            'recordingDuration' => null,
            'recordingDurationVal' => null,
            'customerEmail' => null,
            'logId' => null,
            'includeNeighborLocations' => null,
            'recordingUrlCheck' => false,
            'hallOfFameCheck' => false,
            'channelType' => ''
        ];

    protected function setUp(): void
    {
        parent::setUp();
        $this->elasticsearchClientMock = Mockery::mock(\Elasticsearch\Client::class)->makePartial();
        $this->idsQueryMock = $this->createMock(IdsQuery::class);
        $this->callHistoryMock = $this->getMockBuilder(CallHistory::class)
                                  ->onlyMethods(['search', 'getIndex', 'getConnection'])
                                  ->getMock();
        $this->callHistoryMock->method('getConnection')->willReturn($this->elasticsearchClientMock);
    }

    private function getAccountCallsResult($params)
    {
        return $this->callHistoryMock->getAccountCalls(
            $params['accountId'],
            $params['startDate'],
            $params['endDate'],
            $params['page'],
            $params['perPage'],
            $params['orderBy'],
            $params['order'],
            $params['locationId'],
            $params['callType'],
            $params['leadId'],
            $params['employeeId'],
            $params['adId'],
            $params['customerId'],
            $params['customerName'],
            $params['callNumber'],
            $params['duration'],
            $params['durationVal'],
            $params['callDuration'],
            $params['callDurationVal'],
            $params['recordingDuration'],
            $params['recordingDurationVal'],
            $params['customerEmail'],
            $params['logId'],
            $params['includeNeighborLocations'],
            $params['recordingUrlCheck'],
            $params['hallOfFameCheck'],
            $params['channelType']
        );
    }

    public function test_GivenExistingCallHistoryId_WhenFindByIdCalled_ItShouldReturnExpectedResult(): void
    {
        // Arrange
        $expectedResult = ['hits' => ['total' => 1, 'hits' => [['id' => 'test_id']]]];
        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $response = $this->callHistoryMock->findById('test_id', null, $this->idsQueryMock);
        
        // Assert
        $this->assertEquals($expectedResult, $response);
    }

    public function test_GivenNonExistentCallHistoryId_WhenFindByIdCalled_ItShouldReturnEmptyResult(): void
    {
        // Arrange
        $emptyResult = ['hits' => ['total' => 0, 'hits' => []]];
        $this->callHistoryMock->method('search')->willReturn($emptyResult);

        // Act
        $response = $this->callHistoryMock->findById('non_existent_id', null, $this->idsQueryMock);

        // Assert
        $this->assertEquals($emptyResult, $response);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingFindByCurrentPreviousMonthIndex_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->callHistoryMock->findByCurrentPreviousMonthIndex($params['accountId'], $params['startDate'], $params['endDate']);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }
    public function test_GivenValidAccountCallsParams_WhenExecutingFindByCurrentPreviousMonthIndexWithNoResults_ItShouldReturnEmptyResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $expectedResult = [
            'hits' => [
                'total' => 0,
                'hits' => []
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->callHistoryMock->findByCurrentPreviousMonthIndex($params['accountId'], $params['startDate'], $params['endDate']);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCalls_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCallsWithSpecificCallType_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $params['callType'] = 'inbound';
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                            'call_type' => 'inbound'
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCallsWithRecordingUrlCheck_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $params['recordingUrlCheck'] = true;
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                            'recording_url' => 'http://example.com/recording.mp3'
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCallsWithHallOfFameCheck_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $params['hallOfFameCheck'] = true;
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                            'halloffame' => 1
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCallsWithCustomerEmail_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $params['customerEmail'] = '<EMAIL>';
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                            'customer_email' => '<EMAIL>'
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCallsWithSpecificCallTypeUnprocessed_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $params = $this->getAccountCallsParams;
        $params['callType'] = 'unprocessed';
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********',
                            'call_type' => 'unprocessed'
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }
    
    public function test_GivenValidAccountCallsParams_WhenExecutingGetAccountCallsWithCallNumber_ItShouldReturnExpectedResults(): void{
        // Arrange
        $params = $this->getAccountCallsParams;
        $params['callNumber'] = '**********';
        $expectedResult = [
            'hits' => [
                'total' => 1,
                'hits' => [
                    [
                        '_source' => [
                            'account_id' => $params['accountId'],
                            'datestamp' => '2023-06-15',
                            'call_number' => '**********'
                        ]
                    ]
                ]
            ]
        ];

        $this->callHistoryMock->method('search')->willReturn($expectedResult);

        // Act
        $result = $this->getAccountCallsResult($params);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenAccountIdAndLocation_WhenFetchingUnprocessedCount_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $accountId = 1;
        $locations = [101, 102];
        $interval = 2;
        $in = 'years';

        $expectedResult = [
            [
                'key' => 101,
                'doc_count' => 5
            ],
            [
                'key' => 102,
                'doc_count' => 3
            ]
        ];

        $this->elasticsearchClientMock->shouldReceive('search')->andReturn([
            'aggregations' => [
                'location' => [
                    'buckets' => $expectedResult
                ]
            ]
        ]);

        // Act
        $result = $this->callHistoryMock->fetchUnprocessedCount($accountId, $locations, $interval, $in);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenAccountIdAndLocation_WhenFetchingUnprocessedCountAndNoResult_ItShouldReturnEmptyArray(): void
    {
        // Arrange
        $accountId = 1;
        $locations = [101, 102];
        $interval = 2;
        $in = 'years';

        $expectedResult = [];

        $this->elasticsearchClientMock->shouldReceive('search')->andReturn([
            'aggregations' => [
                'location' => [
                    'buckets' => []
                ]
            ]
        ]);

        // Act
        $result = $this->callHistoryMock->fetchUnprocessedCount($accountId, $locations, $interval, $in);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenAccountIdAndLocation_WhenFetchingUnprocessedCountAndException_ItShouldReturnErrorResponse(): void
    {
        // Arrange
        $accountId = 1;
        $locations = [101, 102];
        $interval = 2;
        $in = 'years';

        $expectedResult = [
            'error' => true,
            'code' => 400,
            'message' => 'Bad Request'
        ];

        $this->elasticsearchClientMock->shouldReceive('search')->andThrow(
            new BadRequest400Exception('Bad Request', 400)
        );

        // Act
        $result = $this->callHistoryMock->fetchUnprocessedCount($accountId, $locations, $interval, $in);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    public function test_GivenIdAndVersion_WhenUpdatingByIdAndVersion_ItShouldReturnExpectedResults(): void
    {
        // Arrange
        $id = 'test_id';
        $data = [
            'version_id' => 1,
            'field1' => 'value1',
            'field2' => 'value2'
        ];

        $expectedResult = [
            '_index' => 'test-index',
            '_type' => '_doc',
            '_id' => $id,
            '_version' => 1,
            'result' => 'updated'
        ];

        $this->elasticsearchClientMock->shouldReceive('index')->andReturn($expectedResult);

        // Act
        $result = $this->callHistoryMock->updateByIdAndVersion($id, $data, 'parent_id');

        // Assert
        $this->assertEquals($expectedResult, $result);
    }
    public function test_GivenIdAndVersion_WhenUpdatingByIdAndVersionAndException_ItShouldReturnEmptyResult(): void
    {
        // Arrange
        $id = 'test_id';
        $data = [
            'version_id' => 1,
            'field1' => 'value1',
            'field2' => 'value2'
        ];

        $this->elasticsearchClientMock->shouldReceive('index')->andThrow(
            new BadRequest400Exception('Bad Request', 400)
        );

        // Act
        $result = $this->callHistoryMock->updateByIdAndVersion($id, $data, 'parent_id');

        // Assert
        $this->assertEquals([], $result);
    }

    

    public function test_GivenLocationId_WhenPrepareLocationInnerQueryCalled_ItShouldReturnExpectedBoolQuery(): void
    {
        // Arrange
        $locationId = 101;
        $expectedBoolQuery = new BoolQuery();
        $expectedBoolQuery->addParameter('minimum_should_match', 1);
        $expectedBoolQuery->add(new TermQuery('location_id', $locationId), BoolQuery::SHOULD);
        $expectedBoolQuery->add(new TermQuery('neighbor_location_id', $locationId), BoolQuery::SHOULD);

        $reflection = new \ReflectionClass($this->callHistoryMock);
        $method = $reflection->getMethod('prepareLocationInnerQuery');
        $method->setAccessible(true);

        // Act
        $result = $method->invoke($this->callHistoryMock, $locationId);

        // Assert
        $this->assertEquals($expectedBoolQuery->toArray(), $result->toArray());
    }

    public function test_GivenMultipleLocationIds_WhenPrepareLocationInnerQueryCalled_ItShouldReturnExpectedBoolQuery(): void
    {
        // Arrange
        $locationIds = [101, 102];
        $expectedBoolQuery = new BoolQuery();
        $expectedBoolQuery->addParameter('minimum_should_match', 1);
        $expectedBoolQuery->add(new TermsQuery('location_id', $locationIds), BoolQuery::SHOULD);
        $expectedBoolQuery->add(new TermsQuery('neighbor_location_id', $locationIds), BoolQuery::SHOULD);

        $reflection = new \ReflectionClass($this->callHistoryMock);
        $method = $reflection->getMethod('prepareLocationInnerQuery');
        $method->setAccessible(true);

        // Act
        $result = $method->invoke($this->callHistoryMock, $locationIds);

        // Assert
        $this->assertEquals($expectedBoolQuery->toArray(), $result->toArray());
    }
     
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

}
