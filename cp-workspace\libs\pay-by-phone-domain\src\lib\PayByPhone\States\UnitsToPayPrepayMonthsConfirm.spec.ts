import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { UnitsToPayPrepayMonthsConfirm } from './UnitsToPayPrepayMonthsConfirm';
import { Locale } from '@cp-workspace/shared';

describe('UnitsToPayPrepayMonthsConfirm', () => {
  let unitsToPayPrepayMonthsConfirm: UnitsToPayPrepayMonthsConfirm;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UnitsToPayPrepayMonthsConfirm],
    }).compile();

    unitsToPayPrepayMonthsConfirm = module.get<UnitsToPayPrepayMonthsConfirm>(UnitsToPayPrepayMonthsConfirm);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.UnitsToPayPrepayMonthsConfirm,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should return next state as UnitsToPayPrepayMonthsPrompt when no response from customer', async () => {
      context.request.Digits = undefined;

      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrepayMonthsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrepayMonthsPrompt);
    });

    it('should return next state as UnitsToPayPrepayMonthsPrompt when the customer chooses to change selection', async () => {
      context.request.Digits = '2';

      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrepayMonthsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrepayMonthsPrompt);
    });

    it('should proceed to GetSavedCards when customer confirms the payment process', async () => {
      context.request.Digits = '1';

      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrepayMonthsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.GetSavedCards);
    });
  });
});
