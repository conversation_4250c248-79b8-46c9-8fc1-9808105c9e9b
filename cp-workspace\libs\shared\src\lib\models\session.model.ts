import { User, AdminUser } from './user.model';
export type CreateSessionRequest = {
    user_id: number;
};

export type Session = {
    user: User;
    adminUser: AdminUser;
    user_data: string;
    last_activity: number;
    session_id: string; //auth token
    ip_address: string;
    account_id: number;
    previous_session_id: string;
    admin: boolean;
    static: boolean;
    vlogin: boolean;
    cp_session: string;
    currentUser: User;
    user_agent: string;
};