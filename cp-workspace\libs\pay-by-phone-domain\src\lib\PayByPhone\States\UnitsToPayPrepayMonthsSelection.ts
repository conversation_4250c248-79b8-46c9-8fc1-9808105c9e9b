import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class UnitsToPayPrepayMonthsSelection extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;
    const customerResponse = context.request.Digits;
    const noResponseFromCustomer = !customerResponse || customerResponse.length === 0;
    const zeroResponse = customerResponse && customerResponse === '0';
    const shouldAskForPrePayAgain = noResponseFromCustomer || zeroResponse;
    if(shouldAskForPrePayAgain) {
      return { nextState: PayByPhoneState.UnitsToPayPrepayMonthsPrompt };
    }

    storage.prepayMonths = parseInt(customerResponse);
    storage.totalAmountDue = await this.services.integrationService.calculateAmountDue(storage.selectedUnits!, storage.prepayMonths);
      
    twilioResponse.gatherWithLocaleSay({
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.prepay-selection',
      locale: storage.locale,
      i18nOptions: { args: [{ numPayments: customerResponse }, { prepayAmountDue: (storage.totalAmountDue + storage.convenienceFee).toFixed(2) }] }
    }]);
    
    return { nextState: PayByPhoneState.UnitsToPayPrepayMonthsConfirm };
  }
}
