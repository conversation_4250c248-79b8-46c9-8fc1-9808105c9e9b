<?php
/**
 * Trackcallcentertasks
 *
 * @category Trackcallcentertasks
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\Controllers\BaseController;
use CallPotential\CPCommon\Controllers\MysqlControllerTrait;
use CallPotential\CPCommon\Controllers\SessionTrait;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\HttpStatusCode;

/**
 * Swagger content
 *
 * @SWG\Definition(type="object",definition="TrackcallcentertasksList",
 * @SWG\Property(
 *     property="items",
 *    type="array",
 * @SWG\Items(ref="#/definitions/Trackcallcentertasks")
 *   ),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData")
 * )
 */

/**
 * Class TrackcallcentertasksController
 *
 * @category TrackcallcentertasksController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class TrackcallcentertasksController extends BaseController
{
    use MysqlControllerTrait, SessionTrait;
    /**
     * Filters to apply while querying for data that typecast fields values passed in GET action
     *
     * @var array
     */
    public $listFilters = [
        "task_sid" => "string",
        "log_id"   => "int",
    ];

    /**
     * Per page records
     *
     * @var int
     */
    protected $perPage = 100;

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    public function getModelName(): string
    {
        return 'TrackCallcenterTasks';
    }

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     Associative array of POST body / query parameters in key value pair
     * @param mixed $parentId Parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($parentId);

        $modelName = $this->getModelName();
        $model = new $modelName();
        $data = $this->preProcessPostData($data);
        $model->assign($data);
        if ($model->save()) {
            return  $model->toArray();
        }

        return false;
    }

    /**
     * Intermediate function to prepare data for update action
     *
     * @param mixed $id       Primary key of database table / es index
     * @param array $data     Associative array of POST body / query parameters in key value pair
     * @param mixed $parentId Parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($parentId);
        $modelName = $this->getModelName();
        $model = $modelName::findFirst($id);
        $data = $this->preProcessPutData($data, $model->toArray());
        $model->assign($data);
        if ($model->save()) {
            return $model->toArray();
        }

        return false;
    }

    /**
     * Define required parameters for different action to manage validation centrally
     *
     * @return array
     */
    public function requiredRequestParams(): array
    {
        $action = $this->dispatcher->getActionName();
        switch ($action) {
            case 'create':
            case 'save':
                $params = [
                    'task_sid' => ['required' => true],
                ];
                break;
            default:
                $params = [];
                break;
        }

        return $params;
    }

    /**
     * Swagger content
     *
     * @SWG\Get(
     *     tags={"Track Callcenter Tasks"},
     *     path="/trackcallcentertasks/{id}",
     *     description="Returns an trackcallcentertasks record on a single ID",
     *     summary="get trackcallcentertasks record",
     *     operationId="TrackCallcenterTasksGetById",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *         type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of record to fetch",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *        type="integer"
     *     ),
     * @SWG\Response(
     *         response=200,
     *         description="trackcallcentertasks response",
     *         @SWG\Schema(ref="#/definitions/Trackcallcentertasks")
     *     ),
     *     @SWG\Response(
     *         response="401",
     *         description="Not Authorized",
     *         @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),
     *     @SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     *         @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),
     *     @SWG\Response(
     *         response="404",
     *         description="Not Found",
     *         @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),
     *     @SWG\Response(
     *         response="500",
     *         description="unexpected error",
     *         @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */
    /**
     * Get resource detail by id
     *
     * @return \Phalcon\Http\Response
     */
    public function getAction(): \Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Format List Response internally call formatGetResponse to change o/p of each item in list
     *
     * @param array $data mixed Associative array of POST body / query parameters in key value pair
     *
     * @return mixed
     */
    public function formatListResponse($data)
    {
        for ($x = 0; $x < count($data); $x++) {
            $data[$x] = $this->formatGetResponse($data[$x]);
        }

        return parent::formatListResponse($data);
    }

    /**
     * Swagger content
     *
     * @SWG\Get(
     *     tags={"Track Callcenter Tasks"},
     *     path="/trackcallcentertasks",
     *     description="Returns all trackcallcentertasks records from the table",
     *     summary="list trackcallcentertasks",
     *     operationId="ListTrackCallcenterTasks",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *     ),
     * @SWG\Parameter(
     *     description="Page Number",
     *     format="int64",
     *     in="query",
     *     name="page",
     *     required=false,
     *     type="integer"
     *     ),
     * @SWG\Parameter(
     *     name="perpage",
     *     in="query",
     *     description="maximum number of results to return",
     *     required=false,
     *     type="integer",
     *     format="int32"
     *    ),
     * @SWG\Parameter(
     *     name="filterTask_sid",
     *     in="query",
     *     description="filter on task sid",
     *     required=false,
     *     type="string"
     *     ),
     * @SWG\Parameter(
     *     name="filterLog_id",
     *     in="query",
     *     description="filter on call log id",
     *     required=false,
     *     type="string"
     *     ),
     * @SWG\Response(
     *     response=200,
     *     description="trackcallcentertasks response",
     *     @SWG\Schema(ref="#/definitions/TrackcallcentertasksList")
     *     ),
     * @SWG\Response(
     *     response="403",
     *     description="Not Authorized Invalid or missing Authorization header",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),
     * @SWG\Response(
     *     response="401",
     *     description="Not Authorized",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),
     * @SWG\Response(
     *     response=500,
     *     description="unexpected error",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */
    /**
     * List all trackcallcentertasks
     *
     * @return \Phalcon\Http\Response
     */
    public function listAction()
    {
        if ($this->validSession()) {
            $taskSid = $this->request->getQuery('filterTask_sid', null, null);
            $logId = $this->request->getQuery('filterLog_id', null, null);
            if ($taskSid || $logId) {
                return parent::listAction();
            } else {
                return $this->sendForbidden();
            }
        } else {
            return $this->sendAccessDeniedResponse();
        }
    }

    /**
     * Swagger content
     *
     * @SWG\Put(path="/trackcallcentertasks/{id}",
     *   tags={"Track Callcenter Tasks"},
     *   summary="Update an trackcallcentertasks record",
     *   description="Update existing trackcallcentertasks record",
     *   operationId="UpdateTrackcallcentertasks",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     description="ID of Trackcallcentertasks",
     *     in="path",
     *     name="id",
     *     required=true,
     *     type="integer",
     *     format="int64"
     *   ),
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Trackcallcentertasks record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/Trackcallcentertasks")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/Trackcallcentertasks")
     *   ),
     * @SWG\Response(
     *     response="200",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/Trackcallcentertasks")
     *   ),
     * @SWG\Response(
     *     response="400",
     *     description="Invalid data supplied",@SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),
     * @SWG\Response(
     *     response="403",
     *     description="Not Authorized Invalid or missing Authorization header",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),
     * @SWG\Response(
     *     response="404",
     *     description="ID Not Found",@SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),
     * @SWG\Response(
     *     response="500",
     *     description="unexpected error",@SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */
    /**
     * PUT action for trackcallcentertasks
     *
     * @return \Phalcon\Http\Response
     */
    public function saveAction(): \Phalcon\Http\Response
    {
        if ($this->validSession()) {
            return parent::saveAction();
        } else {
            return $this->sendAccessDeniedResponse();
        }
    }

    /**
     * Swagger content
     *
     * @SWG\Post(path="/trackcallcentertasks",
     *   tags={"Track Callcenter Tasks"},
     *   summary="Create a new trackcallcentertasks record",
     *   description="create new trackcallcentertasks record",
     *   operationId="CreateTrackcallcentertasks",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     *   @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Trackcallcentertasks record",
     *     required=false,
     *     @SWG\Schema(ref="#/definitions/Trackcallcentertasks")
     *   ),
     *   @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     *   @SWG\Response(
     *     response="201",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/Trackcallcentertasks")
     *   ),
     *   @SWG\Response(
     *     response="400",
     *     description="Invalid data supplied", @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),
     *   @SWG\Response(
     *     response=500,
     *     description="unexpected error", @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),
     *   @SWG\Response(
     *     response="403",
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */
    /**
     * POST action for trackcallcentertasks
     *
     * @return \Phalcon\Http\Response
     */
    public function createAction(): \Phalcon\Http\Response
    {
        if ($this->validSession()) {
            return parent::createAction();
        } else {
            return $this->sendAccessDeniedResponse();
        }
    }

    /**
     * DELETE trackcallcentertasks record from database
     *
     * @return \Phalcon\Http\Response
     */
    public function deleteAction(): \Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data Associative array of POST body / query parameters in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return $this->validSession();
    }

    /**
     * Determine is user has delete access to the data
     *
     * @param array $data Associative array of POST body / query parameters in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);

        return false;
    }

    /**
     * Determine is user has write access to the data
     *
     * @param array $data Associative array of POST body / query parameters in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return $this->validSession();
    }
}
