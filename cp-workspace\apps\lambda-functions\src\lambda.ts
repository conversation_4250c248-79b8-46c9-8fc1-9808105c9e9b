import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { ExpressAdapter } from '@nestjs/platform-express';
import express from 'express';
import * as awsServerlessExpress from 'aws-serverless-express';
import { Con<PERSON>, Handler } from 'aws-lambda';
import * as config from './config.js';
import { AsyncWorkersIntegrationService } from '@cp-workspace/shared';
import { PayByPhoneController } from './app/PayByPhone/PayByPhone.controller';
import { AsyncTestController } from './app/AsyncTest/AsyncTest.controller';
import { BugsnagService } from '@cp-workspace/shared';

let cachedServer;
let cachedApp;


const bootstrapLambdaServer = async () => {
  if (!cachedServer) {
    const expressApp = express();
    const adapter = new ExpressAdapter(expressApp);
    const app = await NestFactory.create(AppModule, adapter);

    const bugsnagService = app.get(BugsnagService);
    const middleware = bugsnagService.getExpressMiddleware();
    expressApp.use(middleware.requestHandler);
    expressApp.use(middleware.errorHandler);

    /**
     * The global prefix is set to match the API Gateway stage name.
     */
    const globalPrefix = `${config['env']}-communications-api`;
    app.setGlobalPrefix(globalPrefix);

    await app.init();
    cachedApp = app;
    cachedServer = awsServerlessExpress.createServer(expressApp);
  }
  return { server: cachedServer, app: cachedApp };
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const lambdaHandler = async (event: any, context: Context): Promise<Handler> => {
  /**
  * Initialize the service layer for the lambda function.  Also, the lambda
  * server app is cached so that it can be reused for subsequent invocations.
  */
  const { server, app } = await bootstrapLambdaServer();
  const bugsnagService = app.get(BugsnagService);

  bugsnagService.leaveBreadcrumb('Lambda handler invoked', { eventSource: event.source });

  if (isKeepWarmEvent(event)) {
    /**
    * If this is a keep warm event, we want to return immediately.
    */
    bugsnagService.leaveBreadcrumb('Keep warm event detected');
    return;
  }

  if (event.source === 'async-invocation') {
    bugsnagService.leaveBreadcrumb('Processing async-invocation');
    try {
      return (await app.get(PayByPhoneController).handlePayByPhoneAsyncInvocation(event)) as Handler;
    } catch (error) {
      bugsnagService.notify(error);
      throw error;
    }
  }

  if (event.source === 'async-invocation-test') {
    /**
    * This is a way to test the async worker invocation and get a log message
    * that displays the exact event that was passed to the lambda.
    */
    bugsnagService.leaveBreadcrumb('Processing async-invocation-test');
    try {
      const testController = app.get(AsyncTestController);
      /**
      * Proxy the HTTP event to the server
      */
      return (await testController.handleAsyncTestInvocation(event)) as Handler;
    } catch (error) {
      bugsnagService.notify(error);
      throw error;
    }
  }

  AsyncWorkersIntegrationService.workerFunctionName = context.invokedFunctionArn;

  bugsnagService.leaveBreadcrumb('Proxying HTTP request to Express');
  return awsServerlessExpress.proxy(server, event, context, 'PROMISE').promise;
};

export const handler = async (event: any, context: Context) => {
  const { app } = await bootstrapLambdaServer();
  const bugsnagService = app.get(BugsnagService);
  const bugsnagHandler = bugsnagService.getLambdaHandler();
  return bugsnagHandler(lambdaHandler)(event, context);
};

/**
 * This lambda function is deployed using the same CDK stack and lambda template
 * as all other lambda functions.  In those functions, there is an expectation
 * that the function will be invoked on a cron schedule with a "keep warm"
 * event.  This is simply an event that gives us an opportunity to instantiate
 * the service code so that its warm and ready for use.
 *
 * We don't actually want to do any service work with this event. Instead, we want
 * to simply return.  The following function uses the same logic as the other
 * lambda functions to determine if the event is a "keep warm" event.
 *
 * @param event
 * @returns
 */
const isKeepWarmEvent = (event) => {
  return event.source === 'aws.events';
};