"use strict";
const AWS = require('aws-sdk');
const _ = require('lodash');
const Twilio = require('twilio');
const TwUtils = require('../routes/utils/twillio');
const redisUtils = require('../routes/utils/redis');
const config = require('../config/config');
const console_c = require('../config/logger').console;
const redis = require('redis');
const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
const acctTable = config.dynamodb.acctTable;
const SipCallHelper = require('./sip-call.js'); // NOTE: WIP -- need to rename this file
const AccountHelper = require('./shared/twilio-account-helper.js'); 
const DynamoHelper = require('./shared/dynamo-helper.js'); 
const TwilioTaskHelper = require('./shared/twilio-task-helper.js'); 
const TwilioVoiceHelper = require('./shared/twilio-voice-helper.js'); 
const TwilioTaskMapHelper = require('./shared/twilio-taskmap-helper.js'); 
const TwilioVideoHelper = require('./shared/twilio-video-helper.js'); 
const TwilioSyncMap = require('./shared/twilio-syncmap-helper.js'); 
const { getTwilioClient } = require('../libraries/common-methods.js');

Array.prototype.forEachAsync = async function (fn) {
    for (let t of this) { await fn(t) }
}
module.exports = async (req, res) => {

  const event = _.pick(req.body, [
    'AccountSid',
    'EventDescription',
    'EventType',
    'Reason',
    'ReservationSid',
    'ResourceSid',
    'ResourceType',
    'Sid',
    'TaskAge',
    'TaskAssignmentStatus',
    'TaskAttributes',
    'TaskChannelSid',
    'TaskChannelUniqueName',
    'TaskPriority',
    'TaskQueueName',
    'TaskQueueSid',
    'TaskQueueTargetExpression',
    'TaskSid',
    'Timestamp',
    'WorkerActivityName',
    'WorkerActivitySid',
    'WorkerAttributes',
    'WorkerName',
    'WorkerPreviousActivityName',
    'WorkerPreviousActivitySid',
    'WorkerSid',
    'WorkerTimeInPreviousActivity',
    'WorkflowName',
    'WorkflowSid',
    'WorkspaceName',
    'WorkspaceSid',
  ]);

  const eventResponse = () => res.type('json').status(204).json();
  const xmlResponse = () => res.type('xml').send('<Response>Success</Response>');

  console.debug('WORKSPACE EVENT: ', event.EventType, event.ResourceSid, {
    query: req.query,
    params: req.params,
    body: req.body,
  });
  console_c.log('QUERY', req.query);
  console_c.log('PARAMS', req.params);
  console_c.log('BODY', req.body);

  var dynamoQueryParameters = {
    TableName: acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: "account_sid = :sid",
    ExpressionAttributeValues: {
      ":sid": event.AccountSid
    }
  };

  switch (event.EventType) {

    case 'workflow.entered': {
      const taskAttribs = JSON.parse(req.body.TaskAttributes);
      if (taskAttribs.queue_name === '_location-call' && taskAttribs.taskchannel === 'custom3') {

        console_c.log('LOCATION CALL WORKFLOW.ENTERED');
        console_c.log('TASK ATTRIBUTES', taskAttribs);

        try {
          let dynamoData = await dynamo.query(dynamoQueryParameters).promise();

          if (!dynamoData.Items || (dynamoData.Items && !dynamoData.Items.length)) {
            console.error("Account not found: dynamoQueryParameters", JSON.stringify(dynamoQueryParameters))
          } else {
            const item = dynamoData.Items[0];
            let customerCallSid = taskAttribs.call_sid;
            let workerSid = req.body.WorkerSid;
            let clientSync = new Twilio(item.account_sid, item.authtoken);

            const response = new Twilio.twiml.VoiceResponse;

            //CP-17446 and CP-17503
            let dialNumberInfo = taskAttribs.dial.number;

            if (dialNumberInfo.webhook_integration === 'Fonality' 
              || dialNumberInfo.webhook_integration === 'FonalityDirect' 
              || dialNumberInfo.webhook_integration === 'RingCentral') {

              let callerId = taskAttribs.caller_id.replace('+', '');
              let locationPhones = taskAttribs.call_destination.split(',');

              //to-from
              let redis_key = `webhook_${taskAttribs.sip_extension}_${callerId}`;
              if (dialNumberInfo.webhook_integration === 'RingCentral') {
                redis_key = `webhook_${locationPhones[0]}_${taskAttribs.tracking_no.replace('+', '')}`;
                if (locationPhones[0].indexOf('@') != -1) {
                  redis_key = `webhook_${locationPhones[0]}_${callerId}`;
                }
              }
              const conferenceStatusUrl = config.call_url + `location_call/conference_call/${customerCallSid}/?task_sid=${event.TaskSid}`;
              const callStatusUrl = config.call_url + `location_call/fonality_call_status/${customerCallSid}/?task_sid=${event.TaskSid}&redis_key=${redis_key}`;
              const waitUrl = 'https://twimlets.com/holdmusic?Bucket=com.twilio.music';

              const recordOption = taskAttribs.dial.dial_options.record;
              const customerRedirectUrl = config.call_url +`location_call/customer_connect/${customerCallSid}/?record_option=${recordOption}`;

              let twilioAccount = await dynamo.query(dynamoQueryParameters).promise();
              twilioAccount = twilioAccount.Items[0];
              const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);

              let conferenceOptions = {
                label: 'webhookParticipant',
                to: locationPhones[0],
                from: taskAttribs.tracking_no, //customer number
                conferenceStatusCallback: conferenceStatusUrl,
                conferenceStatusCallbackEvent: 'start join leave end',
                statusCallback: callStatusUrl,
                waitUrl: `${waitUrl}.ambient`,
                startConferenceOnEnter: false,
                endConferenceOnExit: false,
                timeout: taskAttribs.max_wait_time,
              };

              if (conferenceOptions.to.indexOf('@') != -1) {
                conferenceOptions.from = taskAttribs.caller_id;
                conferenceOptions.to = `sip:${locationPhones[0]}`;
              }

              var locationCallSids = [];
              const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
              let redisTtl = 60;
              await redisClient.connect();

              //create conf
              let participantConfCall = await twilio.conferences(customerCallSid)
                .participants.create(conferenceOptions);

              console.debug("Ringcentral/Fonality participantConfCall "+locationPhones[0], participantConfCall);

              if (participantConfCall) {
                let dialInfo = { 
                  'customer_call_sid' : customerCallSid,
                  'conference_sid' : participantConfCall.conferenceSid,
                  'webhook_integration' : dialNumberInfo.webhook_integration,
                  'customer_connect_url': customerRedirectUrl,
                  'location_redirect_url': customerRedirectUrl,
                  'account_sid' : twilioAccount.account_sid,
                  'twilio_authtoken' : twilioAccount.authtoken,
                  'location_call_sid' : participantConfCall.callSid,
                  'redirected' : 'false',
                  'moveLocationNextStep' : 'false'
                }
                
                await redisClient.set(redis_key, JSON.stringify(dialInfo), {EX: redisTtl});
                await redisClient.set(redis_key + "_extended",
                  JSON.stringify(dialInfo), {EX: redisTtl + 60});
                await redisClient.set("conference_sid_" + customerCallSid, 
                  participantConfCall.conferenceSid, {EX: 300});
                await redisClient.set("webhook_key_" + customerCallSid,
                  redis_key, {EX: redisTtl});
                locationCallSids.push(participantConfCall.callSid);
              }

              //remove first number from locations phones as it will be ringcnetral/fonality
              locationPhones.shift();

              //add remaining location numbers in same conference
              await locationPhones.forEachAsync(async (number) => {
                conferenceOptions.to = number;
                conferenceOptions.label = "participant_"+number
                conferenceOptions.statusCallbackEvent = ['completed', 'answered'];

                if (conferenceOptions.to.indexOf('@') != -1) {
                  conferenceOptions.from = taskAttribs.caller_id;
                  conferenceOptions.to = `sip:${number}`;
                } else {
                  conferenceOptions.from = taskAttribs.tracking_no;
                }

                let participantConfCall = await twilio.conferences(customerCallSid)
                .participants.create(conferenceOptions);

                locationCallSids.push(participantConfCall.callSid);
              });

              //set locations phone numbers in redis
              await redisClient.set("location_call_sids_"+customerCallSid, JSON.stringify(locationCallSids), {EX: 300});

              await redisClient.disconnect();

            } else {
              const dial = response.dial({
                  action: taskAttribs.dial.dial_options.action + `&WorkerSid=${workerSid}&locationTaskSid=${req.body.TaskSid}&queueSid=${req.body.TaskQueueSid}&Customer=${customerCallSid}&WorkspaceSid=${req.body.WorkspaceSid}&locationId=${taskAttribs.location_id}`,
                  timeout: taskAttribs.dial.dial_options.timeout,
                 'ringTone': 'us',
                  record: (taskAttribs.dial.dial_options.record == "record-from-answer-dual") ? "record-from-answer-dual" : 0,
              });
  
              let location_phones = taskAttribs.call_destination.split(',');
  
              location_phones.forEach((number) => {

                if (number.indexOf('@') != -1) {
  
                  if (!number.toLowerCase().startsWith('sip:')){
                    number = `sip:${number}`;
                  }
  
                  if (taskAttribs.dial.number.number_options.username) {
                    dial.sip({
                        username: taskAttribs.dial.number.number_options.username,
                        password: taskAttribs.dial.number.number_options.password
                    }, number);
                  } else {
                    dial.sip(number);
                  }
                } else {
                  dial.number(number);
                }
              });
  
              await clientSync
                .calls(customerCallSid)
                .update({
                  twiml: response.toString(),
                })
                .then(async () => {
                  taskAttribs.worker_sid = workerSid;
  
                  await clientSync.taskrouter
                    .workspaces(req.body.WorkspaceSid)
                    .tasks(req.body.TaskSid)
                    .update({
                      attributes: JSON.stringify(taskAttribs)
                    })
                    .catch((e) => {
                      console.log(e, new Error().stack);
                    })
                })
                .catch(e => {
                    console.log(e, new Error().stack);
                });
            }
          }
        } catch (error) {
          console.log(error, new Error().stack);
        }
      }
      return xmlResponse();
    }
      
    case 'task.canceled': {
      const taskAttribs = JSON.parse(req.body.TaskAttributes);
      let twilioAccount = await TwUtils.getAccountFromSid(event.AccountSid);
      if (taskAttribs.taskchannel !== 'voice') {
        if (taskAttribs.taskchannel === 'video') {
          const twilio = await getTwilioClient(twilioAccount.account_sid, twilioAccount.authtoken);
          await TwilioVideoHelper.completeVideoRoom(twilio, taskAttribs, event);
        }
      } else {
        await TwUtils.make_leave_queue_request(event);
      }
    
      let result = await TwUtils.remove_sync_task_map(twilioAccount, event);
      return res.type('xml').send(result);
    }

    case 'worker.activity.update': {
      await TwUtils.worker_activity_update_request(event);
      try {
        let dynamoData = await dynamo.query(dynamoQueryParameters).promise();
        if (!dynamoData.Items || (dynamoData.Items && !dynamoData.Items.length)) {
          console.error("Account not found: dynamoQueryParameters", JSON.stringify(dynamoQueryParameters))
        } else {
          await TwilioSyncMap.updateWorkerActivityMap(dynamoData, event);
        }
      } catch (error) {
        console.error(error, new Error().stack);
      }
      return xmlResponse();
    }
      
    case 'task.created': {
      let taskAttributes = JSON.parse(event.TaskAttributes);
      const taskchannel = (taskAttributes.taskchannel) ? taskAttributes.taskchannel.toLowerCase() : '';
      if (taskchannel === 'voice' || taskchannel === 'custom3') {
        let redis_key = `g5_${taskAttributes.call_sid}`;
        let redisTtl = 600;

        if (taskchannel === 'custom3') {
          redis_key = `locationCall_${taskAttributes.call_sid}`;
        }
  
        // Used to find task from callSid
        // Used in location_call.js and queue-manage.js and g5 update lambda
        await redisUtils.set_redis_data(redis_key, redisTtl, event.TaskSid);
      }

      await TwUtils.insert_task_activity(event);

      let twilioAccount = await TwUtils.getAccountFromSid(event.AccountSid);

      let result = await TwUtils.sync_task_map(twilioAccount, event);

      return res.type('xml').send(result);
    }

    case 'reservation.created': {
      await TwUtils.insert_task_activity(event);

      const twilioAccount = await AccountHelper.getTwilioAccount(dynamoQueryParameters);
      const context = {
        accountSid: event.AccountSid,
        twilioAccount: twilioAccount,
        twilioClient: new Twilio(twilioAccount.account_sid, twilioAccount.authtoken),
        redisClient: redis.createClient({'url': `redis://${config.redis.host}`}),
        reservationData: event,
        taskAttributes: JSON.parse(event.TaskAttributes),
        workerAttributes: JSON.parse(event.WorkerAttributes),
      };

      const isTaskEnded = await TwilioTaskHelper.isTaskEnded(context.twilioClient, event.WorkspaceSid, event.TaskSid);
      if (!isTaskEnded) {

        await context.redisClient.connect();


        if (context.reservationData.WorkerSid) {
          await SipCallHelper.writeWorkerSidToTaskAttributes(context);
        }
      
        if (SipCallHelper.agentConnectedBySip(context)) {
        
          await SipCallHelper.createSipCallConference(context);

        }

        await context.redisClient.disconnect();

      }
        
      return eventResponse();

    }

    case 'reservation.rejected': {
      await TwUtils.insert_task_activity(event);

      // Using this event to end the voip/phone call to agent
      // because reservation is rejected
      const taskAttributes = JSON.parse(event.TaskAttributes);
      const workerAttributes = JSON.parse(event.WorkerAttributes);

      const taskchannel = (taskAttributes.taskchannel) ? taskAttributes.taskchannel.toLowerCase() : '';
      const contactURI = (workerAttributes.contact_uri) ? workerAttributes.contact_uri.toLowerCase() : '';

      // if not voip or phone leave
      if (taskchannel !== 'voice' || contactURI.includes('client:agent')) {
        return xmlResponse();
      }

      // reservation has been accepted by other agent, so hangup this reservation voip/phone call
      let twilioAccount = await dynamo.query(dynamoQueryParameters).promise();
      twilioAccount = twilioAccount.Items[0];
      const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();

      let call_key = 'voipPhoneSid-' + event.ReservationSid;
      const callSid = await redisClient.get(call_key);
      await redisClient.disconnect();
      if (callSid) {
        await twilio.calls(callSid)
          .update({status: 'completed'})
          .catch((e) => {
            console.error(e, new Error().stack);
          });
      }

      return xmlResponse();
    }

    case 'reservation.canceled':
    case 'reservation.rescinded': {
      await TwUtils.insert_task_activity(event);
      let twilioAccount = await AccountHelper.getTwilioAccount(dynamoQueryParameters);
      const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();
      
      await TwilioVoiceHelper.handleVoiceChannelCancellation(redisClient, twilio, event);
      
      await redisClient.disconnect();

      return xmlResponse();
    }

    case 'reservation.timeout': {
      await TwUtils.insert_task_activity(event);
      let twilioAccount = await AccountHelper.getTwilioAccount(dynamoQueryParameters);
      const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();
    
      await TwilioVoiceHelper.handleVoiceChannelTimeout(redisClient, twilio, event);
    
      await redisClient.disconnect();

      return xmlResponse();
    }

    case 'reservation.accepted': {
      await TwUtils.insert_task_activity(event);
    
      const taskAttributes = JSON.parse(event.TaskAttributes);
      const taskchannel = (taskAttributes.taskchannel) ? taskAttributes.taskchannel.toLowerCase() : '';
    
      if (taskchannel === 'voice' || taskchannel === 'video') {
        const workerAttributes = JSON.parse(event.WorkerAttributes);
        await DynamoHelper.updateCallData(taskAttributes, workerAttributes, config);
    
        let twilioAccount = await TwUtils.getAccountFromSid(event.AccountSid);
        let result = await TwilioTaskMapHelper.syncTaskMap(twilioAccount, event);
    
        return res.type('xml').send(result);
      }
    
      return xmlResponse();
    }
    case 'task.deleted':
    case 'task.updated': {
      let twilioAccount = await TwUtils.getAccountFromSid(event.AccountSid);
      const result = await TwilioTaskMapHelper.syncTaskMapAndRespond(twilioAccount, event);
      return res.type("xml").send(result);
    }
      
    case 'task.wrapup': {
      const { TaskSid, TaskAssignmentStatus, AccountSid } = event;
      
      let acct = await DynamoHelper.getAccountDetail(AccountSid);
      let twilioAccount = await TwUtils.getAccountFromSid(event.AccountSid);
      const twilioClient = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
      const workspace = twilioClient.taskrouter.workspaces(acct.workspace_sid);
      
      try {
        // Update task to completed
        await TwilioTaskHelper.updateTwilioTask(workspace, TaskSid, TaskAssignmentStatus);
      } catch (error) {
        // Handle case where task is already completed or can't be completed
        console.log(`Error completing task ${TaskSid}: ${error.message}`);
        // Continue processing even if task update fails
      }
      
      const result = await TwilioTaskMapHelper.syncTaskMapAndRespond(twilioAccount, event);
      return res.type("xml").send(result);
    }
    
    case 'task.completed': {
      let twilioAccount = await TwUtils.getAccountFromSid(event.AccountSid);
      const result = await TwilioTaskMapHelper.removeSyncTaskMapAndRespond(twilioAccount, event);
      return res.type("xml").send(result);
    }
      
    case 'task-queue.entered':
    case 'task-queue.moved':
    case 'task-queue.timeout': {
      return xmlResponse();
    }
    default: {
      return xmlResponse();
    }
  }
};
