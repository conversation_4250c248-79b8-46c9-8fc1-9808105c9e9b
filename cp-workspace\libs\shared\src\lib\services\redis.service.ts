import { Inject, Injectable } from "@nestjs/common";
import { RedisClientInitialization, IoRedisProvider } from "./redis-ioredis.provider";
import { Redis, RedisKey } from "ioredis";

@Injectable()
export class RedisService {
  public static ioredis: IoRedisProvider;
  public static client: unknown;
  private initialized = false;
  get isInitialized(): boolean {
    return this.initialized;
  }

  static async stop() {
    if (RedisService.ioredis) {
      await RedisService.ioredis.stopClient();
    }
  }

  constructor(@Inject('IoRedisProvider') private readonly ioredis: IoRedisProvider) {}

  initialize(options?: RedisClientInitialization): unknown {
    RedisService.ioredis = this.ioredis;
    RedisService.client = this.ioredis.getClient(options);
    this.initialized = true;
    return RedisService.client;
  }

  getClient(): unknown | undefined {
    if (!RedisService.client) {
      throw new Error('RedisService not initialized. Call initialize() first.');
    }
    return RedisService.client;
  }

  async get(key: RedisKey): Promise<string | null> {
    const redis = RedisService.client as Redis;
    return await redis.get(key);
  }

  async set(key: RedisKey, value: string | number | Buffer, secondsToken: "EX", seconds: string | number): Promise<void> {
    const redis = RedisService.client as Redis;
    await redis.set(key, value, secondsToken, seconds);
  }

  async del(...args: RedisKey[]): Promise<number> {
    const redis = RedisService.client as Redis;
    return await redis.del(args);
  }

}
