import { RedisService } from "../services/redis.service";
export interface HasRedisService {
    redisService: RedisService;
}

export function Cacheable(expirationTimeInSeconds: number) {
    return (target: HasRedisService, propertyName: string, descriptor: PropertyDescriptor) => {
        const originalMethod = descriptor.value;
        descriptor.value = async function (...args: any[]) {
            const cacheKey = `${propertyName}-${JSON.stringify(args)}`;
            const redis = target.redisService?.getClient() as any;
            if (!redis) {
                return await originalMethod.apply(this, args);
            }

            try {
                const cachedResult = await redis.get(cacheKey);
                if (cachedResult) {
                    return JSON.parse(cachedResult);
                }
            } catch (error) {
                console.error('Error accessing Redis', error);
            }

            const result = await originalMethod.apply(this, args);
            try {
                await redis.set(cacheKey, JSON.stringify(result), 'EX', expirationTimeInSeconds);
            } catch (error) {
                console.error('Error saving data to Redis', error);
            }
            return result;
        };
    };
}
