<?php
namespace Test;

class SwaggerPascleCaseValidateTest extends IntegrationCase
{
    public function testSwaggerPascleCase()
    {
        $this->isLocalScan = true;
        $this->checkPascleCase('call');
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }
}
