import { <PERSON>, <PERSON>, Req, BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { AsyncWorkersService } from '@cp-workspace/shared';

@Controller('async-test')
export class AsyncTestController {
  constructor(private workerService: AsyncWorkersService) {}

  @Post()
  async handleAsyncTestRequest(@Req() request: Request) {
    if (!request.body) {
      throw new BadRequestException('Bad Request');
    }

    await this.workerService.invokeAsyncWorker({
      source: 'async-invocation-test',
      body: request.body
    });

    return request.body;
  }

  async handleAsyncTestInvocation(event: any): Promise<boolean> {
    console.log("handleAsyncTestInvocation called", event);
    return true;
  }
  
}
