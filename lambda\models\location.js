'use strict';

var config = require('../config/config');
const cpapiClient = require('../libraries/cpapi-client');
const TwilioVoiceHelper = require("../libraries/shared/twilio-voice-helper.js");
const DynamoHelper = require('../routes/shared/dynamo-helper');
const redis = require("redis");
var twilio = require('twilio');

Array.prototype.forEachAsync = async function (fn) { 
  for (let t of this) { await fn(t) }
}

var LocationModel = class LocationModel {

  async get_location_by_id(location_id) {
    const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
    return await locClient.getLocation(location_id);
  }

  async get_inbound_route_info(call_number, account_id, callRouteId) {
    const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
    const acctClient = new cpapiClient.acctClient(config.db.serviceTokens.readWrite);

    let trackingNumber = await acctClient.cache.getData(
      `trackingnumber?filterActive=true&filterCall_number=${call_number}&filterUser_id=${account_id}`
      );
    trackingNumber = trackingNumber['items'][0].trackingnumber;

    let locData = await locClient.getLocation(trackingNumber.location_id);
    let locConfigData = await locClient.getLocationConfiguration(trackingNumber.location_id);

    let callRouteConfig = {};
    if (!callRouteId) {
      if (trackingNumber.call_route_config_id && trackingNumber.call_route_config_id > 0) {
        callRouteId = trackingNumber.call_route_config_id;
      } else {
        callRouteId = locConfigData.call_route_config_id; 
      }
    }

    callRouteConfig = await acctClient.cache.getData(`callroute/${callRouteId}`);

    return {...trackingNumber, ...locData, ...locConfigData, callRouteConfig};
  }

  async redirect_call(acct_data, call_sid, redirect_url) {  
    const client = new twilio(acct_data.account_sid, acct_data.twilio_authtoken);

    try {
      await client.calls(call_sid).update({
        url: redirect_url,
        method: "POST"
      });
      return true;
    } catch(error) {
      console.error("Error in webhook call redirect fonality ", error, redirect_url, call_sid);
      return false;
    }
  }

  /**
   * This function returns the call details stored in the redis cache for a given
   * external SIP call along with the key and type of call (location or agent)
   */
  async getSipExternalCallDetails(data) {
    const redis_client = redis.createClient({'url': `redis://${config.redis.host}`});
    await redis_client.connect();

    const callDetails = {
      type: 'unknown',
      key: '',
      data: {}
    };

    /**
     * The way that we will determine the type of call is by looking in redis cache
     * for a key that matches the following pattern:
     *  agentSIP-<caller_id>-<callee-extension>
     * 
     * If this key exists, then we know that this is a call to a call center agent.
     * Otherwise, we will assume that this is a call to a location.
     */

    let agentCallKey = `agentSIP-${data.callNumber.toString()}-${data.phone.toString()}`;

    const agentData = await redis_client.get(agentCallKey);
    if (agentData && Object.keys(agentData).length !== 0) {
      callDetails.type = 'agent';
      callDetails.key = agentCallKey;
      callDetails.data = JSON.parse(agentData);
    } else {
      const locationCallKey = "webhook_" + data.phone.toString() + "_" + data.callNumber.toString();
      const locationData = await redis_client.get(locationCallKey);
      if (locationData && Object.keys(locationData).length !== 0) {  
        callDetails.type = 'location';
        callDetails.key = locationCallKey;
        callDetails.data = locationData;
      }
    }

    await redis_client.disconnect();

    return callDetails;
  }

  /**
   * We have a single webhook for handling external SIP call events to both
   * call center agents and locations.  This function will determine which
   * type of SIP call this is and execute the appropriate handler.
   */
  async webhook_call_redirect(data) {

    const callDetails = await this.getSipExternalCallDetails(data);
    if (callDetails.type === 'agent') {
      return this.handleAgentSipCall(callDetails);
    } else if (callDetails.type === 'location') {
      return this.handleLocationSipCall(callDetails);
    } else {
      return this.handleUnknownSipCall();
    }

  }

  async handleLocationSipCall(callDetails) {
    const redis_client = redis.createClient({'url': `redis://${config.redis.host}`});
    await redis_client.connect();

    //to-from
    var redis_key = callDetails.key;
    let redis_data = callDetails.data;
    var twilio_response = new twilio.twiml.VoiceResponse();

    console.debug("Fonality webhook redis data", redis_data);

    if (redis_data && Object.keys(redis_data).length !== 0) {
      redis_data =  JSON.parse(redis_data); 
  
      if (redis_data.customer_call_sid && redis_data.redirected === 'false') {
        redis_data.redirected = 'true';
        await redis_client.set(redis_key, JSON.stringify(redis_data), 300);

        const twilioClient = new twilio(redis_data.account_sid, redis_data.twilio_authtoken);

        let otherLocationsCallSids = JSON.parse(await redis_client.get("location_call_sids_"+redis_data.customer_call_sid));

        //remove first callsid from array as it will for RC/Fonality
        otherLocationsCallSids.shift();
        
        if (otherLocationsCallSids) {
          await otherLocationsCallSids.forEachAsync(async (callSid) => {
            //end other participant calls
            try {
                await twilioClient.calls(callSid)
                  .update({status: 'completed'})
                  .catch((e) => {
                    console.error(e, new Error().stack);
                  });
            } catch (error) {
              console.debug("Participant remove error", error)
            }
          })
        }

        let redirectStatus = await this.redirect_call(
          redis_data,
          redis_data.customer_call_sid,
          redis_data.customer_connect_url
        );

        if (!redirectStatus) {
          twilio_response.say('The customer caller is no longer in queue');
          twilio_response.hangup();

          let twiml_redirect_url = 'https://twimlets.com/echo?Twiml='
            + encodeURIComponent(twilio_response.toString());

          await this.redirect_call(
            redis_data,
            redis_data.location_call_sid,
            twiml_redirect_url
          );
        }
        await redis_client.disconnect();
        return twilio_response;
      } else {
        twilio_response.say('Already redirected');
        twilio_response.hangup();

        await redis_client.disconnect();
        return twilio_response;
      }
    }

    twilio_response.say('The caller is no longer in queue');
    twilio_response.hangup();

    // If key has expired, then search for extended key to fetch call info
    // which can be used to redirect the call to play twiml message.
    let redis_data_extended = await redis_client.get(redis_key + '_extended');

    if (redis_data_extended && Object.keys(redis_data_extended).length !== 0) {
      redis_data_extended = JSON.parse(redis_data_extended);
      if (redis_data_extended) {
        let twiml_redirect_url = 'https://twimlets.com/echo?Twiml='
          + encodeURIComponent(twilio_response.toString());

        await this.redirect_call(
          redis_data,
          redis_data.location_call_sid,
          twiml_redirect_url
        );
      }
    }
    
    await redis_client.disconnect();
    return twilio_response;
  }

  async handleAgentSipCall(callDetails) {

    const { accountSid, taskSid, reservationSid, agentCallSid, customerCallSid } = callDetails.data;
    const acct = await DynamoHelper.getAccountDetail(accountSid);
    const twilioClient = new twilio(acct.account_sid, acct.authtoken);
    const workspace = twilioClient.taskrouter.workspaces(acct.workspace_sid)

    /**
     * Accept the task reservation
     */
    await TwilioVoiceHelper.updateReservationStatus(twilioClient, workspace, taskSid, reservationSid, agentCallSid);

    /**
     * Dial the customer into the conference
     */
    await TwilioVoiceHelper.dialCustomerInConference(twilioClient, customerCallSid, taskSid);

    return new twilio.twiml.VoiceResponse();
  }

  async handleUnknownSipCall() {
    return new twilio.twiml.VoiceResponse();
  }

}


module.exports = {
  'LocationModel': LocationModel
}
