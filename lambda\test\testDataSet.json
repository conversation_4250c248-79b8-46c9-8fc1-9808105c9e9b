{"siteAdmin_values": [3, "<EMAIL>", "Stage Test", "Storage", "", "What is your pets name?", "", "", "Stage Storage", 1, "2017-01-30 15:48:40", "2017-01-30 15:48:40", "", 0, 1, "2017-11-22 09:16:30", "Other", 1, 0, "", "{\"home\":{\"page1\":1,\"page2\":1,\"page3\":1,\"page4\":1,\"page5\":1,\"page6\":1,\"page7\":1,\"page8\":1,\"page9\":1,\"page10\":1,\"page11\":1,\"page12\":1,\"page13\":1,\"page14\":1,\"page15\":1},\"people\":{\"page1\":1,\"page2\":1,\"page3\":1,\"page4\":1,\"page5\":1,\"page6\":1,\"page7\":1},\"leads\":{\"page1\":1,\"page2\":1,\"page3\":1,\"page14\":1,\"page5\":1},\"calls\":{\"page1\":1,\"page2\":1,\"page3\":1,\"page4\":1,\"page5\":1,\"page6\":1},\"grades\":{\"page1\":1,\"page2\":1,\"page3\":1,\"page4\":1,\"page5\":1,\"page16\":1,\"page7\":1,\"page8\":1,\"page9\":1},\"broadcasts\":{\"page1\":1,\"page2\":1,\"page3\":1,\"page4\":1,\"page5\":1,\"page16\":1,\"page7\":1}}", 0, 2590, "America/Chicago", "none", "", 0, 0, 0, 1, 1, "", 1, 0, 0, "offline", "", "", 0, "", "", 0, "{\"custom_variables\":{\"allVariables\":\"\"}}", 1, 100, 100, 100, 1, 0, 0, "0", "0", 0, "", "location", 0, 1, 0, "", "0", 10, 0], "siteAdmin_keys": "user_id, plan_id, email, firstname, lastname, phone, question1, question2, question3,companyname, master, signupdate,lastagreement,agreement_ip,creditamount,active,lastlogin,howheard,confirmed,parent_id,reports,tips,dashboard,default_location,default_timezone,dm_report_period,dm_report_last_update,newlead_email_notify,send_followup_notification,send_collection_notification,optin_mktg,is_test_account,dm_report_send_day,delayed_collection_email_notify,is_agent,is_agent_call_connected,agent_status,inactive_reason,tc_access_token,is_logged_in,scheduled_logout,session_refresh,disable_unsubscribe_txt,custom_variables,show_admin_in_emp_dropdown,employee_restrict_percent,manager_restrict_percent,agent_restrict_percent,enable_console_log,other_option_employee,other_option_required,consolidate_invoices,restrict_manager_edit_employee_grade,is_response_note_enabled,transcribe_api,exclusion_list_type,opt_in_txt,txt_consent,enable_duplicate_lead_email_notification,duplicate_lead_email_notification_setup,restrict_manager_edit_location_grade,kiosk_connection,sms_silence, password, sid, authtoken, agent_call_sid, agent_calling_sid, workspace_sid, worker_sid", "location_keys": "location_id, location_name, email, address1, city, province, postal, country, latitude, longitude, phone, primary_sip_setup, callcard_id, location_gradesheet_id, employee_gradesheet_id, greeting, record_outgoing, whisper, user_id, active, api_type, reserved_vacant, max_reservation_length, last_api_update, rateType, dateCreated, is_mktgsrc_required, is_mktgsrc_editable, is_units_always_rentable, play_music_on_hold, delay, followup_rule_id, hide_phone_number, round_rate, is_inquiry_type_req, timezone, use_daylight_saving, include_all_phone, include_all_email, include_all_text, minimum_balance, reset_collection_on_payment, greeting_type, friendly_name, autopay_text, exclude_payment_link, outbound_phone, is_rate_restricted, is_collection_only, script_callcard_id, call_route_config_id, website, batch_event_count, overide_tracking_number, is_api_call_allowed_threshold_1, card_type, enable_reservation_payment, api_call_frequency, sip_domain_sid, is_source_required, bypass_press_1, enable_transcribe, transcribe_minimum_duration, transcribe_maximum_duration, convenience_fee_employee, convenience_fee_phone, convenience_fee_user, is_available_filter_on, restrict_lead_to_mark_rented, is_pull_reservation_cron_running, paybyphone_all, record_payment_calls, sitelink_pull_units_last_time_tick, save_cc, gate_code, reset_followup_workflow, collection_rule_changed, select_unit_upon_time_vacant, rate_type_cc, rate_type_tc, sl_update_followup_date, movein_failure_template, is_mktg_source_hide, is_inquiry_type_hide, mktg_source_default, inquiry_type_default, esign_email_template, esign_sms_template, allow_prev_cc, txt_to_email, downgrade_reservation, v2_data_sync, is_class_hide, is_class_required, v2_location_call_support, hours_availability, can_rent_reserved_units, record_video_call", "location_values": ["Apartments@Halsted_test", "<EMAIL>", "233 <PERSON>", "Chicago", "IL", "60606", "US", "41.878807", "-87.636005", "6309952010", "", "355", "644", "342", "This call may be recorded.", "1", "0", "121", "1", "0", "1", "30", "0000-00-00 00:00:00", "0", "2015-02-16 19:24:06", "0", "1", "0", "0", "121", "880-student", "1", "0", "1", "America/Chicago", "1", "0", "0", "0", "0", "0", "1", "Apartments @ Halsted_test", "I understand by checking this box the card above will be charged each month, helping me avoid future late fees.", "181", "", "0", "0", "2080", "0", "www.callpotential.com", "0", "13123130368", "0", "script", "0", "5000", "SDcc6618c51830cec01381eb5e65ef84f0", "0", "0", "0", "60", "240", "0", "0", "0", "1", "0", "0", "0", "0", "0", "1", "phone", "0", "0", "0", "0", "0", "1", "0", "0", "0", "0", "0", "0", "0", "1", "0", "0", "1", "1", "1", "1", "'{\"office_hours\":[{\"weekday\":\"Monday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Tuesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Wednesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Thursday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Friday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Saturday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Sunday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0}],\"access_hours\":[{\"weekday\":\"Monday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Tuesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Wednesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Thursday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Friday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Saturday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Sunday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0}]}", "{\"employee\":1,\"agent\":1,\"manager\":1,\"admin\":1,\"kiosk_manager\":1,\"kiosk_unattended\":1}", "1"], "location_registration_values": ["qa-testcase-test", "rendom_string", 135, "", ""], "location_registration_keys": "machine_id, auth_token, location_id, last_login, date_created", "call_log_es": {"log_id": 1, "twilio_id": "CA046b65e58134c5c7ffd54fa1526ad186", "account_id": 121, "call_number": "+***********", "caller_name": "Test", "call_name": "+***********", "datestamp": "2022-11-10 10:10:56", "call_type": "inbound", "call_destination": "**********", "ad_id": -1, "location_id": 135, "current_route_step": "{}", "is_route_complete": 1, "customer_id": "454", "customer_name": "Test", "customer_type": 1, "rollover_index": 0, "lead_id": 0, "employee_id": 0, "answered_by": 1, "is_auto_call": 0}, "call_log_db": {"log_id": 1, "fk_lead_id": 123, "user_id": 121, "recording_sid": "", "caller_name": "", "call_processed_by": "", "recording_url": "", "duration": "121", "rollover_index": 0, "answered_by": "", "employee_id": 0, "grade": "", "halloffame": "", "gradesheet": "", "gradesheet_id": 0, "customer_card": "", "call_status": "", "gradesheet_points_appointed": 0, "gradesheet_points_possible": 0, "manager_score": 0, "confirm_action": "", "confirmed_by": 0, "is_excluded": "", "route_config": "", "is_assigned": 0, "customer_type": "", "customer_name": "", "customer_id": "", "is_auto_call": 0, "neighbor_location_id": 0, "version_id": 0, "twilio_id": "calltestingtwilio", "account_id": "121", "call_number": "+***********", "call_name": "+***********", "datestamp": "2022-11-10 10:10:56", "call_type": "inbound", "call_destination": "+***********", "ad_id": 0, "location_id": 135, "current_route_step": "{}", "is_route_complete": 1, "call_duration": 46, "timetolive": 121}, "call_history": {"id": "testcase-callhistory-121-135", "log_id": 1, "twilio_id": "test-CA5d18d2dd0b34055080f08f5561d0a594", "recording_sid": "", "call_number": "+************", "caller_name": null, "call_name": "+**************", "datestamp": "2022-11-09 15:48:42", "call_type": "inbound", "call_processed_by": "0", "recording_url": "", "call_destination": "*************", "answered_by": 0, "ad_id": 1, "location_id": 135, "employee_id": 121, "grade": "U", "halloffame": 0, "gradesheet": "", "gradesheet_id": 0, "customer_card": "", "call_status": 1, "gradesheet_points_appointed": 0, "gradesheet_points_possible": 0, "manager_score": 0, "confirm_action": "", "confirmed_by": 0, "is_excluded": 0, "customer_type": 1, "customer_name": "test case", "customer_id": "121", "is_auto_call": 0, "neighbor_location_id": 0, "version_id": ***********, "call_duration": 10, "ad_name": "Google 2", "employee_name": "test case", "es_tenant_id": "", "recording_duration": 10, "_lastupdate": **********, "queue_time": 0, "num_events": 0, "num_rollovers": 0, "num_agents": 0, "num_queues": 0, "agent_queue": 0, "agent_answered": 0, "lead_id": 1, "es_lead_id": "test-CCTT-Demo-d500f76a36840aea5ef25bb", "account_id": 121, "duration": 10, "rollover_index": 0}, "tracking_number": {"trackingnumber_id": 1, "location_id": 135, "record": 1, "ad_id": 1, "call_number": "+***********", "user_id": 121, "active": 1, "twilio_phone_sid": "", "dateCreated": "2020-06-28 01:01:01", "dateRemoved": "", "type": "local", "archived_by": 0, "exclude_number": 0, "call_route_config_id": 0, "callcard_id": 0, "webhook_params": "[]", "webhook_url": ""}, "call_route": {"user_id": 121, "config": "testcase-config-121-135", "name": "test case", "cascade_time": 0, "is_active": 1, "is_default": 0, "workflow_sid": "testcase-workflow-121-135", "created_date": "2020-11-09 12:12:12", "updated_date": "2020-11-09 12:12:12"}, "twilio_account": {"id": 121, "location_id": 135, "account_sid": "**********************************", "active": 1, "api_key": "**********************************", "api_secret": "OP7IlpUvcGafoQXXar1lExKlcflPXjr8", "authtoken": "f5fc036b9d541e9f7311584aab673d38", "workspace_sid": "WS56421f171b6a8a0492f420d7f74be83a", "chat_service_sid": "ISa71ce6249b74430f887e1c36ffb6448d", "sync_service_sid": "IS92dd4dbe37ed9f8965d2b5353c90af68", "voice_workflow": "WWd556798622f9047d17476f41d7b92eec", "main_queue": "", "location_call_queue_sid": "WQ029e62e1eaedb2fc329e149c50b6e702", "tr_client_token_url": "", "client_token_url": "", "conference_terminate_url": "http://www.test.com/", "outbound_url": "", "outbound_callback_url": "", "sms_service_sid": "MG869888599a56665775fcbcc66c346341", "sms_queue_sid": "WQd7768a8ae9473eaef0e27246c3bad3b0", "sms_workflow_sid": "WW5f616761ad5cf6d1738fc0004b56d9d2", "chat_workflow_sid": "WW943654ab4f7f58f7c7ba341e70c5c21d", "chat_queue_sid": "WQc84a9ed48302c8b76ce7a651c1424092", "default_channel_sid": "TC70b7e16b17a59562d52cad8a34f3a007", "voice_channel_sid": "TC5778b7cee2315cd40e6afa030a2aac46", "chat_channel_sid": "TCdeb16466b870c7b3705e01e9ac5925b3", "sms_channel_sid": "TCe1863281bf88bae9fc119dd0411b0f17", "video_channel_sid": "TC7457e6b5378d8bc254848db123d31d12", "video_workflow_sid": "WW0e3408380d8fca56837690f0248fa324", "video_default_queue": "WQ00ac3e7e87d4347dd6bbd04ba34bdc83"}}