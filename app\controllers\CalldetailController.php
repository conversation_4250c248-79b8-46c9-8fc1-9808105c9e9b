<?php
/**
 * CalldetailController
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 3/30/17
 * Time: 4:38 PM
 *
 * @category CalldetailController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use \Phalcon\Mvc\Model\Query\Builder;
use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\Controllers;
use Phalcon\Db\Enum;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\EventRouter;

/**
 * Swagger
 *
 * @SWG\Definition(definition="CallDetailItem",type="object",
 *    allOf={@SWG\Schema(ref="#/definitions/CallDetail")},
 * @SWG\Property(property="lead_first_name",type="string"),
 * @SWG\Property(property="lead_last_name",type="string"),
 * @SWG\Property(property="lead_phone",type="string"),
 * @SWG\Property(property="lead_email",type="string"),
 * @SWG\Property(property="lead_qt_rental_type",type="integer"),
 * @SWG\Property(property="lead_inquiry_type",type="integer"),
 * @SWG\Property(property="call_duration",type="integer")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallDetailList",
 * @SWG\Property(
 *     property="items",
 *     type="array",
 * @SWG\Items(ref="#/definitions/CallDetailItem")
 *   ),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallDetailBulkResultItem",
 * @SWG\Property(property="message",type="string"),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallDetail"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallDetailBulkResult",
 * @SWG\Property(property="success",type="array",@SWG\Items(type="number")),
 * @SWG\Property(property="error",type="array",@SWG\Items(ref="#/definitions/CallDetailBulkResultItem"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallDetailBulkResponse",
 * @SWG\Property(property="status",type="string",enum={"OK","ERROR","MIXED"}),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallDetailBulkResult"))
 * )
 */

/**
 * CalldetailController
 *
 * @category CalldetailController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class CalldetailController extends Controllers\BaseController
{
    use Controllers\MysqlControllerTrait, Controllers\SessionTrait;

    /**
     * Constructor
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        $sql = "select call_logs.*,
		            leads.inquiry_type as lead_inquiry_type,
                    leads.qt_rental_type as lead_qt_rental_type,
                    leads.first_name as lead_first_name, leads.last_name as lead_last_name,
		            leads.phone as lead_phone, leads.email as lead_email,
                    call_billing.call_duration
                    from call_logs
                    LEFT JOIN leads ON (call_logs.fk_lead_id=leads.lead_id)
                    LEFT JOIN call_billing ON (call_logs.log_id=call_billing.log_id)";

        if (is_numeric($id) && $id > 0) {
            $sql .= " where call_logs.log_id={$id}";
        } else {
            $sql .= " where call_logs.twilio_id='{$id}'";
        }

        $result = $this->dbLegacy->query($sql);
        $result->setFetchMode(Enum::FETCH_ASSOC);
        $data = $result->fetchAll();
        if (count($data) > 0) {
            $data = $data[0];
        }

        return $data;
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/calldetail/{id}",
     *     description="Returns a calldetail based on a single ID",
     *     summary="get calldetail",
     *     operationId="CalldetailGetById",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *       description="Authorization token",
     *       type="string",
     *       name="Authorization",
     *       in="header",
     *       required=true
     *     ),
     * @SWG\Parameter(
     *       description="ID of calldetail to fetch",
     *       in="path",
     *       name="id",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Response(
     *       response=200,
     *       description="call detail response",
     * @SWG\Schema(ref="#/definitions/CallDetailItem")
     *     ),@SWG\Response(
     *       response="403",
     *       description="Not Authorized Invalid or missing Authorization header"
     *     ),@SWG\Response(
     *       response="404",
     *       description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="500",
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if ($this->validSession()) {
            return parent::getAction();
        } else {
            return $this->sendNotAuthorized();
        }
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/calldetail",
     *     description="Returns call detail listing",
     *     summary="list call details",
     *     operationId="ListCallDetails",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *        description="Authorization token",
     *        type="string",
     *        name="Authorization",
     *        in="header",
     *        required=true
     *     ),
     * @SWG\Parameter(
     *        description="Start Date (yyyy-mm-dd)",
     *        type="string",
     *        name="startDate",
     *        in="query",
     *        required=true
     *     ),
     * @SWG\Parameter(
     *        description="End Date (yyyy-mm-dd)",
     *        type="string",
     *        name="endDate",
     *        in="query",
     *        required=true
     *     ),
     * @SWG\Parameter(
     *        description="List Type",
     *        type="string",
     *        name="listType",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="ignore cache and durable storage",
     *        type="boolean",
     *        name="skipCache",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *        description="use cache and ignore durable storage",
     *        type="boolean",
     *        name="forceCache",
     *        in="query",
     *        required=false
     *     ),
     * @SWG\Parameter(
     *     name="channel",
     *     in="query",
     *     description="filter for channel type",
     *     required=false,
     *     type="string",
     *     enum={"voice", "video"}
     * ),
     * @SWG\Response(
     *        response=200,
     *        description="calldetail response",
     * @SWG\Schema(ref="#/definitions/CallDetailList")
     *     ),@SWG\Response(
     *        response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *        description="Not Authorized Invalid or missing Authorization header"
     *     ),@SWG\Response(
     *        response="401",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *        description="Not Authorized"
     *     ),@SWG\Response(
     *        response=500,
     *        description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }

        $this->infoMessage('list request begin', 'CallDetailController::listAction:'.__LINE__);
        $userId = $this->getCurrentAccountId();
        if (empty($userId)) {
            return $this->sendNotAuthorized();
        }

        $this->addListCondition(
            'CallDetail.datestamp',
            $this->request->getQuery('startDate').' 00:00:00',
            '>='
        );
        $this->addListCondition(
            'CallDetail.datestamp',
            $this->request->getQuery('endDate').' 23:59:59',
            '<='
        );
        if (!$this->isStaticToken($this->authToken)) {
            $this->addListCondition('loc.user_id', $this->getCurrentAccountId());
        }
        switch ($this->request->getQuery('listType')) {
            case 'unprocessed':
                $this->addListCondition('CallDetail.call_type', 'inbound');
                break;
            case 'complete':
                $this->addListCondition('CallDetail.duration', 0, '>');
                $this->addListCondition('CallDetail.call_type', 'inbound', '!=');
                break;
        }

        $channel = $this->request->getQuery('channel', null, 'voice');
        $this->addListCondition('CallDetail.channel', $channel);

        return parent::listAction();
    }


    /**
     * Swagger
     *
     * @SWG\Post(path="/calldetail",
     *   tags={"Call Data"},
     *   summary="Create a calldetail(mysql)",
     *   description="create new calldetail(mysql)",
     *   operationId="CreateCalldetail",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Calldetail record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/CallDetail")
     *   ),@SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/CallDetail")
     *   ),
     * @SWG\Response(
     *     response="400",
     *     description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response=500,
     *     description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *     response="403",
     * @SWG\Schema(ref="#/definitions/ErrorResponse"),
     *     description="Not Authorized Invalid or missing Authorization header"
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }
            $this->infoMessage('create request begin', __METHOD__ . __LINE__);
            $result = parent::createAction();
            $this->infoMessage('create request end', __METHOD__ . __LINE__);

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Put(path="/calldetail/{id}",
     *   tags={"Call Data"},
     *   summary="Update an existing calldetail(mysql)",
     *   description="Update existing calldetail(mysql)",
     *   operationId="UpdateCalldetail",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *     description="mysql call log id",
     *     in="path",
     *     name="id",
     *     required=true,
     *     type="string"
     *   ),
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Calldetail record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/CallDetail")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/CallDetail")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        try {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }
            $this->infoMessage(
                'save request begin',
                'CallHistoryController::saveAction:' . __LINE__
            );
            $result = parent::saveAction();
            $this->infoMessage(
                'save request end',
                'CallHistoryController::saveAction:' . __LINE__
            );

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Swagger
     *
     * @SWG\Delete(
     *     tags={"Call Data"},
     *     path="/calldetail/{id}",
     *     description="deletes a calldetail based on the ID supplied",
     *     summary="delete calldetail",
     *     operationId="DeleteCallDetail",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *         description="Authorization token",
     *         type="string",
     *         name="Authorization",
     *         in="header",
     *         required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of calldetail to delete",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="integer"
     *     ),
     * @SWG\Response(
     *         response=204,
     *         description="call queue deleted",
     * @SWG\Schema(type="null")
     *     ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        try {
            $this->infoMessage(
                'delete request begin for id '.$this->getParamID(),
                'CallDetailController::deleteAction:'.__LINE__
            );
            if (!strlen($this->authToken)) {
                return $this->sendNotAuthorized();
            }
            if (!$this->validSession()) {
                return $this->sendForbidden();
            }
            $result =  parent::deleteAction();
            $this->infoMessage(
                'save request end for id '.$this->getParamID(),
                'CallDetailController::deleteAction:' . __LINE__
            );

            return $result;
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Functions related to Create Item
     *
     * @param array $data     data
     * @param mixed $parentId parent Id
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($parentId);
        $modelObject = $this->getModelObject();
        unset($data['workflow_step']);

        if (isset($data['billingDuration'])) {
            $billingDuration = $data['billingDuration'];
            unset($data['billingDuration']);
        }

        if (isset($data['is_note_added'])) {
            unset($data['is_note_added']);
        }
        // Create object in database
        $modelObject->assign($data);
        if ($modelObject->save()) {
            $result = $modelObject->formatJson();
            if ($data['channel'] === 'video') {
                $result['billingDuration'] = $billingDuration ?? '';
            }

            if (isset($result['duration'])) {
                $this->updateBillingData(0, $result);
            }

            if ($data['channel'] === 'voice') {
                $this->checkCallWebhook($result);
            }

            return $result;
        } else {
            $errors = array();
            foreach ($modelObject->getMessages() as $message) {
                $errors[] = $message->getMessage();
            }
            throw new AppException(implode(', ', $errors));
        }

        return false;
    }

    /**
     * Function to update Item
     *
     * @param mixed $id       item id
     * @param array $data     data
     * @param mixed $parentId parent Id
     *
     * @throws Exception
     * @return mixed
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($parentId);
        $modelObject = $this->getModelObject();

        if (is_numeric($id) && $id > 0) {
            $modelObject = $modelObject::findFirst($id);
        } else {
            $modelObject = $modelObject::findFirst("twilio_id = '{$id}'");
        }

        // Create object in database
        $modelObject->assign($data);
        if ($modelObject->save()) {
            return $modelObject->formatJson();
        } else {
            $errors = array();
            foreach ($modelObject->getMessages() as $message) {
                $errors[] = $message->getMessage();
            }
            throw new AppException(array_pop($errors));
        }

        return false;
    }

    /**
     * Execute eventhandler calls
     *
     * @param array $data     request data
     * @param array $original original data
     *
     * @return bool
     */
    protected function executeEventHandlers(array $data, array $original): bool
    {
        $locClient = ClientFactory::getLocClient($this->getRequestAuthToken());
        $locationDetail = (array) $locClient->getLocationConfigurationDetails(
            $original['location_id'],
            'false'
        );
        $accountId = $locationDetail['user_id'];

        $data['recording_sid'] = $data['recording_sid'] ?? $original['recording_sid'];
        $data['call_type'] = $data['call_type'] ?? $original['call_type'];
        $data['duration'] = $data['duration'] ?? $original['duration'];
        $data['twilio_id'] = $data['twilio_id'] ?? $original['twilio_id'];
        $data['location_id'] = $original['location_id'];
        $data['log_id'] = $original['log_id'];

        if (isset($data['duration'])) {
            $this->updateBillingData($accountId, $data);
        }

        $paymentCallTypes = $this->config->application->paymentCallTypes->toArray();

        $callTypesForAuditLog = [
            'inbound_lead',
            'inbound_customer',
            'inbound_collection',
            'inbound_autopay',
        ];

        $callTypesForAuditLog = array_merge($callTypesForAuditLog, $paymentCallTypes);

        $addAuditLog = true;

        if (!in_array($data['call_type'], $callTypesForAuditLog)) {
            $addAuditLog = false;
        }

        if ($data['call_type'] === $original['call_type']
            && (string) $data['recording_sid'] === (string) $original['recording_sid']) {
            $addAuditLog = false;
        }

        // Outbound call audit log is added from confernece lambda
        if (strpos($data['call_type'], 'inbound') === false) {
            $addAuditLog = false;
        }

        $deleteRecording = false;
        if (in_array($data['call_type'], $paymentCallTypes) &&
            $locationDetail['record_payment_calls'] !== 1
        ) {
            $deleteRecording = true;
        }


        $eventData = [
            'action'          => 'save',
            'previousData'    => $original,
            'newData'         => array_merge($original, $data),
            'authToken'       => $this->getRequestAuthToken(),
            'envUrl'          => $this->config->application->services->call,
            'accountId'       => $accountId,
            'addAuditLog'     => $addAuditLog,
            'deleteRecording' => $deleteRecording,
        ];

        $locationId = !empty($eventData['newData']['neighbor_location_id'])
        ? $eventData['newData']['neighbor_location_id']
        : $eventData['newData']['location_id'];

        $eventData['location_id'] = $locationId;

        /**for video call */
        if ($this->isVideoCallSid($eventData['newData']['twilio_id'])) {
            // If recording is enabled then recording sid is required before adding audit log
            if ($locationDetail['record_video_call']
            && empty($eventData['newData']['recording_sid'])) {
                $eventData['addAuditLog'] = false;
            }

            /**Do not save recording url in audit log for payment calls */
            if (in_array($data['call_type'], $paymentCallTypes)) {
                $eventData['deleteRecording'] = true;
            }
        }

        $eventData['addOutboundNote'] = false;
        // $data['addOutboundNote'] will be set from conference-event lambda function call
        if (strpos($data['call_type'], 'outbound') !== false
            && !empty($data['addOutboundNote'])) {
            $eventData['addOutboundNote'] = true;
        }

        $eventData['updateLeadDynamoLogId'] = true;

        EventRouter::route('CallDetail', $eventData, true);

        // Send call to process CI only if update not coming from CallController
        if (!$deleteRecording &&
            empty($data['update_source']) &&
            empty($original['recording_url']) &&
            !empty($data['recording_url'])
        ) {
            $eventData['oldData'] = array_merge($original, ['account_id' => $accountId]);
            $eventData['staticToken'] = $this->config->application->staticToken->readWrite;
            $eventData['callUrl'] = $this->config->application->services->call;
            $eventData['locUrl'] = $this->config->application->services->loc;
            $eventData['coreUrl'] = $this->config->application->services->core;
            $eventData['recordingBucket'] = $this->config->aws->buckets->recordings;
            $eventData['env'] = $this->config->application->env;
            $eventData['convIntelApi'] = $this->config->conv_intel_api;

            EventRouter::route('Call', $eventData, true);
            $data['recording_url'] = '';
        }

        return true;
    }

    /**
     * Any preprocessing of put data is done here
     *
     * @param array $data     request data
     * @param array $original original data
     *
     * @return array
     */
    protected function preProcessPutData(array $data, array $original): array
    {
        //if twilio_id starts wth RM set channel as video
        $data['twilio_id'] = $data['twilio_id'] ?? $original['twilio_id'];

        if ($this->isVideoCallSid($data['twilio_id'])) {
            $data['channel'] = 'video';
        }

        $data['channel'] = $data['channel'] ??  'voice';

        $this->executeEventHandlers($data, $original);

        if (isset($data['recording_sid'])) {
            $data['recording_sid'] = (string) $data['recording_sid'] ?? '';
        }

        unset(
            $data['log_id'],
            $data['billingDuration'],
            $data['update_source'],
            $data['db_log_id']
        );

        return $data;
    }

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    protected function getModelName(): string
    {
        return "CallDetail"; //model
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {

        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('user_id', $data));
    }

    /**
     * This is called from controller file for validation purpose
     *
     * @return array
     */
    protected function requiredRequestParams(): array
    {
        $required = true;
        if (isset($this->requestParams['channel'])) {
            $required = $this->requestParams['channel'] === 'voice' ? true : false;
        }

        $twilioIdRequired = true;
        if (strpos($this->requestParams['call_type'], 'outbound') !== false) {
            $twilioIdRequired = false;
        }

        $action = $this->dispatcher->getActionName();
        switch ($action) {
            case 'create':
                return [
                    'call_name' => ['required' => $required],
                    'call_number' => ['required' => $required],
                    'call_type' => ['required' => true],
                    'datestamp' => ['required' => true],
                    'location_id' => ['required' => true],
                    'twilio_id' => ['required' => $twilioIdRequired],
                ];
            break;
            default:
                return [];
        }
    }

    /**
     * GetQueryBuilder
     *
     * @param array $params parameters
     *
     * @return \Phalcon\Mvc\Model\Query\Builder
     */
    protected function getQueryBuilder(array $params): \Phalcon\Mvc\Model\Query\Builder
    {
        $queryBuilder = new Builder($params);
        $queryBuilder->columns(
            [
            'CallDetail.*',
            'loc.location_name as location_name',
            'l.inquiry_type as lead_inquiry_type',
            'l.qt_rental_type as lead_qt_rental_type',
            'l.first_name as lead_first_name',
            'l.last_name as lead_last_name',
            'l.phone as lead_phone',
            'l.email as lead_email',
            'cb.call_duration',
            ]
        );
        $queryBuilder->leftJoin('Lead', 'CallDetail.fk_lead_id = l.lead_id', 'l');
        $queryBuilder->leftJoin('CallBilling', 'CallDetail.log_id = cb.log_id', 'cb');
        $queryBuilder->leftJoin('Location', 'CallDetail.location_id = loc.location_id', 'loc');

        $queryBuilder = $this->setListConditions($queryBuilder);
        $queryBuilder = $this->setOrderByClause($queryBuilder);

        return $queryBuilder;
    }

    /**
     * Format List Response change o/p of each item in list
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    protected function formatListResponse($data)
    {
        for ($x = 0; $x < count($data); $x++) {
            $data[$x] = array_merge($data[$x], $data[$x]['callDetail']);
            unset($data[$x]['callDetail']);
        }

        return parent::formatListResponse($data);
    }

    /**
     * Validate data before create record for create action
     *
     * @param array<mixed> $data array of POST body / query params in key value pair
     *
     * @return string validation message
     */
    protected function validateCreate(array $data): string
    {
        $validationMessage = $this->getValidationErrorMessage($data);

        if (!$this->validateCallTypeExists($data)) {
            $validationMessage .= ", Invalid call type";
        }

        return  $validationMessage === false ? '' : $validationMessage;
    }


    /**
     * Validate call type is from existing list of call types
     *
     * @param array<mixed> $data array of POST body / query params in key value pair
     *
     * @return bool true/false based of call type exist
     */
    protected function validateCallTypeExists($data): bool
    {
        if (in_array($data['call_type'], $this->config->application->allCallTypes->toArray())) {
            return true;
        }

        if (substr($data['call_type'], 0, 15) === 'inbound_nolead_') {
            return true;
        }

        return false;
    }

    /**
     * Change in data before sending to insert flow
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    protected function preProcessPostData(array $data): array
    {
        $data['fk_lead_id'] = $data['fk_lead_id'] ?? 0;
        $data['recording_sid'] = $data['recording_sid'] ?? '';
        $data['call_destination'] = $data['call_destination'] ?? '';
        $data['rollover_index'] = $data['rollover_index'] ?? 0;
        $data['ad_id'] = $data['ad_id'] ?? 0;
        $data['gradesheet'] = $data['gradesheet'] ?? '';
        $data['gradesheet_points_appointed'] = $data['gradesheet_points_appointed'] ?? 0;
        $data['gradesheet_points_possible'] = $data['gradesheet_points_possible'] ?? 0;
        $data['manager_score'] = $data['manager_score'] ?? 0;
        $data['confirm_action'] = $data['confirm_action'] ?? '';
        $data['confirmed_by'] = $data['confirmed_by'] ?? 0;
        $data['route_config'] = $data['route_config'] ?? '';
        $data['current_route_step'] = $data['current_route_step'] ?? '';
        $data['is_route_complete'] = $data['is_route_complete'] ?? 0;
        $data['is_assigned'] = $data['is_assigned'] ?? 0;
        $data['customer_type'] = $data['customer_type'] ?? 'Unknown';
        $data['is_auto_call'] = $data['is_auto_call'] ?? 0;
        $data['version_id'] = !empty($data['version_id']) ? $data['version_id'] : 10000000000;

        $data['channel'] = $data['channel'] ??  'voice';

        if (isset($data['employee_id'])) {
            $data['employee_id'] = (int) $data['employee_id'];
        }

        $di = \Phalcon\DI::getDefault();
        $vars = $di->getShared('statecodes')->toArray();
        $dialingCode = Util::getDialingCode(
            $vars["country_dialing_code"],
            $data['location_id']
        );

        $data['call_number'] = isset($data['call_number']) ?
        Util::addDialingCode($data['call_number'], $dialingCode) : "";
        $data['call_name'] = isset($data['call_name']) ?
        Util::addDialingCode($data['call_name'], $dialingCode) : "";

        return $data;
    }

    /**
     * Validate data before save record for save action
     *
     * @param array $data array of POST body / query params in key value pair
     * @param mixed $id   primary key value of the record to update
     *
     * @return mixed
     */
    protected function validateSave(array $data, $id = 0)
    {
        unset($data, $id);

        return [];
    }

    /**
     * Update call billing table record with billing details
     *
     * @param int   $accountId Account id
     * @param array $data      Request data
     *
     * @return bool
     */
    protected function updateBillingData(int $accountId, array $data) : bool
    {
        try {
            $eventData = [];
            $eventData['data'] = $data;
            $eventData['accountId'] = $accountId;
            $eventData['insertCallBilling'] = true;

            EventRouter::route('CallDetail', $eventData, true);

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error in updating call_billing for log id ' .
                $this->getParamID() . ' Message ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            return false;
        }
    }

    /**
     * Call tracking number webhook
     *
     * @param array $data Request data
     *
     * @return bool
     */
    protected function checkCallWebhook(array $data) : bool
    {
        try {
            if (strpos($data['call_type'], 'inbound') !== false) {
                $eventData = [];
                $eventData['data'] = $data;
                $eventData['checkCallWebhook'] = true;

                if (!empty($data['recording_sid'])) {
                    $paymentCallTypes = $this->config->application->paymentCallTypes->toArray();
                    $callTypesForAuditLog = [
                        'inbound_lead',
                        'inbound_customer',
                        'inbound_collection',
                        'inbound_autopay',
                    ];
                    $callTypesForAuditLog = array_merge($callTypesForAuditLog, $paymentCallTypes);

                    if (in_array($data['call_type'], $callTypesForAuditLog)) {
                        $locClient = ClientFactory::getLocClient($this->getRequestAuthToken());
                        $locationDetail = (array) $locClient->getLocationConfigurationDetails(
                            $data['location_id'],
                            'false'
                        );

                        $eventData['previousData'] = $data;
                        $eventData['newData'] = $data;
                        $eventData['authToken'] = $this->getRequestAuthToken();
                        $eventData['envUrl'] = $this->config->application->services->call;
                        $eventData['accountId'] = $locationDetail['user_id'];
                        $eventData['addAuditLog'] = true;
                    }
                }

                EventRouter::route('CallDetail', $eventData, true);
            }

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error in executing checkCallWebhook for log id ' .
                $this->getParamID() . ' Message ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            return false;
        }
    }

    /**
     * function to check if call sid is video
     * @param integer $twilioId
     * @return boolean
     */
    protected function isVideoCallSid($twilioId)
    {
        if (substr($twilioId, 0, 2) === 'RM') {
            return true;
        }

        return false;
    }
}
