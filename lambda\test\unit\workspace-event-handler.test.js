const twilioMockData = require('./mockData/twilio-mock-data.json');
const taskData = {
    EventType: 'task.canceled',
    EventDescription : "Task canceled",
    Reason : "hangup",
    TaskAttributes: JSON.stringify(twilioMockData.task_data_video.task_attributes),
    TaskSid: twilioMockData.task_data_video.task_sid,
    TaskAge : 20,
    TaskChannelUniqueName: 'video',
    TaskQueueName: twilioMockData.task_data_video.task_queue_name,
    TaskAssignmentStatus: twilioMockData.task_data_video.task_assignment,
}

const mockRequest = (requestData) => {
    return {
        body : requestData
    }
}

const mockResponse = () => {
    return {
        type : () => {
            return  {send : (result) => result};
        }
    }
}

describe("Test Workspace Handler Events", () => {
    jest.resetModules();
    const {setup} = require("./mockData/mock-twilio-client");
    setup(true);
    
    test("should test task canceled event", async () => {
        const workspaceEventHanlder = require('../../libraries/workspace-event-handler');

        //Mock twilio utils functions return values
        const twilioUtils = require('../../routes/utils/twillio');
        twilioUtils.getAccountFromSid = jest.fn().mockReturnValue(twilioMockData.twilio_account);
        twilioUtils.update_callcener_task = jest.fn().mockReturnValue(true);
        twilioUtils.remove_sync_task_map = jest.fn().mockReturnValue("<Response>Success.</Response>");
        
        const workspaceHandlerRequest = mockRequest(taskData)
        const workspaceHandlerResponse = mockResponse();

        const result = await workspaceEventHanlder(workspaceHandlerRequest, workspaceHandlerResponse);

        expect(result).toEqual("<Response>Success.</Response>");
    });
});