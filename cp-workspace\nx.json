{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "workspaceLayout": {"libsDir": "libs", "appsDir": "apps"}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": []}, "targetDefaults": {"@nx/js:tsc": {"cache": false, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "build": {"inputs": ["production", "^production"]}}, "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["apps/lambda-functions2-e2e/**/*"]}, {"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}]}