const config = require("../../config/config");
const URL = require('url');

let CONFIG_ENV = config.env;
if (config.qatest || (config.env.indexOf('qa') !== -1 && config.env.indexOf('cpt') !== -1)){
  CONFIG_ENV = config.env.split('-')[0];
}

function defineCustomerConferenceParams(
  taskAttributes,
  agent_id,
  twilioAccount,
  ReservationSid
) {
  const baseURL = URL.parse(config.call_url);
  const callLambdaApi = `${baseURL.protocol}//${baseURL.host}/${CONFIG_ENV}`;
  
  return {
    participantLabel: `customer-${agent_id}`,
    startConferenceOnEnter: true,
    endConferenceOnExit: true,
    statusCallback:
      `${callLambdaApi}-conference-event/handler` +
      `?connectType=phone` +
      `&WorkerSid=${taskAttributes.worker_sid}` +
      `&customerCallSid=${taskAttributes.call_sid}` +
      `&reservationSid=${ReservationSid}`,
    statusCallbackEvent: ["start", "join", "leave", "end"],
    record: true,
    recordingStatusCallback:
      `${callLambdaApi}-conference-event/handler` +
      `?connectType=phone&locationId=${taskAttributes.location_id}` +
      `&WorkspaceSid=${twilioAccount.workspace_sid}&Customer=${taskAttributes.call_sid}` +
      `&AgentId=${agent_id}`,
    waitUrl: 'https://twimlets.com/holdmusic?Bucket=com.twilio.music.ambient',
    beep: "onEnter",
  };
}

function formatPhoneNumber(phoneNumber) {
  let phoneNum = phoneNumber.replace(/\D/g, '');
  if (phoneNum.length === 10) {
    phoneNum = `1${phoneNum}`;
  }
  if (phoneNum.length >= 11 && phoneNum.length <= 13) {
    phoneNum = `+${phoneNum}`;
  }
  return phoneNum;
}

function formatSipAddress(sipAddress) {
  if (!sipAddress.startsWith("sip:")) {
    sipAddress = `sip:${sipAddress}`
  }
  return sipAddress;
}

const INTERNAL_SIP_DOMAINS = [
  'twilio.com',
  'callpotential.com',
  'thinq.com',
  '***********'
];

function isInternalSipAddress(sipAddress) {
  /**
   * Internal SIP Addresses are SIP addresses that, when called, follows the SIP protocol by
   * returning the following responses:
   * - 100 Trying
   * - 180 Ringing (or 183 Session Progress)
   * - 200 OK
   * 
   * For these kinds of SIP addresses, we can rely on Twilio events to let our system know when
   * the call has been answered.  This is in contrast to external SIP addresses, which require an
   * external system to notify our system when the call has been answered (via a webhook).
   * 
   * NOTE: An internal sipAddress might look like any of these:
   * 
   * <EMAIL>?X-account-id=14856&X-account-token=d2cf5452406d270d864b296ff946458b3c7b0363
   * 7625@*********** - NOTE: This represents a SIP address for the SafeGuard account
   * <EMAIL>
   * +<EMAIL>
   */

  if (typeof sipAddress !== 'string' || !sipAddress.includes('@')) {
    return false;
  }

  const sipAddressParts = sipAddress.split('@');
  const sipDomain = sipAddressParts[1];
  const sipDomainParts = sipDomain.split('?');
  const sipDomainName = sipDomainParts[0].toLowerCase();

  let isInternal = false;
  for(let i = 0; i < INTERNAL_SIP_DOMAINS.length; i++) {
    if (sipDomainName.endsWith(INTERNAL_SIP_DOMAINS[i])) {
      isInternal = true;
      break;
    }
  }

  return isInternal
}

function formatCommunicationIdentifiers(workerAttributes, taskAttributes) {
  /**
   * Format the to SIP address and store it as the `to` callee participant.
   * Use the caller_id as the `from` caller participant.
   */
  const info = {
    callee: {
      to: workerAttributes.contact_uri,
      type: "", // phone, sip-internal, sip-external
    },
    caller: {
      from: "",
      type: "", // phone, sip
    }
  };

  if (info.callee.to.includes("@")) {
    if (info.callee.to && isInternalSipAddress(info.callee.to)) {
      info.callee.type = "sip-internal";
    } else {
      info.callee.type = "sip-external";
    }
    info.callee.to = formatSipAddress(info.callee.to);
    info.caller.from = isValidSipAddress(taskAttributes.caller_id) ? taskAttributes.caller_id : taskAttributes.tracking_no;
  } else {
    info.callee.type = "phone";
    info.callee.to = formatPhoneNumber(info.callee.to);
    info.caller.from = taskAttributes.tracking_no;
  }

  info.caller.type = info.caller.from.includes("@") ? "sip" : "phone";

  /**
   * The caller.from number cannot be prefixed with a + sign.
   */
  if (info.caller.type === "phone" && info.caller.from.startsWith("+")) {
    info.caller.from = info.caller.from.slice(1);
  }

  /**
   * NOTE: I find it odd that the caller.from is set to the 
   * tracking_no OR caller_id. Anyone else find that odd?
   * 
   * TODO: Check a SIP and computer call to an agent and see 
   * if the task attributes have the same value for those fields.
   */

  return info;
}

/**
 * Validates a SIP address.
 * 
 * @param {string} sipAddress
 * @returns {boolean}
 */
function isValidSipAddress(sipAddress) {
  const sipRegex = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(:[0-9]+)?(;transport=(udp|tcp|tls))?$/;
  return sipRegex.test(sipAddress);
}

function defineAgentConferenceParams(context) {

  const baseURL = URL.parse(config.call_url);
  const { twilioAccount, taskAttributes, workerAttributes, reservationData, communicationIdentifiers } = context;
  const { ReservationSid } = reservationData;
  const workspaceSid = twilioAccount.workspace_sid;
  const reservationSid = ReservationSid;
  const callLambdaApi = `${baseURL.protocol}//${baseURL.host}/${CONFIG_ENV}`;
  const { callee } = communicationIdentifiers;

  console.debug("defineAgentConferenceParams", taskAttributes);
  return {
    participantLabel: `agent-${workerAttributes.agent_id}`,
    startConferenceOnEnter: true,
    endConferenceOnExit: false,
    statusCallback:
      `${callLambdaApi}-conference-event/handler` +
      `?connectType=phone` +
      `&WorkerSid=${taskAttributes.worker_sid}` +
      `&customerCallSid=${taskAttributes.call_sid}` +
      `&reservationSid=${reservationSid}` +
      `&agentTo=${callee.to}` +
      `&agentToType=${callee.type}`,
    statusCallbackEvent: ["start", "join", "leave", "end"],
    record: true,
    recordingStatusCallback:
      `${callLambdaApi}-conference-event/handler` +
      `?connectType=phone&locationId=${taskAttributes.location_id}` +
      `&WorkspaceSid=${workspaceSid}&Customer=${taskAttributes.call_sid}` +
      `&AgentId=${workerAttributes.agent_id}`,
    waitUrl: "",
    beep: "onEnter",
  };
}


module.exports = {
  defineCustomerConferenceParams,
  defineAgentConferenceParams,
  formatCommunicationIdentifiers,
  formatPhoneNumber,
  formatSipAddress,
  isInternalSipAddress,
  INTERNAL_SIP_DOMAINS
};