<?php
/**
 * Twilio workspace related functions
 *
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: cwalker
 * Date: 12/16/17
 * Time: 12:10 PM
 *
 * @category Workspace
 * @package  CallCenter
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

namespace CallCenter;

use CallPotential\CPCommon\Cli\CliEvent;
use CallPotential\CPCommon\Cli\CliSubjectTrait;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\CoreClient;

use Twilio\Exceptions\RestException;
use CallPotential\CallCenter\TwilioTaskRouter;
use Aws\S3\S3Client;
use Phalcon\Db\Enum;

/**
 * Twilio workspace related functions
 *
 * @category Workspace
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class Workspace
{
    use CliSubjectTrait;

    /**
     * Account Id
     *
     * @var integer|null
     */
    private $accountId = null;

    /**
     * Task router
     *
     * @var \TwilioTaskRouter
     */
    private $taskRouter = null;

    /**
     * Workspace
     *
     * @var \Twilio\Rest\Taskrouter\V1\WorkspaceInstance
     */
    public $workspace;

    /**
     * Activities
     *
     * @var array|null
     */
    private $managedActivities = null;

    /**
     * Activities
     *
     * @var array|null
     */
    private $optionalActivities = null;

    /**
     * Outbound queue
     *
     * @var object|null
     */
    private $outboundQueue  = null;

    /**
     * Workers
     *
     * @var array|null
     */
    private $workers = null;

    /**
     * Constructor to initialize data
     *
     * @param int $accountId account Id
     */
    public function __construct(int $accountId)
    {
        $this->CliSubjectName = 'workspace-config';
        $this->accountId = $accountId;
        $this->taskRouter = new TwilioTaskRouter($this->accountId);

        return $this;
    }

    /**
     * Check workspace
     *
     * @param string $prefix prefix if exists
     *
     * @return void
     */
    public function validateWorkspace(string $prefix = '')
    {
        $this->verifyDynamoData();
        $this->manageWorkspace($prefix);
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Workspace Verified",
                null,
                ['progress' => 15]
            )
        );
        $this->manageActivities();
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Activities Verified",
                null,
                ['progress' => 25]
            )
        );
        $this->manageWorkers();
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Workers Synced",
                null,
                ['progress' => 40]
            )
        );

        $this->addQueues();
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Legacy queues added",
                null,
                ['progress' => 55]
            )
        );

        $this->manageQueues();
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Default Queue Verified",
                null,
                ['progress' => 70]
            )
        );

        $this->manageQueueWorkflows();
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Main Workflow Created",
                null,
                ['progress' => 85]
            )
        );

        $this->updateCallRoutes();
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Call Routes Updated",
                null,
                ['progress' => 90]
            )
        );

        $this->updateAgentLocationAssignments($prefix);
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Agent Location Assignment Completed",
                null,
                ['progress' => 95]
            )
        );
        $this->updateAgentInactiveReasons($prefix);
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Worker Activities Assignment Completed",
                null,
                ['progress' => 100]
            )
        );
    }

    /**
     * Verify dynamo DB
     *
     * @return mixed
     */
    public function verifyDynamoData()
    {
        $this->infoMessage('Verifying DynamoDB account record ' . $this->accountId);
        $model = new \TwilioAccount();
        $acct = $model->findById($this->accountId);
        if ($acct) {
            $acct = $acct->toArray();
        }
        $sid = Util::array_get('account_sid', $acct, '');
        echo "Account SID: $sid\n";
        if (!strlen($sid)) {
            $legacy = $this->importAccount($this->accountId);
            echo "Legacy:\n";
            print_r($legacy);
            if (is_array($legacy)) {
                $acct = $model->save($legacy);
                if ($acct) {
                    $acct = $acct->toArray();
                    $this->debugMessage('Created DynamoDB record for account '.$this->accountId);
                }
            }
        }
        $this->debugMessage('DynamoDB Account: '.print_r($acct, true));
        $this->infoMessage('DynamoDB account record verified for'.$this->accountId);

        return $acct;
    }

    /**
     * Import twilio account
     *
     * @param integer $accountId account Id
     *
     * @return mixed
     */
    public function importAccount(int $accountId)
    {
        $client = ClientFactory::getCoreClient();
        $userRec = (array) $client->getUser($accountId);
        if (count($userRec) > 0) {
            $acct = [];
            $acct['id'] = $accountId;
            $acct['active'] = $userRec['active'];
            $acct['account_sid'] = $userRec['sid'];
            $acct['authtoken'] = $userRec['authtoken'];

            return $acct;
        } else {
            return false;
        }
    }

    /**
     * Method log information messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function infoMessage(string $msg, $src = null)
    {
        unset($src);
        $this->notify(new CliEvent(CliEvent::CLI_EVENT_INFO, $msg));
    }

    /**
     * Method log debug messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function debugMessage(string $msg, $src = null)
    {
        unset($src);
        $this->notify(new CliEvent(CliEvent::CLI_EVENT_DEBUG, $msg));
    }

    /**
     * Manage workespace
     *
     * @param string $workspacePrefix prefix if exists
     *
     * @return object
     */
    public function manageWorkspace(string $workspacePrefix = '')
    {
        $this->infoMessage("Checking Call Center Workspace");
        $workspaces = $this->taskRouter->listWorkspace();
        foreach ($workspaces as $w) {
            if ($w->friendlyName === $workspacePrefix . '-CallCenter') {
                $this->workspace = $w;
                $this->debugMessage('Existing Call Center Workspace found: '.$this->workspace->sid);
                break;
            }
        }
        if (is_null($this->workspace)) {
            $workspaceConfig = $this->getWorkspaceConfig($workspacePrefix);
            $this->debugMessage(
                'Creating Call Center Workspace with configuration: '.
                print_r($workspaceConfig, true)
            );
            $this->workspace = $this->taskRouter->createWorkspace(
                $workspacePrefix . '-CallCenter',
                $workspaceConfig
            );
        }
        $taskChannels = $this->taskRouter->getTaskChannels($this->workspace->sid);
        $model = new \TwilioAccount();
        $acct = $model->findById($this->accountId);
        $acct->workspace_sid = $this->workspace->sid;
        foreach ($taskChannels as $channel) {
            $channelName = $channel["UniqueName"];
            $acct->$channelName = $channel["sid"];
        }
        $acct->save();
        $this->infoMessage("Call Center Workspace Complete. SID:".$this->workspace->sid);

        return $this;
    }

    /**
     * Manage activities
     *
     * @return object
     */
    public function manageActivities()
    {
        $this->infoMessage("Verifying Required Activities");
        $optional = [];
        if ($this->workspace) {
            $activities = $this->taskRouter->getWorkspaceActivities($this->workspace->sid);
            $activities = (array) $activities;
            $idleActivity = false;
            $required = $this->setActivityState($activities);
            if (!empty($required['idleActivity'])) {
                $idleActivity = $required['idleActivity'];
            }
            if (!empty($required['optional'])) {
                $optional = $required['optional'];
            }
            if (!array_key_exists('ready', $required)) {
                $required['ready'] = $this->taskRouter->addActivity(
                    'Ready',
                    true,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity Ready:'.
                    $required['ready']->sid
                );
            }
            if (!array_key_exists('offline', $required)) {
                $required['offline'] = $this->taskRouter->addActivity(
                    'Offline',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity Offline:'
                    .$required['offline']->sid
                );
            }
            if (!array_key_exists('oncall', $required)) {
                $required['oncall'] = $this->taskRouter->addActivity(
                    'On-call',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity On-call:'.$required['oncall']->sid
                );
            }
            if (!array_key_exists('cooldown', $required)) {
                $required['cooldown'] = $this->taskRouter->addActivity(
                    'Cool-Down',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity Cool-Down:'.$required['cooldown']->sid
                );
            }
            if (!array_key_exists('unavailable', $required)) {
                $required['unavailable'] = $this->taskRouter->addActivity(
                    'Unavailable',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity Unavailable:'.$required['unavailable']->sid
                );
            }
            if (!array_key_exists('rejected', $required)) {
                $required['rejected'] = $this->taskRouter->addActivity(
                    'Rejected',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity Rejected:'.$required['rejected']->sid
                );
            }
            if (!array_key_exists('noanswer', $required)) {
                $required['noanswer'] = $this->taskRouter->addActivity(
                    'No-Answer',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity No-Answer:'.$required['noanswer']->sid
                );
            }
            if (!array_key_exists('mic', $required)) {
                $required['mic'] = $this->taskRouter->addActivity(
                    'No-Mic',
                    false,
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Created missing required activity No-Mic:'.$required['mic']->sid
                );
            }
            if ($idleActivity) {
                $this->taskRouter->deleteActivity($idleActivity->sid);
            }

            $workspaceDefaultData = ['TimeoutActivitySid' => $required['noanswer']->sid];
            $this->taskRouter->updateWorkspace($this->workspace->sid, $workspaceDefaultData);
        }
        $this->managedActivities = $required;
        $this->optionalActivities = $optional;
        $this->infoMessage("Activity Validation Complete");

        return $this;
    }

    /**
     * Get all active users and add to workspace
     *
     * @return object
     */
    public function manageWorkers()
    {
        $this->infoMessage('Started Worker Sync');
        $core = \CallPotential\CPCommon\ClientFactory::getCoreClient();
        $users = $core->getUsers(['filterParent_id' => $this->accountId]);
        $users = (array) $users->items;
        $users[] = (array) $core->getUser($this->accountId);
        $this->debugMessage("Loaded ".count($users)." users.");
        foreach ($users as $user) {
            $user = (array) $user;
            $w = $this->taskRouter->getWorkerByEmail($user['email'], $this->workspace->sid);
            if (is_object($w)) {
                $w = $w->toArray();
                $attributes = json_decode($w['attributes'], true);
                $attributes['agent_id'] = $user['user_id'];
                $attributes['email'] = $user['email'];
                $w['attributes'] = json_encode($attributes);
                $this->workers[$user['user_id']] = $this->taskRouter->setWorkerAttributes(
                    $w['sid'],
                    $attributes,
                    $this->workspace->sid
                );
            } else {
                $attributes = [
                    'agent_id' => $user['user_id'],
                    'email' => $user['email'],
                ];
                $this->workers[$user['user_id']] = $this->taskRouter->createWorker(
                    $user['email'],
                    [
                        "attributes" => json_encode($attributes),
                        "activitySid" => $this->managedActivities['offline']->sid,
                    ],
                    $this->workspace->sid
                );
                $this->infoMessage(
                    'Added worker for '.$user['email'].': '.$this->workers[$user['user_id']]->sid
                );
            }
        }
        $this->debugMessage('Completed Worker Sync');

        return $this;
    }

    /**
     * Get all legacy db queues and add it to workspace
     *
     * @return void
     */
    public function addQueues()
    {
        $twilioQueue = new \TwilioQueue();
        $this->infoMessage("Getting queues from db");
        if ($this->workspace) {
            $di = \Phalcon\DI\FactoryDefault::getDefault();
            $dbLegacy = $di->getShared('dbLegacy');
            $queueResult = $dbLegacy->query(
                "select crq.queue_id, crq.hold_music, crq.delay_for_agent,
                 crq.number_of_agent, crq.notify_caller_status, crq.name ,
                 crq.user_id, u.email
                FROM call_route_queue crq
                INNER JOIN agent_queue_assignment aq on aq.queue_id = crq.queue_id
                INNER JOIN users u on aq.user_id = u.user_id
                WHERE is_active=1 and crq.user_id = ".$this->accountId
            );

            $queueResult->setFetchMode(Enum::FETCH_ASSOC);
            $queueData = $queueResult->fetchAll();
            /*fetch users with email to add them to queue*/

            echo date('Y-m-d H:i:s') . " Total " . count($queueData) . " queues found" . PHP_EOL;
            if (count($queueData) > 0) {
                /*check database queue friendly name exists in twilio and then add*/
                foreach ($queueData as $que) {
                    $result[$que['queue_id']][] = $que;
                }

                $allTwilioQueueNames = [];
                $queues = $this->taskRouter->getQueues($this->workspace->sid);
                if (count($queues) > 0) {
                    $allTwilioQueueNames = $this->getQueueNames($queues);
                }

                foreach ($result as $q) {
                    $queueName = $q[0]['name'];
                    $hold_music = $q[0]['hold_music'];
                    $account_id = $q[0]['user_id'];
                    $delay_for_agent = $q[0]['delay_for_agent'];
                    $number_of_agent = $q[0]['number_of_agent'];
                    $notify_caller_status = $q[0]['notify_caller_status'];
                    if (!in_array($queueName, $allTwilioQueueNames)) {
                        $this->infoMessage('Creating queue '. $queueName);
                        $result = $this->taskRouter->createQueue(
                            $queueName,
                            $this->managedActivities['offline']->sid,
                            $this->managedActivities['oncall']->sid,
                            [],
                            $this->workspace->sid
                        );
                        $this->debugMessage(
                            $queueName.' Queue Created. SID:'.$result->sid .
                            ' for workspace '.$this->workspace->sid
                        );
                        $queueSid = $result->sid;
                        $this->debugMessage('Insert new queue detail to dynamo start');
                        $data = [];
                        $data['created_at'] = gmdate('Y-m-d H:i:s');
                        $data['FriendlyName'] = $queueName;
                        $data['delay_for_agent'] = $delay_for_agent;
                        $data['number_of_agent'] = $number_of_agent;
                        $data['hold_music'] = $hold_music;
                        $data['notify_caller_status'] = (int) $notify_caller_status;
                        $data['account_id'] = (int) $account_id;
                        $data["queue_sid"] = $queueSid;
                        $twilioQueue->add($data);
                    } else {
                        $queueSid = array_search($queueName, $allTwilioQueueNames);
                    }

                    foreach ($q as $rec) {
                        $w = $this->taskRouter->getWorkerByEmail(
                            $rec['email'],
                            $this->workspace->sid
                        );
                        if (is_object($w)) {
                            $w = $w->toArray();
                            $this->taskRouter->addWorkerToQueue(
                                $w['sid'],
                                $queueSid,
                                $this->workspace->sid
                            );
                            $this->debugMessage('worker '.$w['sid'].' added to queue '.$queueSid);
                        } else {
                             $this->infoMessage('Worker doesnt exists '.$rec['email']);
                        }
                    }
                }
            }
        }
    }

    /**
     * Create the default queue and ensure all users are assigned to it
     *
     * @return object
     */
    public function manageQueues()
    {
        $this->infoMessage("Verifying Default Queue");
        if ($this->workspace) {
            $mainQueue = null;
            $queues = $this->taskRouter->getQueues($this->workspace->sid);
            foreach ($queues as $queue) {
                if ($queue->friendlyName === '_outbound-voice') {
                    $this->outboundQueue = $queue;
                    $this->debugMessage('Found outbound Queue: '.$this->outboundQueue->sid);
                } elseif ($queue->friendlyName === 'Main Queue') {
                    $mainQueue = $queue;
                    $this->debugMessage('Found Main Queue: '.$mainQueue->sid);
                }
            }
            if (!$this->outboundQueue) {
                $this->infoMessage('Creating default outbound call queue');
                $this->outboundQueue = $this->taskRouter->createQueue(
                    '_outbound-voice',
                    $this->managedActivities['offline']->sid,
                    $this->managedActivities['oncall']->sid,
                    ['targetWorkers' => '1 == 1'],
                    $this->workspace->sid
                );
                $this->debugMessage('Outbound Queue Created. SID:'.$this->outboundQueue->sid);
            }
            if (!$mainQueue && count($queues) < 2) {
                $mainQueue = $this->taskRouter->createQueue(
                    'Main Queue',
                    $this->managedActivities['offline']->sid,
                    $this->managedActivities['oncall']->sid,
                    [],
                    $this->workspace->sid
                );

                // Update main_queue sid in dynamodb
                $model = new \TwilioAccount();
                $acct = $model->findById($this->accountId);
                if ($acct) {
                    $acct = $acct->toArray();
                    $acct['main_queue'] = $mainQueue->sid;
                    $model->save($acct);
                }
            }
        }

        return $this;
    }

    /**
     * Manager workflows
     *
     * @return void
     */
    public function manageQueueWorkflows()
    {
        $queues = $this->taskRouter->getQueues($this->workspace->sid);
        $workflows  = $this->taskRouter->getWorkflows($this->workspace->sid);

        $queueTemplateJson = file_get_contents(APP_PATH.'/config/queue-workflow.json');

        $callBackUrl = '';
        $workflowJson = file_get_contents(APP_PATH.'/config/workflow-template.json');
        $workflowJson = str_replace('__OUTBOUND_QUEUE__', $this->outboundQueue->sid, $workflowJson);

        $queueFlows = [];
        foreach ($queues as $queue) {
            $queueName = $queue->friendlyName;


            //skip queues that begin w/ underscore
            if (substr($queueName, 0, 1) !== '_') {
                $queueFlow = str_replace('__TARGET_QUEUE__', $queue->sid, $queueTemplateJson);
                $queueFlow = str_replace('__TARGET_QUEUE_NAME__', $queue->friendlyName, $queueFlow);
                $queueFlows[] = $queueFlow;
            }
        }
        $workflowJson = str_replace(
            '__QUEUE_FILTERS__',
            implode(",\n", $queueFlows),
            $workflowJson
        );
        $mainWorkflow = null;
        foreach ($workflows as $w) {
            if ($w->friendlyName === "InboundOutboundVoice") {
                $mainWorkflow = $w;
                break;
            }
        }


        //workflow doesn't exist
        if (!is_object($mainWorkflow)) {
            $mainWorkflow = $this->taskRouter->createWorkflow(
                '',
                "InboundOutboundVoice",
                $workflowJson,
                [
                    "AssignmentCallbackUrl" => $callBackUrl,
                    "FallbackAssignmentCallbackUrl" => $callBackUrl,
                    "TaskReservationTimeout" => 15,
                ]
            );
            $this->infoMessage('Created voice workflow, SID: '.$mainWorkflow->sid);

            // Update voice_workflow sid in dynamodb
            $model = new \TwilioAccount();
            $acct = $model->findById($this->accountId);
            if ($acct) {
                $acct = $acct->toArray();
                $acct['voice_workflow'] = $mainWorkflow->sid;
                $model->save($acct);
            }
        } else {
            $diffs = [];
            if ($mainWorkflow->assignmentCallbackUrl !== $callBackUrl) {
                $diffs["AssignmentCallbackUrl"] = $callBackUrl;
            }
            if ($mainWorkflow->fallbackAssignmentCallbackUrl !== $callBackUrl) {
                $diffs["FallbackAssignmentCallbackUrl"] = $callBackUrl;
            }
            if ($mainWorkflow->taskReservationTimeout !== 15) {
                $diffs["TaskReservationTimeout"] = 15;
            }
            if ($mainWorkflow->configuration !== $workflowJson) {
                $diffs['configuration'] = $workflowJson;
            }

            if (count($diffs) > 0) {
                $this->taskRouter->updateWorkflow($mainWorkflow->sid, $diffs);
                $this->infoMessage(
                    'Updated All Queues workflow, changed: '.
                    implode(',', array_keys($diffs))
                );
            }
        }
    }

    /**
     * Delete workspace
     *
     * @param string $workspacePrefix prefix if exists
     *
     * @return boolean
     */
    public function deleteWorkspace(string $workspacePrefix = ''): bool
    {
        $this->infoMessage("Checking Call Center Workspace");
        $workspaces = $this->taskRouter->listWorkspace();
        foreach ($workspaces as $w) {
            if ($w->friendlyName === $workspacePrefix . '-CallCenter') {
                $this->infoMessage('Existing Call Center Workspace found: '.$w->sid);
                $response = $this->taskRouter->deleteWorkspace($w->sid);
                $this->debugMessage('Workspace deletion response: '.print_r($response, true));

                return true;
            }
        }

        $this->debugMessage('Workspace not found: '.$workspacePrefix.'-CallCenter');

        return false;
    }

    /**
     * Update inactive reasons
     *
     * @param string $prefix prefix if exists
     *
     * @return void
     */
    public function updateAgentInactiveReasons(string $prefix)
    {
        unset($prefix);
        $this->infoMessage('Updating Agent Inactive Reasons');
        $defaultReasons = [
            'Ready',
            'Offline',
            'On-call',
            'Cool-Down',
            'Unavailable',
            'No-Answer',
            'Rejected',
            'No-Mic',
            'Idle',
        ];
        $localReasons = \AgentInactiveReasons::find(
            [
                "is_default = 0 AND user_id = $this->accountId",
            ]
        );

        $twilioActivities = $this->taskRouter->getWorkspaceActivities($this->workspace->sid);
        $twilioActivitiesArray = array_map(
            function ($item) {
                return $item->toArray();
            },
            $twilioActivities
        );
        $twilioActivityNames = array_column($twilioActivitiesArray, 'friendlyName');
        $twilioActivityNames = array_unique(
            array_merge($twilioActivityNames, $defaultReasons)
        ); //These will be excluded

        foreach ($localReasons as $localReason) {
            if (!in_array($localReason->getDescription(), $twilioActivityNames)) {
                $this->taskRouter->addActivity(
                    $localReason->getDescription(),
                    false,
                    $this->workspace->sid
                );
            }
        }
    }

    /**
     * Update locations assigned to agents
     *
     * @param string $prefix prefix if exists
     *
     * @return void
     */
    public function updateAgentLocationAssignments(string $prefix)
    {
        $this->infoMessage("Updating Agent Location Assignments");
        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $dbLegacy = $di->getShared('dbLegacy');

        $select = 'SELECT config_id, config FROM call_route_config
        WHERE is_active = 1 AND user_id = ' . $this->accountId;
        $queryResult = $dbLegacy->query($select);
        $routeConfigs = $queryResult->fetchAll(Enum::FETCH_ASSOC);
        $this->infoMessage(" Total " . count($routeConfigs) . " configs found" . PHP_EOL);

        //Get all agents
        $agentResult = $dbLegacy->query('SELECT user_id FROM users WHERE
         is_agent = 1 AND parent_id = ' . $this->accountId);
        $agentList = $agentResult->fetchAll(Enum::FETCH_ASSOC);
        $this->infoMessage(" Total " . count($agentList) . " agents found" . PHP_EOL);

        $workspaces = $this->taskRouter->listWorkspace();
        foreach ($workspaces as $w) {
            if ($w->friendlyName === $prefix . '-CallCenter') {
                $workflowQueues = $this->taskRouter->getQueues($w->sid);
                break;
            }
        }

        $twilioQueues = [];
        foreach ($workflowQueues as $queue) {
            $twilioQueues[$queue->friendlyName] = $queue->sid;
        }

        if (is_array($agentList) && count($agentList) > 0) {
            foreach ($agentList as $agent) {
                $dbLegacy->execute("DELETE FROM user_assignments WHERE
                    user_id={$agent['user_id']}");
                $agentQueueSids = [];
                $agentQueueNames = [];
                $allowedCallRouteIds = [];

                $select_queue = 'SELECT GROUP_CONCAT(crq.name) AS q_names
                     FROM agent_queue_assignment aq
                     JOIN call_route_queue crq ON crq.queue_id = aq.queue_id
                     WHERE aq.user_id = '. $agent['user_id'];
                $queueResult = $dbLegacy->query($select_queue);
                $queues = $queueResult->fetchAll(Enum::FETCH_ASSOC);
                $agentQueueNames = explode(',', $queues[0]['q_names']);
                $this->infoMessage(" Total " . count($queues) . " queues found" . PHP_EOL);

                if (count($agentQueueNames) === 1 && empty($agentQueueNames[0])) {
                    continue;
                }

                foreach ($agentQueueNames as $value) {
                    $agentQueueSids[] = $twilioQueues[$value];
                }

                foreach ($routeConfigs as $config) {
                    $data = get_object_vars(json_decode($config['config']));

                    $routeData = isset($data['children'][0]) ?
                        get_object_vars($data['children'][0]) : array();
                    $allowedCallRouteIds = $this->getAssociatedIds(
                        $allowedCallRouteIds,
                        $routeData,
                        'call_route',
                        $agentQueueSids,
                        $config['config_id']
                    );
                }

                $callRouteIds = (is_array($allowedCallRouteIds)) ?
                    '\'' . implode('\',\'', array_unique($allowedCallRouteIds)) . '\'' : '';

                if (isset($callRouteIds)) {
                    $sql = "SELECT location_id
                        FROM locations
                        WHERE user_id = {$this->accountId}
                            AND active = 1
                            AND call_route_config_id IN ({$callRouteIds})
                        ORDER BY location_name";

                    $result = $dbLegacy->fetchAll($sql, Enum::FETCH_ASSOC);
                }

                $agentAssignments = [];
                foreach ($result as $location) {
                    $agentAssignments = [
                            null,
                            $agent['user_id'],
                            $location['location_id'],
                            0,
                            5,
                            $this->accountId,
                        ];
                    $dbLegacy->execute("INSERT INTO user_assignments
                        VALUES (?, ?, ?, ?, ?, ?)", $agentAssignments);
                }
            }
        }
    }

    /**
     * Update call routes
     *
     * @return void
     */
    public function updateCallRoutes()
    {
        $this->infoMessage("Updating Call Routes");
        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $dbLegacy = $di->getShared('dbLegacy');

        $select = 'SELECT config_id, config FROM call_route_config
        WHERE is_active = 1 AND user_id = ' . $this->accountId;
        $queryResult = $dbLegacy->query($select);
        $routeConfigs = $queryResult->fetchAll(Enum::FETCH_ASSOC);
        $this->infoMessage(" Total " . count($routeConfigs) . " configs found" . PHP_EOL);

        $queues = $this->taskRouter->getQueues($this->workspace->sid);
        $twilioQueues = [];

        foreach ($queues as $queue) {
            $twilioQueues[$queue->friendlyName] = $queue->sid;
        }

        foreach ($routeConfigs as $config) {
            $configId = $config['config_id'];
            $this->infoMessage("Processing config id " . $configId . PHP_EOL);
            $config = json_decode($config['config'], true);
            $updatedConfig = $this->updateRouteQueues($config, $twilioQueues);
            $this->infoMessage("Updating config id " . $configId . PHP_EOL);
            $dbLegacy->update(
                "call_route_config",
                ['config'],
                [json_encode($updatedConfig)],
                "config_id = " . $configId
            );
            $this->infoMessage("Updated config id " . $configId . PHP_EOL);
        }

        $this->infoMessage("Call Routes Updated");
    }

    /**
     * Reserve call routes
     *
     * @return void
     */
    public function reverseUpdateCallRoutes()
    {
        $this->infoMessage("Updating Call Routes");
        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $dbLegacy = $di->getShared('dbLegacy');

        $select = 'SELECT config_id, config FROM call_route_config
        WHERE is_active = 1 AND user_id = ' . $this->accountId;
        $queryResult = $dbLegacy->query($select);
        $routeConfigs = $queryResult->fetchAll(Enum::FETCH_ASSOC);
        $this->infoMessage(" Total " . count($routeConfigs) . " configs found" . PHP_EOL);

        $queues = $this->taskRouter->getQueues($this->workspace->sid);
        $twilioQueues = [];

        foreach ($queues as $queue) {
            $selectCallRoute = 'SELECT queue_id FROM call_route_queue WHERE
             is_active = 1 AND user_id = ' . $this->accountId.' AND
             name = "'.$queue->friendlyName.'"';
            $queryResultCallRoute = $dbLegacy->query($selectCallRoute);
            $callRoutes = $queryResultCallRoute->fetch(Enum::FETCH_ASSOC);
            if ($callRoutes) {
                $twilioQueues[$queue->friendlyName] = $callRoutes["queue_id"];
            } else {
                $twilioQueues[$queue->friendlyName] = "";
            }
        }

        foreach ($routeConfigs as $config) {
            $configId = $config['config_id'];
            $this->infoMessage("Processing config id " . $configId . PHP_EOL);
            $config = json_decode($config['config'], true);
            $updatedConfig = $this->updateRouteQueues($config, $twilioQueues);
            $this->infoMessage("Updating config id " . $configId . PHP_EOL);
            $dbLegacy->update(
                "call_route_config",
                ['config'],
                [json_encode($updatedConfig)],
                "config_id = " . $configId
            );
            $this->infoMessage("Updated config id " . $configId . PHP_EOL);
        }

        $this->infoMessage("Call Routes Updated");
    }

    /**
     * Get twilio queue names
     *
     * @param array $queues queues
     *
     * @return array
     */
    private function getQueueNames(array $queues): array
    {
        $result = [];
        foreach ($queues as $q) {
            if (!is_array($q)) {
                $q = $q->toArray();
            }
            $result[$q['sid']] = $q['friendlyName'];
        }

        return $result;
    }

    /**
     * Get workspace config
     *
     * @param string $workspacePrefix prefix if exists
     *
     * @return array
     */
    private function getWorkspaceConfig(string $workspacePrefix): array
    {
        if ($workspacePrefix !== 'sys') {
            $callBackHost = 'https://call-api.callpotential.com/';
        } else {
            $callBackHost = 'https://call2-api.callpotential.com/';
        }

        $callBackUrl = $callBackHost.$workspacePrefix.'/v1/twilio/workspace_event_callback/cc2';

        return [
            'EventCallbackUrl' => $callBackUrl,
           'EventsFilter' => 'task.created,task.updated,task.canceled,
           task.wrapup,task.completed,task.deleted,task.deleted,
           reservation.created,reservation.accepted,reservation.rejected,
           reservation.timeout,reservation.canceled,reservation.rescinded,
           reservation.completed,task-queue.entered,task-queue.timeout,
           task-queue.moved,workflow.target-matched,workflow.entered,
           workflow.timeout,worker.activity.update,worker.capacity.update,
           worker.channel.availability.update',
            'MultiTaskEnabled' => true,
        ];
    }

    /**
     * Update route queues
     *
     * @param array $routeConfig  route config
     * @param array $twilioQueues twilio queues
     *
     * @return array
     */
    private function updateRouteQueues(array $routeConfig, array $twilioQueues): array
    {
        $this->infoMessage(" Inside updateRouteQueues function " . PHP_EOL);
        switch ($routeConfig['type']) {
            case 'route_connect':
                if (array_key_exists('queue_type', $routeConfig)
                    && array_key_exists('options', $routeConfig['queue_type'])
                ) {
                            $routeQueues = $routeConfig['queue_type']['options'];
                            $this->debugMessage(
                                " Total " . count($routeQueues) .
                                 " queues for processing " . PHP_EOL
                            );

                            $selected = 0;

                    if (array_key_exists('selected', $routeConfig['queue_type'])) {
                                $selected = $routeConfig['queue_type']['selected'];
                    }

                    foreach ($routeQueues as $index => $queue) {
                        if (array_key_exists($queue['name'], $twilioQueues)) {
                            $queueName = $twilioQueues[$queue['name']];
                            if ($selected === $queue['value']) {
                                $routeConfig['queue_type']['selected'] = $queueName;
                            }

                            $routeConfig['queue_type']['options'][$index]['value'] = $queueName;
                        }
                    }
                }

                if (array_key_exists('children', $routeConfig) &&
                    count($routeConfig['children']) > 0) {
                    $this->debugMessage(
                        "Processing children for " . $routeConfig['type'] . PHP_EOL
                    );
                    $routeConfig['children'][0] = $this->updateRouteQueues(
                        $routeConfig['children'][0],
                        $twilioQueues
                    );
                }
                break;

            default:
                if (array_key_exists('children', $routeConfig) &&
                    count($routeConfig['children']) > 0) {
                    $this->debugMessage(
                        "Processing children for " . $routeConfig['type'] . PHP_EOL
                    );
                    $routeConfig['children'][0] = $this->updateRouteQueues(
                        $routeConfig['children'][0],
                        $twilioQueues
                    );
                }
                break;
        }

        return $routeConfig;
    }

    /**
     * Get Ids
     *
     * @param mixed   $associatedIds associated Ids
     * @param array   $routeData     route data
     * @param string  $type          type
     * @param array   $agentQueueIds agent queue Ids
     * @param integer $configId      config Id
     *
     * @return mixed
     */
    private function getAssociatedIds(
        $associatedIds,
        array $routeData,
        string $type = 'call_queue',
        array $agentQueueIds = [],
        int $configId = 0
    ) {
        // Processes all steps in route searching for call_center step
        // and if queue present for it is
        // assigned to the agent.
        if (is_array($routeData) && $routeData) {
            // If step is ivr or smart_route, then process all the children
            //  elements for callcenter steps.
            if ('ivr' === $routeData['type'] || 'smart_route' === $routeData['type']) {
                foreach ($routeData['children'] as $child) {
                    // Check if any callcenter steps with matching agent queue
                    //  ids are present in child
                    // steps.
                    $associatedIds = $this->getAssociatedIds(
                        $associatedIds,
                        get_object_vars($child),
                        $type,
                        $agentQueueIds,
                        $configId
                    );
                }

                // Since all the child elements are processed or call route
                // is detected to be assigned
                // to the agent if $type = call_route, so return the list.
                return $associatedIds;
            } elseif (isset($routeData['connect_type']->selected, $routeData['queue_type'])
                && 'call_center' === $routeData['connect_type']->selected
            ) {
                // If step is call center, then check if any of agent
                // assigned queues is present in the
                // callcenter step (if $type = call_route) or find
                // assigned queue_id for the call center
                // (if $type = call_queue).
                foreach ($routeData['queue_type'] as $key => $value) {
                    switch ($type) {
                        case 'call_route':
                            if ('selected' === $key && in_array($value, $agentQueueIds)) {
                                    // Assign config_id to allowed list and
                                    //  return the list since it is
                                    // detected that agent is assigned to the call route.
                                if (! in_array($configId, $associatedIds)) {
                                    $associatedIds[] = $configId;
                                }

                                return $associatedIds;
                            }

                            break;
                        case 'call_queue':
                            if ('selected' === $key && ! in_array($value, $associatedIds)) {
                                    // Assign queue_id present in call center
                                    //  to associated queue ids.
                                    $associatedIds[] = $value;
                            }

                            break;
                    }
                }
            }

            // For any other step except ivr, control comes here and then we proceed with the next
            // child element step.
            $routeData = isset($routeData['children'][0])
                ? get_object_vars($routeData['children'][0]) : [];
            $associatedIds = $this->getAssociatedIds(
                $associatedIds,
                $routeData,
                $type,
                $agentQueueIds,
                $configId
            );
        }

        return $associatedIds;
    }

    /**
     * Set Activities
     *
     * @param array $activities Acitivities Array
     *
     * @return array
     */
    private function setActivityState(array $activities = []) : array
    {
        $required = [];
        foreach ($activities as $activity) {
            switch ($activity->friendlyName) {
                case 'Ready':
                    $this->debugMessage("Matched Ready activity: ".$activity->sid);
                    $required['ready'] = $activity;
                    break;
                case 'Offline':
                    $this->debugMessage("Matched Offline activity: ".$activity->sid);
                    $required['offline'] = $activity;
                    break;
                case 'On-call':
                    $this->debugMessage("Matched On-Call activity: ".$activity->sid);
                    $required['oncall'] = $activity;
                    break;
                case 'Cool-Down':
                    $this->debugMessage("Matched Cool-Down activity: ".$activity->sid);
                    $required['cooldown'] = $activity;
                    break;
                case 'Unavailable':
                    $this->debugMessage("Matched Unavailable activity: ".$activity->sid);
                    $required['unavailable'] = $activity;
                    break;
                case 'No-Answer':
                    $this->debugMessage("Matched No-Answer activity: ".$activity->sid);
                    $required['noanswer'] = $activity;
                    break;
                case 'Rejected':
                    $this->debugMessage("Matched Rejected activity: ".$activity->sid);
                    $required['rejected'] = $activity;
                    break;
                case 'No-Mic':
                    $this->debugMessage("Matched No-Mic activity: ".$activity->sid);
                    $required['mic'] = $activity;
                    break;
                case 'Idle':
                    $required['idleActivity'] = $activity;
                    break;
                default:
                    $this->debugMessage("Found Unmanaged activity: ".$activity->friendlyName);
                    $required['optional'][] = $activity;
                    break;
            }
        }

        return $required;
    }
}
