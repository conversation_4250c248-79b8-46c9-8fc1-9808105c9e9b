import { Injectable } from "@nestjs/common";
import { AsyncWorkerEvent, IAsyncWorkersInvoker } from "../models/async-workers.model";
import { AsyncWorkersIntegrationService } from "../integrations/lambda/async-workers.integration.service";

@Injectable()
export class AsyncWorkersService implements IAsyncWorkersInvoker {
  constructor(
    private readonly service: AsyncWorkersIntegrationService,
  ) { }
  async invokeAsyncWorker(event: AsyncWorkerEvent): Promise<void> {
    await this.service.invokeAsyncWorker(event);
  }
}