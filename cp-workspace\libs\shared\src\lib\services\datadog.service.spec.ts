import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { DataDogService } from './datadog.service';
import { StatsD } from 'hot-shots';

jest.mock('hot-shots');

describe('DataDogService', () => {
  let service: DataDogService;
  let statsDClientMock: jest.Mocked<StatsD>;

  beforeAll(() => {
    jest.useFakeTimers();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DataDogService, ConfigService],
    }).compile();

    service = module.get<DataDogService>(DataDogService);
    statsDClientMock = StatsD.prototype as jest.Mocked<StatsD>;
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // it('should log an event', () => {
  //   service.logEvent('Test Event', 'This is a test event');
  //   expect(statsDClientMock.event).toHaveBeenCalledWith('Test Event', 'This is a test event', {}, undefined);
  // });

  it('should increment a counter', () => {
    service.incrementCounter('test.counter');
    expect(statsDClientMock.increment).toHaveBeenCalledWith(
      'test.counter',
      1,
      undefined
    );
  });

  it('should record a gauge', () => {
    service.recordGauge('test.gauge', 42);
    jest.advanceTimersByTime(30000);
    expect(statsDClientMock.gauge).toHaveBeenCalledWith(
      'test.gauge',
      42,
      undefined
    );
  });

  // it('should record a histogram', () => {
  //   service.recordDistribution('test.histogram', 123);
  //   jest.advanceTimersByTime(30000);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.avg', 123, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.count', 1, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.median', 123, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.95percentile', 123, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.max', 123, undefined);
  // });

  it('should flush gauge values', () => {
    service.recordGauge('test.gauge', 42);
    jest.advanceTimersByTime(30000);
    expect(statsDClientMock.gauge).toHaveBeenCalledWith(
      'test.gauge',
      42,
      undefined
    );
  });

  // it('should flush histogram values', () => {
  //   service.recordDistribution('test.histogram', 123);
  //   jest.advanceTimersByTime(30000);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.avg', 123, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.count', 1, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.median', 123, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.95percentile', 123, undefined);
  //   expect(statsDClientMock.histogram).toHaveBeenCalledWith('test.histogram.max', 123, undefined);
  // });
});
