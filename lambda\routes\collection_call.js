const querystring = require('querystring');
const Twilio = require('twilio');
const AWS = require('aws-sdk');
const express = require('express');
const config = require('../config/config');
const moment_tz = require('moment-timezone');
const cpapiClient = require('../libraries/cpapi-client');
const router = express.Router();
const redis = require('redis');
const locationSettings = require('../config/location_setting/location-settings');
const got = require('got');

Array.prototype.forEachAsync = async function (fn) {
    for (let t of this) { await fn(t) }
};

function getAccountDetail(accountSid, callback) {
  const params = {
    TableName: config.dynamodb.acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: "account_sid = :sid",
    ExpressionAttributeValues: {
      ":sid": accountSid
    }
  };

  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  dynamo.query(params, function (err, dynamoData) {
    if (err) {
      return callback(new Error("Unable to query. Error:", JSON.stringify(err, null, 2)));
    } else {
      if (!dynamoData.Items || (dynamoData.Items && !dynamoData.Items.length)) {
        return callback(new Error("Account not found: params", JSON.stringify(params)));
      } else {
        return callback(null, dynamoData.Items[0]);
      }
    }
  });
}

function getAccountDetailAsync(accountSid) {
  return new Promise((resolve, reject) => {
    getAccountDetail(accountSid, (err, acct) => {
      if (err) reject(err);
      else resolve(acct);
    });
  });
}

/*
 * Route to process collection callback for auto collection call
 */
router.route('/collection_callback')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;

    if (getData.attempt) {
      getData.attempt++;
    } else {
      getData.attempt = 1;
    }
    let twiml = new Twilio.twiml.VoiceResponse();
    const machineDetectionComplete = getData.machineDetectionComplete ? getData.machineDetectionComplete : false;

    const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
    const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
    const locData = await locClient.getLocationConfiguration(getData.location_id);
    const outboundCallMessage = locData.record_call_announcement ? locData.record_call_announcement : 'false';
    const collectionsMp3Message = getData.mp3_file ? getData.mp3_file : 'false';
    const isNewPbpEnabled = await locationSettings.getLocationSetting('CollectionsManager::PayByPhone::UseBeta', locData.user_id, getData.location_id);

    if (!machineDetectionComplete) {
      if (outboundCallMessage && outboundCallMessage !== 'false') {
        twiml.say(outboundCallMessage);
      }

      if (collectionsMp3Message && collectionsMp3Message !== 'false') {
        twiml.play(collectionsMp3Message);
      }

      let redirectUrl;

      if (isNewPbpEnabled) {
        redirectUrl = `${config.call_url}collection_call/redirect_to_pbp?${querystring.stringify(getData)}`;
      } else {
        redirectUrl = `${config.call_url}collection_call/collection_callback?${querystring.stringify({ ...getData, machineDetectionComplete: true })}`;
      }

      twiml.redirect(redirectUrl);
      res.status(200);
      res.set('Content-Type', 'text/xml');
      res.send(twiml.toString());
      return;
    }

    if (getData.attempt > 3) {
      let dateCompleted = moment_tz.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss');
      let collectionPayload = { 'date_completed': dateCompleted, 'outcome': 'no-answer' };
      await intClient.putData(`tenant/${getData.tenantEsId}/collection/${getData.report_id}`, collectionPayload);
      let callPayload = { 'twilio_id': postData.CallSid, 'call_status': 'no-answer' };
      await callClient.putData(`calldetail/${getData.log_id}`, callPayload);
      twiml.say('Invalid Response. Disconnecting...');
      twiml.hangup();
      res.status(200);
      res.set('Content-Type', 'text/xml');
      res.send(twiml.toString());
      return;
    }

    if (getData.report_id) {
      let dateCompleted = moment_tz.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss');
      let collectionPayload = { 'date_completed': dateCompleted, 'outcome': postData.AnsweredBy || 'unknown' };
      await intClient.putData(`tenant/${getData.tenantEsId}/collection/${getData.report_id}`, collectionPayload);
      let callPayload = { 'twilio_id': postData.CallSid, 'call_status': postData.AnsweredBy || 'unknown' };
      await callClient.putData(`calldetail/${getData.log_id}`, callPayload);

      let paymentExcluded = false;
      getData.accountId = locData.user_id;
      const ledgerData = await intClient.getData(`tenant/${getData.tenantEsId}/ledger?skipCache=false`);
      if (ledgerData && ledgerData.items[0]) {
        ledgerData.items.forEach((ledger) => {
          let today = moment_tz();
          let paidThruDate = new Date(ledger.paid_thru_date);
          let daysPastDue = today.diff(paidThruDate, 'days');
          if (ledger.status != 'Current' && locData.exclude_payment_link < daysPastDue) {
            paymentExcluded = true;
          }
        });
      }

      let queryString = querystring.stringify(getData, '&', '=');
      let gatherCallback = `${config.call_url}collection_call/collection_response?${queryString}`;
      let collectionCallback = `${config.call_url}collection_call/collection_callback?${queryString}`;
      let gather = twiml.gather({ 'numDigits': 1, 'timeout': 5, 'action': gatherCallback });

      if (!paymentExcluded) {
        gather.say('Press 1 to make a payment.');
      }
      gather.say('Press 2 to connect to manager.');
      gather.say('Press 9 to be removed from our list.');

      twiml.redirect(collectionCallback);
      res.status(200);
      res.set('Content-Type', 'text/xml');
      res.send(twiml.toString());
      return;
    } else {
      res.status(400).send('Missing report_id');
    }
  });

/*
 * Route to process gather input from collection callback for auto collection call
 */
router.route('/collection_response')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;

    if (getData.attempt) {
      getData.attempt++;
    } else {
      getData.attempt = 1;
    }

    const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);

    let twiml = new Twilio.twiml.VoiceResponse();
    let redirectUrl = false;
    let queryString = querystring.stringify(getData, '&', '=');

    let collectionPayload = { 'outcome' : postData.Digits };
    let callPayload = { 'call_status' : postData.Digits };

    if (postData.Digits && postData.Digits == 1 && !getData.payment_excluded) {
      await intClient.putData(`tenant/${getData.tenantEsId}/collection/${getData.report_id}`, collectionPayload);
      await callClient.putData(`calldetail/${getData.log_id}`, callPayload);
      redirectUrl = `${config.call_url}collection_call/prepare_payment?${queryString}`;
    } else if (postData.Digits && postData.Digits == 2) {
      await intClient.putData(`tenant/${getData.tenantEsId}/collection/${getData.report_id}`, collectionPayload);
      await callClient.putData(`calldetail/${getData.log_id}`, callPayload);
      redirectUrl = `${config.call_url}collection_call/connect_manager?${queryString}`;
    } else if (postData.Digits && postData.Digits == 9) {
      await intClient.putData(`tenant/${getData.tenantEsId}/collection/${getData.report_id}`, collectionPayload);
      await callClient.putData(`calldetail/${getData.log_id}`, callPayload);

      const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
      const session = await coreClient.postData(`session`, {'user_id': getData.accountId});
      const acctClient = new cpapiClient.acctClient(session.session_id);

      let timestamp = moment_tz().format('YYYY-MM-DD HH:mm:ss');

      let exclusionPayload = {
        'location_id'   : getData.location_id,
        'contact_type'  : 'collection',
        'exclusion_type': 'call',
        'excluded_contact': postData.To,
        'reason': 'Auto: Received Stop Request',
        'request_sid': postData.CallSid,
        "exclusion_date": timestamp,
        "entity_type": 'customer',
      };
      await acctClient.postData(`customerexclusion`, exclusionPayload);

      twiml.say('Your number has been removed from our list.');
      twiml.hangup();
    } else {
      redirectUrl = `${config.call_url}collection_call/collection_callback?${queryString}`;
    }

    if (redirectUrl) {
      twiml.redirect(redirectUrl);
    }

    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  });

/*
 * Route to connect to manager (collection number) from auto collection call
 */
router.route('/connect_manager')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;

    const customerNumber = getData.customerNumber;

    let queryString = querystring.stringify(getData, '&', '=');
    let actionUrl = `${config.call_url}collection_call/collection_recording?${queryString}`;

    let twiml = new Twilio.twiml.VoiceResponse();

    twiml.say('Connecting you to the manager. Please hold.');
    twiml.dial({
      action: actionUrl,
      timeout: 120,
      callerId: customerNumber,
    }, getData.call_number);

    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());

    try {
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();

      let call_key = 'collectionCall-' + postData.CallSid;
      let call_key_value = 1;

      let redisTtl = 1800;
      await redisClient.set(call_key, JSON.stringify(call_key_value), {EX: redisTtl});
    } catch (e) {
      console.error(e, new Error().stack);
    }
    return;
  });

/*
 * Route to process pyament details from collection call.
 */
router.route('/prepare_payment')
  .post(async function (req, res) {
    let getData = req.query;

    const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
    let amountOwed = 0;
    getData.paymentHash = [];
    getData.paymentId = [];
    getData.ledgerId = [];
    getData.amountDue = [];
    getData.esUnitId = [];

    const ledgerData = await intClient
      .getData(`tenant/${getData.tenantEsId}/ledger?skipCache=false`);

    if (ledgerData && ledgerData.items[0]) {
      await ledgerData.items.forEachAsync(async (ledger) => {
        if ('customer_id' in ledger) {
          ledger.tenant_id = ledger.customer_id;
        }

        amountOwed = amountOwed + ledger.amount_owed;

        const paymentHashResponse = await intClient.postData('payment_hash', {
          customer_id : ledger.tenant_id,
          location_id : ledger.location_id,
          ledger_id   : ledger.ledger_id,
          source      : 'call',
        });

        let paymentHashGet = await intClient.getData(`payment_hash/${paymentHashResponse.hash}`);

        getData.paymentHash.push(paymentHashResponse.hash);
        getData.paymentId.push(paymentHashGet.items.payment_id);
        getData.ledgerId.push(ledger.ledger_id);
        getData.amountDue.push(ledger.amount_owed);
        getData.esUnitId.push(ledger.es_unit_id);
      });
    }

    let queryString = querystring.stringify(getData, '&', '=');
    let actionUrl = `${config.call_url}collection_call/collect_cc?${queryString}`;

    let twiml = new Twilio.twiml.VoiceResponse();
    twiml.say(`Your credit card will be charged for ${parseFloat(amountOwed).toFixed(2)}`);

    let gather = twiml.gather({'timeout': 10, 'action': actionUrl, 'finishOnKey': '*'});
    gather.say('Enter your credit card number followed by *.');

    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  });

/*
 * Route to collect cc detailt to process payment.
 */
router.route('/collect_cc')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;

    const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);

    let cardParams = {
      'cc_num': 'Credit card number',
      'cvc_num': 'CVC number',
      'exp_date': 'Expiration date in format m m y y.'
    };

    let twiml = new Twilio.twiml.VoiceResponse();
    let responseSet = false;
    await Object.entries(cardParams).forEachAsync(async ([key, value]) => {
      if (getData[key]) {
        getData.attempt = 1;
      } else if (postData.Digits) {
        let inputDigits = postData.Digits.replace('*', '');
        twiml.say(`You have entered ${value} as ${inputDigits.replace(/(\d)/g, "$1 ")} .`);

        let encodeData = await coreClient
          .postData(`encodetoken`, {
            'type': 'encode',
            'terms_list': [inputDigits]
          });

        getData[key] = encodeData[0];
        delete postData.Digits;
      } else {
        getData.attempt++;

        let queryString = querystring.stringify(getData, '&', '=');
        let actionUrl = `${config.call_url}collection_call/collect_cc?${queryString}`;

        if (!responseSet) {
          responseSet = true;
          let gather = twiml.gather({'timeout': 10, 'action': actionUrl, 'finishOnKey': '*'});
          gather.say(`Enter ${value} followed by *.`);

          res.status(200);
          res.set('Content-Type', 'text/xml');
          res.send(twiml.toString());
          return;
        }
      }
    });

    if (!responseSet) {
      let queryString = querystring.stringify(getData, '&', '=');
      let actionUrl = `${config.call_url}collection_call/process_payment?${queryString}`;

      let gather = twiml.gather({'timeout': 5, 'action': actionUrl, 'finishOnKey': '*', 'numDigits': 1});
      gather.say(`Press 1 to confirm payment.`);

      res.status(200);
      res.set('Content-Type', 'text/xml');
      res.send(twiml.toString());
      return;
    }
  });

/*
 * Route to intitiate payment processing and put call on wait hold loop.
 */
router.route('/process_payment')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;
    let twiml = new Twilio.twiml.VoiceResponse();

    try {
      const got = require('got');
      getData.account_sid = postData.AccountSid;
      getData.callSid = postData.CallSid;
      let queryString = querystring.stringify(getData, '&', '=');
      let paymentUrl = `${config.call_url}collection_call/process_payment_async?${queryString}`;
      got.get(paymentUrl);

      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });

      for (var i = 0; i < 5; i++) {
        twiml.say('Your Payment is being processed! Please wait!');
        twiml.pause({'length': 10});
      }
    } catch (e) {
      console.error(e, new Error().stack);
    }

    twiml.say('We were unable to process your payment. Please wait while we connect you to a manager.');
    let queryString = querystring.stringify(getData, '&', '=');
    let redirectUrl = `${config.call_url}collection_call/connect_manager?${queryString}`;
    twiml.redirect(redirectUrl);

    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  });

/*
 * Route to process payment async
 */
router.route('/process_payment_async')
  .get(async function (req, res) {
    let getData = req.query;
    let cardParamList = [getData.cc_num, getData.cvc_num, getData.exp_date];
    const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
    const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
    const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);

    const locData = await locClient
        .getLocationConfiguration(getData.location_id);

    let decodeCardData = await coreClient
      .postData(`encodetoken`, {
        'type': 'decode',
        'terms_list': cardParamList
      })
      .catch(error => console.log(error, new Error().stack));

    let payIndex = 0;

    getAccountDetail(getData.account_sid, async (err, acct) => {
      if (err) {
        console.log(err, new Error().stack);
        res.send();
        return;
      }

      const twilio = new Twilio(acct.account_sid, acct.authtoken);

      if (!Array.isArray(getData.paymentHash)) {
        getData.paymentHash = [getData.paymentHash];
        getData.amountDue = [getData.amountDue];
        getData.esUnitId = [getData.esUnitId];
        getData.paymentId = [getData.paymentId];
        getData.ledgerId = [getData.ledgerId];
      }

      let bulkPayment = [];

      await getData.paymentHash.forEachAsync(async (paymentHash) => {
        let paymentData = {
          amount           : getData.amountDue[payIndex],
          total_amount_due : getData.amountDue[payIndex],
          unit_id          : getData.esUnitId[payIndex],
          payment_id       : getData.paymentId[payIndex],
          ledger_id        : getData.ledgerId[payIndex],
          convenience_fee  : false,
          cvc_number       : decodeCardData[1],
          card_number      : decodeCardData[0],
          exp_month        : decodeCardData[2].substring(0, 2),
          exp_year         : '' + '20' + decodeCardData[2].substring(2),
          is_auto_pay      : false,
          pay_method       : 'creditcard',
          payment_hash     : paymentHash,
          save_cc          : locData.save_cc == 1 ? true : false,
        };

        if (locData.convenience_fee_phone && locData.convenience_fee_phone > 0 && payIndex == 0) {
          paymentData.convenience_fee_amount = locData.convenience_fee_phone;
          paymentData.convenience_fee = true;
          paymentData.convenience_fee_source = 'call';
        }

        bulkPayment.push(paymentData);
      });

      if (locData.allow_bulk_payment && locData.allow_bulk_payment == 1) {
        const makePayment = await intClient
          .postData(`tenant/${getData.tenantEsId}/payment_bulk`, bulkPayment)
          .catch(error => {
            console.error(error, new Error().stack);
          });

        if (makePayment.success) {
          getData.payStatus = 'success';
        } else {
          getData.payStatus = 'failure';
        }
      } else {
        await bulkPayment.forEachAsync(async (paymentItem) => {
          if (!getData.payStatus || getData.payStatus != 'failure') {
            const makePayment = await intClient
              .postData(`tenant/${getData.tenantEsId}/payment`, paymentItem)
              .catch(error => {
                console.error(error, new Error().stack);
              });

            if (makePayment.success) {
              getData.payStatus = 'success';
            } else {
              getData.payStatus = 'failure';
            }
          }
        })
      }

      try {
        let queryString = querystring.stringify(getData, '&', '=');
        await twilio.calls(getData.callSid)
              .update({method: 'POST', url: `${config.call_url}collection_call/payment_status?${queryString}`})
              .catch(error => console.log(error, new Error().stack));
      } catch (e) {
        console.log(e, new Error().stack);
      }

      res.status(200);
      res.send();
      return;
    });
  });

/*
 * Route to process payment status
 */
router.route('/payment_status')
  .post(async function (req, res) {
    let getData = req.query;
    let twiml = new Twilio.twiml.VoiceResponse();

    if (getData.payStatus && getData.payStatus == 'success') {
      twiml.say('Payment was processed successfully! Thank You!');
    } else {
      let queryString = querystring.stringify(getData, '&', '=');
      twiml.say('We were unable to process your payment. Please wait while we connect you to a manager.');
      let redirectUrl = `${config.call_url}collection_call/connect_manager?${queryString}`;
      twiml.redirect(redirectUrl);
    }

    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  });

/*
 * Route to process payment async
 */
router.route('/collection_recording')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;

    const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
    let twiml = new Twilio.twiml.VoiceResponse();

    let dateCompleted = moment_tz.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss');
    let recordingUrl = (postData.RecordingUrl) ? postData.RecordingUrl : '';
    let recordingSid = (postData.RecordingSid) ? postData.RecordingSid : '';

    let collectionPayload = {
      'date_completed': dateCompleted,
      'outcome': postData.AnsweredBy,
      'length': postData.DialCallDuration,
      'details': recordingUrl
    };
    await intClient.putData(`tenant/${getData.tenantEsId}/collection/${getData.report_id}`, collectionPayload);

    let callPayload = {
      'recording_url': recordingUrl,
      'duration': postData.DialCallDuration,
      'twilio_id': postData.CallSid,
      'recording_sid': recordingSid
    };
    await callClient.putData(`calldetail/${getData.log_id}`, callPayload);

    if (postData.DialCallDuration && postData.DialCallDuration > 0) {
      twiml.hangup();

      const smsClient = new cpapiClient.smsClient(config.db.serviceTokens.readWrite);
      const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
      let longUrl = `${config.API_CALL_URL}/recording/${postData.CallSid}`;
      let shortUrl = await smsClient.getData(`shortner?url=${longUrl}`);
      let noteString = `Auto > Collection call to ${postData.To} > Play ${shortUrl.short_url}`;
      let notedata = {
        'note': noteString,
      }

      await intClient.postData(`tenant/${getData.tenantEsId}/ledger/${getData.ledger_id}/note`, notedata);
    } else {
      let msg = 'The customer has disconnected the call or the phone number is invalid.';

      if ('busy' == postData.DialCallStatus) {
          msg = 'A busy signal was received when trying to connect to the customer. Please try again after sometime.';
      }
      twiml.say(msg);
    }

    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  });

router.route('/call_status_callback/')
  .post(async function (req, res) {
    let getData = req.query;
    let postData = req.body;
    let twiml = new Twilio.twiml.VoiceResponse();

    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
    let callPayload = { 'twilio_id' : postData.CallSid };

    if (postData.AnsweredBy == 'machine') {
      callPayload.call_status = postData.AnsweredBy;
    }

    await callClient.putData(`calldetail/${getData.log_id}`, callPayload);

    const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
    await redisClient.connect();

    let call_key = 'collectionCall-' + postData.CallSid;
    const call_key_value = await redisClient.get(call_key);

    if (!call_key_value) {
      const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
      let noteString = `Auto > Collection call to ${postData.To}`;
      let notedata = {
        'note': noteString,
      }

      intClient.postData(`tenant/${getData.tenantEsId}/ledger/${getData.ledger_id}/note`, notedata);
      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });
    }
    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  });

router.route('/amd_status_callback/')
  .post(async function (req, res) {
    let postData = req.body;
    let getData = req.query;
    

    const machineAnswered = postData.AnsweredBy.startsWith('machine') ? true : false;
    getData.machineAnswered = machineAnswered;
    const callSid = postData.CallSid;
    const accountSid = postData.AccountSid;
    getData.customerNumber = postData.To;

    const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);
    const locData = await locClient.getLocationConfiguration(getData.location_id);
    const outboundCallMessage = locData.record_call_announcement ? locData.record_call_announcement : false;
    const isPbpRefactorFeatureEnabled = await locationSettings.getLocationSetting('CollectionsManager::PayByPhone::UseBeta', locData.user_id, getData.location_id);
    getData.isNewPbpEnabled = isPbpRefactorFeatureEnabled;
    getData.outboundCallMessage = outboundCallMessage;

    let redirectUrl;

    if (machineAnswered) {
      redirectUrl = `${config.call_url}collection_call/voicemail_flow?${querystring.stringify(getData)}`;
    }

    try {
      const acct = await getAccountDetailAsync(accountSid);
      const twilio = new Twilio(acct.account_sid, acct.authtoken);
      await twilio.calls(callSid).update({
        method: 'POST',
        url: redirectUrl
      });
      res.status(200).send('');
    } catch (error) {
      console.error('Error in voicemail callback:', error);
      res.status(500).send('Error');
    }
  });

router.route('/voicemail_flow/')
  .post(async function (req, res) {
    let getData = req.query;
    let twiml = new Twilio.twiml.VoiceResponse();

    const outboundCallMessage = getData.outboundCallMessage ? getData.outboundCallMessage : 'false';
    const collectionsMp3Message = getData.mp3_file ? getData.mp3_file : 'false';
    const isNewPbpEnabled = getData.isNewPbpEnabled;

    if (outboundCallMessage && outboundCallMessage !== 'false') {
      twiml.say(outboundCallMessage);
    }
    
    if (collectionsMp3Message && collectionsMp3Message !== 'false') {
      twiml.play(collectionsMp3Message);
    }

    let queryString = querystring.stringify({ ...getData, machineDetectionComplete: 'true' }, '&', '=');
    if (isNewPbpEnabled) {
      twiml.redirect(`${config.call_url}collection_call/redirect_to_pbp?${queryString}`);
      res.status(200);
      res.set('Content-Type', 'text/xml');
      res.send(twiml.toString());
      return;
    }

    twiml.redirect(`${config.call_url}collection_call/collection_callback?${queryString}`);
    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
  });

/*
 * Route to handle redirecting to pay-by-phone after playing mp3
 */
router.route('/redirect_to_pbp')
.post(async function (req, res) {
  const getData = req.query;
  const postData = req.body;

  const callSid = postData.CallSid;
  const locationId = getData.location_id;
  const tenantId = getData.tenantEsId;

  // Validate required parameters
  if (!callSid || !locationId || !tenantId) {
    const twiml = new Twilio.twiml.VoiceResponse();
    twiml.say('An error occurred. Please try again later.');
    twiml.hangup();
    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
    return;
  }

  const url = config.comms_api_url + 'pay-by-phone';
  const queryString = querystring.stringify(getData, '&', '=');
  const postBody = {
    CallSid: callSid,
    LocationId: locationId,
    TenantId: tenantId,
    OverrideEntryState: 'LocalePrompt',
    TransferToAgentUrl: `${config.call_url}collection_call/connect_manager?${queryString}`,
    PaymentFailureRedirectUrl: `${config.call_url}collection_call/connect_manager?${queryString}`,
    Origin: 'collections',
  };

  const options = {
    body: JSON.stringify(postBody),
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await got.post(url, options);
    console.log('new pbp initiated successfully in collections:', response.body);
    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(response.body);
  } catch (error) {
    console.error('Error contacting PBP API:', error);
    const twiml = new Twilio.twiml.VoiceResponse();
    twiml.say('An error occurred while connecting to the payment system.');
    twiml.hangup();
    res.status(200);
    res.set('Content-Type', 'text/xml');
    res.send(twiml.toString());
  }
});

module.exports = router;
