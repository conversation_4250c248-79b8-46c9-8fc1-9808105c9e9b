import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase, PayByPhoneSayAttributes } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class UnitsToPayPrompt extends PayByPhoneStateBase {
  private maximumUnits = 7;
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;

    if(!storage.matchedTenants || storage.matchedTenants.length === 0) {
      return { nextState: PayByPhoneState.TransferToAgent };
    }
  
    let currentUnitsCount = 0;
    const messages: PayByPhoneSayAttributes[] = [];

    messages.push({
      messageId: 'pay-by-phone.multiple-accounts',
      locale: storage.locale
    });
    messages.push({
      messageId: 'pay-by-phone.pay-all',
      locale: storage.locale
    });
    
    for (const tenant of storage.matchedTenants) {
      if (currentUnitsCount >= this.maximumUnits) break;
      for (const ledger of tenant.ledgers) {
        if (currentUnitsCount >= this.maximumUnits) break;
        const tenantName = `${tenant.first_name} ${tenant.last_name}`;
        const unitName = ledger.unit_name;
    
        messages.push({
          messageId: 'pay-by-phone.account-select',
          locale: storage.locale,
          i18nOptions: { args: [{keyPress: (currentUnitsCount + 2).toString()}, {tenantName: tenantName}, {unitName: unitName}]}
        });
    
        currentUnitsCount++;
      }
    }
    
    messages.push({
      messageId: 'pay-by-phone.start-over',
      locale: storage.locale
    });
    
    twilioResponse.gatherWithLocaleSay({
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    }, messages);
    
    return { nextState: PayByPhoneState.UnitsToPaySelection };
  }
}
