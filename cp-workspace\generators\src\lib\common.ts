import { promises as fs } from "fs";
import { join } from "path";

export interface CodeArtifact {
  fileName: string;
  codeContent: string;
  overwrite?: boolean;
}

/**
 * Writes a file to the specified location containing generated code.
 * @param outputPath The location where the artifact should be written
 * @param artifact The artifact to be written
 */
export async function saveArtifact(outputPath: string, artifact: CodeArtifact) {
  const outputFilePath = !outputPath ? artifact.fileName : join(outputPath, artifact.fileName).replace(/\\/g, "/");
  console.log(`Writing file: ${outputFilePath}`);
  const outputDir = outputFilePath.substring(0, outputFilePath.lastIndexOf("/"));
  try {
    await ensureDir(outputDir);
    /**
     * if overwrite is false, check if the file already exists
     * before writing the file. If it does, skip writing the file.
     */
    if (!artifact.overwrite) {
      try {
        await fs.access(outputFilePath);
        console.log(`File already exists at ${outputFilePath}. Skipping write.`);
        return;
      } catch (error) {
        // File does not exist. Proceed to write the file.
      }
    }
    await fs.writeFile(outputFilePath, artifact.codeContent);
    console.log(`File written successfully to ${outputFilePath}`);
  } catch (error) {
    console.error("Failed to write file:", error);
  }
}

/**
 * This function will convert between the two formats:
 * this-is-a-test -> ThisIsATest
 * ThisIsATest -> this-is-a-test
 */
export function convertCase(input: string, toCamelCase = true): string {
  if (toCamelCase) {
    return input.replace(/-([a-z])/g, function (g) {
      return g[1].toUpperCase();
    }).replace(/^\w/, (c) => c.toUpperCase());
  } else {
    return input.replace(/([A-Z])/g, function (g) {
      return "-" + g[0].toLowerCase();
    });
  }
}

export type ServiceDescription = {
  [serviceName: string]: { methods: { [methodName: string]: Operation } };
};

export interface MetaDataDescription {
  [sourceFilePath: string]: ServiceDescription
}

export type Operation = {
  params: { [key: string]: string };
  returnType: string;
};

async function ensureDir(outputPath: string) {
  try {
    await fs.mkdir(outputPath, { recursive: true });
  } catch (error) {
    throw new Error(`Failed to create directory: ${outputPath}`);
  }
}

