<?php
/**
 * Twilio sync service library
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: cwalker
 * Date: 12/18/17
 * Time: 8:31 PM
 *
 * @category SyncService
 * @package  CallCenter
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

namespace CallCenter;

use Mockery\Exception;
use Swagger\Util;
use \Twilio\Exceptions\RestException;
use Twilio\Rest\Client;

use CallPotential\CPCommon\Cli\CliEvent;
use CallPotential\CPCommon\Cli\CliSubjectTrait;
use CallPotential\CallCenter\TwilioSync;

/**
 * Twilio sync service library
 *
 * @category SyncService
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class SyncService
{
    use CliSubjectTrait;

    /**
     * Account Id
     *
     * @var integer|null
     */
    private $accountId = null;

    /**
     * Sync
     *
     * @var object|null
     */
    private $sync = null;

    /**
     * Service
     *
     * @var object
     */
    public $service;

    /**
     * Documents
     *
     * @var array
     */
    public $documents = [];

    /**
     * Maps
     *
     * @var array
     */
    public $maps = [];

    /**
     * Workers
     *
     * @var array
     */
    public $workers = [];

    /**
     * Constructor to initialize data
     *
     * @param int $accountId account Id
     */
    public function __construct(int $accountId)
    {
        $this->accountId = $accountId;
        $this->sync = new TwilioSync($this->accountId);
        $this->CliSubjectName = 'sync-config';

        return $this;
    }

    /**
     * Add new worker to workspace
     *
     * @param string  $workerSid         worker SID
     * @param integer $agentId           agent Id
     * @param string  $friendlyName      worker name
     * @param string  $email             email address
     * @param string  $status            status to set
     * @param string  $statusSid         status SID
     * @param string  $dateStatusChanged date when status changed
     *
     * @return object
     */
    public function addWorker(
        string $workerSid,
        int $agentId,
        string $friendlyName,
        string $email,
        string $status,
        string $statusSid,
        string $dateStatusChanged
    ) {
        $this->workers[$workerSid] = [
            'name' => $friendlyName,
            'agent_id' => $agentId,
            'email' => $email,
            'status' => $status,
            'status_sid' => $statusSid,
            'status_change_date' => $dateStatusChanged,
        ];

        return $this;
    }

    /**
     * Validate sync service
     *
     * @param string $prefix prefix if exists
     *
     * @return void
     */
    public function validateSyncService(string $prefix = '')
    {
        $this->manageService($prefix);
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "Service Verified",
                null,
                ['progress' => 20]
            )
        );
        $this->manageSyncData();
    }

    /**
     * Delete twilio sync service
     *
     * @param string $prefix prefix if exists
     *
     * @return boolean
     */
    public function deleteSyncService(string $prefix = ''): bool
    {
        $this->infoMessage("Checking Call Center Sync Service");
        $services = $this->sync->listServices();
        foreach ($services as $s) {
            if ($s->friendlyName === $prefix . '-CallCenter') {
                $this->debugMessage('Existing Call Center Sync Service found: '.$s->sid);
                $response = $this->sync->deleteService($s->sid);
                $this->debugMessage('Sync Service deletion response: '.print_r($response, true));

                return true;
            }
        }

        $this->debugMessage('Sync Service not found: '.$prefix.'-CallCenter');

        return false;
    }

    /**
     * Get sync API key
     *
     * @return mixed
     */
    public function getSyncApiKey()
    {
        $model = new \TwilioAccount();
        $account = $model->findById($this->accountId)->toArray();
        //$secret = \CallPotential\CPCommon\Util::array_get('sync_key_secret', $account);
        $secret = \CallPotential\CPCommon\Util::array_get('api_secret', $account);
        $syncKey = false;
        if (!strlen($secret)) {
            $this->debugMessage('Creating new sync API key');
            $client = new Client($account['account_sid'], $account['authtoken']);
            $syncKey = $client->newKeys->create(array('friendlyName' => 'syncService'));
        }

        return $syncKey;
    }

    /**
     * Mange sync service
     *
     * @param string $prefix prefix if exists
     *
     * @return object
     */
    public function manageService(string $prefix = '')
    {
        $this->infoMessage("Checking Call Center Sync Service");
        $services = $this->sync->listServices();
        foreach ($services as $s) {
            if ($s->friendlyName === $prefix . '-CallCenter') {
                $this->service = $s;
                $this->debugMessage(
                    'Existing Call Center Sync Service found: '.$this->service->sid
                );
                break;
            }
        }
        if (is_null($this->service)) {
            $serviceConfig = $this->getServiceConfig();
            $this->debugMessage(
                'Creating Call Center Sync Service with configuration: '.
                print_r($serviceConfig, true)
            );
            $this->service = $this->sync->createService($prefix . '-CallCenter', $serviceConfig);
        }
        $this->infoMessage("Call Center Sync Service Complete. SID:".$this->service->sid);

        return $this;
    }

    /**
     * Manage service data
     *
     * @return object
     */
    public function manageSyncData()
    {
        $this->infoMessage("Verifying Sync Data");

        $this->infoMessage("Verifying Sync Maps");
        $this->manageMap('agent_status');
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "agent_status Map Verified",
                null,
                ['progress' => 40]
            )
        );

        $this->manageMap('task_list');
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "task_list Map Verified",
                null,
                ['progress' => 80]
            )
        );

        $this->manageMap('agent_stats');
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "agent_stats Map Verified",
                null,
                ['progress' => 80]
            )
        );

        $this->manageMap('queue_stats');
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "queue_stats Map Verified",
                null,
                ['progress' => 80]
            )
        );

        $this->manageDocument('workspace_stats');
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "workspace_stats Document Verified",
                null,
                ['progress' => 60]
            )
        );

        $this->infoMessage("Sync Data Verified");
        if (count($this->workers) > 0) {
            $this->infoMessage("Verifing worker status data");
            $items = $this->sync->listMapItems($this->service->sid, 'agent_status', '', 100);
            foreach ($items as $item) {
                $workerSid = $item->key;
                if (array_key_exists($workerSid, $this->workers)) {
                    unset($this->workers[$workerSid]);
                    $this->debugMessage("Worker $workerSid already in sync map");
                }
            }
            if (count($this->workers) > 0) {
                foreach ($this->workers as $sid => $workerData) {
                    $this->sync->addItemToMap(
                        $this->service->sid,
                        'agent_status',
                        $sid,
                        $workerData
                    );
                    $this->debugMessage("Worker $sid added to sync map");
                }
            }
            $this->infoMessage("Worker status data verified");
        } else {
            $this->infoMessage("Worker status not data verified, no workers loaded");
        }
        $this->notify(
            new CliEvent(
                CliEvent::CLI_EVENT_STATUS,
                "worker status data verified",
                null,
                ['progress' => 100]
            )
        );

        return $this;
    }

    /**
     * Method log information messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function infoMessage(string $msg, $src = null)
    {
        unset($src);
        echo $msg."\n";
        $this->notify(new CliEvent(CliEvent::CLI_EVENT_INFO, $msg));
    }

    /**
     * Method log debug messages to log file.
     *
     * @param string     $msg log message
     * @param mixed|null $src source
     *
     * @return void
     */
    public function debugMessage(string $msg, $src = null)
    {
        unset($src);
        echo $msg."\n";
        $this->notify(new CliEvent(CliEvent::CLI_EVENT_DEBUG, $msg));
    }

    /**
     * Manager document
     *
     * @param string     $uniqueName  document name
     * @param array|null $initialData data
     * @param integer    $ttl         time to live
     *
     * @throws Exception
     * @return object
     */
    protected function manageDocument(string $uniqueName, array $initialData = null, int $ttl = 0)
    {
        $this->infoMessage("Verifying Sync Document ".$uniqueName);
        try {
            $doc = $this->sync->getDocument($this->service->sid, $uniqueName);
            $this->debugMessage('Sync document '.$uniqueName.' matched. SID: '.$doc->sid);
        } catch (RestException $e) {
            if ($e->getStatusCode() === 404) {
                $doc = $this->sync->createDocument(
                    $this->service->sid,
                    $uniqueName,
                    $initialData,
                    $ttl
                );
                $this->debugMessage('Created sync document '.$uniqueName.' SID: '.$doc->sid);
            } else {
                throw new \Exception($e->getMessage(), $e->getStatusCode(), $e);
            }
        }

        $this->documents[$uniqueName] = $doc;
        $this->infoMessage("Sync document $uniqueName verified. SID:".$doc->sid);

        return $doc;
    }

    /**
     * Manage map
     *
     * @param string  $uniqueName map name
     * @param integer $ttl        time to live
     *
     * @return object
     */
    protected function manageMap(string $uniqueName, int $ttl = 0)
    {
        $this->infoMessage("Verifying Sync Map ".$uniqueName);
        try {
            $map = $this->sync->getMap($this->service->sid, $uniqueName);
            $this->debugMessage('Sync map '.$uniqueName.' matched. SID: '.$map->sid);
        } catch (RestException $e) {
            $map = $this->sync->createMap($this->service->sid, $uniqueName, $ttl);
            $this->debugMessage('Created sync map '.$uniqueName.' SID: '.$map->sid);
        }
        $this->maps[$uniqueName] = $map;
        $this->infoMessage("Sync map $uniqueName verified. SID:".$map->sid);

        return $map;
    }

    /**
     * Gey sync service config
     *
     * @return array
     */
    private function getServiceConfig(): array
    {
        return [
            'webhookUrl' => '',
            'aclEnabled' => false,
            'reachabilityWebhooksEnabled' => false,
        ];
    }
}
