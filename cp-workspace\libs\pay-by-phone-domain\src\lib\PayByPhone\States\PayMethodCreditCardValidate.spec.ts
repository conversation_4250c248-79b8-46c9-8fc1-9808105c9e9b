import { Test, TestingModule } from '@nestjs/testing';
import { PayMethodCreditCardValidate } from './PayMethodCreditCardValidate';
import { ApiType, PayByPhoneStateContext, PayByPhoneStorage } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { Locale } from '@cp-workspace/shared';


describe('PayMethodCreditCardValidate', () => {
  let service: PayMethodCreditCardValidate;
  let mockBinCheckResponse = {surchargeable: true};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayMethodCreditCardValidate,
        {
          provide: 'LocationService',
          useValue: {
            getLocationDetails: jest.fn(),
          }
        },
        {
          provide: 'CoreService',
          useValue: {
            encodePaymentToken: jest.fn(),
            decodePaymentToken: jest.fn().mockResolvedValue({ cardNumber: '****************' }),
          }
        },
        {
          provide: 'Services',
          useValue: {
            coreService: {
              encodePaymentToken: jest.fn(),
            },
            integrationService: {
              doBinCheck: jest.fn().mockResolvedValue(mockBinCheckResponse),
            },
            locationService: {
              getLocationDetails: jest.fn().mockResolvedValue({
                api_type: 9,
                user_id: 'user123'
              }),
            },
          },
        },
      ],
    }).compile();

    service = module.get<PayMethodCreditCardValidate>(
      PayMethodCreditCardValidate
    );
    service.services = module.get('Services');
  });

  it('should return nextState as PayMethodCreditCardPrompt when credit card number is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
  });

  it('should return nextState as PayMethodCreditCardConfirm when credit card number is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodCreditCardConfirm);
  });

  it('should call sayInLocale with correct arguments when credit card number is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;


    await service.handler(context);

    expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({
      messageId: 'pay-by-phone.invalid-input',
      locale: context.storage.locale
    });
  });

  it('should call encodePaymentToken with correct argument when credit card number is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;


    await service.handler(context);

    expect(
      service.services.coreService.encodePaymentToken
    ).toHaveBeenCalledWith({
      cardNumber: context.request.Digits,
    });
  });

  it('should call gatherWithLocaleSay with correct arguments when credit card number is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;


    await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      numDigits: 1,
      timeout: 10
    }, [{
      messageId: "pay-by-phone.cc-confirm",
      locale: context.storage.locale,
      i18nOptions: {args: [{enteredNumber: context.request.Digits?.split('').join(' ')}]}
    }]);
  });

  describe('when matchedTenants length is greater than zero', () => {
    let storage: PayByPhoneStorage = {
      state: PayByPhoneState.PayMethodCreditCardValidate,
      locationId: 0,
      locale: Locale.English,
      transferToAgentUrl: '',
      totalBalance: 0,
      totalAmountDue: 0,
      convenienceFee: 0,
      surchargeDetails: {
        surchargeEnabledAPITypes: [ApiType.STOREDGE],
      }
    };
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { ...storage, locationId: 0 } as any,
    } as any;

    beforeAll(() => {
      context.storage.matchedTenants = [{ customer_id: '123' }] as any;
    });


    describe('when surcharge is enabled for the api type', () => {
      beforeEach(async () => {
        await service.handler(context);
      });

      it('should call doBinCheck with correct arguments', async () => {
        // await service.handler(context);
        expect(
          service.services.integrationService.doBinCheck
        ).toHaveBeenCalledWith(
          context.request.Digits,
          context.storage.locationId
        );
      });

      describe('when bin check response is received', () => {
        describe('when surchargeable is true', () => {
          beforeAll(() => {
            mockBinCheckResponse = { surchargeable: true };
          });

          it('should set isSurchargeEnabledForCard in storage', async () => {
            expect(context.storage.surchargeDetails?.isSurchargeEnabledForCard).toBeDefined();
          });
        });

        describe('when surchargeable is false', () => {
          beforeAll(() => {
            mockBinCheckResponse = { surchargeable: false };
          });

          it('should set isSurchargeEnabledForCard in storage to false', async () => {
            expect(context.storage.surchargeDetails?.isSurchargeEnabledForCard).toBe(false);
          });
        });
      });
    });
  });
});
