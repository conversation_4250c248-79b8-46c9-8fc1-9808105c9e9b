<?php
namespace Test;

class CallHistoryTest extends IntegrationCase
{
    public function testCallHistoryGeyByIdAction()
    {
        $this->validateSwaggerPath(
            'call',
            'CallhistoryGetById',
            '/callhistory/{id}',
            'get'
        );

        $params = $this->getListParams(0);
        $response = $this->runGET('/callhistory/testcase-callhistory-120-135', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/callhistory/testcase-callhistory-120-135 GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'CallhistoryGetById', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'CallhistoryGetById', [200, 404, 500]);   
    }

    public function testCallHistoryListWithoutPagination()
    {
        $this->validateSwaggerPath(
            'call',
            'ListCallHistorys',
            '/callhistory',
            'get'
        );

        $params = $this->getListParams(1);
        $response = $this->runGET('/callhistory', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/callhistory GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'ListCallHistorys', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'ListCallHistorys', [200,401,403,500]);
    }

    public function testCallHistoryListWithPagination()
    {
        $this->validateSwaggerPath(
            'call',
            'ListCallHistorys',
            '/callhistory',
            'get'
        );

        $params = $this->getListParams(2);
        $response = $this->runGET('/callhistory', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/callhistory GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'ListCallHistorys', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'ListCallHistorys', [200,401,403,500]);
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }

    /**
     * @param integer $case
     * @return array
     */
    private function getListParams($case = 0) :array
    {
        switch ($case) {
            case 0:
                return [
                    'listType' => 'mysql'
                ];
                break;
            case 1:
                return [
                    'page' => 'false',
                    'startDate' => '2020-11-01',
                    'endDate' => '2020-11-30',
                    'locationId' => 135
                ];
                break;
            case 2:
                return [
                    'page' => 1,
                    'perPage' => 20,
                    'startDate' => '2020-11-01',
                    'endDate' => '2020-11-30',
                    'locationId' => 135
                ];
                break;
            default:
                return [];
                break;
        }
    }
}
