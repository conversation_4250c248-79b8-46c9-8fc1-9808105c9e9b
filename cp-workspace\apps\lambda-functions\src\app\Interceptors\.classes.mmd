classDiagram
  class CallSidRequestInterceptor {
    +intercept(context, next): Observable
  }
  class PayByPhoneRequestInterceptor {
    +intercept(context, next): Observable
  }
  class RequestResponseInterceptor {
    source: string
    detailType: string
    +intercept(context, next): Observable
  }
  RequestResponseInterceptor <|-- CallSidRequestInterceptor
  CallSidRequestInterceptor <|-- PayByPhoneRequestInterceptor
  NestInterceptor <|.. RequestResponseInterceptor

