import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneLongRunningState, LoopbackPayByPhoneStateHandlerResponse } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { CustomerSearch } from "@cp-workspace/shared";

@Injectable()
export class CustomerByPhoneSearch extends PayByPhoneLongRunningState {
  constructor() {
    super(PayByPhoneState.CustomerByPhoneSearch, PayByPhoneState.InputPhoneGather);
  }
  override async handler(context: PayByPhoneStateContext): Promise<LoopbackPayByPhoneStateHandlerResponse> {
    const { twilioResponse, storage } = context;

    if (!storage.phoneNumber || storage.phoneNumber?.length < 10) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.account-not-found',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.InputPhoneGather };
    }
    const searchResults = await CustomerSearch.byPhoneNumber(storage.locationId, storage.phoneNumber, this.services, true);

    if (searchResults.couldNotFindAccount) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.account-not-found',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.InputPhoneGather };
    }

    storage.matchedTenants = searchResults.items;

    storage.payByPhoneAllowed = await searchResults.isPayByPhoneAllowed(storage.locationId, this.services.locationService);
    if (!storage.payByPhoneAllowed) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.not-allowed',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.InputPhoneGather };
    }

    if (searchResults.multipleUnitsFound) {
      storage.multipleUnitsFound = true;
      return { nextState: PayByPhoneState.UnitsToPayPrompt };
    }

    storage.selectedUnits = [searchResults.firstUnit];
    storage.selectedUnit = searchResults.firstUnit;
    storage.selectedTenant = searchResults.firstUnitTenant;
    storage.isDelinquentPaymentFlow = searchResults.hasDelinquentUnits;

    twilioResponse.sayAccountFoundDetails(storage.selectedUnit, storage.selectedTenant, storage);

    return { nextState: PayByPhoneState.ConfirmCustomerInfo };
  }
}
