const app = require('../../app');
const supertest = require('supertest');
const config = require('../../config/config');
const { uniqueId } = require('lodash');
const requestWithSupertest = supertest(app);
const moment = require('moment-timezone');
let testData = require('../testDataSet.json');

const workspaceSid = testData.twilio_account.workspace_sid;
const cpapiClient = require('../../libraries/cpapi-client');
const { generateRowId } = require('../../routes/utils/twillio');
const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
const accountSid = testData.twilio_account.account_sid
const authToken = testData.twilio_account.authtoken
const client = require('twilio')(accountSid, authToken)
const locationId = testData.twilio_account.location_id
const videoWorkflowSid = testData.twilio_account.video_workflow_sid;
const videoQueueSid = testData.twilio_account.video_default_queue;
let roomSid
let taskData

beforeAll(async() => {
  // create room
  await client.video.v1.rooms.create({
    uniqueName: uniqueId,
    statusCallback :  "qa/v1/video/callback",
  }).then(room => roomSid = room.sid).catch(e => console.log(e));

  // create task
  taskData = await client.taskrouter.v1
    .workspaces(testData.twilio_account.workspace_sid)
    .tasks
    .create({
      attributes: JSON.stringify(
        {
          location_id : locationId,
          direction   : "inbound",
          taskchannel : "video",
          room_sid    : roomSid,
          call_sid    : roomSid,
          queue_sid   : videoQueueSid,
          queue_id    : videoQueueSid,
          log_id      : generateRowId(5)
        }),
      workflowSid : videoWorkflowSid,
      taskchannel : 'video',
      "timeout"   : 60,
    }).catch((e) => console.log(e));

  const datestamp = moment.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss');
  let callLogData =  {
    'twilio_id'     : roomSid,
    'task_id'       : taskData.sid,
    'workspace_sid' : workspaceSid,
    'location_id'   : locationId,
    'account_id'    : accountSid,
    'customer_name' : "Robert",
    'datestamp'     : datestamp,
    'call_name'     : "**********",
    'call_type'     : 'inbound',
    'channel'       : 'video',
  }

  await callClient.postData('call', callLogData);
}, 20000)

describe('Workspace event handler', () => {
  test('POST /twilio/workspace_event_callback video reservation accepted', async () => {
    let payload = {
      EventType: 'reservation.accepted',
      TaskAttributes: taskData.attributes, 
      WorkerAttributes: '{"agent_id" : 2238}',
      TaskSid : taskData.sid,
      ReservationSid: "WRfd1041650c1961eab676e79bede50f73",
      TaskChannelUniqueName: 'video',
      WorkflowName : 'InboundVideoTest',
      TaskAge: '40'
    }
    
    const res = await requestWithSupertest.post('/qa/v1/twilio/workspace_event_callback').send(payload);
    expect(res.status).toEqual(200);

    let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${taskData.sid}`);
    expect(taskList.items.length).not.toBe(0);
    
    // check if reservation accepted entry is added in track call center tasks
    let reservationAccepted = taskList.items.filter(task => { 
      return task['reservation_accepted'] != ""
    });
    expect(reservationAccepted.length).not.toBe(0);

    // check if employee id(agent/worker id) is assigned
    let callData = await callClient.getData(`call/${locationId}/${roomSid}`);
    expect(callData.employee_id).toEqual(2238);


    //check if log_id > 0
    expect(reservationAccepted[0].log_id).toBeGreaterThan(0);

  }, 20000);

  test('POST /twilio/workspace_event_callback video task timeout', async () => {
    let payload = {
      EventType: 'task.canceled',
      TaskAttributes: taskData.attributes, 
      AccountSid : accountSid,
      ResourceSid: taskData.sid
    }
    
    const res = await requestWithSupertest.post('/qa/v1/twilio/workspace_event_callback').send(payload);

    expect(res.status).toEqual(200);

    // check if room status updated to completed
    await client.video.v1.rooms(roomSid)
      .fetch()
      .then(room => expect(room.status).toEqual('completed'));
   
  }, 20000);

  test('POST /twilio/workspace_event_callback video reservation created', async () => {

    let payload = {
      AccountSid : accountSid,
      EventType: 'reservation.created',
      TaskAttributes: taskData.attributes, 
      WorkerAttributes: '{"agent_id" : 2238, "email":"<EMAIL>"}',
      WorkerSid : "WK3867bfe41b717d2ec0d92c4f31bdda93",
      TaskSid : taskData.sid,
      ReservationSid: "WRfd1041650c1961eab676e79bede50f73",
      TaskChannelUniqueName: 'video',
      WorkflowName : 'InboundVideoTest',
      TaskAge: '40'
    }
    
    const res = await requestWithSupertest.post('/qa/v1/twilio/workspace_event_callback').send(payload);
    expect(res.status).toEqual(204);

    let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${taskData.sid}`);
    expect(taskList.items.length).not.toBe(0);
    
    // check if reservation created entry is added in track call center tasks
    let reservationCreated = taskList.items.filter(task => { 
      return task['reservation_created'] != ""
    });
    expect(reservationCreated.length).not.toBe(0);

    const task = await client.taskrouter.v1.workspaces(workspaceSid)
    .tasks(taskData.sid).fetch().then(task => expect(JSON.parse(task.attributes).worker_sid).toEqual(payload.WorkerSid));

    //check if log_id > 0
    expect(reservationCreated[0].log_id).toBeGreaterThan(0);
  }, 20000);

  test('POST /twilio/workspace_event_callback video reservation timeout', async () => {

    let payload = {
      AccountSid : accountSid,
      EventType: 'reservation.timeout',
      TaskAttributes: taskData.attributes, 
      WorkerAttributes: '{"agent_id" : 2238, "email":"<EMAIL>"}',
      TaskSid : taskData.sid,
      ReservationSid: "WRfd1041650c1961eab676e79bede50f73",
      TaskChannelUniqueName: 'video',
      WorkflowName : 'InboundVideoTest',
      TaskAge: '40',
      TaskAssignmentStatus: 'pending',
      WorkerActivityName : 'No-Answer'
    }
    
    const res = await requestWithSupertest.post('/qa/v1/twilio/workspace_event_callback').send(payload);
    expect(res.status).toEqual(200);

    let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${taskData.sid}`);
    expect(taskList.items.length).not.toBe(0);
    
    // check if reservation timeout entry is added in track call center tasks
    let reservationTimeout = taskList.items.filter(task => { 
      return task['reservation_timeout'] != ""
    });
    expect(reservationTimeout.length).not.toBe(0);

    //check if log_id > 0
    expect(reservationTimeout[0].log_id).toBeGreaterThan(0);
  }, 20000);

  test('POST /twilio/workspace_event_callback video reservation rejected', async () => {

    let payload = {
      AccountSid : accountSid,
      EventType: 'reservation.rejected',
      TaskAttributes: taskData.attributes, 
      WorkerAttributes: '{"agent_id" : 2238, "email":"<EMAIL>"}',
      TaskSid : taskData.sid,
      ReservationSid: "WRfd1041650c1961eab676e79bede50f73",
      TaskChannelUniqueName: 'video',
      WorkflowName : 'InboundVideoTest',
      TaskAge: '40',
    }
    
    const res = await requestWithSupertest.post('/qa/v1/twilio/workspace_event_callback').send(payload);
    expect(res.status).toEqual(200);

    let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${taskData.sid}`);
    expect(taskList.items.length).not.toBe(0);
    
    // check if reservation rejeceted entry is added in track call center tasks
    let reservationRejected = taskList.items.filter(task => { 
      return task['reservation_rejected'] != ""
    });
    expect(reservationRejected.length).not.toBe(0);

    //check if log_id > 0
    expect(reservationRejected[0].log_id).toBeGreaterThan(0);
  }, 20000);

});
