const redis = require('redis');
var config = require('../../config/config');

var set_redis_data = async function(redisKey,redisTtl, redisData) {
    const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
    await redisClient.connect();
    await redisClient.set(redisKey, redisData, {EX: redisTtl});
    await redisClient.disconnect();
}


module.exports = {
    set_redis_data: set_redis_data
};
  