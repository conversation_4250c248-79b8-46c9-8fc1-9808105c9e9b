import { Injectable } from "@nestjs/common";
import { PayByPhoneStorage } from "./PayByPhone.model";
import { DomainEventsService, RedisClientInitialization, RedisService } from '@cp-workspace/shared';
import Bugsnag from "@bugsnag/js";

@Injectable()
export class PayByPhoneStorageService {
  private expirationTime = 1800; //1800 seconds = 30 minutes

  constructor(
    private readonly redisService: RedisService,
    private readonly domainEventService: DomainEventsService,
  ) { }

  initialize(options?: RedisClientInitialization) {
    if (!this.redisService.isInitialized) {
      this.redisService.initialize(options);
    }
  }

  private checkInitialized() {
    if (!this.redisService.isInitialized) {
      this.redisService.initialize();
    }
  }

  async getStorage(callSid: string): Promise<PayByPhoneStorage | undefined> {
    this.checkInitialized();
    const storage = await this.redisService.get(callSid);
    await this.domainEventService.publish({
      source: 'comms-api.pay-by-phone.storage',
      detailType: 'PayByPhoneGetStorage',
      detail: {
        callSid, storage: storage ? JSON.parse(storage) : undefined,
      },
    });
    Bugsnag.leaveBreadcrumb('PayByPhoneGetStorage', {
      source: 'comms-api.pay-by-phone.storage',
      callSid,
      storage: storage ? JSON.parse(storage) : undefined,
    });
    return storage ? JSON.parse(storage) : undefined;
  }

  async saveStorage(callSid: string, storage: PayByPhoneStorage): Promise<void> {
    this.checkInitialized();
    await this.redisService.set(callSid, JSON.stringify(storage), 'EX', this.expirationTime);
    await this.domainEventService.publish({
      source: 'comms-api.pay-by-phone.storage',
      detailType: 'PayByPhoneSaveStorage',
      detail: {
        callSid, storage,
      },
    });
    Bugsnag.leaveBreadcrumb('PayByPhoneGetStorage', {
      source: 'comms-api.pay-by-phone.storage',
      callSid, storage,
    });
  }

  async deleteStorage(callSid: string): Promise<void> {
    this.checkInitialized();
    await this.redisService.del(callSid);
  }
}