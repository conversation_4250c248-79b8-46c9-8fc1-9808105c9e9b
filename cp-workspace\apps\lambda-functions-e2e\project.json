{"name": "lambda-functions-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["lambda-functions"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/lambda-functions-e2e/jest.config.ts", "passWithNoTests": true, "codeCoverage": true, "testPathPattern": ["apps/lambda-functions-e2e/src/lambda-functions"], "coverageDirectory": "{workspaceRoot}/coverage/lambda-functions-e2e", "coverageReporters": ["lcov", "text"]}, "dependsOn": ["lambda-functions:build"]}}}