<?php
/**
 * Event handler for call detail create/update
 *
 * @category CallDetailEventHandler
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\BaseEventHandler;
use CallPotential\CPCommon\EventHandler;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Util;
use Twilio\Rest\Client as TwilioClient;
use Bugsnag\Client as BugsnagClient;
use Phalcon\Di\FactoryDefault;

/**
 * Event handler for call detail create/update
 *
 * @category CallDetailEventHandler
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class CallDetailEventHandler extends BaseEventHandler implements EventHandler
{
    use LoggerTrait;

    /**
     * Event Data
     *
     * @var private
     */
    private $eventData;

    private BugsnagClient $bugsnag;

    /**
     * Handle send notification to location email address
     *
     * @param array $eventData array
     *
     * @return bool
     */
    public function handle(array $eventData) : bool
    {
        $di = \Phalcon\Di::getDefault();
        $this->bugsnag = $di->getShared(BugsnagClient::class);
        $this->eventData = $eventData;
        
        if (!empty($this->eventData['addAuditLog'])) {
            $this->addAuditLog();
        }
        if (!empty($this->eventData['addOutboundNote'])) {
            $this->saveOutboundNotes();
        }
        if (!empty($this->eventData['insertCallBilling'])) {
            $this->updateBillingData($this->eventData['data']);
        }
        if (!empty($this->eventData['updateLeadDynamoLogId'])) {
            $this->updateLeadDynamoLogId();
        }
        if (!empty($this->eventData['checkCallWebhook'])) {
            $this->checkCallWebhook($this->eventData['data']);
        }

        return true;
    }

    /**
     * Return success debug message
     *
     * @return string
     */
    public function getSuccessMessage() : string
    {
        return "Outbound call note added";
    }

    /**
     * Return failure error message
     *
     * @return string
     */
    public function getFailureMessage() : string
    {
        return "Problem in adding outbound call note";
    }

    /**
     * Update lead having dynamo db log id with mysql log id
     *
     * @return string
     */
    public function updateLeadDynamoLogId() : bool
    {
        $callClient = ClientFactory::getCallClient($this->eventData['authToken']);
        if (!empty($this->eventData['newData'])) {
            $locationId = $this->eventData['newData']['location_id'];
            $callSid = $this->eventData['newData']['twilio_id'];

            try {
                $callLog = $callClient->callEndpoint("call/{$locationId}/{$callSid}", 'get', []);
                $callLog = json_decode($callLog->getBody(), true);

                if ($callLog && !empty($callLog['log_id']) && !empty($callLog['db_log_id'])) {
                    $leadData = Lead::findFirst('log_id = '.$callLog['log_id']);
                    if ($leadData) {
                        $leadData->log_id = $callLog['db_log_id'];
                        $leadData->update();
                    }
                }
            } catch (Exception $e) {
                $this->errorMessage("Error in updating lead log id" . $e->getMessage());
            }
        }

        return true;
    }

    /**
     * Save outbound call note
     *
     * @return bool
     */
    private function saveOutboundNotes() : bool
    {
        $data = $this->eventData;
        $data = array_merge($data, $data['newData']);
        $sendEmail = false;

        $callClient = ClientFactory::getCallClient($data['authToken']);
        try {
            $callRecord = $callClient->getCallDetail($data['twilio_id']);
        } catch (Exception $e) {
            $this->errorMessage("Could not find Call Details. Twilio Id : " .
            $data['twilio_id'] . ". Error : " . $e->getMessage());
        }

        if (empty($callRecord->is_location_call)) {
            // Check user is agent
            if (!empty($data['employee_id'])) {
                $coreClient = ClientFactory::getCoreClient();
                $agent = $coreClient->getUser($data['employee_id']);

                if ($agent->is_agent === 1) {
                    $sendEmail = true;
                }
            }
        }

        $this->addNoteData($data, $callClient, $sendEmail);

        return true;
    }

    /**
     * Add note data to lead or customer
     *
     * @param  array  $data       request params
     * @param  mixed  $callClient call client
     * @param  bool   $sendEmail  send email
     * @return void
     */
    private function addNoteData($data, $callClient, $sendEmail)
    {
        try {
            $data['workflow_step'] = Util::array_get('workflow_step', $data, false) === 'true';

            $token = $data['authToken'] = $this->getAuthToken($data);

            $this->infoMessage(
                var_export($data, true).' token : '.var_export($token, true),
                __METHOD__ . ":" . __LINE__
            );

            $locClient = ClientFactory::getLocClient($data['authToken']);
            $intClient = ClientFactory::getIntClient($data['authToken']);
            $smsClient = ClientFactory::getSmsClient($data['authToken']);

            $noteData = [];
            $noteData['added_by'] = $noteData['employee_id'] = $data['employee_id'];
            $noteData['date_created'] = gmdate('Y-m-d H:i:s');
            $noteData['send_email'] = $sendEmail;

            $locationDetail = (array) $locClient->getLocationConfigurationDetails(
                $data['location_id'],
                'false'
            );
            $callNumber = $data['call_number'];
            $eventType = $this->setEventType($data);

            $callDetailsDynamo = (array) $callClient->getCallDataDynamo(
                $data['location_id'],
                $data['twilio_id']
            );
            $data = array_merge($callDetailsDynamo, $data);

            if ((int) $locationDetail['record_outgoing'] === 1
                && !empty($data['recording_sid'])
                && $data['deleteRecording'] === false) {
                $url = $data['envUrl'].'/recording/'.$data['twilio_id'];
                $result = $smsClient->getShortUrl($url);
                $shortUrl = $result->short_url;
                $noteData['note'] = "Call to $callNumber > $shortUrl";
            } else {
                $noteData['note'] = "Call to ".$callNumber;
            }
            $leadCallTypes = [
                'outbound_followup',
                'outbound_lead_payment',
            ];
            $customerCallTypes = [
                'outbound_collection',
                'outbound_payment',
            ];
            $isLeadCall = in_array($data['call_type'], $leadCallTypes);
            $isCustomerCall = in_array($data['call_type'], $customerCallTypes);

            $hasLeadData = !empty($data['fk_lead_id']);
            $hasCustomerData = !empty($data['customer_id']) && !empty($data['ledger_id']);

            $shouldWriteLeadNote = $hasLeadData && $isLeadCall;
            $shouldWriteCustomerNote = $hasCustomerData && $isCustomerCall;

            if ($shouldWriteLeadNote) {
                $leadData = Lead::findFirst(
                    [
                        "columns" => "reservation_id,es_lead_id,followup_rule_type",
                        "conditions" => "lead_id = ".$data['fk_lead_id'],
                    ]
                );
                if (!empty($leadData)) {
                    $leadData = $leadData->toArray();
                    if ($data['workflow_step'] === true &&
                        $data['call_type'] === 'outbound_followup') {
                        $noteData['message_type'] = "followup";
                        $noteData['note'] = $eventType." ".lcfirst($noteData['note']);
                    }
                    $intClient->addLeadNotes($leadData['es_lead_id'], array($noteData));
                }
            } elseif ($shouldWriteCustomerNote) {
                $esTenantId = $this->generateESTenantId($data);
                $employeeInitial = $this->getEmployeeInitials($data['employee_id'], false);
                $ledgerInfo = (array) $intClient->getLedger(
                    $data['ledger_id'],
                    $data['location_id']
                );
                if (!empty($ledgerInfo) && $data['workflow_step'] === true &&
                    $data['call_type'] === 'outbound_collection') {
                    $noteData['message_type'] = "collection";
                    $noteData['note'] = $eventType." ".lcfirst($noteData['note']);
                }
                $noteData['note'] = trim($employeeInitial.$noteData['note']);
                $ledgerId = Util::array_get('ledger_id', $data, 0);
                $intClient->addLedgerNote($esTenantId, $ledgerId, $noteData);
            }
        } catch (Exception $e) {
            $this->bugsnag->notifyException($e, function ($report) use ($data) {
                $report->setMetaData(['note_data' => $data]);
            });
            $this->infoMessage(
                'error adding note data' . var_export(json_encode($data), true) .
                var_export(json_encode($noteData), true).': '.
                var_export($e->getMessage(), true),
                __METHOD__ . ":" . __LINE__
            );
            $this->errorMessage("Unable to save outbound call note" . $e->getMessage());
        }

        return true;
    }

    /**
     * Generate ES Tenant Id based on location and customer Id
     *
     * @param array $data request params
     * @return string
     */
    private function generateESTenantId($data) : string
    {
        return join(
            '-',
            [
                $data['location_id'],
                $data['customer_id'],
            ]
        );
    }

    /**
     * Retrieve employee initial value
     *
     * @param int   $employeeId  employee id
     * @param bool  $getFullName boolean true or false
     *
     * @return string
     */
    private function getEmployeeInitials(int $employeeId, bool $getFullName) : string
    {
        if ((int) $employeeId === 0) {
            return "";
        }
        $coreClient = ClientFactory::getCoreClient($this->eventData['authToken']);
        $employee = $coreClient->getUser($employeeId);
        $fname = $employee->firstname;
        $lname = $employee->lastname;

        if ($getFullName) {
            return $fname . ' ' . $lname;
        }

        $employeeInitials = $fname[0].$lname[0];

        return $employeeInitials." > ";
    }

    /**
     * Add audit log for call
     *
     * @return bool
     */
    private function addAuditLog() : bool
    {
        $cacheKey = 'audit_log_' . $this->eventData['newData']['twilio_id'];
        try {
            $di = \Phalcon\DI\FactoryDefault::getDefault();
            $this->badgeCache =  $di->getShared('badgeCache');
            $cacheExist = $this->badgeCache->get($cacheKey);
            if (!empty($cacheExist)) {
                $this->errorMessage(
                    "Audit log already added ". $this->eventData['newData']['twilio_id']
                );

                return false;
            }
        } catch (Exception $e) {
            $this->errorMessage("Error in checking cache for audit log");
        }

        $acctClient = ClientFactory::getAcctClient($this->eventData['authToken']);
        $intClient = ClientFactory::getIntClient($this->eventData['authToken']);

        $trackingNumberParam = [
            'filterCall_number' => str_replace('+', '', $this->eventData['newData']['call_name']),
            'filterLocation_id' => $this->eventData['newData']['location_id'],
        ];

        /** only check for voice call */
        if (substr($this->eventData['newData']['twilio_id'], 0, 2) === 'CA') {
            $trackingNumbers = Util::objectToArray(
                $acctClient->getTrackingNumber($trackingNumberParam)
            );
            $trackingNumber = $trackingNumbers['items'][0]['trackingnumber'];

            // If record is on then recording sid is required before adding audit log
            if (!empty($trackingNumber['record'])
                && empty($this->eventData['newData']['recording_sid'])) {
                return true;
            }
        }

        $leadCallTypes = [
            'inbound_lead',
            'inbound_lead_payment',
        ];
        $customerCallTypes = [
            'inbound_customer',
            'inbound_collection',
            'inbound_payment',
            'inbound_autopay',
        ];

        $locationId = $this->eventData['location_id'] ?? $this->eventData['newData']['location_id'];

        if (!empty($this->eventData['newData']['fk_lead_id'])) {
            $mysqlEsConvertPayload = [
                "account_id"  => $this->eventData['accountId'],
                "location_id" => $this->eventData['newData']['location_id'],
                "mysql_id"    => $this->eventData['newData']['fk_lead_id'],
                "entity_type" => 'lead',
            ];
            $esID = Util::objectToArray($intClient->convertMysqlEsId($mysqlEsConvertPayload));
            $esLeadId = (string) Util::array_get('es_id', $esID['data'], '');
        }

        if (!empty($this->eventData['newData']['customer_id'])) {
            $mysqlEsConvertPayload = [
                "account_id"  => $this->eventData['accountId'],
                "location_id" => $locationId,
                "mysql_id"    => $this->eventData['newData']['customer_id'],
                "entity_type" => 'customer',
            ];
            $esID = Util::objectToArray($intClient->convertMysqlEsId($mysqlEsConvertPayload));
            $tenantId = (string) Util::array_get('es_id', $esID['data'], '');
        }

        if (in_array($this->eventData['newData']['call_type'], $leadCallTypes)) {
            $entityType = 'lead';

            if (empty($esLeadId)) {
                $this->infoMessage("Calllog event data : ". json_encode($this->eventData));
                $this->infoMessage("Unable to determine valid leadId for audit log entry");

                return true;
            }
        } elseif (in_array($this->eventData['newData']['call_type'], $customerCallTypes)) {
            $entityType = 'customer';

            if (empty($tenantId)) {
                $this->infoMessage("Calllog event data : ". json_encode($this->eventData));
                $this->infoMessage("Unable to determine valid esTenantId for audit log entry");

                return true;
            }
        }

        $linkType = '';
        $recording_url = '';

        if (!empty($this->eventData['newData']['recording_sid'])) {
            $recording_url = $this->eventData['envUrl'] . '/recording/' .
            $this->eventData['newData']['twilio_id'];
            $linkType = 'recording';
        }

        try {
            $employeeName = $this->getEmployeeInitials(
                $this->eventData['newData']['employee_id'],
                true
            );
        } catch (Exception $e) {
            $this->errorMessage(
                "Error in getting employee name for employee id ".
                $this->eventData['newData']['employee_id']
            );
            $employeeName = '';
        }

        $paymentCall = false;
        if (strpos($this->eventData['newData']['call_type'], 'payment') !== false) {
            $paymentCall = true;

            if ($this->eventData['deleteRecording']) {
                $linkType = '';
                $recording_url = '';
            }
        }

        $logType = $this->eventData['newData']['channel'] ===
        'video' ? 'inbound_video_call' : 'inbound_call';

        $payload =  [
            'logType' => $logType,
            'logMessage' => '',
            'logOrigin' => 'inbound',
            'entityType' => $entityType,
            'esLeadId' => $esLeadId ?? '',
            'esCustomerId' => $tenantId ?? '',
            'esLedgerId' => '',
            'esUnitId' => '',
            'employeeId' => $this->eventData['newData']['employee_id'],
            'employeeName' => $employeeName,
            'phoneNumber' => $this->eventData['newData']['call_number'],
            'callDuration' => $this->eventData['newData']['duration'],
            'callOutcome' => 'answered',
            'link' => $recording_url,
            'linkText' => '',
            'linkType' => $linkType,
            'message' => '',
            'emailBody' => '',
            'emailSubject' => '',
            'emailTo' => '',
            'emailFrom' => '',
            'accountId' => $this->eventData['accountId'],
            'locationId' => $this->eventData['newData']['location_id'],
            'meta' => [
                "callSid" => $this->eventData['newData']['twilio_id'],
                'paymentCall' => $paymentCall,
            ],
            'date' => $this->eventData['newData']['datestamp'],
        ];

        try {
            $acctClient->callAuditLogNew($payload);
        } catch (Exception $e) {
            $logType = $payload['logType'];
            $this->errorMessage("Request payload : ". json_encode($payload));
            $this->errorMessage(
                "Insert $logType audit log returned in error response ".
                $e->getMessage()
            );
        }

        try {
            $this->badgeCache->set($cacheKey, 1, 60);
        } catch (Exception $e) {
            $this->errorMessage("Error in setting cache for audit log:" . $e->getMessage());
        }

        return true;
    }

    /**
     * set event type
     *
     * @param array   $data data
     *
     * @return string
     */
    private function setEventType($data)
    {
        $eventType = '';
        if ($data['call_type'] === 'outbound_followup') {
            $eventType = "Follow-up";
        } elseif ($data['call_type'] === 'outbound_collection') {
            $eventType = "Collection";
        }

        return $eventType;
    }

    /**
     * get session auth token
     *
     * @param array   $data data
     *
     * @return string
     */
    private function getAuthToken($data)
    {
        $token = $data['authToken'];
        $coreClient = ClientFactory::getCoreClient($data['authToken']);
        $getSession = $coreClient->createUserSession($data['employee_id']);
        if (!empty((array) $getSession)) {
            $token = $getSession->session_id;
        }

        return $token;
    }

    /**
     * Update call billing table record with billing details
     *
     * @param array $data Request data
     *
     * @return bool
     */
    private function updateBillingData(array $data) : bool
    {
        try {
            if (isset($this->eventData['accountId']) && $this->eventData['accountId'] > 0) {
                $accountId = $this->eventData['accountId'];
            } else {
                $locClient = ClientFactory::getLocClient();
                $locationDetail = (array) $locClient->getLocationConfigurationDetails(
                    $data['location_id'],
                    'false'
                );
                $accountId = $locationDetail['user_id'];
            }

            $callBilling = CallBilling::findFirst($data['log_id']);

            $data['billingDuration'] = $data['billingDuration'] ?? false;
            $data['channel'] = $data['channel'] ??  'voice';

            if ($callBilling) {
                $callBillingdata = $callBilling->toArray();

                // We might receive recording duration before the original call ends (ie voicemail)
                // In such case we inserte call billing with call_duration=duration
                // So when call ends we want to check and update call_duration
                // duration = recording duration
                // call_duration = entire duration of call progression
                if (empty($callBillingdata['duration'])
                    || (int) $callBillingdata['call_duration']
                    === (int) $callBillingdata['duration']) {
                    if (!$data['billingDuration'] && $data['channel'] === 'voice') {
                        $data = $this->getTwilioCallDuration($data, $accountId);
                    }
                    if ($data['channel'] === 'voice') {
                        $callBilling->setCallDuration((int) $data['billingDuration']);
                    }
                    $callBilling->setDuration($data['duration']);
                    $callBilling->save();
                }
            } else {
                if (strpos($data['call_type'], 'inbound') !== false &&
                $data['channel'] === 'voice') {
                    $data = $this->getTwilioCallDuration($data, $accountId);
                }

                if ($data['billingDuration']) {
                    $callBilling = new CallBilling();
                    $callBilling->setLogId((int) $data['log_id']);
                    $callBilling->setLocationId($data['location_id']);
                    $callBilling->setDuration($data['duration']);
                    $callBilling->setCallDuration($data['billingDuration']);
                    $callBilling->setUserId($accountId);
                    $callBilling->setDatestamp(date('Y-m-d H:i:s'));

                    $callBilling->save();
                }
            }

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error in updating call_billing for log id ' .
                $data['log_id'] . ' Message ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            return false;
        }
    }

    /**
     * Get full call duration from twilio
     *
     * @param array $data      Request data
     * @param int   $accountId Account Id
     *
     * @return array
     */
    private function getTwilioCallDuration(array $data, int $accountId) : array
    {
        $mccClient = ClientFactory::getMccClient();
        $twilio_account = $mccClient->getTwilioAccount($accountId);

        $twilio = new TwilioClient($twilio_account->account_sid, $twilio_account->authtoken);
        $callData = $twilio->calls($data['twilio_id'])->fetch();

        if ($callData->status === 'completed') {
            $data['billingDuration'] = $callData->duration;
        } else {
            $data['billingDuration'] = $data['duration'];
        }

        return $data;
    }

    /**
     * Check and execute webhook for tracking number
     *
     * @param array $data Request data
     *
     * @return bool
     */
    private function checkCallWebhook(array $data) : bool
    {
        try {
            $acctClient = ClientFactory::getAcctClient();
            $intClient = ClientFactory::getIntClient();
            $trackingNumberParam = [
                'filterCall_number' => str_replace('+', '', $data['call_name']),
                'filterLocation_id' => $data['location_id'],
            ];

            $trackingNumbers = Util::objectToArray(
                $acctClient->getTrackingNumber($trackingNumberParam)
            );
            $trackingNumber = $trackingNumbers['items'][0]['trackingnumber'];

            if (!empty($trackingNumber['webhook_url'])) {
                $webhookData = [
                    'webhook' => [
                        'log_id' => $data['log_id'],
                        'call_number' => $data['call_name'],
                        'triggered_url' => $trackingNumber['webhook_url'],
                        'date_time' => date('Y-m-d H:i:s'),
                    ],
                ];

                $webhookData['trackingNumber'] = $trackingNumber;

                if ($data['fk_lead_id'] > 0) {
                    $requestData = [
                        'mysql_id' => $data['fk_lead_id'],
                        'location_id' => $data['location_id'],
                        'entity_type' => 'lead',
                        'account_id' => $trackingNumber['user_id'],
                    ];
                    try {
                        $esIdRecord = Util::objectToArray(
                            $intClient->convertMysqlEsId($requestData)
                        );
                        $data['es_lead_id'] = Util::array_get('es_id', $esIdRecord['data'], '');
                    } catch (Exception $e) {
                    }
                }

                if (!empty($data['customer_id'])) {
                    $requestData = [
                        'mysql_id' => $data['customer_id'],
                        'location_id' => $data['location_id'],
                        'account_id' => $trackingNumber['user_id'],
                    ];

                    try {
                        $esIdRecord = Util::objectToArray(
                            $intClient->convertMysqlEsId($requestData)
                        );
                        $data['es_tenant_id'] = Util::array_get('es_id', $esIdRecord['data'], '');
                    } catch (Exception $e) {
                    }
                }
                $webhookData['leadEsId'] = $data['es_lead_id'] ?? '';
                $webhookData['tenatEsId'] = $data['es_tenant_id'] ?? '';
                $webhookData['agentId'] = $data['employee_id'] ?? '';
                $webhookData['customerPhone'] = $data['call_number'] ?? '';
                $acctClient->postWebhook($webhookData);
            }

            return true;
        } catch (Exception $e) {
            $this->errorMessage(
                'Error in executing checkCallWebhook for log id ' .
                $data['log_id'] . ' Message ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );
        }
    }
}
