import { Test, TestingModule } from '@nestjs/testing';
import { CollectionsPrompt } from './CollectionsPrompt';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { Locale, CustomerSearch } from '@cp-workspace/shared';

jest.mock('@cp-workspace/shared', () => {
  return {
    ...jest.requireActual('@cp-workspace/shared'),
    CustomerSearch: {
      isPayByPhoneAllowed: jest.fn(),
    },
  };
});

describe('CollectionsPrompt', () => {
  let collectionsPrompt: CollectionsPrompt;
  let context: PayByPhoneStateContext;
  const gatherOptions = {
    numDigits: 1,
    method: 'POST',
    timeout: 10,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CollectionsPrompt],
    }).compile();

    collectionsPrompt = module.get<CollectionsPrompt>(CollectionsPrompt);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.CollectionsPrompt,
        locationId: 123,
        locale: Locale.English,
        tenantId: 'exampleTenantId',
        transferToAgentUrl: 'exampleTransferToAgentUrl',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: {
        sayInLocale: jest.fn(),
        gather: jest.fn(),
        gatherWithLocaleSay: jest.fn()
      } as any
    };
  });

  it('should allow pay by phone and confirm collection when pay by phone is allowed', async () => {

    (CustomerSearch.isPayByPhoneAllowed as jest.Mock).mockResolvedValue(true);

    const response = await collectionsPrompt.handler(context);

    expect(CustomerSearch.isPayByPhoneAllowed).toHaveBeenCalledWith(context.storage.locationId, context.storage.tenantId, undefined);
    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(
      gatherOptions, [{messageId: 'pay-by-phone.collections-prompt-pbp-allowed', locale: Locale.English}]
    );
    expect(response.nextState).toBe(PayByPhoneState.CollectionsConfirm);
  });

  it('should not allow pay by phone and say not allowed when pay by phone is not allowed', async () => {
    (CustomerSearch.isPayByPhoneAllowed as jest.Mock).mockResolvedValue(false);

    const response = await collectionsPrompt.handler(context);

    expect(CustomerSearch.isPayByPhoneAllowed).toHaveBeenCalledWith(123, 'exampleTenantId', undefined);
    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(
      gatherOptions, [{messageId: 'pay-by-phone.collections-prompt-pbp-not-allowed', locale: Locale.English}]
    );
    expect(response.nextState).toBe(PayByPhoneState.CollectionsConfirm);
  });
});
