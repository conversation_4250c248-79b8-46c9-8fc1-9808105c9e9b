# Dependency Analysis Tool

## Purpose

The Dependency Analysis Tool analyzes JavaScript/TypeScript bundle files to extract and manage dependencies. It performs the following functions:

1. Analyzes a bundle file to identify all external module dependencies
2. Extracts module names from require statements
3. Generates a new package.json file with only the necessary dependencies
4. Identifies dependencies that are not found in the original package.json

This tool is particularly useful for optimizing deployment packages by including only the dependencies that are actually used in the bundled code.

## Usage

You can run the dependency analysis tool using the following command:

```bash
ts-node generators/src/lib/dependency-analysis/generator.ts <workspaceRoot> <bundleFile> <outputPath>
```

### Parameters

- `workspaceRoot`: The root directory of your workspace containing the original package.json
- `bundleFile`: The path to the bundled JavaScript/TypeScript file to analyze
- `outputPath`: The directory where the generated package.json and other artifacts will be saved

### Example

```bash
ts-node generators/src/lib/dependency-analysis/generator.ts /path/to/workspace /path/to/workspace/dist/bundle.js /path/to/output
```

## Output Files

The tool generates the following files in the specified output directory:

1. `package.json`: A new package.json file containing only the dependencies found in the bundle
2. `not-found.json` (optional): A list of dependencies that were found in the bundle but not in the original package.json

## How It Works

1. The tool scans the bundle file for all `require()` statements
2. It extracts the module names from these statements
3. It checks if each module is in the dependencies or devDependencies of the original package.json
4. It creates a new package.json with only the necessary dependencies
5. Standard library modules are automatically filtered out using a predefined list 
