import { OverrideByFactoryOptions, Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneController } from './PayByPhone.controller';
import { I18nTranslationService, PayByPhoneService, PayByPhoneServicesModule, PayByPhoneServicesModuleOptions, PayByPhoneStorage, storageKey } from '@cp-workspace/pay-by-phone-domain';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import VoiceResponse from 'twilio/lib/twiml/VoiceResponse';
import { AsyncWorkersClientType, DataDogService, DomainEventsClientType, DomainEventsService, IoRedisClientType, LocationService, SharedServicesModuleOptions, BugsnagService } from '@cp-workspace/shared';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';

describe('PayByPhoneController', () => {

  describe('handlePayByPhoneRequest', () => {
    let controller: PayByPhoneController;
    let payByPhoneService: PayByPhoneService;
    let dataDogService: DataDogService;
    let bugsnagService: BugsnagService;

    beforeEach(async () => {
      process.env['BUGSNAG_API_KEY'] = '616b106fc4dba0412968d8c0e91995be';
      const module: TestingModule = await Test.createTestingModule({
        controllers: [PayByPhoneController],
        providers: [
          {
            provide: PayByPhoneService,
            useValue: {
              isFirstRequest: jest.fn(),
              handleRequest: jest.fn(),
              logPayByPhoneEnd: jest.fn(),
            },
          },
          {
            provide: DataDogService,
            useValue: {
              incrementCounter: jest.fn(),
              recordDistribution: jest.fn(),
            },
          },
          {
            provide: BugsnagService,
            useValue: {
              leaveBreadcrumb: jest.fn(),
              notify: jest.fn(),
            },
          },
          { provide: DomainEventsService, useValue: {} },
        ],
      }).compile();

      controller = module.get<PayByPhoneController>(PayByPhoneController);
      payByPhoneService = module.get<PayByPhoneService>(PayByPhoneService);
      dataDogService = module.get<DataDogService>(DataDogService);
      bugsnagService = module.get<BugsnagService>(BugsnagService);
    });

    it('should throw BadRequestException if CallSid is missing', async () => {
      const request = { body: {} } as any;

      await expect(controller.handlePayByPhoneRequest(request)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if it is the first request and LocationId or TransferToAgentUrl are missing', async () => {
      const request = { body: { CallSid: '12345' } } as any;

      jest.spyOn(payByPhoneService, 'isFirstRequest').mockResolvedValue(true);

      await expect(controller.handlePayByPhoneRequest(request)).rejects.toThrow(BadRequestException);
    });

    it('should return TwiML response if request is successful', async () => {
      const request = {
        body: {
          CallSid: '12345',
          LocationId: '456',
          TransferToAgentUrl: 'http://example.com',
        },
      } as any;

      const twilioResponse = new VoiceResponse();
      twilioResponse.say('Hello');

      jest.spyOn(payByPhoneService, 'isFirstRequest').mockResolvedValue(false);
      jest.spyOn(payByPhoneService, 'handleRequest').mockResolvedValue(twilioResponse.toString());

      const result = await controller.handlePayByPhoneRequest(request);

      expect(result).toBe(twilioResponse.toString());
    });

    it('should throw InternalServerErrorException if handleRequest throws an error', async () => {
      const request = {
        body: {
          CallSid: '12345',
          LocationId: 'location1',
          TransferToAgentUrl: 'http://example.com',
        },
      } as any;

      jest.spyOn(payByPhoneService, 'isFirstRequest').mockResolvedValue(false);
      jest.spyOn(payByPhoneService, 'handleRequest').mockRejectedValue(new Error('Some error'));

      await expect(controller.handlePayByPhoneRequest(request)).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('handlePayByPhoneAsyncInvocation', () => {
    let controller: PayByPhoneController;
    let payByPhoneService: PayByPhoneService;
    let dataDogService: DataDogService;
    let bugsnagService: BugsnagService;

    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          PayByPhoneServicesModule.forRoot({
            sharedServicesOptions: {
              redisOptions: { clientType: IoRedisClientType.IN_MEMORY },
              domainEventsOptions: { clientType: DomainEventsClientType.IN_MEMORY, options: { enabled: true } },
              asyncWorkersOptions: { clientType: AsyncWorkersClientType.IN_MEMORY, enabled: true },
            } as SharedServicesModuleOptions,
          } as PayByPhoneServicesModuleOptions),
        ],
        controllers: [PayByPhoneController],
        providers: [
          {
            provide: DataDogService,
            useValue: {
              incrementCounter: jest.fn(),
              recordDistribution: jest.fn(),
            },
          },
          {
            provide: BugsnagService,
            useValue: {
              leaveBreadcrumb: jest.fn(),
              notify: jest.fn(),
            },
          },
          { provide: DomainEventsService, useValue: {} },
        ],
      })
        .overrideProvider(I18nTranslationService)
        .useValue({ translate: jest.fn().mockReturnValue('Test Translation') })
        .overrideProvider(HttpService)
        .useValue({ get: jest.fn(), post: jest.fn() })
        .overrideProvider(LocationService)
        .useFactory({
          factory: () => {
            const mockService = { getLocationConfiguration: jest.fn() };
            mockService.getLocationConfiguration.mockResolvedValue({
              locales: ['en-US', 'es-MX'],
              location_id: 123,
              tenant_id: 456,
              tenant_name: 'Test Tenant',
              tenant_phone: '************',
              tenant_email: '<EMAIL>',
            });
            return mockService;
          },
        } as OverrideByFactoryOptions)
        .overrideProvider(ConfigService)
        .useValue({
          get: jest.fn((key: string) => {
            if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
            if (key === 'API_INT_URL') return 'http://api.example.com';
            if (key === 'API_ACCT_URL') return 'http://api.example.com';
            if (key === 'API_CORE_URL') return 'http://api.example.com';
            if (key === 'API_LOC_URL') return 'http://api.example.com';
            if (key === 'DD_API_KEY') return 'dummy_key';
            if (key === 'BUGSNAG_API_KEY') return '616b106fc4dba0412968d8c0e91995be';
            return '';
          }),
        })
        .compile();

      controller = module.get<PayByPhoneController>(PayByPhoneController);
      payByPhoneService = module.get<PayByPhoneService>(PayByPhoneService);
      dataDogService = module.get<DataDogService>(DataDogService);
      bugsnagService = module.get<BugsnagService>(BugsnagService);
    });

    it('should store the result in the storage service and return true', async () => {
      const event = {
        source: 'async-invocation',
        body: {
          CallSid: '12345',
          LocationId: 456,
          TransferToAgentUrl: 'http://example.com',
          StorageKeyPrefix: 'in_progress_',
        },
      };

      const inProgressStorage = {
        state: 'CustomerByPhoneSearch',
        locale: 'en-US',
        transferToAgentUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1',
        locationId: 456,
        phoneNumber: '6143852455',
        paymentSuccessRedirectUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1',
        paymentFailureRedirectUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1',
        payByPhoneStartTime: 1718387787715,
        origin: 'callroute',
      } as PayByPhoneStorage;

      payByPhoneService.storageService.initialize({ data: { [storageKey(event.body)]: JSON.stringify(inProgressStorage) } });

      const twimlResponse = '<Response><Say>Hello</Say></Response>';

      jest.spyOn(controller, 'handlePayByPhone').mockResolvedValue(twimlResponse);
      jest.spyOn(payByPhoneService.storageService, 'getStorage');
      jest.spyOn(payByPhoneService.storageService, 'saveStorage');

      const result = await controller.handlePayByPhoneAsyncInvocation(event);

      expect(result).toBe(true);
      expect(controller.handlePayByPhone).toHaveBeenCalledWith(event.body);
      expect(payByPhoneService.storageService.getStorage).toHaveBeenCalledWith(storageKey(event.body));
      expect(payByPhoneService.storageService.saveStorage).toHaveBeenCalledWith(storageKey(event.body), {
        ...inProgressStorage,
        stateInProgressResult: { twimlResponse, error: undefined },
      });
    });

    it('should return true and log to storage when an error occurs handling the pay-by-phone state', async () => {
      const event = {
        source: 'async-invocation',
        body: {
          CallSid: '12345',
          LocationId: 456,
          TransferToAgentUrl: 'http://example.com',
          StorageKeyPrefix: 'in_progress_',
        },
      };

      const inProgressStorage = {
        state: 'CustomerByPhoneSearch',
        locale: 'en-US',
        transferToAgentUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1',
        locationId: 456,
        phoneNumber: '6143852455',
        paymentSuccessRedirectUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1',
        paymentFailureRedirectUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1',
        payByPhoneStartTime: 1718387787715,
        origin: 'callroute',
      } as PayByPhoneStorage;

      payByPhoneService.storageService.initialize({ data: { [storageKey(event.body)]: JSON.stringify(inProgressStorage) } });

      const error = new Error('Some error');

      jest.spyOn(controller, 'handlePayByPhone').mockRejectedValue(error);
      jest.spyOn(payByPhoneService.storageService, 'getStorage');
      jest.spyOn(payByPhoneService.storageService, 'saveStorage');

      const result = await controller.handlePayByPhoneAsyncInvocation(event);

      expect(result).toBe(true);
      expect(controller.handlePayByPhone).toHaveBeenCalledWith(event.body);
      expect(payByPhoneService.storageService.getStorage).toHaveBeenCalledWith(storageKey(event.body));
      expect(payByPhoneService.storageService.saveStorage).toHaveBeenCalledWith(storageKey(event.body), {
        ...inProgressStorage,
        stateInProgressResult: { error },
      });
    });

    it('should return true and log to stderr when an error occurs saving the storage', async () => {
      const event = {
        source: 'async-invocation',
        body: {
          CallSid: '12345',
          LocationId: 456,
          TransferToAgentUrl: 'http://example.com',
          StorageKeyPrefix: 'in_progress_',
        },
      };

      const inProgressStorage = {
        state: 'CustomerByPhoneSearch',
        locale: 'en-US',
        transferToAgentUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1',
        locationId: 456,
        phoneNumber: '6143852455',
        paymentSuccessRedirectUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1',
        paymentFailureRedirectUrl: 'https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1',
        payByPhoneStartTime: 1718387787715,
        origin: 'callroute',
      } as PayByPhoneStorage;

      payByPhoneService.storageService.initialize({ data: { [storageKey(event.body)]: JSON.stringify(inProgressStorage) } });

      const error = new Error('Some error');

      jest.spyOn(controller, 'handlePayByPhone').mockRejectedValue(error);
      jest.spyOn(payByPhoneService.storageService, 'getStorage');
      jest.spyOn(payByPhoneService.storageService, 'saveStorage').mockRejectedValue(error);
      jest.spyOn(console, 'error');

      const result = await controller.handlePayByPhoneAsyncInvocation(event);

      expect(result).toBe(true);
      expect(console.error).toHaveBeenCalledWith('Error saving storage: ', error);
    });
  });
});