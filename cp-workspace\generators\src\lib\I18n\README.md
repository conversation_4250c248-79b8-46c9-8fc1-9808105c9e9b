# I18n Type Generator

## Purpose

The I18n Type Generator is a tool that generates TypeScript type definitions from internationalization (i18n) translation files. It uses the nestjs-i18n module to:

1. Load translation files from a specified directory
2. Generate TypeScript type definitions based on the translation keys
3. Output the generated types to a specified file

This tool helps maintain type safety when working with internationalization in your application by providing type checking for translation keys.

## Usage

You can run the I18n Type Generator using the following command:

```bash
node generators/src/lib/I18n/generate-types.js
```

### Configuration

The generator is configured with hardcoded paths in the script:

- Input path: `../../../../libs/pay-by-phone-domain/src/i18n/`
- Output path: `../../../../libs/pay-by-phone-domain/src/lib/PayByPhone/Generated/i18n.generated.ts`

These paths are relative to the script's location.

### Customizing Paths

If you need to use different paths, you'll need to modify the script directly. The relevant section is:

```javascript
const app = await NestFactory.createApplicationContext(I18nModule.forRoot({
  loaderOptions: {
    path: path.join(__dirname, '../../../../libs/pay-by-phone-domain/src/i18n/')
  },
  typesOutputPath: path.join(__dirname, '../../../../libs/pay-by-phone-domain/src/lib/PayByPhone/Generated/i18n.generated.ts'),
}));
```

## Requirements

This tool requires:

1. Node.js
2. @nestjs/core
3. nestjs-i18n

Make sure these dependencies are installed before running the generator. 
