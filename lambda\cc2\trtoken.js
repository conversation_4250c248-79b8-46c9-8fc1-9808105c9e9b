const AWS = require('aws-sdk');
const Twilio = require('twilio');
const got = require('got');
const config = require('./config');

AWS.config.update({ region: process.env.AWS_REGION });

const JWT = Twilio.jwt;
const { AccessToken } = Twilio.jwt;
const { TaskRouterCapability } = JWT.taskrouter;

async function handler(event) {
  // keep warm!
  if (event.source === 'aws.events') return { statusCode: 200 };

  if (!event.headers.Authorization) return responseError(401, 'Not authorized.');
  if (!event.queryStringParameters.tokenType) return responseError(400, 'Token type is missing.');

  const authorization = event.headers.Authorization;
  const tokenType = event.queryStringParameters.tokenType.toLowerCase();
  const ttl = parseInt(event.queryStringParameters.ttl, 10) || 3600;

  const allowedTokens = ['workspace', 'worker', 'sync', 'chat', 'voice', 'video'];
  if (!allowedTokens.includes(tokenType)) {
    return responseError(400, `invalid tokenType ${tokenType}.`);
  }

  const { workerSid, guid } = event.queryStringParameters;
  if (tokenType === 'worker' && !workerSid) return responseError(400, 'Worker sid is missing.');

  const session = {};
  try {
    Object.assign(session, await getSessionUserData(authorization));
  } catch (e) {
    console.error('Error obtaining session user data', e);
    return responseError(401, 'Not authorized.');
  }

  const account = {};
  try {
    Object.assign(account, await getTwilioAccount(session.parent_id || session.user_id));
  } catch (e) {
    console.error('Error obtaning Twilio account from dynamo', e);
    return responseError(404, 'Twilio account not found.');
  }

  const voice_identity = guid ? `agent_${session.user_id}_${guid}` : `agent_${session.user_id}`;
  // if (tokenType === 'voice'){
  //   console.log("VOICE IDENTITY:", voice_identity);
  // }

  const video_identity = guid ? `agent_${session.user_id}_${guid}` : `agent_${session.user_id}`;

  /*
    CPAPI-2327
    Adds support for specifying the chatIdentity with fallback to session email.
  */
  let chatIdentity = event.queryStringParameters.chatIdentity;
  if (!chatIdentity){
    chatIdentity = session.email;
  }

  try {
    const options = { account, ttl };
    const tokenMaker = {
      chat: () => chatAccessToken({ ...options, identity: chatIdentity }),
      sync: () => syncAccessToken({ ...options, identity: authorization }),
      voice: () => voiceAccessToken({ ...options, identity: voice_identity }),
      video: () => videoAccessToken({ ...options, identity: video_identity }),
      worker: () => capabilityToken({ ...options, tokenType, channelId: workerSid }),
      workspace: () => capabilityToken({ ...options, tokenType, channelId: account.workspace_sid }),
    };

    const result = { token: tokenMaker[tokenType]() };
    if (tokenType === 'voice'){
      result.voice_identity = voice_identity;
    }
    if (tokenType === 'video'){
      result.video_identity = video_identity;
    }

    return responseSuccess(result);
  } catch (e) {
    console.error('Error while generating Token', e);
    return responseError(500, e.message);
  }
}

async function getTwilioAccount(userId) {
  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  const params = {
    TableName: config.dynamodb.acctTable,
    KeyConditionExpression: 'id = :id',
    ExpressionAttributeValues: { ':id': userId },
  };

  try {
    const data = await dynamo.query(params).promise();
    return (data.Items.length) ? data.Items[0] : {};
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

async function getSessionUserData(authorization) {
  try {
    const url = `${config.API_CORE_URL}/session`;
    const options = {
      headers: { Authorization: authorization },
    };
    const res = await got.get(url, options);
    return JSON.parse(res.body).user;
  } catch (e) {
    console.error('ERROR obtaning twilio credentials', e);
    return {};
  }
}

function accessToken(options) {
  const {
    account,
    ttl,
    identity,
    grant,
  } = options;

  const token = new AccessToken(
    account.account_sid,
    account.api_key, // keySid
    account.api_secret,
    { ttl, identity } // options
  );
  token.addGrant(grant);
  return token.toJwt();
}

function chatAccessToken(options) {
  const serviceSid = options.account.chat_service_sid;
  if (!serviceSid || serviceSid === '') {
    console.error('chatAccessToken', options);
    throw new Error('Service sid is required to crete a chat access token.');
  }

  const grant = new AccessToken.ChatGrant({ serviceSid });
  return accessToken({ ...options, grant });
}

function syncAccessToken(options) {
  const serviceSid = options.account.sync_service_sid;
  if (!serviceSid || serviceSid === '') {
    console.error('syncAccessToken', options);
    throw new Error('Service sid is required to crete a sync access token.');
  }

  const grant = new AccessToken.SyncGrant({ serviceSid });
  return accessToken({ ...options, grant });
}

function voiceAccessToken(options) {
  const outgoingApplicationSid = config.twilio.twiml_app_sid;
  if (!outgoingApplicationSid || outgoingApplicationSid === '') {
    console.error('voiceAccessToken', options);
    throw new Error('Application sid is required to crete a voice access token.');
  }

  const grant = new AccessToken.VoiceGrant({ outgoingApplicationSid, incomingAllow: true });
  return accessToken({ ...options, grant });
}

function videoAccessToken(options) {
  const grant = new AccessToken.VideoGrant({  });
  return accessToken({ ...options, grant });
}

function capabilityToken(params) {
  const {
    account,
    tokenType,
    ttl,
    channelId,
  } = params;

  const capability = new TaskRouterCapability({
    accountSid: account.account_sid,
    authToken: account.authtoken,
    workspaceSid: account.workspace_sid,
    channelId,
    ttl: parseInt(ttl, 10) || 3600,
  });

  [
    ...JWT.taskrouter.util.defaultEventBridgePolicies(account.account_sid, channelId),
    ...taskRouterPolicies(account.workspace_sid, tokenType),
  ].forEach(policy => capability.addPolicy(policy));

  return capability.toJwt();
}

function taskRouterPolicies(workspaceSid, tokenType) {
  const buildPolicy = workspacePolicyBuilder(workspaceSid);

  // Add Base policies for taskrouter tokens: [workspace, worker]
  const policies = [
    buildPolicy({ resources: [], method: 'GET' }),
    buildPolicy({ resources: ['**'], method: 'GET' }),
    buildPolicy({ resources: ['**'], method: 'POST' }),
  ];

  if (tokenType === 'worker') {
    policies.push(buildPolicy({ resources: ['**'], method: 'POST' }));
  }

  if (tokenType === 'workspace') {
    policies.push(
      // Workspace Tasks Update Policy
      buildPolicy({ resources: ['Tasks', '**'], method: 'GET' }),
      buildPolicy({ resources: ['Tasks', '**'], method: 'POST' }),
      // Workspace Activities Worker Policy
      buildPolicy({ resources: ['Workers', workspaceSid], method: 'GET' }),
      buildPolicy({ resources: ['Workers', workspaceSid], method: 'POST' })
    );
  }

  return policies;
}

function workspacePolicyBuilder(workspaceSid) {
  if (!workspaceSid) throw new Error('Workspace SID is required to build policies');

  const TASKROUTER_BASE_URL = 'taskrouter.twilio.com';
  const version = 'v1';
  const baseUrl = `https://${TASKROUTER_BASE_URL}/${version}/Workspaces/${workspaceSid}`;

  return (options = {}) => {
    const {
      resources = [],
      method = 'GET',
      allow = true,
      postFilter = {},
      queryFilter = {},
    } = options;

    return new TaskRouterCapability.Policy({
      url: [baseUrl, ...resources].join('/'),
      method,
      allow,
      postFilter,
      queryFilter,
    });
  };
}

function responseError(statusCode = 500, message) {
  return {
    statusCode,
    body: message ? JSON.stringify({ message }) : '',
  };
}

function responseSuccess(data) {
  return {
    statusCode: 200,
    body: data ? JSON.stringify(data) : '',
  };
}

function noOp(event, context) {
  // console.log(event);
  context.done();
}

module.exports = {
  handler,
  no_op: noOp,
};
