name: Cleanup Stale Branches

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *' # This schedule runs the workflow at midnight every day

jobs:
  cleanup-stale-branches:
    runs-on: ubuntu-latest
    steps:
      - name: Cleanup Stale Branches
        uses: cbrgm/cleanup-stale-branches-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          ignore-branches: "rc-*, legacy-*"
          last-commit-age-days: 60
          dry-run: true
          rate-limit: true
