/**
 * This is a list of all the standard libraries that are available in Node.js.
 * If we encounter any require statements for these libraries in the code, we will
 * not include them in the list of external dependencies.
 */
export const standardLibs = {
  "assert": "Provides a set of assertion tests.",
  "async_hooks": "Provides an API to track asynchronous resources.",
  "buffer": "Used to handle binary data.",
  "child_process": "Provides the ability to spawn child processes.",
  "cluster": "Allows creating a network of processes sharing server ports.",
  "console": "Provides a simple debugging console.",
  "constants": "Provides commonly used constants.",
  "crypto": "Provides cryptographic functionalities.",
  "dgram": "Provides an implementation of UDP/datagram sockets.",
  "dns": "Enables name resolution functions.",
  "domain": "Provides a way to handle multiple different IO operations as a single group.",
  "events": "Provides a way to work with events and event-driven architecture.",
  "fs": "Provides an API for interacting with the file system.",
  "http": "Enables creating HTTP servers and clients.",
  "http2": "Provides an implementation of the HTTP/2 protocol.",
  "https": "Provides a way to create HTTPS servers and clients.",
  "inspector": "Provides an interface for debugging and profiling Node.js.",
  "module": "Provides the module API.",
  "net": "Provides an implementation of networking functions.",
  "os": "Provides operating system-related utility methods and properties.",
  "path": "Provides utilities for working with file and directory paths.",
  "perf_hooks": "Provides an implementation of Performance Timing APIs.",
  "process": "Provides information and control over the current Node.js process.",
  "punycode": "Provides a way to encode and decode Punycode strings.",
  "querystring": "Provides utilities for parsing and formatting URL query strings.",
  "readline": "Provides an interface for reading data from a Readable stream (such as process.stdin) one line at a time.",
  "repl": "Provides a Read-Eval-Print Loop (REPL) implementation.",
  "stream": "Provides an API for implementing stream-based interfaces.",
  "string_decoder": "Provides a way to decode buffer objects into strings.",
  "timers": "Provides functions for scheduling the execution of code.",
  "tls": "Provides an implementation of the Transport Layer Security (TLS) and Secure Socket Layer (SSL) protocols.",
  "trace_events": "Provides a way to generate tracing information.",
  "tty": "Provides an interface for TTY (teletypewriter) streams.",
  "url": "Provides utilities for URL resolution and parsing.",
  "util": "Provides utility functions that are useful for applications and modules.",
  "v8": "Provides an API for interacting with the V8 JavaScript engine.",
  "vm": "Provides APIs for compiling and running code within VMs.",
  "worker_threads": "Enables the use of threads that execute JavaScript in parallel.",
  "zlib": "Provides compression functionalities."
} as { [key: string]: string };
