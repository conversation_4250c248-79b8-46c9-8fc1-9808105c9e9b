
/**
 * This file was auto-generated by the state-diagram-code generator.
 * DO NOT MODIFY THIS FILE DIRECTLY.
 * To make changes, modify the source diagram file and re-run the generator.
 * Diagram source: /libs/pay-by-phone-domain/src/.diagrams/PayByPhone.state.mmd
 * Generated at: 2025-02-26T20:51:04.908Z
 */

import { Module } from '@nestjs/common';
import { LocalePrompt } from '../States/LocalePrompt';
import { LocaleConfirm } from '../States/LocaleConfirm';
import { CollectionsPrompt } from '../States/CollectionsPrompt';
import { CollectionsConfirm } from '../States/CollectionsConfirm';
import { SayAmountDue } from '../States/SayAmountDue';
import { TransferToAgent } from '../States/TransferToAgent';
import { CustomerOptOut } from '../States/CustomerOptOut';
import { DisconnectCall } from '../States/DisconnectCall';
import { InputPhoneGather } from '../States/InputPhoneGather';
import { InputPhoneValidate } from '../States/InputPhoneValidate';
import { CustomerByPhoneSearch } from '../States/CustomerByPhoneSearch';
import { UnitsToPayPrompt } from '../States/UnitsToPayPrompt';
import { ConfirmCustomerInfo } from '../States/ConfirmCustomerInfo';
import { UnitsToPayPrepayMonthsPrompt } from '../States/UnitsToPayPrepayMonthsPrompt';
import { UnitsToPayPrepayMonthsSelection } from '../States/UnitsToPayPrepayMonthsSelection';
import { GetSavedCards } from '../States/GetSavedCards';
import { PayMethodPrompt } from '../States/PayMethodPrompt';
import { PayMethodCreditCardPrompt } from '../States/PayMethodCreditCardPrompt';
import { UnitsToPaySelection } from '../States/UnitsToPaySelection';
import { UnitsToPayPrepayMonthsConfirm } from '../States/UnitsToPayPrepayMonthsConfirm';
import { PayMethodSelection } from '../States/PayMethodSelection';
import { ExistingPayMethodVerify } from '../States/ExistingPayMethodVerify';
import { PayMethodSecurityCodePrompt } from '../States/PayMethodSecurityCodePrompt';
import { PayMethodCreditCardValidate } from '../States/PayMethodCreditCardValidate';
import { PayMethodCreditCardConfirm } from '../States/PayMethodCreditCardConfirm';
import { PayMethodExpirationPrompt } from '../States/PayMethodExpirationPrompt';
import { PayMethodExpirationValidate } from '../States/PayMethodExpirationValidate';
import { PayMethodExpirationConfirm } from '../States/PayMethodExpirationConfirm';
import { PayMethodSecurityCodeValidate } from '../States/PayMethodSecurityCodeValidate';
import { PayMethodSecurityCodeConfirm } from '../States/PayMethodSecurityCodeConfirm';
import { PayMethodPostalCodePrompt } from '../States/PayMethodPostalCodePrompt';
import { FinalPayAmountPrompt } from '../States/FinalPayAmountPrompt';
import { PayMethodPostalCodeValidate } from '../States/PayMethodPostalCodeValidate';
import { PayMethodPostalCodeConfirm } from '../States/PayMethodPostalCodeConfirm';
import { FinalPayAmountConfirm } from '../States/FinalPayAmountConfirm';
import { FinalPayAmountSubmitted } from '../States/FinalPayAmountSubmitted';
import { PaymentSuccessful } from '../States/PaymentSuccessful';
import { PaymentFailure } from '../States/PaymentFailure';

@Module({
  providers: [
    LocalePrompt,
    LocaleConfirm,
    CollectionsPrompt,
    CollectionsConfirm,
    SayAmountDue,
    TransferToAgent,
    CustomerOptOut,
    DisconnectCall,
    InputPhoneGather,
    InputPhoneValidate,
    CustomerByPhoneSearch,
    UnitsToPayPrompt,
    ConfirmCustomerInfo,
    UnitsToPayPrepayMonthsPrompt,
    UnitsToPayPrepayMonthsSelection,
    GetSavedCards,
    PayMethodPrompt,
    PayMethodCreditCardPrompt,
    UnitsToPaySelection,
    UnitsToPayPrepayMonthsConfirm,
    PayMethodSelection,
    ExistingPayMethodVerify,
    PayMethodSecurityCodePrompt,
    PayMethodCreditCardValidate,
    PayMethodCreditCardConfirm,
    PayMethodExpirationPrompt,
    PayMethodExpirationValidate,
    PayMethodExpirationConfirm,
    PayMethodSecurityCodeValidate,
    PayMethodSecurityCodeConfirm,
    PayMethodPostalCodePrompt,
    FinalPayAmountPrompt,
    PayMethodPostalCodeValidate,
    PayMethodPostalCodeConfirm,
    FinalPayAmountConfirm,
    FinalPayAmountSubmitted,
    PaymentSuccessful,
    PaymentFailure,
  ],
  exports: [
    LocalePrompt,
    LocaleConfirm,
    CollectionsPrompt,
    CollectionsConfirm,
    SayAmountDue,
    TransferToAgent,
    CustomerOptOut,
    DisconnectCall,
    InputPhoneGather,
    InputPhoneValidate,
    CustomerByPhoneSearch,
    UnitsToPayPrompt,
    ConfirmCustomerInfo,
    UnitsToPayPrepayMonthsPrompt,
    UnitsToPayPrepayMonthsSelection,
    GetSavedCards,
    PayMethodPrompt,
    PayMethodCreditCardPrompt,
    UnitsToPaySelection,
    UnitsToPayPrepayMonthsConfirm,
    PayMethodSelection,
    ExistingPayMethodVerify,
    PayMethodSecurityCodePrompt,
    PayMethodCreditCardValidate,
    PayMethodCreditCardConfirm,
    PayMethodExpirationPrompt,
    PayMethodExpirationValidate,
    PayMethodExpirationConfirm,
    PayMethodSecurityCodeValidate,
    PayMethodSecurityCodeConfirm,
    PayMethodPostalCodePrompt,
    FinalPayAmountPrompt,
    PayMethodPostalCodeValidate,
    PayMethodPostalCodeConfirm,
    FinalPayAmountConfirm,
    FinalPayAmountSubmitted,
    PaymentSuccessful,
    PaymentFailure,
  ],
})
export class PayByPhoneStatesModule {}
