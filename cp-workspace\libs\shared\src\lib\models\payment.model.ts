import { PagingData } from "./paging.model";
import { CustomerId, LedgerId, UnitId } from "./ledger.model";
import { LocationId } from "./location-configuration.model";

export type PaymentToken = {
    cardNumber?: string;
    securityCode?: string;
    expiration?: string;
    postalCode?: string;
};

export type PaymentData = {
    amount: number;
    convenience_fee: boolean;
    is_auto_pay: boolean;
    pay_method: string;
    payment_hash: string;
    save_cc: boolean;
    total_amount_due: number;
    unit_id: string;
    prepay_months?: number;
    card_id?: string;
    cvc_number: string;
    postal_code: string;
    card_number?: string;
    exp_month?: string;
    exp_year?: string;
    convenience_fee_amount?: number;
    convenience_fee_source?: string;
    payment_id: string;
};

export type PaymentHashRequest = {
    customer_id: CustomerId,
    location_id: LocationId,
    ledger_id: LedgerId
};

export type PaymentHashResponse = {
    hash: string;
};

export type PaymentHashDetails = {
    location_id: string;
    customer_id: string;
    ledger_id: string;
    source: string;
    employee_id: string;
    payment_id: string;
};

export type PaymentHashDetailsResponse = {
    status: "OK" | "ERROR" | "MIXED";
    items: PaymentHashDetails;
    paging: PagingData;
};

export type TenantPaymentResponse = TenantPaymentErrorResponse | BaseTenantPaymentResponse;

export type BaseTenantPaymentResponse = {
    success: boolean;
    msg?: string;
};

export type TenantPaymentErrorResponse = BaseTenantPaymentResponse & {
    error_msg: string;
};

export type WithResponseMetadata<T> = 
{ 
    data: T;
    status: number;
};

export type SavedCard = {
    id: string;
    card_name: string;
    card_number: string;
    autopay_enabled: string;
    surchargeable?: boolean;
};

export type SavedCardsResponse = {
    items: SavedCard[];
};

export type AmountDueItem = {
    unit_id: string;
    unit_name: string;
    description: string;
    total: string;
    tax: string;
    amount_paid: string;
    amount_due: string;
};

export type DueMeta = {
    total: string;
    breakup_match: boolean;
};

export type AmountDueRequest = {
    ledger_id: LedgerId;
    unit_id: UnitId;
    prepay_month: number;
};

export type AmountDueResponse = AmountDueSuccessfulResponse[] | AmountDueErrorResponse;

export type AmountDueSuccessfulResponse = {
    ledger_id: string;
    unit_id: string;
    prepay_month: number;
    data: AmountDueItem[];
    meta: DueMeta;
};

export type AmountDueErrorResponse = {
    error: true;
};

export type BinCheckResponse = {
    surchargeable: boolean;
};

export type TotalWithSurchargeRequest = {
    total: number;
    tenant_id: string;
    location_id: string;
};

export type TotalWithSurchargeResponse = {
    surcharge_enabled: boolean;
    surcharge: SurchargeObject;

};

export type SurchargeObject = {
    total_with_surcharge: number;
    surcharge_amount: number;
    surcharge_percentage: number;
  };