'use strict';

const Bugsnag = require('@bugsnag/js');
const BugsnagPluginAwsLambda = require('@bugsnag/plugin-aws-lambda');
const config = require('../config/config');

class BugsnagService {
  constructor() {
    if (process.env.NODE_ENV === 'test' || !config.BUGSNAG_API_KEY) {
      this.client = { notify: () => {} };
    }
    else {      
      // Initialize Bugsnag
      Bugsnag.start({
        apiKey: config.BUGSNAG_API_KEY || '',
        plugins: [BugsnagPluginAwsLambda],
        enabledBreadcrumbTypes: ['error', 'log', 'navigation', 'request', 'user'],
        releaseStage: process.env.NODE_ENV || config.env || 'development',
        appVersion: '1.0.0',
      });
    }
  }

  /**
   * Get the Bugsnag handler for AWS Lambda
   */
  getLambdaHandler() {
    const awsLambdaPlugin = Bugsnag.getPlugin('awsLambda');
    if (!awsLambdaPlugin) {
      console.error('AWS Lambda plugin is not available');
      return (handler) => handler;
    }
    return awsLambdaPlugin.createHandler();
  }

  /**
   * Get the Bugsnag handler for Express
   */
  getExpressRequestHandler() {
    // Return a no-op middleware if Express plugin is not available
    return (req, res, next) => {
      // Add Bugsnag to request object for manual error reporting
      req.bugsnag = Bugsnag;
      next();
    };
  }

  /**
   * Get the Bugsnag handler for Express error middleware
   */
  getExpressErrorHandler() {
    // Return a no-op error middleware if Express plugin is not available
    return (err, req, res, next) => {
      // Manually notify Bugsnag of the error
      Bugsnag.notify(err, {
        request: req,
        response: res
      });
      next(err);
    };
  }

  /**
   * Method to leave a breadcrumb in Bugsnag with message and optional metadata
   * @param {string} message
   * @param {any} metadata
   */
  leaveBreadcrumb(message, metadata) {
    Bugsnag.leaveBreadcrumb(message, metadata);
  }

  /**
   * Method to set user context for better error tracking
   * @param {string} id - User ID
   * @param {string} email - User email
   * @param {string} name - User name
   */
  setUser(id, email, name) {
    Bugsnag.setUser(id, email, name);
  }

  /**
   * Method to add context to errors
   * @param {string} context - Context string
   */
  setContext(context) {
    Bugsnag.setContext(context);
  }

  /**
   * Method to capture an error and notify Bugsnag
   * @param {Error|string} error - Error object or string message
   * @param {any} options - Additional options including metadata
   */
  notify(error, options) {
    Bugsnag.notify(error, function(event) {
      if (options && options.severity) {
        event.severity = options.severity;
      }
      if (options && options.metadata) {
        for (let section in options.metadata) {
          event.addMetadata(section, options.metadata[section]);
        }
      }
    });
  }
}

const bugsnagService = new BugsnagService();
module.exports = bugsnagService;