<?php
/**
 * UnprocessedcounterController - retrieve unprocessed call counter
 *
 * @category UnprocessedcounterController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Controllers\SessionTrait;
use CallPotential\CPCommon\Controllers\NoDataTrait;
use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\HttpStatusCode;

/**
 * UnprocessedcounterList
 *
 * @SWG\Definition(type="object",   definition="UnprocessedcounterList",
 * @SWG\Property(property="items",  type="array",
 * @SWG\Items(ref="#/definitions/Unprocessedcounter")),
 * @SWG\Property(property="paging", ref="#/definitions/PagingData"),
 * @SWG\Property(property="status", type="string"),
 * @SWG\Property(property="cacheHit", type="boolean"),
 *  )
 */

/**
 * Unprocessedcounter Result Item
 *
 * @SWG\Definition(type="object",      definition="Unprocessedcounter",
 * @SWG\Property(property="key",       type="number", description="location id"),
 * @SWG\Property(property="doc_count", type="number", description="counter"))
 * )
 */

/**
 * UnprocessedcounterController - retrieve unprocessed call counter
 *
 * @category UnprocessedcounterController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class UnprocessedcounterController extends \CallPotential\CPCommon\Controllers\BaseController
{
    use SessionTrait, NoDataTrait;

    const WRITING_COUNTER_CACHE = -1;

    /**
     * Session expire time
     *
     * @var int
     */
    public $expire = 60;

    /**
     * Swagger
     *
     * @SWG\Get(
     *      tags={"Call Data"},
     *      path="/unprocessedcounter",
     *      description="Returns the amount of unprocessed calls.",
     *      summary="List unprocessed calls count",
     *      operationId="Unprocessedcounter",
     *      produces={"application/json"},
     * @SWG\Parameter(
     *          in="header",
     *          type="string",
     *          name="Authorization",
     *          required=true,
     *          description="Authorization token to identify user"
     *      ),
     * @SWG\Response(
     *          response=200,
     *          description="Unprocessed calls count data",
     * @SWG\Schema(ref="#/definitions/UnprocessedcounterList")
     *      ),@SWG\Response(
     *          response=403,
     *          description="Not authorized invalid or missing authorization token",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *      ),@SWG\Response(
     *          response=404,
     *          description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *      ),@SWG\Response(
     *          response=500,
     *          description="Unexpected Error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *      )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return \Phalcon\Http\Response
     */
    public function listAction()
    {
        if (!$this->validSession()) {
            return $this->sendAccessDeniedResponse();
        }
        $messageInfo = __CLASS__ . '::' . __FUNCTION__ . ':' ;
        $this->infoMessage('list request begin', $messageInfo . __LINE__);
        $accountId = $this->getCurrentAccountId();
        if ((int) $accountId) {
            $cacheKey = $accountId . '-unprocessedcounter';
            $cacheHit = true;
            // CP-18259
            // Avg response time is less than 600 ms so set Retry-After to 1 sec
            $retryAfter  = 1;
            if (empty($this->badgeCache->get($cacheKey))) {
                $cacheHit = false;
                $this->infoMessage('Unprocessed Badge counter cache data is empty');
                $this->badgeCache->set($cacheKey, self::WRITING_COUNTER_CACHE, $this->expire);

                $allLocations = $this->getAccountLocations($accountId);

                $result = $this->getUnprocessedCount($accountId, $allLocations);

                if (!empty($result['error'])) {
                    // Reset counter cache if error
                    $this->badgeCache->delete($cacheKey);
                    $this->response->setStatusCode(
                        $result['code']
                    );

                    $this->response->setJsonContent(
                        [
                            'status' => "ERROR",
                        ]
                    );

                    return $this->response;
                }

                $this->badgeCache->set($cacheKey, $result, $this->expire);
            }

            $data = $this->badgeCache->get($cacheKey);

            if (!is_array($data) && $data === self::WRITING_COUNTER_CACHE) {
                $this->response->setStatusCode(
                    HttpStatusCode::HTTP_TOO_MANY_REQUESTS
                );

                $this->response->setHeader(
                    'Access-Control-Expose-Headers',
                    'Retry-After'
                );

                // CP-18259 Set Retry-After header
                $this->response->setHeader(
                    'Retry-After',
                    $retryAfter
                );
                $this->response->setJsonContent(
                    [
                        'status' => "ERROR",
                    ]
                );

                return $this->response;
            }
            $this->infoMessage('Unprocessed Badge counter data fetched from cache');

            // Prepare and send response
            $this->response->setStatusCode(
                HttpStatusCode::HTTP_OK,
                HttpStatusCode::getMessageForCode(HttpStatusCode::HTTP_OK)
            );
            $this->response->setJsonContent([
                'status' => "OK",
                'items' => $this->formatListResponse($data),
                'cacheHit' => $cacheHit,
                'paging' => false,
            ]);

            $this->infoMessage('list request end', $messageInfo . __LINE__);

            return $this->response;
        }
    }

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    protected function getModelName(): string
    {
        return "CallHistory";
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return false;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);

        return false;
    }

    /**
     * Format response
     *
     * @param mixed $data response data
     *
     * @return mixed
     */
    protected function formatListResponse($data)
    {

        //Format response
        $searchLocations = [];
        $sessionUserData = unserialize($this->currentSession->user_data);
        if (!empty($sessionUserData['locations'])) {
            $searchLocations = array_keys($sessionUserData['locations']);
        }

        $responseData = [];
        if (!empty($data)  && $searchLocations) {
            $getAllResultLocations = array_column($data, 'key');
            foreach ($searchLocations as $searchLocation) {
                if (in_array($searchLocation, $getAllResultLocations)) {
                    $key = array_search($searchLocation, $getAllResultLocations);
                    $responseData[] = $data[$key];
                }
            }
        }

        return $responseData;
    }

     /**
     * Get all locations of account
     *
     * @param int $accountId
     *
     * @return array
     */
    protected function getAccountLocations($accountId)
    {
        $allLocations = [];
        $acctLocations = [];
        // user_id field in the Location table corresponds to the accountId
        $acctLocations = Location::find(
            [
                'user_id = :accountId: AND active = :active:',
                'bind' => [
                    'accountId' => $accountId,
                    'active'  => 1,
                ],
            ]
        );
        if ($acctLocations) {
            $acctLocations = $acctLocations->toArray();
        }
        $allLocations = array_column($acctLocations, 'location_id');

        return $allLocations;
    }

    /**
     * Get unprocessed call count
     *
     * @param int $accountId
     * @param array $allLocations
     *
     * @return array
    */
    protected function getUnprocessedCount($accountId, $allLocations)
    {
        $result = [];
        // Create and configure call history object
        $callHistoryModel = new CallHistory();
        if (!empty($allLocations)) {
               // Fetch count from model
                $result = $callHistoryModel->fetchUnprocessedCount(
                    $accountId,
                    $allLocations,
                    2,
                    'years'
                );
        }

        return $result;
    }
}
