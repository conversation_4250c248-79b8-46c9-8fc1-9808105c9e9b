"use strict";

/**
 * Smart_route Class
 *
 * Handles smart_route for call route functionality
 * Retrieves the customer_type of caller
 *
 * @package    Callpotential
 * @subpackage Application
 * @category   Libraries
 * <AUTHOR>
 * @link       void
 */
var smartRoute = class SmartRoute {
  constructor(config) {
    this.name = null;
    this.customer_types = null;
    this.location_id = null;
    this.log_id = null;

    // Instance variable holding map from customer type returned by
    // call logs table to customer type saved in call route config connector_label
    this.cust_type_map = {
      'Lead': 'lead',
      'Current': 'customer_current',
      'Delinquent': 'customer_delinquent',
      'Current Customer': 'customer_current',
      'Delinquent Customer': 'customer_delinquent',
      'Unknown': 'other'
    };

    if (config) {
      this.location_id = config['location_id'];
      this.name = config['name'];
      this.customer_types = config['customer_types'];
      this.log_id = config['log_id'];
    }

  }
}

module.exports = {
  'SmartRoute': smartRoute
}
