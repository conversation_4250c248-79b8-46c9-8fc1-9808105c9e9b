import { Module } from '@nestjs/common';
import { DomainEventServiceOptions, IPublishDomainEventsProvider } from '../../models/domain-event.model';
import { DomainEventsMockClientProvider } from './domain-event.mock.provider';
import { DomainEventsClientProvider } from './domain-event.aws.provider';
import { DomainEventsIntegrationService } from './domain-events.integration.service';

export enum DomainEventsClientType {
  EXTERNAL = 'EXTERNAL',
  IN_MEMORY = 'IN_MEMORY',
}

export interface DomainEventsModuleOptions {
  clientType: DomainEventsClientType;
  options?: DomainEventServiceOptions;
}

@Module({})
export class DomainEventsIntegrationModule {


  static forRoot(options: DomainEventsModuleOptions) {
    if (options?.options) {
      DomainEventsIntegrationService.options = options.options;
    }
    if (options.clientType === DomainEventsClientType.IN_MEMORY) {
      return {
        module: DomainEventsIntegrationModule,
        providers: [
          IPublishDomainEventsProvider,
          DomainEventsMockClientProvider,
          {
            provide: 'IPublishDomainEventsProvider',
            useClass: DomainEventsMockClientProvider,
          },
        ],
        exports: [
          IPublishDomainEventsProvider,
          DomainEventsMockClientProvider,
          'IPublishDomainEventsProvider',
        ],
      };
    }
    if (options.clientType === DomainEventsClientType.EXTERNAL) {
      return {
        module: DomainEventsIntegrationModule,
        providers: [
          IPublishDomainEventsProvider,
          DomainEventsClientProvider,
          {
            provide: 'IPublishDomainEventsProvider',
            useClass: DomainEventsClientProvider,
          },
        ],
        exports: [
          IPublishDomainEventsProvider,
          DomainEventsClientProvider,
          'IPublishDomainEventsProvider',
        ],
      };
    }
    return {
      module: DomainEventsIntegrationModule,
      providers: [
        IPublishDomainEventsProvider,
        DomainEventsMockClientProvider,
        DomainEventsClientProvider,
        {
          provide: 'IPublishDomainEventsProvider',
          useClass: IPublishDomainEventsProvider,
        },
      ],
      exports: [
        IPublishDomainEventsProvider,
        DomainEventsMockClientProvider,
        DomainEventsClientProvider,
        'IPublishDomainEventsProvider',
      ],
    };
  }
}