import { Injectable } from "@nestjs/common";

export interface IIoRedisProvider {
  getClient(options?: RedisClientInitialization): unknown;
  stopClient(): Promise<void>;
}


export interface RedisClientInitialization {
  data?: Record<string, unknown>
}

@Injectable()
export class IoRedisProvider implements IIoRedisProvider {
  getClient(_options?: RedisClientInitialization): unknown {
    throw new Error('IoRedisProvider.getClient() not implemented. Did you add `IoRedisProvider` to your Nest Module `providers`?');
  }

  async stopClient() {
    throw new Error('IoRedisProvider.stopClient() not implemented. Did you add `IoRedisProvider` to your Nest Module `providers`?');
  }
}
