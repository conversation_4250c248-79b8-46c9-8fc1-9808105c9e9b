<?php
namespace Test;

class VersionTest extends IntegrationCase
{
    public function testCallGetVersion()
    {
        $this->validateSwaggerPath(
            'call',
            'GetVersion',
            '/version',
            'get'
        );
        $response = $this->runGET('/version');
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/version GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'GetVersion', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'GetVersion', [200, 404, 500]);
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }
}
