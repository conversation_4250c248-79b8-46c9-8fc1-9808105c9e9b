import { PayMethodPostalCodePrompt } from './PayMethodPostalCodePrompt';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodPostalCodePrompt', () => {
  let service: PayMethodPostalCodePrompt;

  beforeEach(() => {
    service = new PayMethodPostalCodePrompt();
  });

  it('should prompt for postal code input and transition to PayMethodPostalCodeValidate', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '12345',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      finishOnKey: "*",
      timeout: 10
    }, [{
      messageId: "pay-by-phone.enter-zip",
      locale: context.storage.locale
    }]);
    expect(result.nextState).toBe(PayByPhoneState.PayMethodPostalCodeValidate);
  });
});
