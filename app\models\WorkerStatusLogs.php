<?php
/**
 * WorkerStatusLogs model
 *
 * @category Workerstatuslogs
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Model
 *
 * @category Workerstatuslogs
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Workerstatuslogs")
 */
class WorkerStatusLogs extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * Model Property
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $id;

    /**
     * Model Property
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $user_id;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $activity_sid;

    /**
     * Model Property
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $start_time;

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Method to set the value of field id
     *
     * @param integer $id id
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $userId
     *
     * @return $this
     */
    public function setUserId(int $userId)
    {
        $this->user_id = $userId;

        return $this;
    }

    /**
     * Method to set the value of field activity_sid
     *
     * @return string
     */
    public function getActivitySid(): string
    {
        return $this->activity_sid;
    }

    /**
     * Method to set the value of field activity_sid
     *
     * @param string $activitySid
     *
     * @return $this
     */
    public function setActivitySid(string $activitySid)
    {
        $this->activity_sid = $activitySid;

        return $this;
    }

    /**
     * Method to set the value of field start_time
     *
     * @return string
     */
    public function getStartTime(): string
    {
        return $this->start_time;
    }

    /**
     * Method to set the value of field start_time
     *
     * @param string $startTime
     *
     * @return $this
     */
    public function setStarTime(string $startTime)
    {
        $this->start_time = $startTime;

        return $this;
    }

    /**
     * Initialize function
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setConnectionService('dbLegacy');
        $this->setSource("worker_status_logs");
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters Parameters
     *
     * @return Ads[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters
     *
     * @return Ads
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
