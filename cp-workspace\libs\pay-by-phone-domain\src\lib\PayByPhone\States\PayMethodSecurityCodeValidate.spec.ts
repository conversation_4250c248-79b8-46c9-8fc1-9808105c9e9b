import { PayMethodSecurityCodeValidate } from './PayMethodSecurityCodeValidate';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodSecurityCodeValidate', () => {
  let service: PayMethodSecurityCodeValidate;

  beforeEach(() => {
    service = new PayMethodSecurityCodeValidate();
    service.services = {
      coreService: {
        encodePaymentToken: jest
          .fn()
          .mockImplementation((token) => Promise.resolve(token)),
        decodePaymentToken: jest
          .fn()
          .mockImplementation((token) => Promise.resolve(token)),
      }
    } as any;
  });

  it('should return nextState as PayMethodSecurityCodePrompt when security code is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '12',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en', paymentToken: {} } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodSecurityCodePrompt);
  });

  it('should return nextState as PayMethodSecurityCodeConfirm when security code is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '123',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en', paymentToken: {} } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodSecurityCodeConfirm);
  });

  it('should create a new paymentToken with security code in storage when no paymentToken is found & useSavedCardId is present', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '123',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en', useSavedCardId: true } as any, // paymentToken is intentionally omitted
    } as any;

    await service.handler(context);

    expect(context.storage.paymentToken).toBeDefined();
    expect(context.storage.paymentToken).toEqual({securityCode: context.request.Digits});
  });
});
