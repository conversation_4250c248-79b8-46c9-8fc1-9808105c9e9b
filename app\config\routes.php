<?php

use Phalcon\Mvc\Router;

$router = new Router();

//Remove trailing slashes automatically
$router->removeExtraSlashes(true);


/* GET VERB - GET ELEMENT */

//Get elemets of relationship. Ex: /department/2/user
$router->addGet('/{controller:([a-zA-Z0-9\_\-]+)}/{id:([a-zA-Z0-9_-]+)}/([a-zA-Z0-9_-]+)', array(
    'action'        => "list",
    'relationship'  => 3,
));

//Get one element. Ex: /user/2
$router->addGet('/{controller:([a-zA-Z0-9\_\-]+)}/{id:[a-zA-Z0-9-_~]+}', array(
    'action'     => "get",
));

$router->addGet('/:controller', array(
    'controller' => 1,
    'action'     => "list",
));

$router->addPost('/:controller', array(
    'controller' => 1,
    'action'     => "create",
));

$router->addPut('/{controller:([a-zA-Z0-9\_\-]+)}/{id:([a-zA-Z0-9-_]+)}', array(
    'action'     => "save",
));

/*Bulk update*/
$router->addPut('/{controller:([a-zA-Z0-9\_\-]+)}', array(
    'controller' => 1,
    'action'     => "save",
));

/* DELETE VERB - UPDATE ELEMENT */
$router->addDelete('/:controller/([a-zA-Z0-9_-]+)', array(
    'controller' => 1,
    "id"         => 2,
    'action'     => "delete",
));

$router->addDelete(
    "/:controller/([a-zA-Z0-9_-]+)",
    [
        "controller" => 1,
        "id"         => 2,
        "action"     => "delete",
    ]
);

$router->addOptions('/:controller', array(
    'controller' => 1,
    'action'     => "options",
));

$router->addOptions('/:controller/:params', array(
    'controller' => 1,
    'action'     => "options",
));

$router->addGet(
    '/conferences/{friendlyName}/participants',
    [
        'controller'    => 'conferences',
        'friendlyName' => 1 ,
        'action'        => "getListOfCallParticipants",
    ]
);

$router->addGet('/call/([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+)', array(
    'controller'    => 'call',
    "location_id"   => 1,
    "twilio_id"     => 2,
    'action'        => "get",
));

$router->addPut('/call/([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+)', array(
    'controller'    => 'call',
    "location_id"   => 1,
    "twilio_id"     => 2,
    'action'        => "save",
));

$router->addDelete('/call/([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+)', array(
    'controller'    => 'call',
    "location_id"   => 1,
    "twilio_id"     => 2,
    'action'        => "delete",
));

//not found route
$router->notFound(array(
    'controller' => 'error',
    'action' => 'route404',
));

$router->setDefaults(array(
    'controller' => 'error',
    'action' => 'route404',
));

return $router;
