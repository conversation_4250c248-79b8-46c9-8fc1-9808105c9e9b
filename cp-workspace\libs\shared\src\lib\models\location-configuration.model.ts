import { Locale } from "./i18n.model";
import { Ledger } from "./ledger.model";
import { Tenant } from "./tenant.model";

export type LocationConfiguration_SIPSetup = {
    sip_username: string;
    sip_password: string;
    is_webhook_enabled: boolean;
    sip_extension: string;
}

export type LocationConfiguration_UnitRates = {
    name: string;
    field: string;
}

export type LocationConfiguration_CanRentReservedUnit = {
    employee: 0 | 1;
    agent: 0 | 1;
    manager: 0 | 1;
    admin: 0 | 1;
    kiosk_manager: 0 | 1;
    kiosk_unattended: 0 | 1;
}

export type LocationConfiguration = {
    location_id: number;
    user_id: number;
    callcard_id: number;
    location_gradesheet_id: number;
    employee_gradesheet_id: number;
    greeting_mp3: string;
    greeting_type: number;
    greeting: string;
    call_route_config_id: number;
    bypass_press_1: number;
    record_outgoing: number;
    record_call_announcement: string;
    record_payment_calls: number;
    play_music_on_hold: number;
    music_file: string;
    rateType: number;
    rate_type_cc: number;
    rate_type_tc: number;
    excluded_specials: string;
    is_rate_restricted: number;
    round_rate: number;
    max_reservation_length: number;
    reserved_vacant: number;
    is_source_required: number;
    is_mktg_source_hide: number;
    is_mktgsrc_editable: number;
    marketing_source_map: string | null;
    mktg_source_default: number;
    is_mktgsrc_required: number;
    is_inquiry_type_hide: number;
    is_inquiry_type_req: number;
    inquiry_type_default: number;
    is_units_always_rentable: number;
    hide_phone_number: number;
    script_callcard_id: number;
    is_class_hide: number;
    is_class_required: number;
    class_default: number;
    is_available_filter_on: number;
    can_rent_reserved_units: LocationConfiguration_CanRentReservedUnit;
    select_unit_upon_time_vacant: number;
    restrict_lead_to_mark_rented: number;
    followup_rule_id: string;
    collection_rule_id: string;
    include_all_phone: number;
    include_all_email: number;
    include_all_text: number;
    minimum_balance: number;
    reset_collection_on_payment: number;
    reset_followup_workflow: number;
    overriden_script_callcard: string;
    lead_expiration_rules: string;
    convenience_fee_employee: string;
    convenience_fee_phone: string;
    convenience_fee_user: string;
    paybyphone_all: number;
    reservation_fee_settings: string;
    enable_reservation_payment: number;
    exclude_payment_link: number;
    autopay_text: string;
    save_cc: number;
    allow_prev_cc: number;
    rental_period: string;
    esign: string;
    gate_code: string;
    movein_rule_id: string;
    movein_required_fields: string | null;
    movein_failure_template: number;
    esign_email_template: number;
    esign_sms_template: number;
    is_priority_notes_hide: number;
    is_collection_notes_hide: number;
    is_pms_lead_record_link_hide: number;
    is_reservation_payment_hide: number;
    is_movein_hide: number;
    lead_cancellation_allowed: number;
    is_partial_payments_allow: number;
    require_reservation_pay_for_hard_res: number;
    allow_reservation_pay_hard_res: number;
    is_res_lead_payment_required_hide: number;
    is_res_lead_payment_required: number;
    reservation_cron_on_unknown_call: number;
    esign_methods: number;
    is_only_credit_card_allow: number;
    restrict_unit_change_reservation: number;
    allow_bulk_payment: number;
    restrict_date_needed_reservation: number;
    unit_availability_threshold_reservation: number;
    active: number;
    video_call_enabled: number;
    video_call_link: string;
    movein_gatecode_settings: any;
    allow_waitlist_without_unit: number;
    allow_notes_to_all_units: number;
    primary_sip_setup: LocationConfiguration_SIPSetup | null;
    convenience_fee_id?: string;
    unit_rates?: LocationConfiguration_UnitRates[];
    card_assignments?: any;
    allow_future_movein: number;
}

export type LocationDetails = {
    location_id?: number;
    location_name?: string;
    email?: string;
    address1?: string;
    address2?: string;
    city?: string;
    province?: string;
    postal?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
    phone?: string;
    primary_sip_setup?: any;
    user_id?: number;
    active?: number;
    api_type?: number;
    api_credentials?: string;
    api_permissions?: string;
    last_api_update?: string; // Date as ISO string
    dateCreated?: string; // Date as ISO string
    dateRemoved?: string; // Date as ISO string
    lastupdate?: string;
    delay?: number;
    timezone?: string;
    use_daylight_saving?: number;
    logo_image?: string;
    friendly_name?: string;
    outbound_phone?: string;
    outbound_sip_setup?: string;
    is_collection_only?: number;
    website?: string;
    hours_availability?: string;
    tags?: string;
    display_phone?: string;
    location_image1?: string;
    location_image2?: string;
    location_image3?: string;
    batch_event_count?: number;
    overide_tracking_number?: string;
    custom_variables?: string;
    is_api_call_allowed_threshold_1?: number;
    api_call_frequency?: number;
    sip_domain_sid?: string;
    keypad_zone?: number;
    sl_timezone?: number;
    is_pull_reservation_cron_running?: number;
    collection_rule_changed?: number;
    sl_update_followup_date?: number;
    txt_to_email?: number;
    other_email?: string;
    sl_gmt_timeoffset?: string;
    sitelink_pull_units_last_time_tick?: string;
    downgrade_reservation?: number;
    v2_data_sync?: number;
    unavailable_units?: string;
    impersonated_by?: number;
    changed_by?: number;
    allow_downgrading_reservations_by_user?: number;
    language?: string;
    location_external_id?: string;
    version_id?: number;
    locales: Locale[];
}

export type LocationConfigurationRequest = {
    formatFields?: boolean;
}

export type LocationDetailsRequest = {
    formatFields?: boolean;
}

export type AccountId = LocationConfiguration['user_id'];
export type LocationId = 
    LocationConfiguration['location_id'] |
    Ledger['location_id'] |
    Tenant['location_id'] |
    Ledger['location_id'];