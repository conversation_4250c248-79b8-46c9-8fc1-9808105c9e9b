"use strict";
var _ = require('lodash');
var mainconfig = require('../config/config');
const console_c = require('../config/logger').console;
const CommonMethod = require('../libraries/common-methods').CommonMethod;
const util = new CommonMethod();
const cpapiClient = require('../libraries/cpapi-client');

/**
 * Voicemail Class
 *
 * Handles functionality to send and record voicemail
 *
 * @package    Callpotential
 * @subpackage Application
 * @category   Libraries
 * <AUTHOR>
 * @link       void
 */
var voiceMail = class VoiceMail {

    /**
     * Initializes required instance variables
     *
     * @access public
     * @param  array $config
     * @return void
     */
    constructor(config, req_q_data, req_p_data) {
        this.name = null;
        this.emails = null;
        this.send_to_location = null;
        this.uploaded_mp3_url = '';
        this.queue_manager = null;
        this.log_id = null;
        this.skip_outside_businesshr = null;
        this.req_q_data = req_q_data;
        this.req_p_data = req_p_data;

        if (config) {
            this.queue_manager = config['queue_manager'];
            this.log_id = config['log_id'];
            this.name = config['name'];
            this.emails = config['emails'];
            this.send_to_location = config['send_to_location'];
            this.skip_outside_businesshr = config['skip_outside_businesshr'];

            if (config.uploaded_mp3_url) {
              if (config.uploaded_mp3_url.includes('uploads/call_route_config/') && ! config.uploaded_mp3_url.includes('http')) {
                this.uploaded_mp3_url = mainconfig.CP_CDN_URL.slice(0, -1) + config.uploaded_mp3_url;
              } else {
                this.uploaded_mp3_url = config.uploaded_mp3_url;
              }
            }

        }

    }

    /**
     * Requests user to record voicemail message
     *
     * @access public
     * @param  void
     * @return void
     */
    record_message() {
        var self = this;
        // Output TwiML to record the message
        // Play the mp3 uploaded by the user, if any
        if (self.uploaded_mp3_url) {
            self.queue_manager.twilio_response.play(self.uploaded_mp3_url);
        } else {
            self.queue_manager.twilio_response.say('Leave a voicemail message at the beep');
        }

        // Record will post to the below url if it receives a message
        // Otherwise it falls through to the next verb
        self.queue_manager.twilio_response.record({
            'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?handle_message',
            'maxLength': '120'
        });

        var gather = self.queue_manager.twilio_response.gather({});
        //, function() {
        gather.say('A message was not received, press any key to try again');
        //});

        self.queue_manager.is_output_set = 1;
    }

    /**
     * Sends voicemail email message
     *
     * @access public
     * @param  array $call
     * @return int
     */
    handle_message(call) {
        var self = this;

        return new Promise(function(resolve) {
            var recording_url = self.req_p_data['RecordingUrl'];
            var duration = self.req_p_data['RecordingDuration'];
            var recording_sid = self.req_p_data['RecordingSid'];

            if (recording_url) {
                // Save recording_url and duration for the call log
                // and mark the call route process to be complete

                var CallLogModel = require('../models/call-log');
                var update = {
                    'is_route_complete': 1,
                    'recording_url': recording_url,
                    'duration': duration,
                    'recording_sid': recording_sid,
                };

                new CallLogModel().update_dynamodb(_.pick(call, ['twilio_id', 'location_id', 'log_id']), update).then(async function(db_log_id) {
                    console_c.log("# db_log_id #", db_log_id);

                    // List of email addresses
                    var emails = self.emails.split(';');

                    if (self.send_to_location) {
                        const locClient = new cpapiClient.locClient(mainconfig.db.serviceTokens.readWrite);
                        const locData = await locClient.getLocation(call.location_id)
                        if (locData['email']) {
                            emails.push(locData['email']);
                        }
                    }

                    // Email the Voicemail message to the specified emails and
                    // to the location email if the "send to location email" option
                    // is enabled

                    // Hash log_id in play_recording url.
                    // CP-2485.
                    var CommonMethod = require('./common-methods').CommonMethod;
                    var common_method = new CommonMethod();

                    let callCopy = Object.assign({}, call);
                    delete callCopy.config;
                    delete callCopy.queue_manager;
                    let payload = {
                      'call': callCopy,
                      duration,
                      emails
                    };

                    await common_method.invokeLambdaFunction(payload, '/twilio/send_email_async');
                    self.queue_manager.twilio_response.say('Thank you, good bye');
                    self.queue_manager.is_output_set = 1;

                    resolve(1);
                });
            } else {
                resolve(0);
            }
        });
    }

    /**
     * Process Voicemail step
     *
     * @access public
     * @param  array $config
     * @return mixed
     */
    async process_voicemail_step(config, call_info) {

        if (config['skip_outside_businesshr']) {
            const locClient = new cpapiClient.locClient(mainconfig.db.serviceTokens.readWrite);
            const locData = await locClient.getLocation(call_info.location_id)
            return util.isLocationOpen(locData['hours_availability'], locData['timezone'], 0);
        } else {
            return true;
        }
    }

}


module.exports = {
    'VoiceMail': voiceMail
}
