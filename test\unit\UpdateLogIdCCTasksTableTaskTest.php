<?php

use Tests\Unit\AbstractUnitTest;

include_once './app/tasks/UpdateLogIdCCTasksTableTask.php';

class UpdateLogIdCCTasksTableTaskTest extends AbstractUnitTest
{
    private $dynamoCallData = [
        'twilio_id'     => 'CAe02c1a3c7700114ea84f346060dec3cd',
        'location_id'   => '462',
        'log_id'     => '123',
        'db_log_id'     => '234',
    ];

    public function testUpdateLogIdCCTasksTableTaskSuccess(): void
    {
        $updateLogIdCCTasksMock = $this->getMockedTaskObject();

        $method = self::getMethod('updateAction');
        $method->invokeArgs($updateLogIdCCTasksMock, ["--date=2023-10-10"]);

        $this->expectNotToPerformAssertions();
    }

    public function testUpdateLogIdCCTasksTableTaskInvalidDateFormat(): void
    {
        $updateLogIdCCTasksMock = $this->getMockedTaskObject();

        $method = self::getMethod('updateAction');
        $method->invokeArgs($updateLogIdCCTasksMock, ["--date=11-10-2023"]);

        $this->expectNotToPerformAssertions();
    }

    public function testUpdateLogIdCCTasksTableTaskFail(): void
    {
        $updateLogIdCCTasksMock = \Mockery::mock('UpdateLogIdCCTasksTableTask')->makePartial();
        $updateLogIdCCTasksMock->shouldAllowMockingProtectedMethods();
        
        $this->injectDB();
        
        $callMockModel = \Mockery::mock('Call');
        $callMockModel->shouldReceive('findByQuery')->andThrow(new Exception());
       
        //bind mock objects to mocked updateLogIdCCTasksMock
        $updateLogIdCCTasksMock->shouldReceive([
            'getCallModel'  => $callMockModel
        ]);

        $method = self::getMethod('updateAction');
        $this->expectException(\Exception::class);
        $method->invokeArgs($updateLogIdCCTasksMock, []);
    }

    public function testUpdateLogIdCCTasksTableTaskMainAction(): void
    {
        $updateLogIdCCTasksMock = $this->getMockedTaskObject();

        $method = self::getMethod('mainAction');
        $method->invokeArgs($updateLogIdCCTasksMock, []);

        $this->expectNotToPerformAssertions();
    }

    protected static function getMethod($name)
    {
        $class = new ReflectionClass("UpdateLogIdCCTasksTableTask");
        $method = $class->getMethod($name);
        $method->setAccessible(true);
        return $method;
    }

    protected function getMockedTaskObject()
    {
        $updateLogIdCCTasksMock = \Mockery::mock('UpdateLogIdCCTasksTableTask')->makePartial();
        $updateLogIdCCTasksMock->shouldAllowMockingProtectedMethods();

        $callMockModel = \Mockery::mock('Call');
        $callMockModel->shouldReceive([
            'findByQuery' => [$this->dynamoCallData]
        ]);

        //bind mock objects to mocked updateLogIdCCTasksMock
        $updateLogIdCCTasksMock->shouldReceive([
            'getCallModel'  => $callMockModel
        ]);

        $this->injectDB();

        return $updateLogIdCCTasksMock;
    }

    private function injectDB()
    {
        $di = \Phalcon\DI\FactoryDefault::getDefault();
        $con = $this->createPartialMock('\\Phalcon\\Db\\Adapter\\Pdo\\Mysql', [
            'getDialect',
            'query',
            'execute',
        ]);

        $di->setShared(
            'db',
            function () use ($con) {
                return $con;
            }
        );

        $di->setShared(
            'dbLegacy',
            function () use ($con) {
                return $con;
            }
        );

        $results = $this->createPartialMock('\\Phalcon\\Db\\Result\\Pdo', [
            'fetchall',
        ]);

        $results->expects($this->any())
            ->method('fetchall')
            ->will($this->returnValue([[
                'log_id' => 123
            ]]));


        $con->expects($this->any())
            ->method('query')
            ->will($this->returnValue($results));

    }
}