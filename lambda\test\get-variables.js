const config = require('../config/config');
const AUTH_TOKEN = config.db.serviceTokens.readOnly;
const locationId = 462;
const got = require('got');
const msg_config = require('./messaging-config');
const { parseSystemVariables, getSystemVarMap, getLocationInfo } = require('@callpotential/CP-Text-Messaging-Services/src/integration/twilio-helpers');

const template = msg_config.conversation_auto_reply;
const data = {
  location_info: {
    "location_id": 462,
    "location_name": "Demo Location - SL",
    "email": "<EMAIL>",
    "address1": "1825 Mulberry Drive",
    "address2": "",
    "city": "Montgomery",
    "province": "IL",
    "postal": "60538",
    "country": "US",
    "latitude": 41.605882,
    "longitude": -88.149603,
    "phone": "6303601727",
    "primary_sip_setup": "{\"sip_username\":\"<EMAIL>\",\"sip_password\":\"Mannino\"}",
    "user_id": 880,
    "active": 1,
    "api_type": 1,
    "api_credentials": "{\"sCorpCode\":\"CCTST\",\"sLocationCode\":\"Demo1\",\"sCorpUserName\":\"Demo\",\"sCorpPassword\":\"Demo\",\"sSLToken\":\"callp1421774089445\",\"Username\":\"\",\"Password\":\"\",\"Location\":\"\",\"CSUsername\":\"\",\"CSPassword\":\"\",\"CSSiteID\":\"\",\"CSOrgID\":\"\",\"CSApiUrl\":\"Sandbox\",\"CSChannel\":\"1\",\"QSUserName\":\"\",\"QSPassword\":\"\",\"QSSiteName\":\"\",\"QSApiUrl\":\"Production1\",\"DIClientID\":\"\",\"DIKey\":\"\",\"DISiteID\":\"\",\"DILocationCode\":\"\",\"SCCompany\":\"\",\"SCSiteID\":\"\",\"SCAuthToken\":\"\",\"DSUsername\":\"\",\"DSPassword\":\"\",\"DSFacilityID\":\"\",\"DSApiUrl\":\"\",\"SEAccessKey\":\"\",\"SEAccessSecret\":\"\",\"SEFacilityId\":\"\",\"SMApiUrl\":\"\",\"SMCustomerCode\":\"\",\"SMCustomerPassword\":\"\",\"SMLocationCode\":\"\"}",
    "api_permissions": "{\"note_updates\":\"1\",\"close_lead\":\"1\",\"customer_page\":\"1\",\"lead_card\":\"1\",\"lead_processing\":\"1\",\"lead_card_customers\":\"1\",\"update_lead\":\"1\",\"product_information\":\"1\",\"sl_phone_integration\":\"1\"}",
    "last_api_update": "2020-09-18 14:37:34",
    "dateCreated": "2013-02-28 21:12:29",
    "dateRemoved": "0000-00-00 00:00:00",
    "lastupdate": "2021-02-23 23:00:40",
    "delay": 120,
    "timezone": "America/Detroit",
    "use_daylight_saving": 1,
    "logo_image": "c599f16e2822da8d8de83674fdcbc567.png",
    "friendly_name": "Next Door Self Storge",
    "outbound_phone": "6303601727",
    "outbound_sip_setup": "{\"sip_username\":\"\",\"sip_password\":\"\"}",
    "is_collection_only": 0,
    "website": "www.yoursitehere.com",
    "hours_availability": "{\"office_hours\":[{\"weekday\":\"Monday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"09:00AM\",\"closed\":0},{\"weekday\":\"Tuesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Wednesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Thursday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Friday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Saturday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Sunday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0}],\"access_hours\":[{\"weekday\":\"Monday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Tuesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Wednesday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Thursday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Friday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Saturday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0},{\"weekday\":\"Sunday\",\"from_hour\":\"09:00AM\",\"to_hour\":\"06:00PM\",\"closed\":0}]}",
    "tags": "{system.LocAddress};{system.LocEmail};{system.LocCity};",
    "display_phone": "",
    "location_image1": "eadb6c6dfb9c09e2e2f67d226fde518f.jpg",
    "location_image2": "d86b0019e075285ee73032d8b812f439.jpg",
    "location_image3": "4bd35b8268564874ce742c26a27919d4.jpg",
    "batch_event_count": 0,
    "overide_tracking_number": "18156059084",
    "custom_variables": "{\"custom_variables\":[{\"name\":\"{custom.CallCtr}\",\"value\":\"https://cp-public-files.s3.amazonaws.com/westport/OrangecrestSS/OrangecrestSSSPA.mp3\"},{\"name\":\"{custom.Chethan}\",\"value\":\"https://cp-public-files.s3.amazonaws.com/westport/OrangecrestSS/OrangecrestSSENG.mp3\"},{\"name\":\"{custom.QA}\",\"value\":\"Wes\"},{\"name\":\"{custom.KnowsEverything}\",\"value\":\"Asma Knows Everything Per Chethan\"},{\"name\":\"{custom.Vijaya}\",\"value\":\"Wheres your avatar?\"},{\"name\":\"{custom.Wes}\",\"value\":\"Eats all the food in the fridge\"},{\"name\":\"{custom.Derekwoohoo}\",\"value\":\"Moving & Packing Supplies Delivered to Your Door! <a href='https://storquest.supplyside.com/'>Click Here</a> for FREE Shipping\"}]}",
    "is_api_call_allowed_threshold_1": 0,
    "api_call_frequency": 5000,
    "sip_domain_sid": "SD5457ab4857129b0a677feb974bda866d",
    "keypad_zone": 0,
    "sl_timezone": 1,
    "is_pull_reservation_cron_running": 0,
    "collection_rule_changed": 1,
    "sl_update_followup_date": 1,
    "txt_to_email": 0,
    "other_email": "<EMAIL>",
    "sl_gmt_timeoffset": "-5",
    "sitelink_pull_units_last_time_tick": "637483623128932853",
    "downgrade_reservation": 1,
    "v2_data_sync": 1,
    "unavailable_units": ""
  }
};
const varMap = {
  "system_fields_mapping": {
    "<CallerID Name>": "lead_info.name",
    "<CallerID First Name>": "lead_info.first_name",
    "<CallerID Last Name>": "lead_info.last_name",
    "<CallerID Number>": "lead_info.phone",
    "<Agent Name>": "employee_name",
    "<CallerID Email>": "lead_info.email",
    "<Ad Source>": "assoc_ad.ad_name",
    "<Date Needed>": "lead_info.date_needed",
    "<Customer Name>": "name",
    "<Name>": "name",
    "<Location Name>": "location_info.location_name",
    "<Unit Type>": "selected_units.0.unit_type_name",
    "<Unit Width>": "selected_units.0.width",
    "<Unit Length>": "selected_units.0.length",
    "<Unit Size>": "<Unit Width>x<Unit Length>",
    "<Unit Price>": "selected_units.0.adjusted_price",
    "<Special>": "selected_units.0.special_name",
    "{system.CalleridName}": "lead_info.name",
    "{system.NameFirst}": "lead_info.first_name",
    "{system.NameLast}": "lead_info.last_name",
    "{system.CustPhone}": "lead_info.phone",
    "{system.MobilePhone}": "lead_info.mobile_phone",
    "{system.AgentName}": "employee_name",
    "{system.CustEmail}": "lead_info.email",
    "{system.AdName}": "assoc_ad.ad_name",
    "{system.LeadDateNeeded}": "lead_info.date_needed",
    "{system.CustomerName}": "name",
    "{system.Name}": "name",
    "{system.LocName}": "location_info.location_name",
    "{system.UnitType}": "selected_units.0.unit_type_name",
    "{system.UnitWidth}": "selected_units.0.width",
    "{system.UnitLength}": "selected_units.0.length",
    "{system.UnitSize}": "{system.UnitWidth}x{system.UnitLength}",
    "{system.UnitPrice}": "selected_units.0.adjusted_price",
    "{system.UnitSpecial}": "selected_units.0.special_name",
    "{system.UnitNumber}": "selected_units.0.unit_name",
    "{system.LeadID}": "lead_info.lead_id",
    "{system.LocAddress}": "location_info.address1 location_info.address2",
    "{system.LocCity}": "location_info.city",
    "{system.LocDirections}": "location_info.directions",
    "{system.LocDisplayPhone}": "location_info.display_phone",
    "{system.LocEmail}": "location_info.email",
    "{system.LocHoursAccess}": "location_info.hours_availability",
    "{system.LocHoursOffice}": "location_info.hours_availability",
    "{system.LocImg1}": "location_info.location_image1",
    "{system.LocImg2}": "location_info.location_image2",
    "{system.LocImg3}": "location_info.location_image3",
    "{system.LocLogo}": "location_info.logo_image",
    "{system.LocNameFriendly}": "location_info.friendly_name",
    "{system.LocPostal}": "location_info.postal",
    "{system.LocRegion}": "location_info.province",
    "{system.LocWebsite}": "location_info.website",
    "{system.TrackNumber}": "call_name.call_name",
    "{system.SSN}": "tenant_info.ssn",
    "{system.Pager}": "tenant_info.pager"
  },
  "sms_template_variables": [
    "{system.AdName}",
    "{system.AgentName}",
    "{system.CustEmail}",
    "{system.CustPhone}",
    "{system.MobilePhone}",
    "{system.LeadDateNeeded}",
    "{system.LeadID}",
    "{system.LocAddress}",
    "{system.LocCity}",
    "{system.LocDirections}",
    "{system.LocDisplayPhone}",
    "{system.LocEmail}",
    "{system.LocHoursAccess}",
    "{system.LocHoursOffice}",
    "{system.LocName}",
    "{system.LocNameFriendly}",
    "{system.LocPostal}",
    "{system.LocRegion}",
    "{system.LocWebsite}",
    "{system.Name}",
    "{system.NameFirst}",
    "{system.NameLast}",
    "{system.TrackNumber}",
    "{system.UnitPrice}",
    "{system.UnitSize}",
    "{system.UnitSpecial}",
    "{system.UnitNumber}",
    "{system.UnitType}",
    "{system.esign}",
    "{system.PaymentLink}"
  ],
  "webhook_variables": [
    "{system.AdName}",
    "{system.AgentName}",
    "{system.CustEmail}",
    "{system.CustPhone}",
    "{system.MobilePhone}",
    "{system.LeadDateNeeded}",
    "{system.LeadID}",
    "{system.LocAddress}",
    "{system.LocCity}",
    "{system.LocDirections}",
    "{system.LocDisplayPhone}",
    "{system.LocEmail}",
    "{system.LocHoursAccess}",
    "{system.LocHoursOffice}",
    "{system.LocName}",
    "{system.LocNameFriendly}",
    "{system.LocPostal}",
    "{system.LocRegion}",
    "{system.LocWebsite}",
    "{system.Name}",
    "{system.NameFirst}",
    "{system.NameLast}",
    "{system.TrackNumber}"
  ],
  "email_template_variables": [
    "{system.AdName}",
    "{system.AgentName}",
    "{system.CustEmail}",
    "{system.CustPhone}",
    "{system.MobilePhone}",
    "{system.LeadDateNeeded}",
    "{system.LeadID}",
    "{system.LocAddress}",
    "{system.LocCity}",
    "{system.LocDirections}",
    "{system.LocDisplayPhone}",
    "{system.LocEmail}",
    "{system.LocHoursAccess}",
    "{system.LocHoursOffice}",
    "{system.LocImg1}",
    "{system.LocImg2}",
    "{system.LocImg3}",
    "{system.LocLogo}",
    "{system.LocName}",
    "{system.LocNameFriendly}",
    "{system.LocPostal}",
    "{system.LocRegion}",
    "{system.LocWebsite}",
    "{system.Name}",
    "{system.NameFirst}",
    "{system.NameLast}",
    "{system.TrackNumber}",
    "{system.UnitPrice}",
    "{system.UnitSize}",
    "{system.UnitSpecial}",
    "{system.UnitNumber}",
    "{system.UnitType}",
    "{system.esign}",
    "{system.PaymentLink}"
  ],
  "script_card_variables": [
    "{system.AdName}",
    "{system.AgentName}",
    "{system.CustEmail}",
    "{system.CustPhone}",
    "{system.MobilePhone}",
    "{system.LeadDateNeeded}",
    "{system.LeadID}",
    "{system.LocAddress}",
    "{system.LocCity}",
    "{system.LocDirections}",
    "{system.LocEmail}",
    "{system.LocHoursAccess}",
    "{system.LocHoursOffice}",
    "{system.LocImg1}",
    "{system.LocImg2}",
    "{system.LocImg3}",
    "{system.LocLogo}",
    "{system.LocName}",
    "{system.LocNameFriendly}",
    "{system.LocPostal}",
    "{system.LocRegion}",
    "{system.LocWebsite}",
    "{system.Name}",
    "{system.NameFirst}",
    "{system.NameLast}",
    "{system.TrackNumber}",
    "{system.UnitType}",
    "{system.UnitPrice}",
    "{system.UnitSize}",
    "{system.UnitSpecial}",
    "{system.UnitNumber}",
    "{system.SSN}",
    "{system.Pager}"
  ]
};

const t1 = `Thanks for contacting the {system.LocName}. A team member will reply as soon as they are available.`;
const t2 = `We have {system.UnitSize} units available at {system.LocAddress}`;
const t3 = `{system.UnitSize}`;

async function run() {

  const data = {};
  data.location_info = await getLocationInfo(locationId);

  console.log("LOCATION INFO", JSON.stringify(data.location_info, null, 2));

  const varMap = await getSystemVarMap();
  console.log(JSON.stringify(varMap, null, 2));

  console.log('BEFORE', template);
  const parsed = parseSystemVariables(template, varMap, data);
  console.log('AFTER', parsed);

}

run();

// const parsed = parseSystemVariables(t2, varMap, data);
// console.log(JSON.stringify(parsed, null, 2));
