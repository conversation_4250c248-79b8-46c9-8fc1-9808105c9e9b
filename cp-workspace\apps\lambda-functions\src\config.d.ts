export type Config = {
  debug_mode: boolean;
  current_env: string;
  set_current_env: string;

  redis_host: string;
  memcache_host: string;

  site_endpoint: string;

  port: number;
  env: string;
  site_url: string;
  env_url: string;
  env_prefix_url: string;
  call_url: string;
  es: {
    prefix: string;
    host: string;
    httpAuth: null;
    log: string | null;
  };
  s3: {
    bucket: string;
    read: unknown;
    write: {
      call_es_write_prefix: string;
    };
  };
  sqs: {
    QueueName: string;
    lambdaDLQErrors: string;
  };
  db: {
    client: string;
    connection: {
      host: string;
      user: string;
      password: string;
      database: string;
      charset: string;
    };
    serviceTokens: {
      readOnly: string;
      readWrite: string;
    };
    pool: {
      min: number;
      max: number;
      requestTimeout: number;
    };
    acquireConnectionTimeout: number;
    debug: boolean;
  };
  db_ro: {
    client: string;
    connection: {
      host: string;
      user: string;
      password: string;
      database: string;
      charset: string;
    };
    serviceTokens: {
      readOnly: string;
      readWrite: string;
    };
    pool: {
      min: number;
      max: number;
      requestTimeout: number;
    };
    acquireConnectionTimeout: number;
    debug: boolean;
  };
  redis: {
    host: string;
    port: string;
    db: number;
  };
  memcache: string;
  locations: {
    SL: string;
    CP: string;
    WSS: string;
    CS: string;
    QS: string;
    DI: string;
    DS: string;
    SC: string;
    ES: string;
  };
  dynamodb: {
    prefix: string;
    acctTable: string;
    callTable: string;
    queueTable: string;
    twilioQueueTable: string;
    endpoint: string;
  };
  twilio: {
    statDuration: string;
    agent_status_map: string;
    task_list_map: string;
    twiml_app_sid: string;
    stats_document: string;
    agent_stats_map: string;
  };
  API_MCC_URL: string;
  API_SMS_URL: string;
  API_EMAIL_URL: string;
  API_CHAT_URL: string;
  API_EVAL_URL: string;
  API_VIDEO_URL: string;
  TZ: string;
  payment_call_types: string[];
  conv_intel_api: string;
  recording_bucket: string;
  video_composition_url: string;
};