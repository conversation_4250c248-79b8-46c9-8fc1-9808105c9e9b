import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { InputPhoneGather } from './InputPhoneGather';
import { Locale } from '@cp-workspace/shared';

describe('InputPhoneGather', () => {
  let inputPhoneGather: InputPhoneGather;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InputPhoneGather],
    }).compile();

    inputPhoneGather = module.get<InputPhoneGather>(InputPhoneGather);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.InputPhoneGather,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should configure gather and prompt the customer to enter their phone number', async () => {
      const response: PayByPhoneStateHandlerResponse = await inputPhoneGather.handler(context);

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
        numDigits: 10,
        method: 'POST',
        timeout: 10,
        finishOnKey: "#"
      }, [{messageId: "pay-by-phone.ask-for-number", locale: Locale.English}]);

      expect(response.nextState).toBe(PayByPhoneState.InputPhoneValidate);
    });
  });
});
