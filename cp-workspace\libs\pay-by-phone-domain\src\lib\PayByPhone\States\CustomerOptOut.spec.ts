import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { CustomerOptOut } from './CustomerOptOut';
import { Locale } from '@cp-workspace/shared';

describe('CustomerOptOut', () => {
  let customerOptOut: CustomerOptOut;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CustomerOptOut],
    }).compile();

    customerOptOut = module.get<CustomerOptOut>(CustomerOptOut);
    customerOptOut.services = {
      accountService: {
        createCustomerExclusion: jest.fn()
      } as any
    } as any;

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.CustomerOptOut,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        From: '+**********',
        CallSid: 'ABCDEFG',
        Digits: '',
        LocationId: 123,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should create customer exclusion and disconnect the call', async () => {
      await customerOptOut.handler(context);

      expect(customerOptOut.services.accountService.createCustomerExclusion).toHaveBeenCalledWith({
        location_id: 123,
        contact_type: 'collection',
        exclusion_type: 'call',
        excluded_contact: '+**********',
        reason: 'Auto: Received Stop Request',
        request_sid: 'ABCDEFG',
        entity_type: 'customer'
      });
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({messageId: "pay-by-phone.opt-out-success", locale: Locale.English});
    });
  });
});
