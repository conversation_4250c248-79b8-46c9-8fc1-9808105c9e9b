import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { InputPhoneValidate } from './InputPhoneValidate';
import { Locale } from '@cp-workspace/shared';

describe('InputPhoneValidate', () => {
  let inputPhoneValidate: InputPhoneValidate;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InputPhoneValidate],
    }).compile();

    inputPhoneValidate = module.get<InputPhoneValidate>(InputPhoneValidate);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.InputPhoneValidate,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should prompt to re-enter phone number when no digits are entered', async () => {
      context.request.Digits = '';

      const response: PayByPhoneStateHandlerResponse = await inputPhoneValidate.handler(context);

      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({ messageId: 'pay-by-phone.must-enter-ten-digits', locale: Locale.English });
      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
    });

    it('should prompt to re-enter phone number when an invalid number is entered', async () => {
      context.request.Digits = '12345'; // Less than 10 digits

      const response: PayByPhoneStateHandlerResponse = await inputPhoneValidate.handler(context);

      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({ messageId: 'pay-by-phone.invalid-input', locale: Locale.English });
      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
    });

    it('should proceed to customer search when a valid 10-digit number is entered', async () => {
      context.request.Digits = '**********'; // Exactly 10 digits

      const response: PayByPhoneStateHandlerResponse = await inputPhoneValidate.handler(context);

      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({ messageId: 'pay-by-phone.wait-for-account-fetch', locale: Locale.English });
      expect(response.nextState).toBe(PayByPhoneState.CustomerByPhoneSearch);
      expect(context.storage.phoneNumber).toBe('**********');
    });
  });
});
