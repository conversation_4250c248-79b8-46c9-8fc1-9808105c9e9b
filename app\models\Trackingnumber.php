<?php
/**
 * Trackingnumber model
 *
 * @category Trackingnumber
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Trackingnumber model
 *
 * @category Trackingnumber
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Trackingnumber")
 */
class Trackingnumber extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * Trackingnumber Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $trackingnumber_id;

    /**
     * Location Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $location_id;

    /**
     * Record
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $record;

    /**
     * Ad Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $ad_id;

    /**
     * Call number
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $call_number;

    /**
     * User Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $user_id;

    /**
     * Active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $active;

    /**
     * Twilio phone SID
     *
     * @var string
     *
     * @Column(type="string", length=34, nullable=true)
     * @SWG\Property()
     */
    protected $twilio_phone_sid;

    /**
     * Date when created
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $dateCreated;

    /**
     * Date when removed
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $dateRemoved;

    /**
     * Type
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $type;

    /**
     * Archived by
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $archived_by;

    /**
     * Exclude number
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $exclude_number;

    /**
     * Call route config Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $call_route_config_id;

    /**
     * Callcard Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $callcard_id;

    /**
     * Webhook params
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $webhook_params;

    /**
     * Webhook URL
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $webhook_url;

    /**
     * Method to set the value of field trackingnumber_id
     *
     * @param integer $trackingnumber_id value to set
     *
     * @return $this
     */
    public function setTrackingnumberId(int $trackingnumber_id)
    {
        $this->trackingnumber_id = $trackingnumber_id;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(int $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field record
     *
     * @param integer $record value to set
     *
     * @return $this
     */
    public function setRecord(int $record)
    {
        $this->record = $record;

        return $this;
    }

    /**
     * Method to set the value of field ad_id
     *
     * @param integer $ad_id value to set
     *
     * @return $this
     */
    public function setAdId(int $ad_id)
    {
        $this->ad_id = $ad_id;

        return $this;
    }

    /**
     * Method to set the value of field call_number
     *
     * @param string $call_number value to set
     *
     * @return $this
     */
    public function setCallNumber(string $call_number)
    {
        $this->call_number = $call_number;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $user_id value to set
     *
     * @return $this
     */
    public function setUserId(int $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active value to set
     *
     * @return $this
     */
    public function setActive(int $active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field twilio_phone_sid
     *
     * @param string $twilio_phone_sid value to set
     *
     * @return $this
     */
    public function setTwilioPhoneSid(string $twilio_phone_sid)
    {
        $this->twilio_phone_sid = $twilio_phone_sid;

        return $this;
    }

    /**
     * Method to set the value of field dateCreated
     *
     * @param string $dateCreated value to set
     *
     * @return $this
     */
    public function setDateCreated(string $dateCreated)
    {
        $this->dateCreated = $dateCreated;

        return $this;
    }

    /**
     * Method to set the value of field dateRemoved
     *
     * @param string $dateRemoved value to set
     *
     * @return $this
     */
    public function setDateRemoved(string $dateRemoved)
    {
        if (!strlen($dateRemoved)) {
            $dateRemoved = null;
        }
        $this->dateRemoved = $dateRemoved;

        return $this;
    }

    /**
     * Method to set the value of field type
     *
     * @param string $type value to set
     *
     * @return $this
     */
    public function setType(string $type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Method to set the value of field archived_by
     *
     * @param integer $archived_by value to set
     *
     * @return $this
     */
    public function setArchivedBy(int $archived_by)
    {
        $this->archived_by = $archived_by;

        return $this;
    }

    /**
     * Method to set the value of field exclude_number
     *
     * @param integer $exclude_number value to set
     *
     * @return $this
     */
    public function setExcludeNumber(int $exclude_number)
    {
        $this->exclude_number = $exclude_number;

        return $this;
    }

    /**
     * Method to set the value of field call_route_config_id
     *
     * @param integer $call_route_config_id value to set
     *
     * @return $this
     */
    public function setCallRouteConfigId(int $call_route_config_id)
    {
        $this->call_route_config_id = $call_route_config_id;

        return $this;
    }

    /**
     * Method to set the value of field callcard_id
     *
     * @param integer $callcard_id value to set
     *
     * @return $this
     */
    public function setCallcardId(int $callcard_id)
    {
        $this->callcard_id = $callcard_id;

        return $this;
    }

    /**
     * Method to set the value of field webhook_params
     *
     * @param string $webhook_params value to set
     *
     * @return $this
     */
    public function setWebhookParams(string $webhook_params)
    {
        $this->webhook_params = $webhook_params;

        return $this;
    }

    /**
     * Method to set the value of field webhook_url
     *
     * @param string $webhook_url value to set
     *
     * @return $this
     */
    public function setWebhookUrl(string $webhook_url)
    {
        $this->webhook_url = $webhook_url;

        return $this;
    }

    /**
     * Returns the value of field trackingnumber_id
     *
     * @return integer
     */
    public function getTrackingnumberId(): int
    {
        return $this->trackingnumber_id;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field record
     *
     * @return integer
     */
    public function getRecord(): int
    {
        return $this->record;
    }

    /**
     * Returns the value of field ad_id
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field call_number
     *
     * @return string
     */
    public function getCallNumber(): string
    {
        return $this->call_number;
    }

    /**
     * Returns the value of field user_id
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field active
     *
     * @return integer
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field twilio_phone_sid
     *
     * @return string
     */
    public function getTwilioPhoneSid(): string
    {
        return $this->twilio_phone_sid;
    }

    /**
     * Returns the value of field dateCreated
     *
     * @return string
     */
    public function getDateCreated(): string
    {
        return $this->dateCreated;
    }

    /**
     * Returns the value of field dateRemoved
     *
     * @return string
     */
    public function getDateRemoved(): string
    {
        return $this->dateRemoved;
    }

    /**
     * Returns the value of field type
     *
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * Returns the value of field archived_by
     *
     * @return integer
     */
    public function getArchivedBy(): int
    {
        return $this->archived_by;
    }

    /**
     * Returns the value of field exclude_number
     *
     * @return integer
     */
    public function getExcludeNumber(): int
    {
        return $this->exclude_number;
    }

    /**
     * Returns the value of field call_route_config_id
     *
     * @return integer
     */
    public function getCallRouteConfigId(): int
    {
        return $this->call_route_config_id;
    }

    /**
     * Returns the value of field callcard_id
     *
     * @return integer
     */
    public function getCallcardId(): int
    {
        return $this->callcard_id;
    }

    /**
     * Returns the value of field webhook_params
     *
     * @return string
     */
    public function getWebhookParams(): string
    {
        return $this->webhook_params;
    }

    /**
     * Returns the value of field webhook_url
     *
     * @return string
     */
    public function getWebhookUrl(): string
    {
        return $this->webhook_url;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setSource("trackingnumbers");
        $this->belongsTo('ad_id', 'Ad', 'ad_id');
        $this->belongsTo('user_id', 'User', 'user_id');
        $this->belongsTo(
            'call_route_config_id',
            'CallRouteConfig',
            'config_id',
            ["alias" => "callroute"]
        );
        $this->belongsTo('callcard_id', 'Callcard', 'callcard_id');
        $this->setConnectionService('db');
        $this->jsonExcludeFields = [];
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Trackingnumber[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Trackingnumber
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
