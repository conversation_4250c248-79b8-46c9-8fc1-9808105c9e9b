const config = require("../../config/config");
const Twilio = require('twilio');
const agent_status_map = config.twilio.agent_status_map;

async function updateWorkerActivityMap(dynamoData, data) {
  let item = dynamoData.Items[0];
  let clientSync = new Twilio(item.account_sid, item.authtoken);
  var service = clientSync.sync.services(item.sync_service_sid);

  const mapData = {
    EventDescription: data.EventDescription,
    Timestamp: data.Timestamp,
    WorkerActivityName: data.WorkerActivityName,
    WorkerActivitySid: data.WorkerActivitySid,
    WorkerAttributes: data.WorkerAttributes,
    WorkerName: data.WorkerName,
    WorkerPreviousActivityName: data.WorkerPreviousActivityName,
    WorkerPreviousActivitySid: data.WorkerPreviousActivitySid,
    WorkerSid: data.WorkerSid,
    WorkerTimeInPreviousActivity: data.WorkerTimeInPreviousActivity,
  };

  await service
    .syncMaps(agent_status_map)
    .syncMapItems(data.WorkerSid)
    .update({
      data: mapData,
    })
    .catch(async function (error) {
      if (error.status == "404") {
        await service
          .syncMaps(agent_status_map)
          .syncMapItems.create({
            key: data.WorkerSid,
            data: mapData,
            ttl: 0,
          })
          .catch((error) => {
            console.error(error, new Error().stack);
          });
      } else {
        console.error(error, new Error().stack);
      }
    });
}

module.exports = {
  updateWorkerActivityMap
};