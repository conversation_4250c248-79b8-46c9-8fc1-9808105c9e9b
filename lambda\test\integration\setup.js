const AWS = require('aws-sdk');
testData = require('../testDataSet.json');
const config = require('../../config/config');
AWS.config.update({ region: 'us-west-2' });
const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
const tableName = config.dynamodb.acctTable;
const locationId = testData.twilio_account.location_id;
const mysql = require('mysql');

module.exports = async () => {
    console.info('Setting up account for test cases')

    let con = await mysql.createConnection({
      host: config.db.connection.host,
      user: config.db.connection.user,
      password: config.db.connection.password,
      database: config.db.connection.database
    });

    await con.connect(async function(err) {
      if (err) console.log(err)
      let locationValues = [locationId];
      locationValues = locationValues.concat(testData.location_values);
      const locationKeys = testData.location_keys;
      let sql = `INSERT INTO locations (${locationKeys})
                          VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                          ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                          ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                          ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                          ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`;


      await con.query(sql, locationValues, function (err, result) {
        if (err) console.log(err); else console.log("location created ",locationId);
      });

      await con.end()

    });

    // setup twilio account
    var params = {
      TableName: tableName,
      Item: testData.twilio_account
    };

    await dynamo.put(params, function(err, data) {
      if (err) {
        console.log("Twilio setup error in dynamodb", err);
      } else {
        console.log("Twilio account setup done in dynamodb");
      }
    });
}