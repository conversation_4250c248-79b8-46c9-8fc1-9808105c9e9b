export const flows = [
  {
    "startState": "LocalePrompt",
    "endState": "TransferToAgent",
    "flows": [
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Speak with manager"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Speak with manager"
        }
      ]
    ]
  },
  {
    "startState": "LocalePrompt",
    "endState": "DisconnectCall",
    "flows": [
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "CustomerOptOut",
          "when": "Opt out"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "LocaleConfirm",
          "when": "More than one language selection"
        },
        {
          "state": "CollectionsPrompt",
          "when": ""
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": "Too many incorrect attempts"
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "SayAmountDue",
          "when": "Make payment"
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "CustomerOptOut",
          "when": "Opt out"
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "LocalePrompt",
          "when": "Coming from Collections Workflow"
        },
        {
          "state": "CollectionsPrompt",
          "when": "Only one language selection"
        },
        {
          "state": "CollectionsConfirm",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": "Too many incorrect attempts"
        }
      ]
    ]
  },
  {
    "startState": "CustomerByPhoneSearch",
    "endState": "TransferToAgent",
    "flows": [
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "InputPhoneGather",
          "when": "Customer not found or no response"
        },
        {
          "state": "InputPhoneValidate",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "Phone is invalid, retries >= 1"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "TransferToAgent",
          "when": "PBP not allowed"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentFailure",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "TransferToAgent",
          "when": "No response, retries >= max"
        }
      ]
    ]
  },
  {
    "startState": "CustomerByPhoneSearch",
    "endState": "DisconnectCall",
    "flows": [
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Pay single unit"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "UnitsToPayPrompt",
          "when": "Multiple units found"
        },
        {
          "state": "UnitsToPaySelection",
          "when": ""
        },
        {
          "state": "SayTotalBalance",
          "when": "Pay all units"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "ConfirmCustomerInfo",
          "when": "Has delinquent units"
        },
        {
          "state": "SayAmountDue",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Passes verification"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "ExistingPayMethodVerify",
          "when": "Verify existing method"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Fails verification"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodSelection",
          "when": "Verify existing or add new?"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "Add new pay method"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodPrompt",
          "when": "Has saved cards"
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No pay method on-file"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ],
      [
        {
          "state": "CustomerByPhoneSearch",
          "when": "Coming from Call Route"
        },
        {
          "state": "SayTotalBalance",
          "when": "Multiple units not found"
        },
        {
          "state": "UnitsToPayPrepayMonthsPrompt",
          "when": "No delinquent units"
        },
        {
          "state": "UnitsToPayPrepayMonthsSelection",
          "when": ""
        },
        {
          "state": "UnitsToPayPrepayMonthsConfirm",
          "when": ""
        },
        {
          "state": "GetSavedCards",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardPrompt",
          "when": "No saved cards"
        },
        {
          "state": "PayMethodCreditCardValidate",
          "when": ""
        },
        {
          "state": "PayMethodCreditCardConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodExpirationPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodExpirationValidate",
          "when": ""
        },
        {
          "state": "PayMethodExpirationConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodSecurityCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodSecurityCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodSecurityCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "PayMethodPostalCodePrompt",
          "when": "Customer confirms"
        },
        {
          "state": "PayMethodPostalCodeValidate",
          "when": ""
        },
        {
          "state": "PayMethodPostalCodeConfirm",
          "when": "Is valid"
        },
        {
          "state": "FinalPayAmountPrompt",
          "when": "Customer confirms"
        },
        {
          "state": "FinalPayAmountConfirm",
          "when": ""
        },
        {
          "state": "FinalPayAmountSubmitted",
          "when": "Customer confirms"
        },
        {
          "state": "PaymentSuccessful",
          "when": ""
        },
        {
          "state": "DisconnectCall",
          "when": ""
        }
      ]
    ]
  }
];