async function updateTaskAttributesWithWorkerSid(
  twilio,
  workspaceSid,
  taskSid,
  workerSid,
  taskAttributes
) {
  const updatedTaskAttributes = { ...taskAttributes, ...{ worker_sid: workerSid } };
  await twilio.taskrouter
      .workspaces(workspaceSid)
      .tasks(taskSid)
      .update({
        attributes: JSON.stringify(updatedTaskAttributes),
      });
    return updatedTaskAttributes;
}

async function isTaskEnded (twilio, workspaceSid, taskSid) {
  const task = await twilio.taskrouter.workspaces(workspaceSid).tasks(taskSid).fetch();
  return task.assignmentStatus === 'canceled' || task.assignmentStatus === 'completed';
}

async function updateTwilioTask(workspace, taskSid, assignmentStatus) {
  let taskUpdate = isTaskAssignedOrWrapping(assignmentStatus)
  ? "completed"
  : "canceled";
return await workspace
  .tasks(taskSid)
  .update({ assignmentStatus: taskUpdate, reason: "Call completed" });
}

async function handleTaskUpdate(workspace, task) {
  let taskUpdate = "completed";
  if (!checkTaskStatus(task)) {
    taskUpdate = "canceled";
  }
  return await updateTwilioTask(workspace, task, taskUpdate);
}

async function checkCallStatusAndCancelTask(twilio, data) {
  if (
    data.TaskChannelUniqueName === "custom3" &&
    data.TaskAssignmentStatus === "pending" &&
    data.WorkerActivityName === "No-Answer"
  ) {
    const taskAttributes = JSON.parse(data.TaskAttributes);
    const call = await twilio.calls(taskAttributes.call_sid).fetch();
    if (["completed", "busy", "failed", "no-answer"].includes(call.status)) {
      return await twilio.taskrouter
        .workspaces(data.WorkspaceSid)
        .tasks(data.TaskSid)
        .update({
          assignmentStatus: "canceled",
          reason: "Call was canceled",
        });
    }
  }
}

function isTaskAssignedOrWrapping(assignmentStatus) {
  return assignmentStatus === "assigned" || assignmentStatus === "wrapping";
}

function checkTaskStatus(task) {
  return (
    task.assignmentStatus === "assigned" || task.assignmentStatus === "wrapping"
  );
}

function isAgentContactURI(contactURI) {
  return contactURI ? contactURI.toLowerCase().includes("client:agent") : false;
}

module.exports = {
  updateTaskAttributesWithWorkerSid,
  isTaskEnded,
  updateTwilioTask,
  handleTaskUpdate,
  checkCallStatusAndCancelTask,
  isAgentContactURI
};
