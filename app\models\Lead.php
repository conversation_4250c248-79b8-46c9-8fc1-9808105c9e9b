<?php
/**
 * Lead model
 *
 * @category Lead
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Phalcon\Validation;
use Phalcon\Mvc\Model\Validator\Email as EmailValidator;

/**
 * Lead model
 *
 * @category Lead
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Lead")
 */
class Lead extends CallPotential\CPCommon\RestModel
{
    /**
     * Name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=100, nullable=true)
     */
    protected $name;

    /**
     * First name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $first_name;

    /**
     * Last name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $last_name;

    /**
     * Phone number
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=20, nullable=true)
     */
    protected $phone;

    /**
     * Mobile phone
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=25, nullable=true)
     */
    protected $mobile_phone;

    /**
     * Other phone number
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=20, nullable=true)
     */
    protected $other_phone;

    /**
     * Email address
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $email;

    /**
     * Notes
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $notes;

    /**
     * Call card
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $callcard;

    /**
     * Script card
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $script_card;

    /**
     * Extra variables
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $custom_vars;

    /**
     * Follow ups
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $follow_up;

    /**
     * Source
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=100, nullable=true)
     */
    protected $source;

    /**
     * Inquiry type
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $inquiry_type;

    /**
     * Lead Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $lead_id;

    /**
     * Version
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $version;

    /**
     * Contact date
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $contact_date;

    /**
     * Location Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $location_id;

    /**
     * Followups
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $followups;

    /**
     * Parent Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $parent_id;

    /**
     * Closed
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $closed;

    /**
     * Date rented
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $daterented;

    /**
     * Unit rented
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=64, nullable=true)
     */
    protected $unitrented;

    /**
     * Last followup
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $lastfollowup;

    /**
     * Date removed
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $date_removed;

    /**
     * Active
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $active;

    /**
     * Log Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $log_id;

    /**
     * Reason
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $reason;

    /**
     * Cancel comment
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $cancel_comment;

    /**
     * Employee Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $employee_id;

    /**
     * Connection
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $connection;

    /**
     * Competitor Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $competitor_id;

    /**
     * Call result
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $call_result;

    /**
     * Date needed
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $date_needed;

    /**
     * Call result offset
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $call_result_offset;

    /**
     * Reservation Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=32, nullable=true)
     */
    protected $reservation_id;

    /**
     * Tenant Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $tenant_id;

    /**
     * Calling number
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $calling_number;

    /**
     * Marketing source
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $marketing_source;

    /**
     * Qt rental type
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $qt_rental_type;

    /**
     * Discount plan Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $discount_plan_id;

    /**
     * Followup rule Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=127, nullable=true)
     */
    protected $followup_rule_id;

    /**
     * Followup rule version
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=6, nullable=true)
     */
    protected $followup_rule_version;

    /**
     * Followup rule step
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=4, nullable=true)
     */
    protected $followup_rule_step;

    /**
     * Followup rule type
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=15, nullable=true)
     */
    protected $followup_rule_type;

    /**
     * Next followup date
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $next_followup_date;

    /**
     * Auto mode
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $auto_mode;

    /**
     * Is instant
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=4, nullable=true)
     */
    protected $is_instant;

    /**
     * Time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $time;

    /**
     * Lead source
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $lead_source;

    /**
     * Sms opt out
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $sms_opt_out;

    /**
     * Opt in date
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $opt_in_date;

    /**
     * Opt in message
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=140, nullable=true)
     */
    protected $opt_in_msg;

    /**
     * Txt consent
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $txt_consent;

    /**
     * Opt in txt
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $opt_in_txt;

    /**
     * Error
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=10, nullable=true)
     */
    protected $error;

    /**
     * Error message
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $error_message;

    /**
     * Last error date
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $last_error_date;

    /**
     * Lead uniqueid
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=30, nullable=true)
     */
    protected $lead_uniqueid;

    /**
     * PCD Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $pcd_id;

    /**
     * PCD name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $pcd_name;

    /**
     * Channel
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=10, nullable=true)
     */
    protected $channel;

    /**
     * Is assigned
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $is_assigned;

    /**
     * Comment
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $scomment;

    /**
     * Closed by matching with
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=20, nullable=true)
     */
    protected $closed_by_matching_with;

    /**
     * Created date time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $created;

    /**
     * Rental Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $rental_id;

    /**
     * Excluded on request
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $excluded_on_request;

    /**
     * Lead type
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $lead_type;

    /**
     * Ad Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $ad_id;

    /**
     * Account class
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $account_class;

    /**
     * Reference Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=20, nullable=true)
     */
    protected $ref_id;

    /**
     * Auth Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=20, nullable=true)
     */
    protected $auth_id;

    /**
     * Posted on API
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $posted_on_api;

    /**
     * Expire date
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $expire_date;

    /**
     * Last note sync
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $last_note_sync;

    /**
     * Followup datre
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $dFollowup;

    /**
     * Transferred to
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $transferred_to;

    /**
     * Transferred by
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $transferred_by;

    /**
     * Followup next skippable step
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=3, nullable=true)
     */
    protected $followup_next_skippable_step;

    /**
     * ES lead Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $es_lead_id;

    /**
     * Method to set the value of field name
     *
     * @param string $name value to set
     *
     * @return \Lead
     */
    public function setName(string $name): \Lead
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Method to set the value of field first_name
     *
     * @param string $first_name value to set
     *
     * @return \Lead
     */
    public function setFirstName(string $first_name): \Lead
    {
        $this->first_name = $first_name;

        return $this;
    }

    /**
     * Method to set the value of field last_name
     *
     * @param string $last_name value to set
     *
     * @return \Lead
     */
    public function setLastName(string $last_name): \Lead
    {
        $this->last_name = $last_name;

        return $this;
    }

    /**
     * Method to set the value of field phone
     *
     * @param string $phone value to set
     *
     * @return \Lead
     */
    public function setPhone(string $phone): \Lead
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Method to set the value of field mobile_phone
     *
     * @param string $mobile_phone value to set
     *
     * @return \Lead
     */
    public function setMobilePhone(string $mobile_phone): \Lead
    {
        $this->mobile_phone = $mobile_phone;

        return $this;
    }

    /**
     * Method to set the value of field other_phone
     *
     * @param string $other_phone value to set
     *
     * @return \Lead
     */
    public function setOtherPhone(string $other_phone): \Lead
    {
        $this->other_phone = $other_phone;

        return $this;
    }

    /**
     * Method to set the value of field email
     *
     * @param string $email value to set
     *
     * @return \Lead
     */
    public function setEmail(string $email): \Lead
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Method to set the value of field notes
     *
     * @param string $notes value to set
     *
     * @return \Lead
     */
    public function setNotes(string $notes): \Lead
    {
        $this->notes = $notes;

        return $this;
    }

    /**
     * Method to set the value of field callcard
     *
     * @param string $callcard value to set
     *
     * @return \Lead
     */
    public function setCallcard(string $callcard): \Lead
    {
        $this->callcard = $callcard;

        return $this;
    }

    /**
     * Method to set the value of field script_card
     *
     * @param string $script_card value to set
     *
     * @return \Lead
     */
    public function setScriptCard(string $script_card): \Lead
    {
        $this->script_card = $script_card;

        return $this;
    }

    /**
     * Method to set the value of field custom_vars
     *
     * @param string $custom_vars value to set
     *
     * @return \Lead
     */
    public function setCustomVars(string $custom_vars): \Lead
    {
        $this->custom_vars = $custom_vars;

        return $this;
    }

    /**
     * Method to set the value of field follow_up
     *
     * @param integer $follow_up value to set
     *
     * @return \Lead
     */
    public function setFollowUp(int $follow_up): \Lead
    {
        $this->follow_up = $follow_up;

        return $this;
    }

    /**
     * Method to set the value of field source
     *
     * @param string $source value to set
     *
     * @return \Lead
     */
    public function setSourceField(string $source): \Lead
    {
        $this->source = $source;

        return $this;
    }

    /**
     * Method to set the value of field inquiry_type
     *
     * @param integer $inquiry_type value to set
     *
     * @return \Lead
     */
    public function setInquiryType(int $inquiry_type): \Lead
    {
        $this->inquiry_type = $inquiry_type;

        return $this;
    }

    /**
     * Method to set the value of field lead_id
     *
     * @param integer $lead_id value to set
     *
     * @return \Lead
     */
    public function setLeadId(int $lead_id): \Lead
    {
        $this->lead_id = $lead_id;

        return $this;
    }

    /**
     * Method to set the value of field version
     *
     * @param integer $version value to set
     *
     * @return \Lead
     */
    public function setVersion(int $version): \Lead
    {
        $this->version = $version;

        return $this;
    }

    /**
     * Method to set the value of field contact_date
     *
     * @param string $contact_date value to set
     *
     * @return \Lead
     */
    public function setContactDate(string $contact_date): \Lead
    {
        $this->contact_date = $contact_date;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return \Lead
     */
    public function setLocationId(int $location_id): \Lead
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field followups
     *
     * @param integer $followups value to set
     *
     * @return \Lead
     */
    public function setFollowups(int $followups): \Lead
    {
        $this->followups = $followups;

        return $this;
    }

    /**
     * Method to set the value of field parent_id
     *
     * @param integer $parent_id value to set
     *
     * @return \Lead
     */
    public function setParentId(int $parent_id): \Lead
    {
        $this->parent_id = $parent_id;

        return $this;
    }

    /**
     * Method to set the value of field closed
     *
     * @param integer $closed value to set
     *
     * @return \Lead
     */
    public function setClosed(int $closed): \Lead
    {
        $this->closed = $closed;

        return $this;
    }

    /**
     * Method to set the value of field daterented
     *
     * @param string $daterented value to set
     *
     * @return \Lead
     */
    public function setDaterented(string $daterented): \Lead
    {
        $this->daterented = $daterented;

        return $this;
    }

    /**
     * Method to set the value of field unitrented
     *
     * @param string $unitrented value to set
     *
     * @return \Lead
     */
    public function setUnitrented(string $unitrented): \Lead
    {
        $this->unitrented = $unitrented;

        return $this;
    }

    /**
     * Method to set the value of field lastfollowup
     *
     * @param string $lastfollowup value to set
     *
     * @return \Lead
     */
    public function setLastfollowup(string $lastfollowup): \Lead
    {
        $this->lastfollowup = $lastfollowup;

        return $this;
    }

    /**
     * Method to set the value of field date_removed
     *
     * @param string $date_removed value to set
     *
     * @return \Lead
     */
    public function setDateRemoved(string $date_removed): \Lead
    {
        $this->date_removed = $date_removed;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active value to set
     *
     * @return \Lead
     */
    public function setActive(int $active): \Lead
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return \Lead
     */
    public function setLogId(int $log_id): \Lead
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field reason
     *
     * @param string $reason value to set
     *
     * @return \Lead
     */
    public function setReason(string $reason): \Lead
    {
        $this->reason = $reason;

        return $this;
    }

    /**
     * Method to set the value of field cancel_comment
     *
     * @param string $cancel_comment value to set
     *
     * @return \Lead
     */
    public function setCancelComment(string $cancel_comment): \Lead
    {
        $this->cancel_comment = $cancel_comment;

        return $this;
    }

    /**
     * Method to set the value of field employee_id
     *
     * @param integer $employee_id value to set
     *
     * @return \Lead
     */
    public function setEmployeeId(int $employee_id): \Lead
    {
        $this->employee_id = $employee_id;

        return $this;
    }

    /**
     * Method to set the value of field connection
     *
     * @param integer $connection value to set
     *
     * @return \Lead
     */
    public function setConnection(int $connection): \Lead
    {
        $this->connection = $connection;

        return $this;
    }

    /**
     * Method to set the value of field competitor_id
     *
     * @param integer $competitor_id value to set
     *
     * @return \Lead
     */
    public function setCompetitorId(int $competitor_id): \Lead
    {
        $this->competitor_id = $competitor_id;

        return $this;
    }

    /**
     * Method to set the value of field call_result
     *
     * @param string $call_result value to set
     *
     * @return \Lead
     */
    public function setCallResult(string $call_result): \Lead
    {
        $this->call_result = $call_result;

        return $this;
    }

    /**
     * Method to set the value of field date_needed
     *
     * @param string $date_needed value to set
     *
     * @return \Lead
     */
    public function setDateNeeded(string $date_needed): \Lead
    {
        $this->date_needed = $date_needed;

        return $this;
    }

    /**
     * Method to set the value of field call_result_offset
     *
     * @param integer $call_result_offset value to set
     *
     * @return \Lead
     */
    public function setCallResultOffset(int $call_result_offset): \Lead
    {
        $this->call_result_offset = $call_result_offset;

        return $this;
    }

    /**
     * Method to set the value of field reservation_id
     *
     * @param string $reservation_id value to set
     *
     * @return \Lead
     */
    public function setReservationId(string $reservation_id): \Lead
    {
        $this->reservation_id = $reservation_id;

        return $this;
    }

    /**
     * Method to set the value of field tenant_id
     *
     * @param integer $tenant_id value to set
     *
     * @return \Lead
     */
    public function setTenantId(int $tenant_id): \Lead
    {
        $this->tenant_id = $tenant_id;

        return $this;
    }

    /**
     * Method to set the value of field calling_number
     *
     * @param string $calling_number value to set
     *
     * @return \Lead
     */
    public function setCallingNumber(string $calling_number): \Lead
    {
        $this->calling_number = $calling_number;

        return $this;
    }

    /**
     * Method to set the value of field marketing_source
     *
     * @param integer $marketing_source value to set
     *
     * @return \Lead
     */
    public function setMarketingSource(int $marketing_source): \Lead
    {
        $this->marketing_source = $marketing_source;

        return $this;
    }

    /**
     * Method to set the value of field qt_rental_type
     *
     * @param integer $qt_rental_type value to set
     *
     * @return \Lead
     */
    public function setQtRentalType(int $qt_rental_type): \Lead
    {
        $this->qt_rental_type = $qt_rental_type;

        return $this;
    }

    /**
     * Method to set the value of field discount_plan_id
     *
     * @param integer $discount_plan_id value to set
     *
     * @return \Lead
     */
    public function setDiscountPlanId(int $discount_plan_id): \Lead
    {
        $this->discount_plan_id = $discount_plan_id;

        return $this;
    }

    /**
     * Method to set the value of field followup_rule_id
     *
     * @param string $followup_rule_id value to set
     *
     * @return \Lead
     */
    public function setFollowupRuleId(string $followup_rule_id): \Lead
    {
        $this->followup_rule_id = $followup_rule_id;

        return $this;
    }

    /**
     * Method to set the value of field followup_rule_version
     *
     * @param integer $followup_rule_version value to set
     *
     * @return \Lead
     */
    public function setFollowupRuleVersion(int $followup_rule_version): \Lead
    {
        $this->followup_rule_version = $followup_rule_version;

        return $this;
    }

    /**
     * Method to set the value of field followup_rule_step
     *
     * @param integer $followup_rule_step value to set
     *
     * @return \Lead
     */
    public function setFollowupRuleStep(int $followup_rule_step): \Lead
    {
        $this->followup_rule_step = $followup_rule_step;

        return $this;
    }

    /**
     * Method to set the value of field followup_rule_type
     *
     * @param string $followup_rule_type value to set
     *
     * @return \Lead
     */
    public function setFollowupRuleType(string $followup_rule_type): \Lead
    {
        $this->followup_rule_type = $followup_rule_type;

        return $this;
    }

    /**
     * Method to set the value of field next_followup_date
     *
     * @param string $next_followup_date value to set
     *
     * @return \Lead
     */
    public function setNextFollowupDate(string $next_followup_date): \Lead
    {
        $this->next_followup_date = $next_followup_date;

        return $this;
    }

    /**
     * Method to set the value of field auto_mode
     *
     * @param integer $auto_mode value to set
     *
     * @return \Lead
     */
    public function setAutoMode(int $auto_mode): \Lead
    {
        $this->auto_mode = $auto_mode;

        return $this;
    }

    /**
     * Method to set the value of field is_instant
     *
     * @param integer $is_instant value to set
     *
     * @return \Lead
     */
    public function setIsInstant(int $is_instant): \Lead
    {
        $this->is_instant = $is_instant;

        return $this;
    }

    /**
     * Method to set the value of field time
     *
     * @param string $time value to set
     *
     * @return \Lead
     */
    public function setTime(string $time): \Lead
    {
        $this->time = $time;

        return $this;
    }

    /**
     * Method to set the value of field lead_source
     *
     * @param integer $lead_source value to set
     *
     * @return \Lead
     */
    public function setLeadSource(int $lead_source): \Lead
    {
        $this->lead_source = $lead_source;

        return $this;
    }

    /**
     * Method to set the value of field sms_opt_out
     *
     * @param integer $sms_opt_out value to set
     *
     * @return \Lead
     */
    public function setSmsOptOut(int $sms_opt_out): \Lead
    {
        $this->sms_opt_out = $sms_opt_out;

        return $this;
    }

    /**
     * Method to set the value of field opt_in_date
     *
     * @param string $opt_in_date value to set
     *
     * @return \Lead
     */
    public function setOptInDate(string $opt_in_date): \Lead
    {
        $this->opt_in_date = $opt_in_date;

        return $this;
    }

    /**
     * Method to set the value of field opt_in_msg
     *
     * @param string $opt_in_msg value to set
     *
     * @return \Lead
     */
    public function setOptInMsg(string $opt_in_msg): \Lead
    {
        $this->opt_in_msg = $opt_in_msg;

        return $this;
    }

    /**
     * Method to set the value of field txt_consent
     *
     * @param integer $txt_consent value to set
     *
     * @return \Lead
     */
    public function setTxtConsent(int $txt_consent): \Lead
    {
        $this->txt_consent = $txt_consent;

        return $this;
    }

    /**
     * Method to set the value of field opt_in_txt
     *
     * @param integer $opt_in_txt value to set
     *
     * @return \Lead
     */
    public function setOptInTxt(int $opt_in_txt): \Lead
    {
        $this->opt_in_txt = $opt_in_txt;

        return $this;
    }

    /**
     * Method to set the value of field error
     *
     * @param string $error value to set
     *
     * @return \Lead
     */
    public function setError(string $error): \Lead
    {
        $this->error = $error;

        return $this;
    }

    /**
     * Method to set the value of field error_message
     *
     * @param string $error_message value to set
     *
     * @return \Lead
     */
    public function setErrorMessage(string $error_message): \Lead
    {
        $this->error_message = $error_message;

        return $this;
    }

    /**
     * Method to set the value of field last_error_date
     *
     * @param string $last_error_date value to set
     *
     * @return \Lead
     */
    public function setLastErrorDate(string $last_error_date): \Lead
    {
        $this->last_error_date = $last_error_date;

        return $this;
    }

    /**
     * Method to set the value of field lead_uniqueid
     *
     * @param string $lead_uniqueid value to set
     *
     * @return \Lead
     */
    public function setLeadUniqueid(string $lead_uniqueid): \Lead
    {
        $this->lead_uniqueid = $lead_uniqueid;

        return $this;
    }

    /**
     * Method to set the value of field pcd_id
     *
     * @param integer $pcd_id value to set
     *
     * @return \Lead
     */
    public function setPcdId(int $pcd_id): \Lead
    {
        $this->pcd_id = $pcd_id;

        return $this;
    }

    /**
     * Method to set the value of field pcd_name
     *
     * @param string $pcd_name value to set
     *
     * @return \Lead
     */
    public function setPcdName(string $pcd_name): \Lead
    {
        $this->pcd_name = $pcd_name;

        return $this;
    }

    /**
     * Method to set the value of field channel
     *
     * @param integer $channel value to set
     *
     * @return \Lead
     */
    public function setChannel(int $channel): \Lead
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * Method to set the value of field is_assigned
     *
     * @param integer $is_assigned value to set
     *
     * @return \Lead
     */
    public function setIsAssigned(int $is_assigned): \Lead
    {
        $this->is_assigned = $is_assigned;

        return $this;
    }

    /**
     * Method to set the value of field scomment
     *
     * @param string $scomment value to set
     *
     * @return \Lead
     */
    public function setScomment(string $scomment): \Lead
    {
        $this->scomment = $scomment;

        return $this;
    }

    /**
     * Method to set the value of field closed_by_matching_with
     *
     * @param string $closed_by_matching_with value to set
     *
     * @return \Lead
     */
    public function setClosedByMatchingWith(string $closed_by_matching_with): \Lead
    {
        $this->closed_by_matching_with = $closed_by_matching_with;

        return $this;
    }

    /**
     * Method to set the value of field created
     *
     * @param string $created value to set
     *
     * @return \Lead
     */
    public function setCreated(string $created): \Lead
    {
        $this->created = $created;

        return $this;
    }

    /**
     * Method to set the value of field rental_id
     *
     * @param integer $rental_id value to set
     *
     * @return \Lead
     */
    public function setRentalId(int $rental_id): \Lead
    {
        $this->rental_id = $rental_id;

        return $this;
    }

    /**
     * Method to set the value of field excluded_on_request
     *
     * @param integer $excluded_on_request value to set
     *
     * @return \Lead
     */
    public function setExcludedOnRequest(int $excluded_on_request): \Lead
    {
        $this->excluded_on_request = $excluded_on_request;

        return $this;
    }

    /**
     * Method to set the value of field lead_type
     *
     * @param integer $lead_type value to set
     *
     * @return \Lead
     */
    public function setLeadType(int $lead_type): \Lead
    {
        $this->lead_type = $lead_type;

        return $this;
    }

    /**
     * Method to set the value of field ad_id
     *
     * @param integer $ad_id value to set
     *
     * @return \Lead
     */
    public function setAdId(int $ad_id): \Lead
    {
        $this->ad_id = $ad_id;

        return $this;
    }

    /**
     * Method to set the value of field account_class
     *
     * @param integer $account_class value to set
     *
     * @return \Lead
     */
    public function setAccountClass(int $account_class): \Lead
    {
        $this->account_class = $account_class;

        return $this;
    }

    /**
     * Method to set the value of field ref_id
     *
     * @param string $ref_id value to set
     *
     * @return \Lead
     */
    public function setRefId(string $ref_id): \Lead
    {
        $this->ref_id = $ref_id;

        return $this;
    }

    /**
     * Method to set the value of field auth_id
     *
     * @param string $auth_id value to set
     *
     * @return \Lead
     */
    public function setAuthId(string $auth_id): \Lead
    {
        $this->auth_id = $auth_id;

        return $this;
    }

    /**
     * Method to set the value of field posted_on_api
     *
     * @param integer $posted_on_api value to set
     *
     * @return \Lead
     */
    public function setPostedOnApi(int $posted_on_api): \Lead
    {
        $this->posted_on_api = $posted_on_api;

        return $this;
    }

    /**
     * Method to set the value of field expire_date
     *
     * @param string $expire_date value to set
     *
     * @return \Lead
     */
    public function setExpireDate(string $expire_date): \Lead
    {
        $this->expire_date = $expire_date;

        return $this;
    }

    /**
     * Method to set the value of field last_note_sync
     *
     * @param string $last_note_sync value to set
     *
     * @return \Lead
     */
    public function setLastNoteSync(string $last_note_sync): \Lead
    {
        $this->last_note_sync = $last_note_sync;

        return $this;
    }

    /**
     * Method to set the value of field dFollowup
     *
     * @param string $dFollowup value to set
     *
     * @return \Lead
     */
    public function setDFollowup(string $dFollowup): \Lead
    {
        $this->dFollowup = $dFollowup;

        return $this;
    }

    /**
     * Method to set the value of field transferred_to
     *
     * @param integer $transferred_to value to set
     *
     * @return \Lead
     */
    public function setTransferredTo(int $transferred_to): \Lead
    {
        $this->transferred_to = $transferred_to;

        return $this;
    }

    /**
     * Method to set the value of field transferred_by
     *
     * @param integer $transferred_by value to set
     *
     * @return \Lead
     */
    public function setTransferredBy(int $transferred_by): \Lead
    {
        $this->transferred_by = $transferred_by;

        return $this;
    }

    /**
     * Method to set the value of field followup_next_skippable_step
     *
     * @param integer $followup_next_skippable_step value to set
     *
     * @return \Lead
     */
    public function setFollowupNextSkippableStep(int $followup_next_skippable_step): \Lead
    {
        $this->followup_next_skippable_step = $followup_next_skippable_step;

        return $this;
    }

    /**
     * Returns the value of field name
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Returns the value of field first_name
     *
     * @return string
     */
    public function getFirstName(): string
    {
        return $this->first_name;
    }

    /**
     * Returns the value of field last_name
     *
     * @return string
     */
    public function getLastName(): string
    {
        return $this->last_name;
    }

    /**
     * Returns the value of field phone
     *
     * @return string
     */
    public function getPhone(): string
    {
        return $this->phone;
    }

    /**
     * Returns the value of field mobile_phone
     *
     * @return string
     */
    public function getMobilePhone(): string
    {
        return $this->mobile_phone;
    }

    /**
     * Returns the value of field other_phone
     *
     * @return string
     */
    public function getOtherPhone(): string
    {
        return $this->other_phone;
    }

    /**
     * Returns the value of field email
     *
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * Returns the value of field notes
     *
     * @return string
     */
    public function getNotes(): string
    {
        return $this->notes;
    }

    /**
     * Returns the value of field callcard
     *
     * @return string
     */
    public function getCallcard(): string
    {
        return $this->callcard;
    }

    /**
     * Returns the value of field script_card
     *
     * @return string
     */
    public function getScriptCard(): string
    {
        return $this->script_card;
    }

    /**
     * Returns the value of field custom_vars
     *
     * @return string
     */
    public function getCustomVars(): string
    {
        return $this->custom_vars;
    }

    /**
     * Returns the value of field follow_up
     *
     * @return integer
     */
    public function getFollowUp(): int
    {
        return $this->follow_up;
    }

    /**
     * Returns the value of field source
     *
     * @return string
     */
    public function getSourceField(): string
    {
        return $this->source;
    }

    /**
     * Returns the value of field inquiry_type
     *
     * @return integer
     */
    public function getInquiryType(): int
    {
        return $this->inquiry_type;
    }

    /**
     * Returns the value of field lead_id
     *
     * @return integer
     */
    public function getLeadId(): int
    {
        return $this->lead_id;
    }

    /**
     * Returns the value of field version
     *
     * @return integer
     */
    public function getVersion(): int
    {
        return $this->version;
    }

    /**
     * Returns the value of field contact_date
     *
     * @return string
     */
    public function getContactDate(): string
    {
        return $this->contact_date;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field followups
     *
     * @return integer
     */
    public function getFollowups(): int
    {
        return $this->followups;
    }

    /**
     * Returns the value of field parent_id
     *
     * @return integer
     */
    public function getParentId(): int
    {
        return $this->parent_id;
    }

    /**
     * Returns the value of field closed
     *
     * @return integer
     */
    public function getClosed(): int
    {
        return $this->closed;
    }

    /**
     * Returns the value of field daterented
     *
     * @return string
     */
    public function getDaterented(): string
    {
        return $this->daterented;
    }

    /**
     * Returns the value of field unitrented
     *
     * @return string
     */
    public function getUnitrented(): string
    {
        return $this->unitrented;
    }

    /**
     * Returns the value of field lastfollowup
     *
     * @return string
     */
    public function getLastfollowup(): string
    {
        return $this->lastfollowup;
    }

    /**
     * Returns the value of field date_removed
     *
     * @return string
     */
    public function getDateRemoved(): string
    {
        return $this->date_removed;
    }

    /**
     * Returns the value of field active
     *
     * @return integer
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field reason
     *
     * @return string
     */
    public function getReason(): string
    {
        return $this->reason;
    }

    /**
     * Returns the value of field cancel_comment
     *
     * @return string
     */
    public function getCancelComment(): string
    {
        return $this->cancel_comment;
    }

    /**
     * Returns the value of field employee_id
     *
     * @return integer
     */
    public function getEmployeeId(): int
    {
        return $this->employee_id;
    }

    /**
     * Returns the value of field connection
     *
     * @return integer
     */
    public function getConnection(): int
    {
        return $this->connection;
    }

    /**
     * Returns the value of field competitor_id
     *
     * @return integer
     */
    public function getCompetitorId(): int
    {
        return $this->competitor_id;
    }

    /**
     * Returns the value of field call_result
     *
     * @return string
     */
    public function getCallResult(): string
    {
        return $this->call_result;
    }

    /**
     * Returns the value of field date_needed
     *
     * @return string
     */
    public function getDateNeeded(): string
    {
        return $this->date_needed;
    }

    /**
     * Returns the value of field call_result_offset
     *
     * @return integer
     */
    public function getCallResultOffset(): int
    {
        return $this->call_result_offset;
    }

    /**
     * Returns the value of field reservation_id
     *
     * @return string
     */
    public function getReservationId(): string
    {
        return $this->reservation_id;
    }

    /**
     * Returns the value of field tenant_id
     *
     * @return integer
     */
    public function getTenantId(): int
    {
        return $this->tenant_id;
    }

    /**
     * Returns the value of field calling_number
     *
     * @return string
     */
    public function getCallingNumber(): string
    {
        return $this->calling_number;
    }

    /**
     * Returns the value of field marketing_source
     *
     * @return integer
     */
    public function getMarketingSource(): int
    {
        return $this->marketing_source;
    }

    /**
     * Returns the value of field qt_rental_type
     *
     * @return integer
     */
    public function getQtRentalType(): int
    {
        return $this->qt_rental_type;
    }

    /**
     * Returns the value of field discount_plan_id
     *
     * @return integer
     */
    public function getDiscountPlanId(): int
    {
        return $this->discount_plan_id;
    }

    /**
     * Returns the value of field followup_rule_id
     *
     * @return string
     */
    public function getFollowupRuleId(): string
    {
        return $this->followup_rule_id;
    }

    /**
     * Returns the value of field followup_rule_version
     *
     * @return integer
     */
    public function getFollowupRuleVersion(): int
    {
        return $this->followup_rule_version;
    }

    /**
     * Returns the value of field followup_rule_step
     *
     * @return integer
     */
    public function getFollowupRuleStep(): int
    {
        return $this->followup_rule_step;
    }

    /**
     * Returns the value of field followup_rule_type
     *
     * @return string
     */
    public function getFollowupRuleType(): string
    {
        return $this->followup_rule_type;
    }

    /**
     * Returns the value of field next_followup_date
     *
     * @return string
     */
    public function getNextFollowupDate(): string
    {
        return $this->next_followup_date;
    }

    /**
     * Returns the value of field auto_mode
     *
     * @return integer
     */
    public function getAutoMode(): int
    {
        return $this->auto_mode;
    }

    /**
     * Returns the value of field is_instant
     *
     * @return integer
     */
    public function getIsInstant(): int
    {
        return $this->is_instant;
    }

    /**
     * Returns the value of field time
     *
     * @return string
     */
    public function getTime(): string
    {
        return $this->time;
    }

    /**
     * Returns the value of field lead_source
     *
     * @return integer
     */
    public function getLeadSource(): int
    {
        return $this->lead_source;
    }

    /**
     * Returns the value of field sms_opt_out
     *
     * @return integer
     */
    public function getSmsOptOut(): int
    {
        return $this->sms_opt_out;
    }

    /**
     * Returns the value of field opt_in_date
     *
     * @return string
     */
    public function getOptInDate(): string
    {
        return $this->opt_in_date;
    }

    /**
     * Returns the value of field opt_in_msg
     *
     * @return string
     */
    public function getOptInMsg(): string
    {
        return $this->opt_in_msg;
    }

    /**
     * Returns the value of field txt_consent
     *
     * @return integer
     */
    public function getTxtConsent(): int
    {
        return $this->txt_consent;
    }

    /**
     * Returns the value of field opt_in_txt
     *
     * @return integer
     */
    public function getOptInTxt(): int
    {
        return $this->opt_in_txt;
    }

    /**
     * Returns the value of field error
     *
     * @return string
     */
    public function getError(): string
    {
        return $this->error;
    }

    /**
     * Returns the value of field error_message
     *
     * @return string
     */
    public function getErrorMessage(): string
    {
        return $this->error_message;
    }

    /**
     * Returns the value of field last_error_date
     *
     * @return string
     */
    public function getLastErrorDate(): string
    {
        return $this->last_error_date;
    }

    /**
     * Returns the value of field lead_uniqueid
     *
     * @return string
     */
    public function getLeadUniqueid(): string
    {
        return $this->lead_uniqueid;
    }

    /**
     * Returns the value of field pcd_id
     *
     * @return integer
     */
    public function getPcdId(): int
    {
        return $this->pcd_id;
    }

    /**
     * Returns the value of field pcd_name
     *
     * @return string
     */
    public function getPcdName(): string
    {
        return $this->pcd_name;
    }

    /**
     * Returns the value of field channel
     *
     * @return integer
     */
    public function getChannel(): int
    {
        return $this->channel;
    }

    /**
     * Returns the value of field is_assigned
     *
     * @return integer
     */
    public function getIsAssigned(): int
    {
        return $this->is_assigned;
    }

    /**
     * Returns the value of field scomment
     *
     * @return string
     */
    public function getScomment(): string
    {
        return $this->scomment;
    }

    /**
     * Returns the value of field closed_by_matching_with
     *
     * @return string
     */
    public function getClosedByMatchingWith(): string
    {
        return $this->closed_by_matching_with;
    }

    /**
     * Returns the value of field created
     *
     * @return string
     */
    public function getCreated(): string
    {
        return $this->created;
    }

    /**
     * Returns the value of field rental_id
     *
     * @return integer
     */
    public function getRentalId(): int
    {
        return $this->rental_id;
    }

    /**
     * Returns the value of field excluded_on_request
     *
     * @return integer
     */
    public function getExcludedOnRequest(): int
    {
        return $this->excluded_on_request;
    }

    /**
     * Returns the value of field lead_type
     *
     * @return integer
     */
    public function getLeadType(): int
    {
        return $this->lead_type;
    }

    /**
     * Returns the value of field ad_id
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field account_class
     *
     * @return integer
     */
    public function getAccountClass(): int
    {
        return $this->account_class;
    }

    /**
     * Returns the value of field ref_id
     *
     * @return string
     */
    public function getRefId(): string
    {
        return $this->ref_id;
    }

    /**
     * Returns the value of field auth_id
     *
     * @return string
     */
    public function getAuthId(): string
    {
        return $this->auth_id;
    }

    /**
     * Returns the value of field posted_on_api
     *
     * @return integer
     */
    public function getPostedOnApi(): int
    {
        return $this->posted_on_api;
    }

    /**
     * Returns the value of field expire_date
     *
     * @return string
     */
    public function getExpireDate(): string
    {
        return $this->expire_date;
    }

    /**
     * Returns the value of field last_note_sync
     *
     * @return string
     */
    public function getLastNoteSync(): string
    {
        return $this->last_note_sync;
    }

    /**
     * Returns the value of field dFollowup
     *
     * @return string
     */
    public function getDFollowup(): string
    {
        return $this->dFollowup;
    }

    /**
     * Returns the value of field transferred_to
     *
     * @return integer
     */
    public function getTransferredTo(): int
    {
        return $this->transferred_to;
    }

    /**
     * Returns the value of field transferred_by
     *
     * @return integer
     */
    public function getTransferredBy(): int
    {
        return $this->transferred_by;
    }

    /**
     * Returns the value of field followup_next_skippable_step
     *
     * @return integer
     */
    public function getFollowupNextSkippableStep(): int
    {
        return $this->followup_next_skippable_step;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setConnectionService('dbLegacy');
        $this->setSource("leads");
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Leads[]|Leads
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Leads
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
