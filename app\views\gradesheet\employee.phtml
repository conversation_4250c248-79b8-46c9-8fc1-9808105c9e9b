<html>
    <head>
        <style type="text/css">
            .border {
                border: 1px solid black;
            }

            .border-bottom {
                border-bottom: 1px;
            }

            .border-right {
                border-right: 1px solid black;
            }

            .text-center {
                text-align: center;
            }

            .pd5 {
                padding: 5px;
            }

            .pd0 {
                padding: 0px;
            }

            .bg-grey {
                background-color: grey;
            }

            .font-size {
                font-size: 16px;
            }
        </style>
    </head>
    <body>
        <table cellpadding="5" cellspacing="0" width="100%" class="border">
            <tbody>
                <tr>
                    <td>
                        <table border="0" width="100%">
                            <tbody>
                                <tr>
                                    <td>
                                        <table border="0" width="100%">
                                            <tbody>
                                                <tr>
                                                    <td><b>Employee</b></td>
                                                    <td><?php echo $employee_name; ?></td>
                                                </tr>
                                                <tr>
                                                    <td><b>Location</b></td>
                                                    <td><?php echo $location_name; ?></td>
                                                </tr>
                                                <tr>
                                                    <td><b>Date/Time of Call</b></td>
                                                    <td><?php echo $datestamp; ?></td>
                                                </tr>
                                                <tr>
                                                    <td><b>Call of Fame</b></td>
                                                    <td><?php echo 1 === (int) $halloffame ? 'Yes' : 'No'; ?></td>
                                                </tr>
                                                </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td width="33%" valign="middle" align="center" class="font-size">
                        <div>
                            <b>Call Grade: <?php echo $grade; ?></b>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table><br/>
        <table width="100%" cellpadding="5" cellspacing="2" class="border" style="page-break-before: avoid;">
            <tr>
                <td><?php
                    $gradesheet_data = json_decode($gradesheet);
                    $summary_data = '';
                    $total_score = 0;
                    $total_points = 0;
                    $overall_notes = "";
                    if(isset($gradesheet_data[0]->overallNotes)) {
                       $overall_notes = $gradesheet_data[0]->overallNotes;
                    }
                    foreach ($gradesheet_data as $gradesheet):
                        $title = '';
                        if(isset($gradesheet->title)){
                            $title = $gradesheet->title;
                        }
                        $summary_data .= "<tr class='border-bottom'>
                            <td>
                                <b><span>{$title}</span>(<span>{$gradesheet->pointsPossible}</span>)</b>
                            </td>
                            <td>
                                <span>{$gradesheet->pointsAppointed}</span>
                            </td>
                        </tr>";
                        $total_score += $gradesheet->pointsAppointed;
                        $total_points += $gradesheet->pointsPossible;
                        ?><table width="100%">
                            <tr>
                               <td>
                                  <b><?php echo $title ?>
                                    (Possible Points = <?php echo $gradesheet->pointsPossible ?>)</b>
                                </td>
                            </tr>
                            <tr>
                                <td width="100%">
                                    <table cellpadding="3" width="100%"><?php
                                    foreach ($gradesheet->answers as $answer):
                                        $margin_left = 0;
                                        $margin_right = 18;

                                        if ($answer->pointsAppointed)
                                        {
                                            $margin_left = ($answer->pointsAppointed * 20 / $answer->points) - 2;
                                            $margin_right = 18 - $margin_left;
                                        }

                                        ?><tr rowspan="2">
                                            <td width="35%" valign="top" class="text_center">
                                                <b><?php echo $answer->question; ?></b>
                                                <?php echo $answer->pointsAppointed . "/" . $answer->points; ?>
                                            </td>
                                            <td width="5%" valign="top" align="center"><?php echo $answer->minimum; ?></td>
                                            <td width="30%" valign="top">
                                                <table class="border">
                                                    <tr>
                                                        <td>
                                                            <table>
                                                                <tr>
                                                                    <td>
                                                                        <div class="border bg-grey" style="
                                                                            margin-left: <?php echo $margin_left; ?>em;
                                                                            margin-right:<?php echo $margin_right; ?>em;">
                                                                        &nbsp;
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                            <td width="5%" valign="top" align="right">
                                                <?php echo $answer->maximum; ?>
                                            </td>
                                            <td valign="top" align="center" width="10%" >
                                                <?php echo (TRUE === $answer->notApplicable) ? 'N/A' : ''; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                        <?php 
                                            if($answer->note) { ?>
                                                <td colspan="4"><b> Notes :</b> <?php echo $answer->note; ?></td>
                                           <?php }

                                            ?>
    
                                        </tr>
                                        <?php
                                    endforeach;
                                   ?></table>
                                </td>
                            </tr>
                            <tr>
                               <td>&nbsp;</td>
                            </tr>
                        </table>
                        <?php
                    endforeach;
                ?></td>
            </tr>
        </table><br/>
        <table cellpadding="0" cellspacing="0" width="100%" class="border pd5">
            <tbody>
                <tr>
                    <td>
                        <table width="100%">
                            <tbody>
                                <tr>
                                    <td width="60%" class="border-right">
                                        <table width="100%" class="pd0">
                                            <tbody>
                                                <tr>
                                                    <td><b>Category (Possible Points)</b></td>
                                                    <td><b>Score</b></td>
                                                </tr>
                                                <?php echo $summary_data; ?>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td><b>Total Score</b></td>
                                                    <td><b><?php echo $total_score . ' / ' . $total_points; ?></b></td>
                                                </tr>
                                                <tr>
                                                    <td><b>Overall Grade</b></td>
                                                    <td><b><?php echo $grade; ?></b></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </td>
                                    <td width="40%" valign="top">
                                        <?php echo $overall_notes; ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </body>
</html>