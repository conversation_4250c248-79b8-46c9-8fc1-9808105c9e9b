import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodCreditCardPrompt extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Compose TwiML to ask for credit card input
     * 2. Compose the TwiML that will gather credit card input
     * 3. Transition to PayMethodCreditCardValidate
     */

    const { twilioResponse, storage } = context;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      timeout: 10,
      finishOnKey: '*',
    }, [{
      messageId: 'pay-by-phone.enter-cc',
      locale: storage.locale
    }]);


    return { nextState: PayByPhoneState.PayMethodCreditCardValidate };
  }
}
