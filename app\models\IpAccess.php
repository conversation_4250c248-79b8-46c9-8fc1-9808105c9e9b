<?php
/**
 * IpAccess model
 *
 * @category IpAccess
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\LoggerTrait;

/**
 * IpAccess model
 *
 * @category IpAccess
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class IpAccess extends CallPotential\CPCommon\RestModel
{
    use JsonModelTrait;

    /**
     * User ID
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     */
    protected $user_id;

    /**
     * Ip acl SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $ip_acl_sid;

    /**
     * Ip friendly name
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $ip_friendly_name;

    /**
     * Ip address
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $ip_address;

    /**
     * Ip address SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $ip_address_sid;

    /**
     * Active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     */
    protected $active;

    /**
     * Date created
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $date_created;

    /**
     * Date removed
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $date_remove;

    /**
     * Method to set the value of field id
     *
     * @param integer $id value to set
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $user_id value to set
     *
     * @return $this
     */
    public function setUserId(int $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field ip_acl_sid
     *
     * @param string $ip_acl_sid value to set
     *
     * @return $this
     */
    public function setIpAclSid(string $ip_acl_sid)
    {
        $this->ip_acl_sid = $ip_acl_sid;

        return $this;
    }

    /**
     * Method to set the value of field ip_friendly_name
     *
     * @param string $ip_friendly_name value to set
     *
     * @return $this
     */
    public function setIpFriendlyName(string $ip_friendly_name)
    {
        $this->ip_friendly_name = $ip_friendly_name;

        return $this;
    }

    /**
     * Method to set the value of field ip_address
     *
     * @param string $ip_address value to set
     *
     * @return $this
     */
    public function setIpAddress(string $ip_address)
    {
        $this->ip_address = $ip_address;

        return $this;
    }

    /**
     * Method to set the value of field ip_address_sid
     *
     * @param string $ip_address_sid value to set
     *
     * @return $this
     */
    public function setIpAddressSid(string $ip_address_sid)
    {
        $this->ip_address_sid = $ip_address_sid;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active value to set
     *
     * @return $this
     */
    public function setActive(int $active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field date_created
     *
     * @param string $date_created value to set
     *
     * @return $this
     */
    public function setDateCreated(string $date_created)
    {
        $this->date_created = $date_created;

        return $this;
    }

    /**
     * Method to set the value of field date_remove
     *
     * @param string $date_remove value to set
     *
     * @return $this
     */
    public function setDateRemove(string $date_remove)
    {
        $this->date_remove = $date_remove;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns the value of field user_id
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field ip_acl_sid
     *
     * @return string
     */
    public function getIpAclSid(): string
    {
        return $this->ip_acl_sid;
    }

    /**
     * Returns the value of field ip_friendly_name
     *
     * @return string
     */
    public function getIpFriendlyName(): string
    {
        return $this->ip_friendly_name;
    }

    /**
     * Returns the value of field ip_address
     *
     * @return string
     */
    public function getIpAddress(): string
    {
        return $this->ip_address;
    }

    /**
     * Returns the value of field ip_address_sid
     *
     * @return string
     */
    public function getIpAddressSid(): string
    {
        return $this->ip_address_sid;
    }

    /**
     * Returns the value of field active
     *
     * @return integer
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field date_created
     *
     * @return string
     */
    public function getDateCreated(): string
    {
        return $this->date_created;
    }

    /**
     * Returns the value of field date_remove
     *
     * @return string
     */
    public function getDateRemove(): string
    {
        return $this->date_remove;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setSource("ip_acl");
    }

    /**
     * Clear model object values
     *
     * @return void
     */
    public function clear()
    {
        $this->id = null;
        $this->user_id = null;
        $this->ip_acl_sid = null;
        $this->ip_friendly_name = null;
        $this->ip_address = null;
        $this->ip_address_sid = null;
        $this->active = null;
        $this->date_created = null;
        $this->date_remove = null;
        $this->properties = null;
    }
}
