export interface DataDogRequestBody {}

export enum MetricType {
    Unspecified = 0,
    Count = 1,
    Rate = 2,
    Gauge = 3
  }
  
export interface Metadata {
    origin: {
      metric_type: number;
      product: number;
      service: number;
    };
  }
  
export interface Point {
    timestamp: number;
    value: number;
  }
  
export interface Resource {
    name: string;
    type: string;
  }
  
export interface Series {
    interval?: number;
    metadata?: Metadata;
    metric: string;
    points: Point[];
    resources?: Resource[];
    source_type_name?: string;
    tags?: string[];
    type: MetricType;
    unit?: string;
  }
  
export interface SeriesApiRequestBody extends DataDogRequestBody {
    series: Series[];
  }


export type AlertType = 'error' | 'warning' | 'info' | 'success' | 'user_update' | 'recommendation' | 'snapshot';
export type Priority = 'normal' | 'low';

export interface EventApiRequestBody extends DataDogRequestBody {
  aggregation_key?: string;
  alert_type?: AlertType;
  date_happened?: number;
  device_name?: string;
  host?: string;
  priority?: Priority;
  related_event_id?: number;
  source_type_name?: string;
  tags?: string[];
  text: string;
  title: string;
}

export enum DataDogApiUrl {
    Series = 'https://api.datadoghq.com/api/v2/series',
    Logs = 'https://http-intake.logs.datadoghq.com/api/v2/logs',
    Events = 'https://api.datadoghq.com/api/v1/events'
}