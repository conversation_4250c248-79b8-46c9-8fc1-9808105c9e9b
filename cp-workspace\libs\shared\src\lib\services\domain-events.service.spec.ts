import { Test, TestingModule } from '@nestjs/testing';
import { DomainEventsService } from './domain-events.service';
import { DomainEventsIntegrationService } from '../integrations/event-bridge/domain-events.integration.service';
import {
  DomainEventsClientType,
  DomainEventsIntegrationModule,
} from '../integrations/event-bridge/domain-events.integration.module';
import { DomainEvent } from '../models/domain-event.model';

describe('DomainEventsService', () => {
  let service: DomainEventsService;
  let integrationService: DomainEventsIntegrationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        DomainEventsIntegrationModule.forRoot({
          clientType: DomainEventsClientType.IN_MEMORY,
          options: { enabled: true },
        }),
      ],
      providers: [DomainEventsService, DomainEventsIntegrationService],
    }).compile();

    service = module.get<DomainEventsService>(DomainEventsService);
    integrationService = module.get<DomainEventsIntegrationService>(
      DomainEventsIntegrationService
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('publish', () => {
    it('should publish the domain event', async () => {
      const domainEvent: any = { detail: { id: 1 } };

      jest.spyOn(integrationService, 'publish').mockResolvedValue(domainEvent);

      const result = await service.publish(domainEvent);

      expect(integrationService.publish).toHaveBeenCalledWith(domainEvent);
      expect(result).toEqual(domainEvent);
    });

    it('should merge global meta data with domain event meta data', async () => {
      const globalMeta = { flowId: '12345' };
      const domainEvent: any = { detail: { id: 1 } };

      service.globalMeta = globalMeta;

      jest.spyOn(integrationService, 'publish').mockResolvedValue(domainEvent);

      await service.publish(domainEvent);

      expect(domainEvent.detail.meta).toEqual(globalMeta);
    });
  });

  describe('isDomainEvent', () => {
    it('should return true if the event is a valid DomainEvent', () => {
      const event: DomainEvent = {
        source: 'example',
        detailType: 'example',
        detail: { id: 1 },
      };

      const result = service.isDomainEvent(event);

      expect(result).toBe(true);
    });

    it('should return false if the event is not a valid DomainEvent', () => {
      const event = { id: 1 };

      const result = service.isDomainEvent(event);

      expect(result).toBe(false);
    });
  });
});
