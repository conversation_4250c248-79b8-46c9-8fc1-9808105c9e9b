<?php

use Tests\Unit\AbstractUnitTest;

include_once './app/controllers/CallController.php';

class CallControllerTest extends AbstractUnitTest
{
    public $allowedMockMethods = [
        "validSession",
        "infoMessage",
    ];

    private $putData = [
        'call_type'     => 'inbound',
        'call_number'   => 2386546646,
        'call_name'     => 9986346343,
        'datestamp'      => '2023-12-12',
        'location_id'   => 462,
        'twilio_id'     => 'CA23kjdg57dtgdj87svb3h39n8765yfh54sft',
        'db_log_id'     => 12334,
        'reservation_payment_id' => 12344,
        'customer_payment_id' => 12344,
    ];

    protected static function getMethod($name)
    {
        $class = new ReflectionClass('CallController');
        $method = $class->getMethod($name);
        $method->setAccessible(true);
        return $method;
    }

    protected static function getPhalconResponseMethod($name)
    {
        $class = new ReflectionClass('\\Phalcon\\Http\\Response');
        $method = $class->getMethod($name);
        $method->setAccessible(true);
        return $method;
    }

    protected function getPhalconMockRequest($requestData = [], $methods = [], $locationIdString='3775')
    {
        $mockRequest = $this->createStub("\\Phalcon\\Http\\Request", $methods);
        foreach ($methods as $method) {
            $mockRequest
                ->method($method)
                ->will($this->returnValue($requestData));
        }

        $mockRequest
            ->method('get')
            ->will($this->returnCallback(function ($arg) use ($locationIdString) {
                if ('locationId' === $arg) {
                    return $locationIdString;
                }
            }));

        return $mockRequest;
    }

    protected function assertStatusCode($response, $code)
    {
        $method = self::getPhalconResponseMethod("getStatusCode");
        $result = $method->invokeArgs($response, []);
        $this->assertEquals($code, $result);
    }

    protected function assertContent($response, $statusString)
    {
        $method = self::getPhalconResponseMethod("getContent");
        $result = $method->invokeArgs($response, []);
        $this->assertEquals($statusString, json_decode($result)->status);
    }

    public function testSaveActionUnauthorized(): void
    {
        $mockMethods = [
            'validSession' => false,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $response = $callController->saveAction();

        $this->assertStatusCode($response, 401);
    }

    public function testSaveActionBadRequest(): void
    {
        $mockMethods = [
            'validSession' => true,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest([], ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $response = $callController->saveAction();

        $this->assertStatusCode($response, 400);
    }

    public function testSaveActionBadRequestForTwilioId(): void
    {
        $callController = \Mockery::mock('CallController')->makePartial();
        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController->di->set('request', $mockRequest, true);

        $callController->shouldAllowMockingProtectedMethods();
        $callController->shouldReceive([
            'validSession' => true,
            'infoMessage' => ""
        ]);
        gettype($callController->response);

        $response = $callController->saveAction();

        $this->assertStatusCode($response, 400);
    }
    
    public function testSaveActionSuccess(): void
    {
        $mockMethods = [
            'validSession' => true,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $callController->shouldReceive([
            'userHasWriteAccess' => true,
            'getItem' => $this->putData,
            'debugMessage' => NULL,
            'auditMessage' => "",
            'errorMessage' => "",
            'preProcessPutData' => $this->putData,
            'updateItem'    => $this->putData,
            'validateSave' => []
        ]);
       
        $response = $callController->saveAction();

        $this->assertStatusCode($response, 200);
    }

    public function testSaveActionWriteAccessDenied(): void
    {
        $mockMethods = [
            'validSession' => true,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $callController->shouldReceive([
            'userHasWriteAccess' => false,
            'getItem' => $this->putData,
        ]);
       
        $response = $callController->saveAction();

        $this->assertStatusCode($response, 401);
    }

    public function testSaveActionValidationError(): void
    {
        $mockMethods = [
            'validSession' => true,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $callController->shouldReceive([
            'userHasWriteAccess' => true,
            'getItem' => $this->putData,
            'debugMessage' => NULL,
            'auditMessage' => "",
            'errorMessage' => "",
            'preProcessPutData' => $this->putData,
            'updateItem'    => $this->putData,
            'validateSave' => ['error']
        ]);
       
        $response = $callController->saveAction();

        $this->assertStatusCode($response, 400);
    }

    public function testSaveActionException(): void
    {
        $mockMethods = [
            'validSession' => true,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $callController->shouldReceive('updateItem')->andThrow(new Exception());

        $callController->shouldReceive([
            'userHasWriteAccess' => true,
            'getItem' => $this->putData,
            'debugMessage' => NULL,
            'auditMessage' => "",
            'errorMessage' => "",
            'preProcessPutData' => $this->putData,
            'validateSave' => []
        ]);
       
        $response = $callController->saveAction();

        $this->assertStatusCode($response, 500);
    }

    public function testSaveAction404NotFound(): void
    {
        $mockMethods = [
            'validSession' => true,
            'infoMessage' => null,
        ];

        $mockRequest = $this->getPhalconMockRequest($this->putData, ['getJsonRawBody']);

        $callController = $this->getMockedControllerObject($mockRequest,$mockMethods);

        $callController->shouldReceive('updateItem')->andThrow(new Exception());

        $callController->shouldReceive([
            'userHasWriteAccess' => true,
            'getItem' => [],
            'debugMessage' => NULL,
            'auditMessage' => "",
            'errorMessage' => "",
            'preProcessPutData' => $this->putData,
            'validateSave' => []
        ]);
       
        $response = $callController->saveAction();

        $this->assertStatusCode($response, 404);
    }
    
    protected function getMockedControllerObject($mockRequest, $mockMethods = [])
    {
        $callController = \Mockery::mock('CallController')->makePartial();
        $callController->di->set('request', $mockRequest, true);
        $callController->shouldAllowMockingProtectedMethods();
        gettype($callController->response);
        $dispatcher = \Mockery::mock('dispatcher')->makePartial();

        $dispatcher->shouldReceive([
            'getParam' => 'CA12344543hgdfgt5erfs45fdgetr34'
        ])->with('twilio_id');

        $dispatcher->shouldReceive([
            'getParam' => 462
        ])->with('location_id');

        $dispatcher->shouldReceive([
            'getParam' => 123
        ])->with('id');

        $dispatcher->shouldReceive([
            'getControllerName' => 'CallController'
        ]);

        $dispatcher->shouldReceive([
            'getParam' => 'test'
        ])->with('relationship');
       
        $dispatcher->shouldReceive([
            'getParam' => 'test'
        ])->with('related_item');
       
        $callController->dispatcher = $dispatcher;
        
        $callController->initialize();

        foreach ($mockMethods as $method => $value) {
            if (!in_array($method, $this->allowedMockMethods)) {
                continue;
            }

            $callController->allows($method)->andReturns($value);
        }

        return $callController;
    }
}