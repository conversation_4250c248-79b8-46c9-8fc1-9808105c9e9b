var PORT = 3001;
var current_env = 'qa-1927';

module.exports = {
  'port': PORT,
  'env': current_env,
  'site_url': 'https://qa-1927.callpotential.com/', // endpoint
  'call_url': 'https://call-api.callpotential.com/qa-1927/v1/',
  'env_url': '/qa-1927/v1', // current environment
  'db': {
    client: 'mysql',
    connection: {
      host: 'rds-cp.callpotential.com',
      user: 'qa',
      password: '***',
      database: 'qa_1927',
      charset: 'utf8',
    },
    serviceTokens: {
      readOnly: '******',
      readWrite: '******',
    },
    pool: {
      min: 0,
      max: 1,
      requestTimeout: 250000,
    },
    acquireConnectionTimeout: 300000, // 5 minute
    debug: false,
  },
  db_ro: { // read only
    client: 'mysql',
    connection: {
      host: 'rds-cp.callpotential.com',
      user: 'qa',
      password: '***',
      database: 'qa_1927',
      charset: 'utf8',
    },
    serviceTokens: {
      readOnly: '******',
      readWrite: '******',
    },
    pool: {
      min: 0,
      max: 1,
      requestTimeout: 250000,
    },
    acquireConnectionTimeout: 300000, // 5 minute
    debug: false,
  },
  env: 'qa-1927',
  dynamodb: {
    outstandingTable: ('' === 'qa-1927') ? 'outstanding_quicklook' : 'qa-1927-outstanding_quicklook',
    acctTable: ('' === 'qa-1927') ? 'twilio_accounts' : 'qa-1927-twilio_accounts',
  },
  twilio: {
    twillio_quicklook_stats_map: 'twillio_quicklook_stats',
  },
  'CP_CDN_URL': 'https://cp-qa-1927-cdn.s3.amazonaws.com/',
  'ES_URL': 'https://*****:******@4f9d228b.qb0x.com:30780',
  'API_CORE_URL': 'https://qa-1927-core.callpotential.com',
  'API_LOC_URL': 'https//qa-1927-loc.callpotential.com',
  'API_ACCT_URL': 'https://qa-1927-acct.callpotential.com',
  'API_INT_URL': 'https://qa-1927-int.callpotential.com',
  'API_SMS_URL': 'https://qa-1927-sms.callpotential.com',
  'API_CALL_URL': 'https://qa-1927-call.callpotential.com',
  'TZ': 'America/Chicago'
};
