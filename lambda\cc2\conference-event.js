const AWS = require('aws-sdk');
AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });
const config = require('./config');
const ConferenceHelper = require('./shared/conference-helper');
const EventContextHelper = require('./shared/event-context-helper');

async function handler(event, context) {
  if (event.source === 'aws.events') return context.done(); // keep warm!

  const eventContext = await EventContextHelper.prepareEventContext(event);

  await ConferenceHelper.handleRecording(eventContext, config);

  return processEvent(eventContext);
}

async function processEvent(eventContext) {
  const { statusCallbackEvent} = eventContext;
  
  eventContext.statusCallbackEvent = statusCallbackEvent;

  if (ConferenceHelper.eventHandlers[statusCallbackEvent]) {
    await ConferenceHelper.eventHandlers[statusCallbackEvent](eventContext);
  }

  return EventContextHelper.response();
}

module.exports = {
  handler,
};
