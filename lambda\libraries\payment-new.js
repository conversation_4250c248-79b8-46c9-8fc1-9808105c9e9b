"use strict";
const config = require('../config/config');
const Twilio = require('twilio');
const cpapiClient = require('./cpapi-client');
const redis = require('redis');
const redisTtl = 1800;

var payment = class Payment {

  static async build (callInfo, currentStep, querystringData, postData) {

    let paymentObj = new payment();

    // console.log('pbp_log', 'querystringData', querystringData);

    paymentObj.language = currentStep.language.selected ? currentStep.language.selected : 'en';
    paymentObj.greetingMp3 = '';
    paymentObj.queueManager = callInfo.queue_manager;
    paymentObj.locationId = callInfo.location_id;
    paymentObj.accountId = callInfo.account_id;
    paymentObj.logId = callInfo.log_id;
    paymentObj.taskChannel = 'payment';
    paymentObj.twilioConfig = {
      'accountSid': callInfo.sid,
      'accountToken': callInfo.authtoken,
      'workspaceSid': callInfo.workspace_sid,
      'paymentWorkflow': callInfo.payment_workflow
    };

    paymentObj.digits = postData['Digits'];
    paymentObj.CallSid = postData.CallSid;
    paymentObj.From = postData.From;

    paymentObj.twilio = new Twilio(paymentObj.twilioConfig.accountSid, paymentObj.twilioConfig.accountToken);
    paymentObj.workspace = paymentObj.twilio.taskrouter.workspaces(paymentObj.twilioConfig.workspaceSid);
    paymentObj.pbpData = await paymentObj.checkPbpDataExist(postData.CallSid, postData.From);

    paymentObj.payState = paymentObj.pbpData.payState;
    paymentObj.paySubState = paymentObj.pbpData.paySubState;

    if (typeof paymentObj.pbpData.unitArray !== 'undefined') {
      if (paymentObj.pbpData.unitSelected == 1) {
        paymentObj.units_found = paymentObj.pbpData.unitArray[2];
      } else {
        paymentObj.units_found = paymentObj.pbpData.unitArray[paymentObj.pbpData.unitSelected];
      }
    }

    if (currentStep.mp3_file.value) {
      if (currentStep.mp3_file.value.includes('uploads/call_route_config/') && ! currentStep.mp3_file.value.includes('http')) {
        paymentObj.greetingMp3 = config.CP_CDN_URL.slice(0, -1) + currentStep.mp3_file.value;
      } else {
        paymentObj.greetingMp3 = currentStep.mp3_file.value;
      }
    }

    if (paymentObj.pbpData.greeting == 0) {
      paymentObj.playGreeting = true;
      await paymentObj.updatePbpData({'greeting' : 1});
    }

    return paymentObj;
  }

  static async getPbpDataFromRedis(callSid) {
    const redisClient = redis.createClient({ 'url': `redis://${config.redis.host}` });
    try {
      await redisClient.connect();
      const pbpData = await redisClient.get(`payment_task_${callSid}`);
      return pbpData;
    } finally {
      await redisClient.disconnect();
    }
  }  

  static async hasExistingPbpSession(callSid) {
    try {
      const pbpData = await Payment.getPbpDataFromRedis(callSid);
      return pbpData && Object.keys(pbpData).length !== 0;
    } catch (e) {
      console.error('ERROR while checking existing PBP session', e);
      return false;
    }
  }

  async checkPbpDataExist(callSid, fromNo) {
    try {
      const pbpData = await Payment.getPbpDataFromRedis(callSid);
      if (pbpData && Object.keys(pbpData).length !== 0) {
        return JSON.parse(pbpData);
      } else {
        return await this.createPbpData(callSid, fromNo);
      }
    } catch (e) {
      console.error('ERROR while checkPbpDataExist', e);
    }
  }

  async createPbpData(callSid, fromNo) {
    const pbpData = {
      'callSid'     : callSid,
      'fromNo'      : fromNo,
      'locationId'  : this.locationId,
      'accountId'   : this.accountId,
      'logId'       : this.logId,
      'payState'    : 'customerSearch',
      'paySubState' : 'numberSearch',
      'greeting'    : 0,
      'unitSelected': 2,
      'invalidCard' : false,
      'startTime'   : Date.now(),
      'locale'      : this.language,
    }

    try {
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();

      let call_task_key = `payment_task_${callSid}`;

      await redisClient.set(call_task_key, JSON.stringify(pbpData), {EX: redisTtl});
      await redisClient.disconnect();
      return pbpData;
    } catch (e) {
      console.error('ERROR while createPbpData', e);
    }
  }

  async updatePbpData(updateData) {
    try {
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();

      let call_task_key = `payment_task_${this.CallSid}`;
      let pbpData = await redisClient.get(call_task_key);

      if (pbpData && Object.keys(pbpData).length !== 0) {
        pbpData = JSON.parse(pbpData);
      }

      await redisClient.set(call_task_key,
        JSON.stringify({ ...pbpData, ...updateData }),
        {EX: redisTtl}
        );
      await redisClient.disconnect();
    } catch (e) {
      console.error('Error in updatePbpData', e, new Error().stack);
    }
  }

  async saveCCData(paySubState, digits) {

    const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
    let encodeData = await coreClient
      .postData(`encodetoken`, {
        'type': 'encode',
        'terms_list': [digits]
      })
      .catch(error => console.log(error, new Error().stack));

    switch (paySubState) {
      case 'repeatCC':
        await this.updatePbpData({'repeatCC' : encodeData[0]});
        break;
      case 'repeatExpiry':
        await this.updatePbpData({'repeatExpiry' : encodeData[0]});
        break;
      case 'repeatCVC':
        await this.updatePbpData({'repeatCVC' : encodeData[0]});
        break;
      case 'repeatZip':
        await this.updatePbpData({'repeatZip' : encodeData[0]});
        break;
    }
  }

  async verifySavedCard(lastFourDigit) {
    if (this.units_found.savedCards.savedCardNumber == lastFourDigit) {
      let savedCardAttrbs = {
        'savedCard': true,
        'savedCardId': this.units_found.savedCards.savedCardId,
        'savedCardNumber': this.units_found.savedCards.savedCardNumber
      }

      await this.updatePbpData(savedCardAttrbs);

      return true;
    } else if (this.pbpData.savedCardNumber == lastFourDigit) {
      return true;
    }

    return false;
  }

  async generatePaymentHash(tenantId, ledgerId) {

    try {
      // Check if payment hash is already generated for given ledger
      if (this.pbpData.paymentHashLedgerId && this.pbpData.paymentHashLedgerId === ledgerId) {
        return true;
      }

      const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
      const paymentHashResponse = await intClient.postData('payment_hash', {
        customer_id : tenantId,
        location_id : this.locationId,
        ledger_id   : ledgerId,
        source      : "call"
      });

      const paymentHashGet = await intClient.getData(`payment_hash/${paymentHashResponse.hash}`);

      await this.updatePbpData({
        'paymentHash': paymentHashResponse.hash,
        'paymentHashLedgerId': ledgerId,
        'paymentHashGet': paymentHashGet,
      });

      return true;
    } catch (e) {
      console.error('Error generating payment hash: ', e, new Error().stack);
      return false;
    }
  }

  async executeAsync() {
    try {
      var CommonMethod = require('./common-methods').CommonMethod;
      var common_method = new CommonMethod();

      await common_method.invokeLambdaFunction(
        {},
        `/pay_by_phone/prepare_pbp_data/${this.twilioConfig.accountSid}/${this.CallSid}`
        );
    } catch (e) {
      console.error(e, new Error().stack);
    }
  }
}

module.exports = {
  'Payment': payment
}
