<?php
/**
 * CallCenterTask model
 *
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: cwalker
 * Date: 10/26/17
 * Time: 10:29 AM
 *
 * @category CallCenterTask
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Models\S3DataTrait;
use CallPotential\CPCommon\Models\ElasticSearchModel;
use ONGR\ElasticsearchDSL\Search;
use \ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use \ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;

/**
 * CallCenterTask model
 *
 * @category CallCenterTask
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="CallCenterTask")
 */
class CallCenterTask extends \Phalcon\Di\Injectable
{
    use ElasticSearchModel, S3DataTrait, LoggerTrait, JsonModelTrait;

    /**
     * Primary key Id
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=256, nullable=true)
     */
    protected $id;

    /**
     * Log Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $log_id;

    /**
     * Queue Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $queue_id;

    /**
     * Agent Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $agent_id;

    /**
     * Task SID
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $task_sid;

    /**
     * Account Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $account_id;

    /**
     * Config step uid
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $config_step_uid;

    /**
     * Reservation created date time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $reservation_created;

    /**
     * Reservation accepted date time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50 nullable=true)
     */
    protected $reservation_accepted;

    /**
     * Reservation rejected date time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $reservation_rejected;

    /**
     * Reservation timeout
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=50, nullable=true)
     */
    protected $reservation_timeout;

    /**
     * Outcome
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $outcome;

    /**
     * Retry attempt
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $retry_attempt;

    /**
     * Is rolled over
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $is_rolled_over;

    /**
     * Is abandoned
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $is_abandoned;

    /**
     * Is task
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $is_task;

    /**
     * Is task complete
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $is_task_complete;

    /**
     * Employee name
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $employee_name;

    /**
     * Queue time
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="boolean")
     */
    protected $queue_time;

    /**
     * Properties
     *
     * @var string
     */
    protected $properties;

    /**
     * Method to set the value of field id
     *
     * @param integer $id value to set
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return $this
     */
    public function setLogId(int $log_id)
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field queue_id
     *
     * @param integer $queue_id value to set
     *
     * @return $this
     */
    public function setQueueId(int $queue_id)
    {
        $this->queue_id = $queue_id;

        return $this;
    }

    /**
     * Method to set the value of field agent_id
     *
     * @param integer $agent_id value to set
     *
     * @return $this
     */
    public function setAgentId(int $agent_id)
    {
        $this->agent_id = $agent_id;

        return $this;
    }

    /**
     * Method to set the value of field task_sid
     *
     * @param string $task_sid value to set
     *
     * @return $this
     */
    public function setTaskSid(string $task_sid)
    {
        $this->task_sid = $task_sid;

        return $this;
    }

    /**
     * Method to set the value of field account_id
     *
     * @param integer $account_id value to set
     *
     * @return $this
     */
    public function setAccountId(int $account_id)
    {
        $this->account_id = $account_id;

        return $this;
    }

    /**
     * Method to set the value of field config_step_uid
     *
     * @param string $config_step_uid value to set
     *
     * @return $this
     */
    public function setConfigStepUid(string $config_step_uid)
    {
        $this->config_step_uid = $config_step_uid;

        return $this;
    }

    /**
     * Method to set the value of field reservation_created
     *
     * @param string $reservation_created value to set
     *
     * @return $this
     */
    public function setReservationCreated(string $reservation_created)
    {
        $this->reservation_created = $reservation_created;

        return $this;
    }

    /**
     * Method to set the value of field reservation_accepted
     *
     * @param string $reservation_accepted value to set
     *
     * @return $this
     */
    public function setReservationAccepted(string $reservation_accepted)
    {
        $this->reservation_accepted = $reservation_accepted;

        return $this;
    }

    /**
     * Method to set the value of field reservation_rejected
     *
     * @param string $reservation_rejected value to set
     *
     * @return $this
     */
    public function setReservationRejected(string $reservation_rejected)
    {
        $this->reservation_rejected = $reservation_rejected;

        return $this;
    }

    /**
     * Method to set the value of field reservation_timeout
     *
     * @param string $reservation_timeout value to set
     *
     * @return $this
     */
    public function setReservationTimeout(string $reservation_timeout)
    {
        $this->reservation_timeout = $reservation_timeout;

        return $this;
    }

    /**
     * Method to set the value of field outcome
     *
     * @param integer $outcome value to set
     *
     * @return $this
     */
    public function setOutcome(int $outcome)
    {
        $this->outcome = $outcome;

        return $this;
    }

    /**
     * Method to set the value of field retry_attempt
     *
     * @param integer $retry_attempt value to set
     *
     * @return $this
     */
    public function setRetryAttempt(int $retry_attempt)
    {
        $this->retry_attempt = $retry_attempt;

        return $this;
    }

    /**
     * Method to set the value of field is_rolled_over
     *
     * @param integer $is_rolled_over value to set
     *
     * @return $this
     */
    public function setIsRolledOver(int $is_rolled_over)
    {
        $this->is_rolled_over = $is_rolled_over;

        return $this;
    }

    /**
     * Method to set the value of field is_abandoned
     *
     * @param integer $is_abandoned value to set
     *
     * @return $this
     */
    public function setIsAbandoned(int $is_abandoned)
    {
        $this->is_abandoned = $is_abandoned;

        return $this;
    }

    /**
     * Method to set the value of field is_task
     *
     * @param integer $is_task value to set
     *
     * @return $this
     */
    public function setIsTask(int $is_task)
    {
        $this->is_task = $is_task;

        return $this;
    }

    /**
     * Method to set the value of field is_task_complete
     *
     * @param integer $is_task_complete value to set
     *
     * @return $this
     */
    public function setIsTaskComplete(int $is_task_complete)
    {
        $this->is_task_complete = $is_task_complete;

        return $this;
    }

    /**
     * Method to set the value of field employee_name
     *
     * @param string $employee_name value to set
     *
     * @return $this
     */
    public function setEmployeeName(string $employee_name)
    {
        $this->employee_name = $employee_name;

        return $this;
    }

    /**
     * Method to set the value of field queue_time
     *
     * @param integer $queue_time value to set
     *
     * @return $this
     */
    public function setQueueTime(int $queue_time)
    {
        $this->queue_time = $queue_time;

        return $this;
    }

    /**
     * Method to set the value of field properties
     *
     * @param string $properties value to set
     *
     * @return $this
     */
    public function setProperties(string $properties)
    {
        $this->properties = $properties;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field queue_id
     *
     * @return integer
     */
    public function getQueueId(): int
    {
        return $this->queue_id;
    }

    /**
     * Returns the value of field agent_id
     *
     * @return integer
     */
    public function getAgentId(): int
    {
        return $this->agent_id;
    }

    /**
     * Returns the value of field task_sid
     *
     * @return string
     */
    public function getTaskSid(): string
    {
        return $this->task_sid;
    }

    /**
     * Returns the value of field account_id
     *
     * @return integer
     */
    public function getAccountId(): int
    {
        return $this->account_id;
    }

    /**
     * Returns the value of field config_step_uid
     *
     * @return string
     */
    public function getConfigStepUid(): string
    {
        return $this->config_step_uid;
    }

    /**
     * Returns the value of field reservation_created
     *
     * @return string
     */
    public function getReservationCreated(): string
    {
        return $this->reservation_created;
    }

    /**
     * Returns the value of field reservation_accepted
     *
     * @return string
     */
    public function getReservationAccepted(): string
    {
        return $this->reservation_accepted;
    }

    /**
     * Returns the value of field reservation_rejected
     *
     * @return string
     */
    public function getReservationRejected(): string
    {
        return $this->reservation_rejected;
    }

    /**
     * Returns the value of field reservation_timeout
     *
     * @return string
     */
    public function getReservationTimeout(): string
    {
        return $this->reservation_timeout;
    }

    /**
     * Returns the value of field outcome
     *
     * @return integer
     */
    public function getOutcome(): int
    {
        return $this->outcome;
    }

    /**
     * Returns the value of field retry_attempt
     *
     * @return integer
     */
    public function getRetryAttempt(): int
    {
        return $this->retry_attempt;
    }

    /**
     * Returns the value of field is_rolled_over
     *
     * @return integer
     */
    public function getIsRolledOver(): int
    {
        return $this->is_rolled_over;
    }

    /**
     * Returns the value of field is_abandoned
     *
     * @return integer
     */
    public function getIsAbandoned(): int
    {
        return $this->is_abandoned;
    }

    /**
     * Returns the value of field is_task
     *
     * @return integer
     */
    public function getIsTask(): int
    {
        return $this->is_task;
    }

    /**
     * Returns the value of field is_task_complete
     *
     * @return integer
     */
    public function getIsTaskComplete(): int
    {
        return $this->is_task_complete;
    }

    /**
     * Returns the value of field employee_name
     *
     * @return string
     */
    public function getEmployeeName(): string
    {
        return $this->employee_name;
    }

    /**
     * Returns the value of field queue_time
     *
     * @return integer
     */
    public function getQueueTime(): int
    {
        return $this->queue_time;
    }

    /**
     * Returns the value of field properties
     *
     * @return string
     */
    public function getProperties(): string
    {
        return $this->properties;
    }

    /**
     * Constructor to initialize data
     */
    public function __construct()
    {
        $this->index = 'call-log';
        $this->type = "call-events";
        $this->s3DataType = 'call-events';
    }

    /**
     * Clear model object values
     *
     * @return void
     */
    public function clear()
    {
        $this->log_id = null;
        $this->id = null;
        $this->properties = null;
    }
}
