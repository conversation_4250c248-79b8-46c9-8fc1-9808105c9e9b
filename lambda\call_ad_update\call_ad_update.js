const config = require('./config');
const AWS = require('aws-sdk');
const Twilio = require('twilio');
const redis = require('redis');
const cpapiClient = require('./cpapi_client');

AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });

exports.handler = async (event, context, callback) => {
  let handles = process._getActiveHandles();

  /* Check auth token exists and valid */
  if ('Authorization' in event.headers && event.headers.Authorization === config.db.serviceTokens.readWrite) {
    const params = JSON.parse(event.body);

    /* Fetch location details */
    const locDetail = await getLocation(params.location_id);
    /* Fetch Ad details */
    const adDetail = await getAdDetail(params.ad_id);
    if (locDetail && locDetail.user_id) {
      /* Fetch twilio account details from dynamodb */
      let twilioAccount = await getAccountDetail(locDetail.user_id);
      if (twilioAccount && twilioAccount.authtoken && twilioAccount.account_sid && twilioAccount.workspace_sid) {
        /* Fetch twilio call */
        let call = await getAccountCall(params, twilioAccount);
        if (call && call.sid) {

          /* Update dynamodb call detail */
          await updateCall(params.location_id, call.sid, {'ad_id': adDetail.ad_id});

          /* Fetch twilio task */
          let task = await getAccountTask(params, twilioAccount, call.sid)

          if (!task || !task.attributes) {
            // wait 2 seconds before trying again
            await new Promise(resolve => setTimeout(resolve, 2000));
            task = await getAccountTask(params, twilioAccount, call.sid)
          }

          if (task && task.attributes) {
            /* Update twilio task */
            await updateTask(task, {'ad_name': adDetail.ad_name});
          }

          return response(200, "ok", call.sid);
        }
      }
    }

    return response(404, "call not found");
  } else {
    return response(400, "Authorization required!");
  }
}

/* Retrieve location details */
async function getLocation(locationId) {
  const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);

  return (locationId)
    ? locClient.getData(`locationconfiguration/${locationId}?formatFields=false`).catch(e => console.error('getLocation', e))
    : Promise.resolve({});
}

/* Retrieve ad details */
async function getAdDetail(adId) {
  const acctClient = new cpapiClient.acctClient(config.db.serviceTokens.readWrite);

  return (adId)
    ? acctClient.getData(`ad/${adId}`).catch(e => console.error('getAdDetail', e))
    : Promise.resolve({});
}

/* Retrieve twilio credentials from dynamodb */
async function getAccountDetail(user_id) {
  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  const params = {
    TableName: config.dynamodb.acctTable,
    KeyConditionExpression: 'id = :id',
    ExpressionAttributeValues: {
      ':id': user_id
    }
  };

  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

/* Retrieve twilio account call */
async function getAccountCall(params, twilioAccount) {
  const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
  const reqparams = {
    to: params.to_number,
    from: params.from_number,
    limit: 1
  };

  try {
    const calls = await twilio.calls.list(reqparams);
    if (calls.length === 0) return {};

    return calls[0];
  } catch (e) {
    console.error('Error querying account calls:', e);
    return {};
  }
}

/* Retrieve twilio account task */
async function getAccountTask(params, twilioAccount, callSid) {
  const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
  const workspace = twilio.taskrouter.workspaces(twilioAccount.workspace_sid);

  try {
    const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
    await redisClient.connect();

    let taskSid = await redisClient.get(`g5_${callSid}`);
    await redisClient.disconnect();

    if (!taskSid) {
      return {};
    } else {
      let task = await workspace.tasks(taskSid).fetch();
      return task;
    }
  } catch (e) {
    console.error('Error querying account task by callsid:', e);
    return {};
  }
}

/* Update twilio task attributes details */
async function updateTask(task, updateData)
{
  let taskAttrb = JSON.parse(task.attributes);
  await task.update({
    attributes: JSON.stringify({ ...taskAttrb, ...updateData })
  })
  .catch(error => console.log(error, new Error().stack, updateData));
}

/* Retrieve dynamodb call details */
async function getCallDetail(locationId, callSid) {
  const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);

  return (callSid)
    ? callClient.getData(`call/${locationId}/${callSid}`).catch(e => console.error('getCallDetail', e))
    : Promise.resolve({});
}

/* Update dynamodb call details */
async function updateCall(locationId, callSid, callData)
{
  const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
  await callClient.putData(`call/${locationId}/${callSid}`, callData);
}

/* Return endpoint response */
function response(statusCode, message, callSId=undefined) {
  // console.log(statusCode);
  return {
    statusCode,
    body: JSON.stringify({
      message,
      callSId
    }),
  };
}
