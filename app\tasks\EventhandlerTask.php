<?php
/**
 * Execute event handler asynchronously
 *
 * @category EventhandlerTask
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Phalcon\Cli\Task;
use CallPotential\CPCommon\Cli\CliOptions;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\EventRouter;

/**
 * Execute event handler asynchronously
 *
 * @category EventhandlerTask
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class EventhandlerTask extends Task
{
    use LoggerTrait;

    /**
     * Main action of task
     *
     * @return void
     */
    public function mainAction()
    {
        echo "LeadSave CLI operations" . PHP_EOL;
        echo "- sync [esLeadId] Reads record from ES and save it to PMS" . PHP_EOL;
    }

    /**
     * Execute event handler asynchronously
     *
     * @param string $eventName     Evant class name
     * @param string $eventDataFile Filename containing event data json
     *
     * @return void
     */
    public function handleAction($eventName, $eventDataFile)
    {
        $cliParams = array();
        array_push($cliParams, $eventName);
        array_push($cliParams, $eventDataFile);

        CliOptions::parseArgs($cliParams);
        $eventName = CliOptions::getValue('eventName', 0);
        $eventDataFile = CliOptions::getValue('eventDataFile', '');

        $eventData = file_get_contents($eventDataFile);
        $this->auditMessage("EventHandler task execution started for
            {$eventName} and eventDataFile {$eventDataFile} and eventData {$eventData}");

        $eventData = json_decode($eventData, true);
        unlink($eventDataFile);

        EventRouter::route($eventName, $eventData);
    }
}
