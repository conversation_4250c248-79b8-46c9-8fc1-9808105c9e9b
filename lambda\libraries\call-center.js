"use strict";
const console_c = require('../config/logger').console;
const got = require('got');

var callCenter = class CallCenter {
    constructor(config, req_q_data, req_p_data) {
        if (config) {
            this.location_id = config['location_id'];
        }
        this.req_q_data = req_q_data;
        this.req_p_data = req_p_data;
    }

    /**
     * Gets the number of available agents
     *
     * @access public
     * @param  int $queue_id
     * @param  array $call_info
     * @return int
     */
    async available_agents_count(queue_id, call_info) {

        if (!queue_id)
            queue_id = 0;

        // Using twilio rest api call here because twilio library is taking around 3 secs to return response
        // api call is taking around 500 ms .
        let twilioWorkspaceEndpoint = `https://taskrouter.twilio.com/v1/Workspaces/${call_info['workspace_sid']}/Workers`;
        const gotOptions = {
            headers: { 'Authorization': 'Basic ' + new Buffer.from(`${call_info['sid']}:${call_info['authtoken']}`).toString('base64') }
        };

        twilioWorkspaceEndpoint += `?TargetWorkersExpression=cc_queues HAS '${queue_id}'&PageSize=1000`;
        let workerList;

        try {
            let res = await got.get(twilioWorkspaceEndpoint, gotOptions);
            let data = JSON.parse(res.body);
            workerList = data.workers;
            console_c.log("matching queue workers", workerList);
        } catch (e) {
            console.error('ERROR getting online worker list data', e, new Error().stack);
            console.error('ERROR request endpoint', twilioWorkspaceEndpoint);
        }

        let available_count = 0;
        workerList.forEach(function(worker) {
          if (worker.activity_name.toLowerCase() != 'offline') {
              available_count++;
          }
        });
        return available_count;
    }
}


module.exports = {
    'CallCenter': callCenter
}
