import { PayByPhoneState } from "./Generated/PayByPhoneState.generated";
import { PayByPhoneStateBase, PayByPhoneStateHandlerMap } from "./PayByPhone.model";
import { buildPayByPhoneStateHandlerMap } from "./Generated/PayByPhoneStateHandlers.generated";
import { PayByPhoneContextService } from "./PayByPhoneContext.service";
import { NotifiableError } from "@bugsnag/js";
import { BugsnagService } from "@cp-workspace/shared";

export class PayByPhoneStateHandlerService {
  private stateHandlers: PayByPhoneStateHandlerMap;
  private context: PayByPhoneContextService;

  constructor(context: PayByPhoneContextService) {
    /**
     * NOTE: This service uses the moduleRef to dynamically resolve
     * state handler classes at runtime.
     *
     * Since we don't need to create instances of every state handler class each
     * time the domain is executed, we are opting to use moduleRef to get
     * class instances only when needed.
     */
    this.context = context;
    this.stateHandlers = buildPayByPhoneStateHandlerMap();
  }

  async getStateHandler(state: PayByPhoneState): Promise<PayByPhoneStateBase> {
    const stateType = this.stateHandlers[state];
    if (!stateType) {
      throw new Error(`State handler type not found for state: ${state}`);
    }
    try {
      const instance = this.context.moduleRef.get<PayByPhoneStateBase>(stateType, { strict: false, each: false });
      if (!instance) {
        throw new Error(`State handler instance not found for state: ${state}`);
      }
      instance.services = this.context;
      return instance;
    } catch (error) {
      throw new Error(`State handler instance not found for state: ${state}`);
    }
  }

  async setStateHandler(state: PayByPhoneState, handlerType: typeof PayByPhoneStateBase): Promise<void> {
    this.stateHandlers[state] = handlerType;
  }

}