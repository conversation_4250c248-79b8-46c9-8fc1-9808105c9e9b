import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { ConfirmCustomerInfo } from './ConfirmCustomerInfo';
import { Locale } from '@cp-workspace/shared';

describe('ConfirmCustomerInfo', () => {
  let confirmCustomerInfo: ConfirmCustomerInfo;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ConfirmCustomerInfo],
    }).compile();

    confirmCustomerInfo = module.get<ConfirmCustomerInfo>(ConfirmCustomerInfo);

    context = {
      storage: {
        retryCount: 0,
        matchedTenants: [],
        state: PayByPhoneState.ConfirmCustomerInfo,
        locale: Locale.English,
        locationId: 123,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: 'uniqueCallSid',
        LocationId: 123,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should return next state as TransferToAgent when there is no response from customer', async () => {
      context.request.Digits = '';

      const response: PayByPhoneStateHandlerResponse = await confirmCustomerInfo.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.TransferToAgent);
    });

    it('should return next state as TransferToAgent when invalid response from customer', async () => {
      context.request.Digits = undefined;

      const response: PayByPhoneStateHandlerResponse = await confirmCustomerInfo.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.TransferToAgent);
    });

    it('should return next state as UnitsToPayPrepayMonthsPrompt when valid response is selected', async () => {
      context.request.Digits = '1';

      const response: PayByPhoneStateHandlerResponse = await confirmCustomerInfo.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrepayMonthsPrompt);
    });

    it('should return next state as SayAmountDue when valid response is selected for delinquent flow', async () => {
      context.request.Digits = '1';
      context.storage.isDelinquentPaymentFlow = true;

      const response: PayByPhoneStateHandlerResponse = await confirmCustomerInfo.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.SayAmountDue);
    });

    it('should return next state as TransferToAgent when matchedTenants is undefined', async () => {
      context.request.Digits = '1';
      context.storage.matchedTenants = undefined;

      const response: PayByPhoneStateHandlerResponse = await confirmCustomerInfo.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.TransferToAgent);
    });
  });
});
