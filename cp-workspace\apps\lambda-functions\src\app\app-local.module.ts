import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PayByPhoneController } from './PayByPhone/PayByPhone.controller';
import { PayByPhoneServicesModule, PayByPhoneServicesModuleOptions } from '@cp-workspace/pay-by-phone-domain';
import { IoRedisClientType, SharedServicesModuleOptions, DomainEventsClientType, AsyncWorkersClientType, SharedServicesModule } from '@cp-workspace/shared';
import * as config from '../config.js';
import { EchoController } from './Echo/Echo.controller';
import { PayByPhoneRequestInterceptor } from './Interceptors/PayByPhoneRequestInterceptor';
import { AsyncTestController } from './AsyncTest/AsyncTest.controller';
import { EventLoggerController } from './EventLogger/EventLogger.controller';

const LOCAL_CONFIG = config;

const sharedServicesOptions: SharedServicesModuleOptions = {
  redisOptions: {
    clientType: IoRedisClientType.IN_MEMORY,
  },
  domainEventsOptions: {
    clientType: DomainEventsClientType.IN_MEMORY,
    options: { enabled: true },
  },
  asyncWorkersOptions: {
    clientType: AsyncWorkersClientType.IN_MEMORY,
    options: { enabled: true },
  },
};

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [() => ({ ...LOCAL_CONFIG })],
    }),
    PayByPhoneServicesModule.forRoot({ sharedServicesOptions } as PayByPhoneServicesModuleOptions),
    SharedServicesModule.forRoot(sharedServicesOptions),
  ],
  providers: [PayByPhoneRequestInterceptor],
  controllers: [PayByPhoneController, EchoController, AsyncTestController, EventLoggerController],
})
export class AppLocalModule {}
