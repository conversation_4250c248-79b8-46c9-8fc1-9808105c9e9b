"use strict";
const config = require('./config');
const got = require('got');
const acct_url = `https://${config.env}-acct.callpotential.com/`;
const loc_url = `https://${config.env}-loc.callpotential.com/`;
const call_url = `https://${config.env}-call.callpotential.com/`;

var apiClient = class apiClient {
  constructor(authToken) {
    if (!authToken) {
      throw new Error('empty authToken!');
    }
    this.url = '';
    this.options = {
        headers: { Authorization: authToken }
    };
  }

  async getData(endpoint) {
    try {
      let url = this.url + endpoint;
      let res = await got.get(url, this.options);
      let data = JSON.parse(res.body);
      return data;
    } catch (e) {
      console.error('ERROR getting data', e, new Error().stack);
      return {};
    }
  }

  async postData(endpoint, postData) {
    try {
      let url = this.url + endpoint;

      let options = {...this.options,
        body: JSON.stringify(postData)
      };

      let res = await got.post(url, options);
      let data = JSON.parse(res.body);
      return data;
    } catch (e) {
      console.error('ERROR posting data', e, new Error().stack);
      console.error('ERROR response body', e.response.body);
      return {};
    }
  }

  async putData(endpoint, putData) {
    try {
      let url = this.url + endpoint;

      let options = {...this.options,
        body: JSON.stringify(putData)
      };

      let res = await got.put(url, options);
      let data = JSON.parse(res.body);
      return data;
    } catch (e) {
      console.error('ERROR putting data', e, new Error().stack);
      console.error('ERROR response body', e.response.body);
      return {};
    }
  }
}

var locClient = class locClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = loc_url;
  }
}

var acctClient = class acctClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = acct_url;
  }
}

var callClient = class callClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = call_url;
  }
}

module.exports = {
  'locClient'  : locClient,
  'acctClient' : acctClient,
  'callClient' : callClient
};
