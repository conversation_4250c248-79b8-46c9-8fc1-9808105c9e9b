import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, ExitPayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class DisconnectCall extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<ExitPayByPhoneStateHandlerResponse> {
    const { twilioResponse } = context;

    twilioResponse.hangup();
    return {};
  }
}
