import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { LocalePrompt } from './LocalePrompt';
import { Locale } from '@cp-workspace/shared';

describe('LocalePrompt', () => {
  let localePrompt: LocalePrompt;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LocalePrompt],
    }).compile();

    localePrompt = module.get<LocalePrompt>(LocalePrompt);
    localePrompt.services = {
      locationService: {
        getLocationDetails: jest.fn().mockResolvedValue({
          locales: [Locale.English, Locale.Spanish, Locale.French]
        })
      } as any
    } as any;

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.LocalePrompt,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {

    it('should proceed to LocaleConfirm if we have more than 1 locale', async () => {

      const response: PayByPhoneStateHandlerResponse = await localePrompt.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.LocaleConfirm);
    });

    it('should proceed to CollectionsPrompt if we dont have more than 1 locale', async () => {

      localePrompt.services.locationService.getLocationDetails = jest.fn().mockResolvedValue({
        locales: [Locale.English]
      });

      const response: PayByPhoneStateHandlerResponse = await localePrompt.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.CollectionsPrompt);
    });

  });
});
