import { saveArtifact } from "../common";
import * as fs from 'fs';
import { join } from 'path';
import { standardLibs } from "./stdlibs-list";

function analyzeBundleDependencies(workspaceRoot: string, bundleFile: string) {
  console.log(`Analyzing dist bundle: ${bundleFile}`);

  const bundleContent = fs.readFileSync(bundleFile, 'utf8');

  /**
   * Apply a regular expression to the bundle content to extract the external modules
   * that are being used in the bundle.  This will allow us to generate a list of
   * external dependencies that can be used to generate a package.json file.
   * 
   * Should match any usage of require(), for example:
   * require("@nestjs/common")
   * require("@nestjs/core")
   * require("tslib")
   * ...etc
   */

  const matches = bundleContent.match(/require\(".*?"\)/g);
  const externalModules = matches?.map((item) => {
    return item.replace('require("', '').replace('")', '');
  });
  return externalModules ? Array.from(new Set(externalModules)) : [];
}

function getModuleNameFromRequire(requireResource: string) {
  /**
   * If the requireResource starts with an @, split the requireResource by / and take the first two elements.
   * If the requireResource does not start with an @, split the requireResource by / and take the first element.
   */

  if (requireResource.startsWith('@')) {
    return requireResource.split('/').slice(0, 2).join('/');
  }

  return requireResource.split('/')[0];

}

function buildNewPackageJson(workspaceRoot: string, externalModules?: string[]) {
  console.log(`Building new package.json file`);

  /**
   * Read the existing package.json file from the workspace root directory
   * and parse it into a JSON object.
   */
  const packageJsonFilePath = join(workspaceRoot, 'package.json').replace(/\\/g, '/');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonFilePath, 'utf8'));
  const packageJsonNew = { ...packageJson };
  delete packageJsonNew.license;
  delete packageJsonNew.scripts;
  packageJsonNew.dependencies = {};
  packageJsonNew.devDependencies = {};
  const notFoundDependencies = [];

  /**
   * Add the external modules to the dependencies section of the package.json file.
   */
  if (externalModules && externalModules.length > 0) {

    for(const moduleRequire of externalModules) {

      const moduleName = getModuleNameFromRequire(moduleRequire);

      /**
       * Check if the module is in the dependencies section of the package.json file.
       */
      if (packageJson.dependencies && packageJson.dependencies[moduleName]) {
        packageJsonNew.dependencies[moduleName] = packageJson.dependencies[moduleName];
        console.log(`Module ${moduleName} is already in the dependencies section of the package.json file.`);
        continue;
      } else {
        /**
         * Check if the module is in the devDependencies section of the package.json file.
         */
        if (packageJson.devDependencies && packageJson.devDependencies[moduleName]) {
          packageJsonNew.dependencies[moduleName] = packageJson.devDependencies[moduleName];
          console.log(`Module ${moduleName} was in devDependencies and is moved to the dependencies section of the package.json file.`);
          continue;
        }
      }

      /**
       * Check if the module is a standard library.
       */
      if (standardLibs[moduleName]) {
        console.log(`Module ${moduleName} is a standard library.`);
        continue;
      }


      /**
       * If the module is not in the dependencies or devDependencies section of the package.json file,
       * add it to the `notFoundDependencies` list.
       */
      console.log(`Module ${moduleName} is not in the dependencies or devDependencies section of the package.json file.`);
      notFoundDependencies.push(moduleName);
    }

  }

  return { 
    package: packageJsonNew,
    notFoundDependencies
  };
}

/**
 * =================================================================================================
 */


// Get the arguments from the command line
const [workspaceRoot, bundleFile, outputPath] = process.argv.slice(2);

if (!workspaceRoot || !bundleFile || !outputPath) {
  throw new Error("Usage: node generator.js <workspaceRoot> <bundleFile> <outputPath>");
}

async function run() {
  const externalModules = analyzeBundleDependencies(workspaceRoot, bundleFile);
  // await saveArtifact(outputPath, {
  //   fileName: 'external-modules.json',
  //   codeContent: JSON.stringify(externalModules, null, 2),
  //   overwrite: true,
  // });

  const newPackageJson = buildNewPackageJson(workspaceRoot, externalModules);
  await saveArtifact(outputPath, {
    fileName: 'package.json',
    codeContent: JSON.stringify(newPackageJson.package, null, 2),
    overwrite: true,
  });
  if (newPackageJson.notFoundDependencies.length > 0) {
    await saveArtifact(outputPath, {
      fileName: 'not-found.json',
      codeContent: JSON.stringify(newPackageJson.notFoundDependencies, null, 2),
      overwrite: true,
    });
  }
}

run();
