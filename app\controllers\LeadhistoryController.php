<?php
/**
 * LeadhistoryController - Retieve call, email and sms history of lead
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 3/30/17
 * Time: 4:38 PM
 *
 * @category LeadhistoryController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\Util;
use Phalcon\Db\Enum;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="LeadHistoryDetails",
 * @SWG\Property(property="direction",type="string"),
 * @SWG\Property(property="content",type="string"),
 * @SWG\Property(property="to",type="string"),
 * @SWG\Property(property="recording_url",type="string"),
 * @SWG\Property(property="duration",type="integer"),
 * @SWG\Property(property="previous_lead_type",type="string"),
 * @SWG\Property(property="new_lead_type",type="string"),
 * @SWG\Property(property="call_type",type="string")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="LeadHistory",
 *     allOf = {@SWG\Schema(
 * @SWG\Property(property="history_type",type="string"),
 * @SWG\Property(property="timestamp",type="string"),
 * @SWG\Property(property="employee_id",type="integer"),
 * @SWG\Property(property="details",type="object",
 *         ref="#/definitions/LeadHistoryDetails"
 *     )
 *   )}
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="LeadHistoryList",
 * @SWG\Property(
 *     property="items",
 *     type="array",
 * @SWG\Items(ref="#/definitions/LeadHistory")
 *   )
 * )
 */

/**
 * LeadhistoryController - Retieve call, email and sms history of lead
 *
 * @category LeadhistoryController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class LeadhistoryController extends \CallPotential\CPCommon\Controllers\BaseController
{
    use \CallPotential\CPCommon\Controllers\SessionTrait;
    use \CallPotential\CPCommon\Controllers\ElasticSearchTrait;

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        $intClient = ClientFactory::getIntClient($this->getRequestAuthToken());
        $leadData = Util::objectToArray($intClient->getLead($id));
        $leadPhones = Util::array_get('lead_phones', $leadData, []);
        $phones = array_column($leadPhones, 'phone_number');

        $phone = Util::array_get('phone', $leadData, "");
        if ($phone !== "") {
            $dialingCode = Util::getDialingCode(Util::DIALING_CODES, $leadData['location_id']);
            $phone = Util::addDialingCode($phone, $dialingCode);
        }
        $leadsParam = [
            "locationId" => $leadData['location_id'],
            "filterPhone" => $phone,
            "page" => 1,
            "perPage" => 999,
        ];
        $leads = $intClient->getLeads($leadsParam);
        if (isset($leads->items) && !empty($leads->items)) {
            $leadsData = Util::objectToArray($leads->items);
            $phonesArray = array_column($leadsData, 'lead_phones');
            foreach ($phonesArray as $value) {
                $phones = array_merge($phones, array_column($value, 'phone_number'));
            }
        }

        $sql = "SELECT
            date_updated AS datestamp,
            leads_history.employee_id,
            'lead_type' AS calltype,
            lead_type
            FROM leads_history
            WHERE lead_id = ".$leadData["lead_id"]."
        ";
        $result = $this->dbLegacy->query($sql);
        $result->setFetchMode(Enum::FETCH_ASSOC);
        $leadTypeData = $result->fetchAll();

        $sql = "SELECT
            date_completed AS datestamp,
            employee_id,
            type AS calltype,
            details
            FROM followup_reports
            WHERE lead_id = ".$leadData["lead_id"]."
            AND (type = 'email' OR type = 'sms')
        ";
        $result = $this->dbLegacy->query($sql);
        $result->setFetchMode(Enum::FETCH_ASSOC);
        $callData = $result->fetchAll();
        $mergedData = $callData;

        $callClient = ClientFactory::getCallClient($this->getRequestAuthToken());
        $callHistoryParam = [
            'locationId' => $leadData["location_id"],
            'includeNeighborLocations' => true,
            'startDate' => '2016-01-01',
            'endDate' => date('Y-m-d'),
            'page' => 1,
            'perPage' => 999,
            'callNumber' => implode(',', $phones),
            'orderBy' => 'datestamp',
            'order' => 'DESC',
        ];
        $histories = (array) $callClient->getCallHistory($callHistoryParam);
        if (isset($histories["items"]) && !empty($histories["items"])) {
            $callHistoryData = Util::objectToArray($histories["items"]);
            $mergedData = array_merge($mergedData, $callHistoryData);
        }

        $return = [];

        $oldleadtype = 0;
        foreach ($leadTypeData as $item) {
            if ((int) $item["lead_type"] !== (int) $oldleadtype && (int) $oldleadtype !== 0) {
                $tmp = [];
                $tmp["history_type"] = Util::array_get('calltype', $item, "call");
                $tmp["timestamp"] = Util::array_get('datestamp', $item, "");
                $tmp["employee_id"] = (int) Util::array_get('employee_id', $item, 1);
                $details = [];
                $details['call_type'] = "";
                $details["direction"] = "";
                $details["content"] = Util::array_get('details', $item, "");
                $details['to'] = Util::array_get('call_number', $item, "");
                $details['recording_url'] = Util::array_get('recording_url', $item, "");
                $details['duration'] = (int) Util::array_get('recording_duration', $item, 0);
                $details['previous_lead_type'] = $oldleadtype;
                $details['new_lead_type'] = $item["lead_type"];
                $tmp["details"] = $details;
                $return[] = $tmp;
            }
            $oldleadtype = $item["lead_type"];
        }

        foreach ($mergedData as $item) {
            $tmp = [];
            $tmp["history_type"] = Util::array_get('calltype', $item, "call");
            $tmp["timestamp"] = Util::array_get('datestamp', $item, "");
            $tmp["employee_id"] = (int) Util::array_get('employee_id', $item, 1);
            $details = [];
            $details['call_type'] = Util::array_get('call_type', $item, "outbound");
            $call_types = explode("_", $details['call_type']);
            $details["direction"] = $call_types[0];
            $details["content"] = Util::array_get('details', $item, "");
            $details['to'] = Util::array_get('call_number', $item, "");
            $details['recording_url'] = Util::array_get('recording_url', $item, "");
            $details['duration'] = (int) Util::array_get('recording_duration', $item, 0);
            $details['previous_lead_type'] = "";
            $details['new_lead_type'] = "";
            $tmp["details"] = $details;
            $return[] = $tmp;
        }

        return array("items" => $return);
    }

    /**
     * Intermediate function to prepare data for list action response
     *
     * @return mixed
     */
    public function getListContent()
    {
        //not implemented
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/leadhistory/{id}",
     *     description="Returns a lead history record based on a lead id",
     *     summary="get leadhistory",
     *     operationId="LeadhistoryGetById",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *       description="Authorization token",
     *       type="string",
     *       name="Authorization",
     *       in="header",
     *       required=true
     *     ),
     * @SWG\Parameter(
     *       description="lead id to fetch lead history",
     *       in="path",
     *       name="id",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Response(
     *       response=200,
     *       description="lead history response",
     * @SWG\Schema(ref="#/definitions/LeadHistory")
     *     ),@SWG\Response(
     *       response="403",
     *       description="Not Authorized Invalid or missing Authorization header"
     *     ),@SWG\Response(
     *       response="500",
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }

        return parent::getAction();
    }

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    protected function getModelName(): string
    {
        return "LeadHistory";
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    protected function doBulkSave(): array
    {
        //not implemented
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    protected function doBulkCreate()
    {
        //not implemented
    }
}
