<?php
/**
 * TwilioAccount model
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 8/1/17
 * Time: 1:29 PM
 *
 * @category TwilioAccount
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\Util;

use Aws\DynamoDb\Exception\DynamoDbException;
use Aws\DynamoDb\Marshaler;

/**
 * TwilioAccount model
 *
 * @category TwilioAccount
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="TwilioAccount")
 */
class TwilioAccount extends CallPotential\CPCommon\RestModel
{
    use JsonModelTrait;

    /**
     * Primary key Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     *
     * @SWG\Property(description="account ID (user_id/parent_id from legacy user table)")
     */
    protected $id;

    /**
     * Active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)

     * @SWG\Property(description="0/1 for inactive/active")
     */
    protected $active;

    /**
     * Account SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="twilio subaccount SID")
     */
    protected $account_sid;

    /**
     * Auth token
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Twilio auth token")
     */
    protected $authtoken;

    /**
     * Workspace SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio workspace SID")
     */
    protected $workspace_sid;

    /**
     * Chat service SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio chat service SID")
     */
    protected $chat_service_sid;

    /**
     * Sync service SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio sync service SID")
     */
    protected $sync_service_sid;

    /**
     * API key
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio sync service key sid for token generator")
     */
    protected $api_key;

    /**
     * API secret
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio sync service secret sid for token generator")
     */
    protected $api_secret;

    /**
     * Voice workflow
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Workflow sid for InboundOutboundVoice workflow in taskrouter")
     */
    protected $voice_workflow;

    /**
     * Main queue
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Queue sid for Main queue in taskrouter")
     */
    protected $main_queue;

    /**
     * Tr client token URL
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Task Router client token function URL")
     */
    protected $tr_client_token_url;

    /**
     * Client token URL
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Twilio client token function url")
     */
    protected $client_token_url;

    /**
     * Conference terminate URL
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Conference terminate function url")
     */
    protected $conference_terminate_url;

    /**
     * Outbound URL
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Outbound function url")
     */
    protected $outbound_url;

    /**
     * Outbound callback URL
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Outbound callback url")
     */
    protected $outbound_callback_url;

    /**
     * Sms service SID
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="sms_service_sid")
     */
    public $sms_service_sid;

    /**
     * Sms queue SID
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="sms_queue_sid")
     */
    protected $sms_queue_sid;

    /**
     * Sms workflow SID
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="sms_workflow_sid")
     */
    protected $sms_workflow_sid;

    /**
     * Chat workflow SID
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Chat service workflow SID")
     */
    protected $chat_workflow_sid;

    /**
     * Chat queue SID
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)

     * @SWG\Property(description="Chat service queue SID")
     */
    protected $chat_queue_sid;

    /**
     * Default channel SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio default channel SID")
     */
    protected $default_channel_sid;

    /**
     * Voice channel SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio voice channel SID")
     */
    protected $voice_channel_sid;

    /**
     * Chat channel SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio chat channel SID")
     */
    protected $chat_channel_sid;

    /**
     * Sms channel SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio sms channel SID")
     */
    protected $sms_channel_sid;

    /**
     * Video channel SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)

     * @SWG\Property(description="Twilio video channel SID")
     */
    protected $video_channel_sid;

    /**
     * Dynamo
     *
     * @var Aws\DynamoDb\DynamoDbClient
     */
    protected $dynamo;

    /**
     * Table prefix
     *
     * @var string
     */
    protected $tablePrefix;

    /**
     * Method to set the value of field id
     *
     * @param integer $id value to set
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active value to set
     *
     * @return $this
     */
    public function setActive(int $active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field account_sid
     *
     * @param string $account_sid value to set
     *
     * @return $this
     */
    public function setAccountSid(string $account_sid)
    {
        $this->account_sid = $account_sid;

        return $this;
    }

    /**
     * Method to set the value of field authtoken
     *
     * @param string $authtoken value to set
     *
     * @return $this
     */
    public function setAuthtoken(string $authtoken)
    {
        $this->authtoken = $authtoken;

        return $this;
    }

    /**
     * Method to set the value of field workspace_sid
     *
     * @param string $workspace_sid value to set
     *
     * @return $this
     */
    public function setWorkspaceSid(string $workspace_sid)
    {
        $this->workspace_sid = $workspace_sid;

        return $this;
    }

    /**
     * Method to set the value of field chat_service_sid
     *
     * @param string $chat_service_sid value to set
     *
     * @return $this
     */
    public function setChatServiceSid(string $chat_service_sid)
    {
        $this->chat_service_sid = $chat_service_sid;

        return $this;
    }

    /**
     * Method to set the value of field sync_service_sid
     *
     * @param string $sync_service_sid value to set
     *
     * @return $this
     */
    public function setSyncServiceSid(string $sync_service_sid)
    {
        $this->sync_service_sid = $sync_service_sid;

        return $this;
    }

    /**
     * Method to set the value of field api_key
     *
     * @param string $api_key value to set
     *
     * @return $this
     */
    public function setApiKey(string $api_key)
    {
        $this->api_key = $api_key;

        return $this;
    }

    /**
     * Method to set the value of field api_secret
     *
     * @param string $api_secret value to set
     *
     * @return $this
     */
    public function setApiSecret(string $api_secret)
    {
        $this->api_secret = $api_secret;

        return $this;
    }

    /**
     * Method to set the value of field voice_workflow
     *
     * @param string $voice_workflow value to set
     *
     * @return $this
     */
    public function setVoiceWorkflow(string $voice_workflow)
    {
        $this->voice_workflow = $voice_workflow;

        return $this;
    }

    /**
     * Method to set the value of field main_queue
     *
     * @param string $main_queue value to set
     *
     * @return $this
     */
    public function setMainQueue(string $main_queue)
    {
        $this->main_queue = $main_queue;

        return $this;
    }

    /**
     * Method to set the value of field tr_client_token_url
     *
     * @param string $tr_client_token_url value to set
     *
     * @return $this
     */
    public function setTrClientTokenUrl(string $tr_client_token_url)
    {
        $this->tr_client_token_url = $tr_client_token_url;

        return $this;
    }

    /**
     * Method to set the value of field client_token_url
     *
     * @param string $client_token_url value to set
     *
     * @return $this
     */
    public function setClientTokenUrl(string $client_token_url)
    {
        $this->client_token_url = $client_token_url;

        return $this;
    }

    /**
     * Method to set the value of field conference_terminate_url
     *
     * @param string $conference_terminate_url value to set
     *
     * @return $this
     */
    public function setConferenceTerminateUrl(string $conference_terminate_url)
    {
        $this->conference_terminate_url = $conference_terminate_url;

        return $this;
    }

    /**
     * Method to set the value of field outbound_url
     *
     * @param string $outbound_url value to set
     *
     * @return $this
     */
    public function setOutboundUrl(string $outbound_url)
    {
        $this->outbound_url = $outbound_url;

        return $this;
    }

    /**
     * Method to set the value of field outbound_callback_url
     *
     * @param string $outbound_callback_url value to set
     *
     * @return $this
     */
    public function setOutboundCallbackUrl(string $outbound_callback_url)
    {
        $this->outbound_callback_url = $outbound_callback_url;

        return $this;
    }


    /**
     * Method to set the value of field sms_service_sid
     *
     * @param string $sms_service_sid value to set
     *
     * @return $this
     */
    public function setSmsServiceSid(string $sms_service_sid)
    {
        $this->sms_service_sid = $sms_service_sid;

        return $this;
    }

    /**
     * Method to set the value of field sms_queue_sid
     *
     * @param string $sms_queue_sid value to set
     *
     * @return $this
     */
    public function setSmsQueueSid(string $sms_queue_sid)
    {
        $this->sms_queue_sid = $sms_queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field sms_workflow_sid
     *
     * @param string $sms_workflow_sid value to set
     *
     * @return $this
     */
    public function setSmsWorkflowSid(string $sms_workflow_sid)
    {
        $this->sms_workflow_sid = $sms_workflow_sid;

        return $this;
    }


    /**
     * Method to set the value of field chat_workflow_sid
     *
     * @param string $chat_workflow_sid value to set
     *
     * @return $this
     */
    public function setChatWorkflowSid(string $chat_workflow_sid)
    {
        $this->chat_workflow_sid = $chat_workflow_sid;

        return $this;
    }

    /**
     * Method to set the value of field chat_queue_sid
     *
     * @param string $chat_queue_sid value to set
     *
     * @return $this
     */
    public function setChatQueueSid(string $chat_queue_sid)
    {
        $this->chat_queue_sid = $chat_queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field default_channel_sid
     *
     * @param string $default_channel_sid value to set
     *
     * @return $this
     */
    public function setDefaultChannelSid(string $default_channel_sid)
    {
        $this->default_channel_sid = $default_channel_sid;

        return $this;
    }

    /**
     * Method to set the value of field voice_channel_sid
     *
     * @param string $voice_channel_sid value to set
     *
     * @return $this
     */
    public function setVoiceChannelSid(string $voice_channel_sid)
    {
        $this->voice_channel_sid = $voice_channel_sid;

        return $this;
    }

    /**
     * Method to set the value of field chat_channel_sid
     *
     * @param string $chat_channel_sid value to set
     *
     * @return $this
     */
    public function setChatChannelSid(string $chat_channel_sid)
    {
        $this->chat_channel_sid = $chat_channel_sid;

        return $this;
    }

    /**
     * Method to set the value of field sms_channel_sid
     *
     * @param string $sms_channel_sid value to set
     *
     * @return $this
     */
    public function setSmsChannelSid(string $sms_channel_sid)
    {
        $this->sms_channel_sid = $sms_channel_sid;

        return $this;
    }

    /**
     * Method to set the value of field video_channel_sid
     *
     * @param string $video_channel_sid value to set
     *
     * @return $this
     */
    public function setVideoChannelSid(string $video_channel_sid)
    {
        $this->video_channel_sid = $video_channel_sid;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns the value of field active
     *
     * @return integer
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field account_sid
     *
     * @return string
     */
    public function getAccountSid(): string
    {
        return $this->account_sid;
    }

    /**
     * Returns the value of field authtoken
     *
     * @return string
     */
    public function getAuthtoken(): string
    {
        return $this->authtoken;
    }

    /**
     * Returns the value of field workspace_sid
     *
     * @return string
     */
    public function getWorkspaceSid(): string
    {
        return $this->workspace_sid;
    }

    /**
     * Returns the value of field chat_service_sid
     *
     * @return string
     */
    public function getChatServiceSid(): string
    {
        return $this->chat_service_sid;
    }

    /**
     * Returns the value of field sync_service_sid
     *
     * @return string
     */
    public function getSyncServiceSid(): string
    {
        return $this->sync_service_sid;
    }

    /**
     * Returns the value of field api_key
     *
     * @return string
     */
    public function getApiKey(): string
    {
        return $this->api_key;
    }

    /**
     * Returns the value of field api_secret
     *
     * @return string
     */
    public function getApiSecret(): string
    {
        return $this->api_secret;
    }

    /**
     * Returns the value of field voice_workflow
     *
     * @return string
     */
    public function getVoiceWorkflow(): string
    {
        return $this->voice_workflow;
    }

    /**
     * Returns the value of field main_queue
     *
     * @return string
     */
    public function getMainQueue(): string
    {
        return $this->main_queue;
    }

    /**
     * Returns the value of field tr_client_token_url
     *
     * @return string
     */
    public function getTrClientTokenUrl(): string
    {
        return $this->tr_client_token_url;
    }

    /**
     * Returns the value of field client_token_url
     *
     * @return string
     */
    public function getClientTokenUrl(): string
    {
        return $this->client_token_url;
    }

    /**
     * Returns the value of field conference_terminate_url
     *
     * @return string
     */
    public function getConferenceTerminateUrl(): string
    {
        return $this->conference_terminate_url;
    }

    /**
     * Returns the value of field outbound_url
     *
     * @return string
     */
    public function getOutboundUrl(): string
    {
        return $this->outbound_url;
    }

    /**
     * Returns the value of field outbound_callback_url
     *
     * @return string
     */
    public function getOutboundCallbackUrl(): string
    {
        return $this->outbound_callback_url;
    }


    /**
     * Returns the value of field sms_service_sid
     *
     * @return string
     */
    public function getSmsServiceSid(): string
    {
        return $this->sms_service_sid;
    }

    /**
     * Returns the value of field sms_queue_sid
     *
     * @return string
     */
    public function getSmsQueueSid(): string
    {
        return $this->sms_queue_sid;
    }

    /**
     * Returns the value of field sms_workflow_sid
     *
     * @return string
     */
    public function getSmsWorkflowSid(): string
    {
        return $this->sms_workflow_sid;
    }


    /**
     * Returns the value of field chat_workflow_sid
     *
     * @return string
     */
    public function getChatWorkflowSid(): string
    {
        return $this->chat_workflow_sid;
    }

    /**
     * Returns the value of field chat_queue_sid
     *
     * @return string
     */
    public function getChatQueueSid(): string
    {
        return $this->chat_queue_sid;
    }

    /**
     * Returns the value of field default_channel_sid
     *
     * @return string
     */
    public function getDefaultChannelSid(): string
    {
        return $this->default_channel_sid;
    }

    /**
     * Returns the value of field voice_channel_sid
     *
     * @return string
     */
    public function getVoiceChannelSid(): string
    {
        return $this->voice_channel_sid;
    }

    /**
     * Returns the value of field chat_channel_sid
     *
     * @return string
     */
    public function getChatChannelSid(): string
    {
        return $this->chat_channel_sid;
    }

    /**
     * Returns the value of field sms_channel_sid
     *
     * @return string
     */
    public function getSmsChannelSid(): string
    {
        return $this->sms_channel_sid;
    }

    /**
     * Returns the value of field video_channel_sid
     *
     * @return string
     */
    public function getVideoChannelSid(): string
    {
        return $this->video_channel_sid;
    }

    /**
     * Get table prefix value
     *
     * @return string
     */
    public function getTablePrefix(): string
    {
        if (is_null($this->tablePrefix)) {
            $config = $this->getDI()->getShared('config');
            $config = $config->toArray();
            $this->tablePrefix = Util::array_get('tablePrefix', $config['dynamodb'], '');
        }

        return $this->tablePrefix;
    }

    /**
     * Returns table name mapped in the model.
     *
     * @return string
     */
    public function getSourceName()
    {
        return $this->getTablePrefix().'twilio_accounts';
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->dynamo = $this->getDI()->getShared('dynamoDB');
        $set_source = $this->getSourceName();
        $this->setSource($set_source);
    }

    /**
     * Save record
     *
     * @param array|null $data      array of date in key value pair
     * @param array|null $whiteList whiteList
     *
     * @return mixed
     */
    public function save(array $data = null, array $whiteList = null): bool
    {
        unset($whiteList);
        if (is_array($data)) {
            $this->reset();
            foreach ($data as $field => $value) {
                if (property_exists($this, $field)) {
                    $this->$field = $value;
                }
            }
        }
        $data = $this->formatJson();
        foreach ($data as $field => $value) {
            if (strlen($data[$field]) === 0) {
                unset($data[$field]);
            }
        }
        $body = json_encode($data);
        $marshaler = new Marshaler();

        $params = [
            'TableName' => $this->getSourceName(),
            'Item' => $marshaler->marshalJson($body),
        ];
        try {
            $this->dynamoDB()->putItem($params);

            return $this;
        } catch (DynamoDbException $e) {
            return false;
        }
    }

    /**
     * Find record by primary key value
     *
     * @param int $id primary key value
     *
     * @return mixed
     */
    public function findById(int $id)
    {
        $marshaler = new Marshaler();
        $key = $marshaler->marshalJson('{"id": ' . $id . '}');

        $params = [
            'TableName' => $this->getSourceName(),
            'Key' => $key,
        ];

        try {
            $result = $this->dynamoDB()->getItem($params);
        } catch (DynamoDbException $e) {
            return false;
        }
        $data = $result["Item"];
        if (is_array($data)) {
            $data = $marshaler->unmarshalItem($data);
            foreach ($data as $field => $value) {
                if (property_exists($this, $field)) {
                    $this->$field = $value;
                }
            }
        } else {
            $this->reset();
        }

        return $this;
    }

    /**
     * Delete record by primary key value
     *
     * @param int $id primary key value
     *
     * @return void
     */
    public function deleteById(int $id)
    {
        unset($id);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return mixed
     */
    public static function findFirst($parameters = null)
    {
        $class = get_called_class();
        $object = new $class();

        return $object->findById($parameters);
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return mixed
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        unset($parameters);

        return [];
    }

    /**
     * Get models meta details
     *
     * @return array
     */
    public function getModelsMetaData(): Phalcon\Mvc\Model\MetaDataInterface
    {
        $meta = parent::getModelsMetaData();
        $meta->setStrategy(new \Phalcon\Mvc\Model\MetaData\Strategy\Annotations());
        $meta->reset();

        return $meta;
    }

    /**
     * Initialize dynamo DB
     *
     * @return object
     */
    private function dynamoDB()
    {
        if (is_null($this->dynamo)) {
            $this->initialize();
        }

        return $this->dynamo;
    }
}
