<?php
namespace Test;

class UnprocessedcounterTest extends IntegrationCase
{
    public function testUnprocessedcounterAction()
    {
        $this->validateSwaggerPath(
            'call',
            'Unprocessedcounter',
            '/unprocessedcounter',
            'get'
        );

        $response = $this->runGET('/unprocessedcounter', true);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/unprocessedcounter GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'Unprocessedcounter', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'Unprocessedcounter', [200, 404, 500]);
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }
}
