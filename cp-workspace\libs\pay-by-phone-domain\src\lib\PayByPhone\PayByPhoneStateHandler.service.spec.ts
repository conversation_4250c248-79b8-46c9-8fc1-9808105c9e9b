import { PayByPhoneState } from "./Generated/PayByPhoneState.generated";
import { PayByPhoneStateHandlerService } from "./PayByPhoneStateHandler.service";
import { PayByPhoneContextService } from "./PayByPhoneContext.service";
import { TestingModule } from "@nestjs/testing";
import { PayByPhoneTest } from "./PayByPhoneTest.module";
import { LocalePrompt } from "./States/LocalePrompt";
import { PayByPhoneStateBase } from "./PayByPhone.model";

class UnregisteredStateClass extends PayByPhoneStateBase {
  async handleRequest(): Promise<void> {
    throw new Error('Method not implemented.');
  }
}

describe('PayByPhoneStateHandlerService', () => {
  let service: PayByPhoneStateHandlerService;
  let contextService: PayByPhoneContextService;

  beforeEach(async () => {
    process.env['BUGSNAG_API_KEY'] = '616b106fc4dba0412968d8c0e91995be';
    const module: TestingModule = await PayByPhoneTest.createTestingModule();
    contextService =  module.get<PayByPhoneContextService>(PayByPhoneContextService);
    service = new PayByPhoneStateHandlerService(contextService);
  });

  describe('getStateHandler', () => {
    it('should return the state handler instance for a valid state', async () => {
      const state = PayByPhoneState.LocalePrompt;
      const result = await service.getStateHandler(state);
      expect(result.constructor).toBe(LocalePrompt);
    });

    it('should throw an error for an unknown state', async () => {
      const state = 'UnknownState' as PayByPhoneState;
      await expect(service.getStateHandler(state)).rejects.toThrowError(`State handler type not found for state: ${state}`);
    });

    it('should throw an error if state handler type is not found', async () => {
      const state = PayByPhoneState.LocalePrompt;
      service.setStateHandler(state, undefined as any);
      await expect(service.getStateHandler(state)).rejects.toThrowError(`State handler type not found for state: ${state}`);
    });

    it('should throw an error if state handler instance is not found', async () => {
      const state = PayByPhoneState.LocalePrompt;
      service.setStateHandler(state, UnregisteredStateClass);
      await expect(service.getStateHandler(state)).rejects.toThrowError(`State handler instance not found for state: ${state}`);
    });
  });
});