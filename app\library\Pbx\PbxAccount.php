<?php
/**
 * Pbx account library
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 12/6/17
 * Time: 11:49 PM
 *
 * @category PbxAccount
 * @package  Pbx
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

namespace Pbx;

use CallPotential\CPCommon\ServiceClient;

/**
 * Pbx account library
 *
 * @category PbxAccount
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class PbxAccount implements PbxAccountInterface
{
    /**
     * URL
     *
     * @var string
     */
    private $url;

    /**
     * Client
     *
     * @var object
     */
    private $client;

    /**
     * PbxAccount constructor.
     *
     * @param string $url URL
     */
    public function __construct(string $url)
    {
        if (substr($url, -1, 1) !== '/') {
            $url .= '/';
        }
        $this->url = $url;
        $this->client = new ServiceClient();
    }

    /**
     * Add account to PBX
     *
     * @param string $number   number
     * @param string $password password
     *
     * @return array
     * @throws Exception
     */
    public function addAccount(string $number, string $password): array
    {
        $data = [ "action" => "add", "user" => $number, "password" => $password];
        $result = $this->client->postUrl($this->url.'user.api', $data);
        if ($result->getStatusCode() < 300) {
            unset($data['action']);

            return $data;
        } else {
            throw new \Exception($result->getReasonPhrase());
        }
    }

    /**
     * Remove account from PBX
     *
     * @param string $number number
     *
     * @return boolean
     */
    public function deleteAccount(string $number): bool
    {
        $data = [ "action" => "remove", "user" => $number, "delete" => true];
        $result = $this->client->postUrl($this->url.'user.api', $data);

        return ($result->getStatusCode() < 300);
    }

    /**
     * Update password for account
     *
     * @param string $number   number
     * @param string $password password
     *
     * @return array
     * @throws Exception
     */
    public function editAccount(string $number, string $password): array
    {
        $data = [ "action" => "passwd", "user" => $number, "password" => $password];
        $result = $this->client->postUrl($this->url.'user.api', $data);
        if ($result->getStatusCode() < 300) {
            unset($data['action']);

            return $data;
        } else {
            throw new \Exception($result->getReasonPhrase());
        }
    }
}
