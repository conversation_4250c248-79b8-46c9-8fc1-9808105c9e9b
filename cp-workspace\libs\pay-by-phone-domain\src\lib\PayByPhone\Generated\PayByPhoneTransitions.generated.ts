
/**
 * This file was auto-generated by the state-diagram-code generator.
 * DO NOT MODIFY THIS FILE DIRECTLY.
 * To make changes, modify the source diagram file and re-run the generator.
 * Diagram source: /libs/pay-by-phone-domain/src/.diagrams/PayByPhone.state.mmd
 * Generated at: 2025-02-26T20:51:04.889Z
 */
import { PayByPhoneState } from "./PayByPhoneState.generated";

export const PayByPhoneTransitions = new Map([
  [PayByPhoneState.LocalePrompt, new Map([
    [PayByPhoneState.LocaleConfirm, "More than one language selection"],
    [PayByPhoneState.CollectionsPrompt, "Only one language selection"],
  ])],
  [PayByPhoneState.LocaleConfirm, new Map([
    [PayByPhoneState.LocalePrompt, "Invalid selection"],
    [PayByPhoneState.CollectionsPrompt, ""],
  ])],
  [PayByPhoneState.CollectionsPrompt, new Map([
    [PayByPhoneState.CollectionsConfirm, ""],
  ])],
  [PayByPhoneState.CollectionsConfirm, new Map([
    [PayByPhoneState.SayAmountDue, "Make payment"],
    [PayByPhoneState.TransferToAgent, "Speak with manager"],
    [PayByPhoneState.CustomerOptOut, "Opt out"],
    [PayByPhoneState.DisconnectCall, "Too many incorrect attempts"],
    [PayByPhoneState.CollectionsPrompt, "Invalid input or no response"],
  ])],
  [PayByPhoneState.CustomerOptOut, new Map([
    [PayByPhoneState.DisconnectCall, ""],
  ])],
  [PayByPhoneState.InputPhoneGather, new Map([
    [PayByPhoneState.InputPhoneValidate, ""],
  ])],
  [PayByPhoneState.InputPhoneValidate, new Map([
    [PayByPhoneState.InputPhoneGather, "Phone is invalid, retries < max"],
    [PayByPhoneState.TransferToAgent, "Phone is invalid, retries >= max or speak to manager"],
    [PayByPhoneState.CustomerByPhoneSearch, "Phone is valid"],
  ])],
  [PayByPhoneState.CustomerByPhoneSearch, new Map([
    [PayByPhoneState.InputPhoneGather, "Customer not found, no response, or PBP not allowed"],
    [PayByPhoneState.UnitsToPayPrompt, "Multiple units found"],
    [PayByPhoneState.ConfirmCustomerInfo, "Single unit found"],
  ])],
  [PayByPhoneState.UnitsToPayPrepayMonthsPrompt, new Map([
    [PayByPhoneState.UnitsToPayPrepayMonthsSelection, ""],
  ])],
  [PayByPhoneState.GetSavedCards, new Map([
    [PayByPhoneState.PayMethodPrompt, "Has saved cards"],
    [PayByPhoneState.PayMethodCreditCardPrompt, "No saved cards"],
  ])],
  [PayByPhoneState.SayAmountDue, new Map([
    [PayByPhoneState.GetSavedCards, ""],
  ])],
  [PayByPhoneState.ConfirmCustomerInfo, new Map([
    [PayByPhoneState.SayAmountDue, "Delinquent payment flow"],
    [PayByPhoneState.TransferToAgent, "No response or invalid input"],
    [PayByPhoneState.UnitsToPayPrepayMonthsPrompt, "Non-delinquent payment flow"],
  ])],
  [PayByPhoneState.UnitsToPayPrompt, new Map([
    [PayByPhoneState.UnitsToPaySelection, ""],
    [PayByPhoneState.TransferToAgent, "No matched tenants"],
  ])],
  [PayByPhoneState.UnitsToPaySelection, new Map([
    [PayByPhoneState.UnitsToPayPrompt, "No response, start over, or invalid selection"],
    [PayByPhoneState.ConfirmCustomerInfo, "Chose to pay single unit"],
  ])],
  [PayByPhoneState.UnitsToPayPrepayMonthsSelection, new Map([
    [PayByPhoneState.UnitsToPayPrepayMonthsPrompt, "No response or invalid input"],
    [PayByPhoneState.UnitsToPayPrepayMonthsConfirm, "Valid selection"],
  ])],
  [PayByPhoneState.UnitsToPayPrepayMonthsConfirm, new Map([
    [PayByPhoneState.UnitsToPayPrepayMonthsPrompt, "Change selection or no response"],
    [PayByPhoneState.GetSavedCards, "Confirm selection"],
  ])],
  [PayByPhoneState.PayMethodPrompt, new Map([
    [PayByPhoneState.PayMethodSelection, ""],
  ])],
  [PayByPhoneState.PayMethodSelection, new Map([
    [PayByPhoneState.ExistingPayMethodVerify, "Verify existing method"],
    [PayByPhoneState.PayMethodCreditCardPrompt, "Add new pay method"],
    [PayByPhoneState.PayMethodPrompt, "No digits provided or invalid digits provided"],
    [PayByPhoneState.TransferToAgent, "Too many retries"],
  ])],
  [PayByPhoneState.ExistingPayMethodVerify, new Map([
    [PayByPhoneState.PayMethodSecurityCodePrompt, "Passes verification"],
    [PayByPhoneState.PayMethodCreditCardPrompt, "Fails verification"],
  ])],
  [PayByPhoneState.PayMethodCreditCardPrompt, new Map([
    [PayByPhoneState.PayMethodCreditCardValidate, ""],
  ])],
  [PayByPhoneState.PayMethodCreditCardValidate, new Map([
    [PayByPhoneState.PayMethodCreditCardPrompt, "Is invalid"],
    [PayByPhoneState.PayMethodCreditCardConfirm, "Is valid"],
  ])],
  [PayByPhoneState.PayMethodCreditCardConfirm, new Map([
    [PayByPhoneState.PayMethodExpirationPrompt, "Customer confirms"],
    [PayByPhoneState.PayMethodCreditCardPrompt, "Customer rejects"],
  ])],
  [PayByPhoneState.PayMethodExpirationPrompt, new Map([
    [PayByPhoneState.PayMethodExpirationValidate, ""],
  ])],
  [PayByPhoneState.PayMethodExpirationValidate, new Map([
    [PayByPhoneState.PayMethodExpirationPrompt, "Is invalid"],
    [PayByPhoneState.PayMethodExpirationConfirm, "Is valid"],
  ])],
  [PayByPhoneState.PayMethodExpirationConfirm, new Map([
    [PayByPhoneState.PayMethodSecurityCodePrompt, "Customer confirms"],
    [PayByPhoneState.PayMethodExpirationPrompt, "Customer rejects"],
  ])],
  [PayByPhoneState.PayMethodSecurityCodePrompt, new Map([
    [PayByPhoneState.PayMethodSecurityCodeValidate, ""],
  ])],
  [PayByPhoneState.PayMethodSecurityCodeValidate, new Map([
    [PayByPhoneState.PayMethodSecurityCodePrompt, "Is invalid"],
    [PayByPhoneState.PayMethodSecurityCodeConfirm, "Is valid"],
  ])],
  [PayByPhoneState.PayMethodSecurityCodeConfirm, new Map([
    [PayByPhoneState.PayMethodPostalCodePrompt, "Customer confirms (U.S. Location)"],
    [PayByPhoneState.FinalPayAmountPrompt, "Customer confirms (Non U.S. Location)"],
    [PayByPhoneState.PayMethodSecurityCodePrompt, "Customer rejects"],
  ])],
  [PayByPhoneState.PayMethodPostalCodePrompt, new Map([
    [PayByPhoneState.PayMethodPostalCodeValidate, ""],
  ])],
  [PayByPhoneState.PayMethodPostalCodeValidate, new Map([
    [PayByPhoneState.PayMethodPostalCodePrompt, "Is invalid"],
    [PayByPhoneState.PayMethodPostalCodeConfirm, "Is valid"],
  ])],
  [PayByPhoneState.PayMethodPostalCodeConfirm, new Map([
    [PayByPhoneState.FinalPayAmountPrompt, "Customer confirms"],
    [PayByPhoneState.PayMethodPostalCodePrompt, "Customer rejects"],
  ])],
  [PayByPhoneState.FinalPayAmountPrompt, new Map([
    [PayByPhoneState.FinalPayAmountConfirm, ""],
  ])],
  [PayByPhoneState.FinalPayAmountConfirm, new Map([
    [PayByPhoneState.FinalPayAmountSubmitted, "Customer confirms"],
    [PayByPhoneState.FinalPayAmountPrompt, "No response or customer rejects, retries < max"],
    [PayByPhoneState.TransferToAgent, "No response, retries >= max"],
  ])],
  [PayByPhoneState.FinalPayAmountSubmitted, new Map([
    [PayByPhoneState.PaymentSuccessful, "Payment successful"],
    [PayByPhoneState.PaymentFailure, "Payment failed"],
  ])],
  [PayByPhoneState.PaymentSuccessful, new Map([
    [PayByPhoneState.DisconnectCall, ""],
  ])],
  [PayByPhoneState.PaymentFailure, new Map([
    [PayByPhoneState.TransferToAgent, ""],
  ])],
]);
