import { Injectable } from "@nestjs/common";
import { DomainEvent, IPublishDomainEvents, IPublishDomainEventsProvider } from "../../models/domain-event.model";

@Injectable()
export class DomainEventsMockClientProvider implements IPublishDomainEventsProvider {
  getClient(): IPublishDomainEvents {
    return new MockEventBridgeClientService();
  }
}

class MockEventBridgeClientService implements IPublishDomainEvents {
  domainEvents: DomainEvent[] = [];
  async publish(domainEvent: DomainEvent): Promise<DomainEvent> {
    this.domainEvents.push(domainEvent);
    return domainEvent;
  }
}