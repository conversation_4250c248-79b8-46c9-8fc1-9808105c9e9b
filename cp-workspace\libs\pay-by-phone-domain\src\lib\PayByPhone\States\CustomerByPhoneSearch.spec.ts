import { Test, TestingModule } from '@nestjs/testing';
import { LoopbackPayByPhoneStateHandlerResponse, PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { CustomerByPhoneSearch } from './CustomerByPhoneSearch';
import { Locale, CustomerSearch } from '@cp-workspace/shared';

jest.mock('@cp-workspace/shared', () => {
  return {
    ...jest.requireActual('@cp-workspace/shared'),
    CustomerSearch: jest.fn().mockImplementation(() => ({
      byPhoneNumber: jest.fn(),
    }))
  };
});

describe('CustomerByPhoneSearch', () => {
  let customerByPhoneSearch: CustomerByPhoneSearch;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerByPhoneSearch,
      ],
    }).compile();

    customerByPhoneSearch = module.get<CustomerByPhoneSearch>(CustomerByPhoneSearch);
    customerByPhoneSearch.services = {
      locationService: {
        getLocationConfiguration: jest.fn().mockResolvedValue({}),
      }
    } as any;

    context = {
      storage: {
        retryCount: 0,
        phoneNumber: '**********',
        locationId: 123,
        locale: Locale.English,
        matchedTenants: [],
        payByPhoneAllowed: true,
        state: PayByPhoneState.CustomerByPhoneSearch,
        transferToAgentUrl: 'exampleTransferToAgentUrl',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: {
        sayInLocale: jest.fn(),
        gather: jest.fn(),
        sayAccountFoundDetails: jest.fn()
      } as any
    };
  });

  describe('handler', () => {
    it('should gather input phone when phone number is undefined', async () => {
      context.storage.phoneNumber = undefined;

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({"locale": Locale.English, "messageId": "pay-by-phone.account-not-found"});
    });

    it('should gather input phone when phone number is too short (less than 10 characters)', async () => {
      context.storage.phoneNumber = '123456';

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({"locale": Locale.English, "messageId": "pay-by-phone.account-not-found"});
    });

    it('should gather input phone when phone number is empty string', async () => {
      context.storage.phoneNumber = '';

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({"locale": Locale.English, "messageId": "pay-by-phone.account-not-found"});
    });

    it('should prompt for input when could not find account', async () => {
      CustomerSearch.byPhoneNumber = jest.fn().mockResolvedValue({
        couldNotFindAccount: true,
        isPayByPhoneAllowed: jest.fn().mockResolvedValue(true)
      });

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({"locale": Locale.English, "messageId": "pay-by-phone.account-not-found"});
    });

    it('should transfer to agent when pay by phone is not allowed', async () => {
      CustomerSearch.byPhoneNumber = jest.fn().mockResolvedValue({
        couldNotFindAccount: false,
        items: [{ id: 'tenant1' }],
        isPayByPhoneAllowed: jest.fn().mockResolvedValue(false)
      });

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.InputPhoneGather);
      expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({"locale": Locale.English, "messageId": "pay-by-phone.not-allowed"});
    });

    it('should go to UnitsToPayPrompt when multiple units found', async () => {
      CustomerSearch.byPhoneNumber = jest.fn().mockResolvedValue({
        couldNotFindAccount: false,
        multipleUnitsFound: true,
        items: [{ id: 'tenant1' }, { id: 'tenant2' }],
        isPayByPhoneAllowed: jest.fn().mockResolvedValue(true)
      });

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrompt);
    });

    it('should go to ConfirmCustomerInfo when conditions are met', async () => {
      CustomerSearch.byPhoneNumber = jest.fn().mockResolvedValue({
        couldNotFindAccount: false,
        hmultipleUnitsFound: false,
        hasDelinquentUnits: false,
        items: [{ id: 'tenant1' }],
        isPayByPhoneAllowed: jest.fn().mockResolvedValue(true)
      });

      const response: LoopbackPayByPhoneStateHandlerResponse = await customerByPhoneSearch.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.ConfirmCustomerInfo);
    });


  });
});
