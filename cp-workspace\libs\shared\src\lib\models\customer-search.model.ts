import { IntegrationService } from "../services/integration.service";
import { LocationService } from "../services/location.service";
import { PagingData} from "./paging.model";
import { Tenant, TenantDataRequest, TenantDataResponse, TenantDataSuccessfulResponse } from "./tenant.model";
import { LocationId } from "./location-configuration.model";
import { TenantId } from "./tenant.model";
import { Ledger } from "./ledger.model";

export interface CustomerSearchServices {
  locationService: LocationService;
  integrationService: IntegrationService
}

export class CustomerSearch {
  static async byPhoneNumber(locationId: number, phoneNumber: string, services: CustomerSearchServices, isPayByPhone = false): Promise<CustomerSearchResults> {
    let matchOnlyByPrimaryNumber = false;
    if(isPayByPhone)
    {
      const locationConfiguration = await services.locationService.getLocationConfiguration(locationId);
      matchOnlyByPrimaryNumber = !locationConfiguration.paybyphone_all;
    }
    const tenantDataRequest: TenantDataRequest = {
      locationId: locationId,
      filterPhone: phoneNumber,
      filterExcludeAlternate: matchOnlyByPrimaryNumber,
      page: 1,
      perPage: 7,
    };
    const tenantDataResponse = await services.integrationService.getTenantData(tenantDataRequest);
    const successfulResponse = tenantDataResponse as TenantDataSuccessfulResponse;
    if (successfulResponse) { // request each tenant's ledger data in parallel
      const ledgerPromises = successfulResponse.items.map(tenant =>
        services.integrationService.getLedgerData(tenant._id, {skipCache: true})
      );
      const ledgerResults = await Promise.all(ledgerPromises);
      successfulResponse.items.forEach((tenant, index) => {
        tenant.ledgers = ledgerResults[index].items;
        tenant.ledgers.forEach(ledger => {
          ledger.tenant_id_es = tenant._id;
        });
      });
    }
    return new CustomerSearchResults(tenantDataResponse);
  }

  static async isPayByPhoneAllowed(locationId: LocationId, tenantId: TenantId, services: CustomerSearchServices): Promise<boolean> {
    const locationConfig = await services.locationService.getLocationConfiguration(locationId);
    const ledgerData = await services.integrationService.getLedgerData(tenantId, {skipCache: false});
    for(const ledger of ledgerData.items) {
      if (ledger.status === 'Current') break;

      const paidThruDate = new Date(ledger.paid_thru_date);
      const daysPastDue = Math.floor((Date.now() - paidThruDate.getTime()) / (1000 * 60 * 60 * 24));
      if (locationConfig.exclude_payment_link < daysPastDue) {
        return false;
      }
    }
    return true;
  }
}

export class CustomerSearchResults {
  items: Tenant[];
  paging?: PagingData;
  constructor(base: TenantDataResponse) {
    this.items = [];

    const successfulResponse = base as TenantDataSuccessfulResponse;
    if(successfulResponse) {
      this.items = successfulResponse.items;
      this.paging = successfulResponse.paging;
    }
  }

  get couldNotFindAccount(): boolean {
    return this.items.length < 1;
  }

  get multipleUnitsFound(): boolean {
    return this.items.length > 1 || this.items.some((item) => item.ledgers.length > 1);
  }

  get firstUnit(): Ledger {
    return this.items[0].ledgers[0];
  }

  get firstUnitTenant(): Tenant {
    return this.items[0];
  }

  get hasDelinquentUnits(): boolean {
    return (
      this.items &&
      this.items.some(
        (tenant) =>
          tenant.ledgers &&
          tenant.ledgers.some((ledger) => ledger.status !== 'Current')
      )
    );
  }

  public async isPayByPhoneAllowed(locationId: LocationId, locationService: LocationService): Promise<boolean> {
      const locationConfig = await locationService.getLocationConfiguration(locationId);
      for (const tenant of this.items) {
        for (const ledger of tenant.ledgers) {
          if (ledger.status === 'Current') break;

          const paidThruDate = new Date(ledger.paid_thru_date);
          const daysPastDue = Math.floor((Date.now() - paidThruDate.getTime()) / (1000 * 60 * 60 * 24));
          if (locationConfig.exclude_payment_link < daysPastDue) {
            return false;
        }
      }
    }
    return true;
  }
}