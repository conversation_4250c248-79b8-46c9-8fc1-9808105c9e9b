import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { CustomerSearch } from "@cp-workspace/shared";

@Injectable()
export class CollectionsPrompt extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { twilioResponse, storage } = context;

    const gatherOptions = {
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    };

    storage.payByPhoneAllowed = await CustomerSearch.isPayByPhoneAllowed(storage.locationId, storage.tenantId!, this.services);
    if (storage.payByPhoneAllowed) {
      twilioResponse.gatherWithLocaleSay(gatherOptions, [{
        messageId: 'pay-by-phone.collections-prompt-pbp-allowed',
        locale: storage.locale,
      }]);
    } else {
      twilioResponse.gatherWithLocaleSay(gatherOptions, [{
        messageId: 'pay-by-phone.collections-prompt-pbp-not-allowed',
        locale: storage.locale,
      }]);
    }
    return { nextState: PayByPhoneState.CollectionsConfirm };
  }
}

