import { Injectable, Inject } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { jest } from '@jest/globals';
import { CallPotentialHttpApiService } from './callpotential-http-api.service';
import { of } from 'rxjs';
import { DomainEventsService } from './domain-events.service';
import Bugsnag from '@bugsnag/js';

Bugsnag.start({
  apiKey: '616b106fc4dba0412968d8c0e91995be',
  releaseStage: 'test',
  appVersion: '1.0.0',
});

jest.spyOn(Bugsnag, 'start').mockImplementation(() => Bugsnag);
jest.spyOn(Bugsnag, 'leaveBreadcrumb').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'notify').mockImplementation(jest.fn());

export const API_URL_CONFIG_KEY_TOKEN = 'API_URL_CONFIG_KEY';

@Injectable()
class CallPotentialHttpApiServiceWrapper extends CallPotentialHttpApiService {
  constructor(
    httpService: HttpService,
    configService: ConfigService,
    domainEventService: DomainEventsService,
    @Inject(API_URL_CONFIG_KEY_TOKEN) apiUrlConfigKey: string,
  ) {
    super(httpService, configService, domainEventService, apiUrlConfigKey);
  }
}

describe('CallPotentialHttpApiService', () => {
  let service: CallPotentialHttpApiServiceWrapper;
  let httpService: HttpService;
  let configService: ConfigService;
  let domainEventService: DomainEventsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CallPotentialHttpApiServiceWrapper,
        HttpService,
        ConfigService,
        DomainEventsService,
        {
          provide: API_URL_CONFIG_KEY_TOKEN,
          useValue: 'API_URL_CONFIG_KEY',
        },
      ],
    })
    .overrideProvider(HttpService).useValue({
      get: jest.fn(),
      post: jest.fn(),
    })
    .overrideProvider(ConfigService).useValue({
      get: jest.fn((key: string) => {
        if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
        if (key === 'API_URL_CONFIG_KEY') return 'http://api.example.com';
        return null;
      }),
    })
    .overrideProvider(DomainEventsService).useValue({
      publish: jest.fn(),
    })
    .compile();

    service = module.get<CallPotentialHttpApiServiceWrapper>(CallPotentialHttpApiServiceWrapper);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    domainEventService = module.get<DomainEventsService>(DomainEventsService);
  });

  describe('Initialization', () => {
    it('should throw an error if db.serviceTokens.readWrite is not defined', () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(undefined);
      expect(() => new CallPotentialHttpApiServiceWrapper(httpService, configService, domainEventService, 'API_URL_CONFIG_KEY_INVALID'))
        .toThrow('db.serviceTokens.readWrite must be defined');
    });

    it('should throw an error if API URL config key is not defined', () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce('valid_token').mockReturnValueOnce(undefined);
      expect(() => new CallPotentialHttpApiServiceWrapper(httpService, configService, domainEventService, 'API_URL_CONFIG_KEY_INVALID'))
        .toThrow('API_URL_CONFIG_KEY_INVALID must be defined');
    });
  });

  describe('HTTP Methods', () => {
    it('should execute a GET request with correct parameters', async () => {
      const mockGet = jest.spyOn(httpService, 'get');
      const mockResponse = {};

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      await service['get']('/test', { param1: 'value1' });
      expect(mockGet).toHaveBeenCalledWith('http://api.example.com/test?param1=value1', {
        headers: { Authorization: 'dummy_auth_token' }
      });
    });

    it('should execute a POST request with correct parameters', async () => {
      const mockPost = jest.spyOn(httpService, 'post');
      const mockResponse = {};

      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      await service['post']('/test', { key: 'value' });
      expect(mockPost).toHaveBeenCalledWith('http://api.example.com/test', { key: 'value' }, {
        headers: { Authorization: 'dummy_auth_token' }
      });
    });
  });

  describe('Utility Functions', () => {
    it('should construct a correct query string from basic parameters', () => {
      const queryString = service['constructQueryString']({ param1: 'value1', param2: 123 });
      expect(queryString).toBe('param1=value1&param2=123');
    });
  
    it('should handle array parameters in query string', () => {
      const queryString = service['constructQueryString']({ 
        param1: 'value1', 
        arrayParam: ['value2', 'value3'] 
      });
      expect(queryString).toBe('param1=value1&arrayParam[]=value2&arrayParam[]=value3');
    });
  
    it('should properly encode special characters in query string', () => {
      const queryString = service['constructQueryString']({ 
        param1: 'value with spaces', 
        param2: 'special&chars?', 
        arrayParam: ['value/1', 'value=2'] 
      });
      expect(queryString).toBe(
        'param1=value%20with%20spaces&param2=special%26chars%3F&arrayParam[]=value%2F1&arrayParam[]=value%3D2'
      );
    });
  
    it('should filter out undefined parameters', () => {
      const queryString = service['constructQueryString']({ 
        param1: 'value1', 
        param2: undefined, 
        arrayParam: ['value2', 'value3'] 
      });
      expect(queryString).toBe('param1=value1&arrayParam[]=value2&arrayParam[]=value3');
    });
  
    it('should return correct headers', () => {
      const headers = service['getHeaders']();
      expect(headers).toEqual({ Authorization: 'dummy_auth_token' });
    });
  });
});
