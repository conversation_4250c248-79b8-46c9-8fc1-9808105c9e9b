import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AccountService } from './account.service';
import { jest } from '@jest/globals';
import { CustomerExclusion } from '../models/customer-exclusion.model';
import { DomainEventsService } from './domain-events.service';
import { DomainEventsClientType, DomainEventsIntegrationModule } from '../integrations/event-bridge/domain-events.integration.module';
import { DomainEventsIntegrationService } from '../integrations/event-bridge/domain-events.integration.service';
import { Observable, of } from 'rxjs';

describe('AccountService', () => {
  let service: AccountService;
  let httpService: HttpService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        DomainEventsIntegrationModule.forRoot({
          clientType: DomainEventsClientType.IN_MEMORY,
          options: { enabled: true },
        })
      ],
      providers: [
        AccountService,
        HttpService,
        ConfigService,
        DomainEventsService,
        DomainEventsIntegrationService,
      ],
    })
      .overrideProvider(HttpService)
      .useValue({
        get: jest.fn(),
        post: jest.fn(),
      })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
          if (key === 'API_ACCT_URL') return 'http://api.example.com';
          return '';
        }),
      })
      .compile();

    service = module.get<AccountService>(AccountService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
  });

  describe('createCustomerExclusion', () => {
    it('should create a customer exclusion and make a POST request with correct endpoint and body', async () => {
      const request: CustomerExclusion = {
        location_id: 1,
        contact_type: 'collection',
        exclusion_type: 'call',
        excluded_contact: 'John Doe',
        reason: 'Auto: Received Stop Request',
        request_sid: '123456',
        entity_type: 'customer',
      };
      const expectedEndpoint = 'customerexclusion';
      const expectedBody = {
        ...request,
        exclusion_date: expect.any(String),
      };

      jest.spyOn(httpService, 'post').mockImplementation((url: string, data?: any, config?: any): Observable<any> => {
        return of({ data: 'success' }); // Replace 'success' with the desired return value
      });
      const response = await service.createCustomerExclusion(request);

      expect(httpService.post).toHaveBeenCalledWith(
        'http://api.example.com/customerexclusion',
        expectedBody,
        {"headers": {"Authorization": "dummy_auth_token"}}
      );
    });
  });
});
