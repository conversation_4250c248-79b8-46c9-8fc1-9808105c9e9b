import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodPrompt extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    const { twilioResponse, storage } = context;
    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      finishOnKey: '*',
      numDigits: 4,
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.ask-saved-cc',
      locale: storage.locale
    }]);
    
    return { nextState: PayByPhoneState.PayMethodSelection };
  }
}
