const cpapiClient = require('./cpapi-client');
const config = require('../config/config');

async function updateCallDetail(callDetail) {
  const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
  const { log_id } = callDetail;
  if (!log_id) throw new Error('log_id is required');
  
  return await callClient.putData(`calldetail/${log_id}`, callDetail);
}

module.exports = {
  updateCallDetail
}
