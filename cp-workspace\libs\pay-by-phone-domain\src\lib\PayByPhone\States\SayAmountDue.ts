import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { Customer } from "@cp-workspace/shared";

@Injectable()
export class SayAmountDue extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;

    storage.totalAmountDue = Customer.calculateBalanceDue(storage.selectedUnits!);

    twilioResponse.sayInLocale({
      messageId: 'pay-by-phone.amount-due',
      locale: context.storage.locale,
      i18nOptions: { args: [{ paymentAmount: (storage.totalAmountDue + storage.convenienceFee).toFixed(2) }] }
    });
    return { nextState: PayByPhoneState.GetSavedCards };
  }
}