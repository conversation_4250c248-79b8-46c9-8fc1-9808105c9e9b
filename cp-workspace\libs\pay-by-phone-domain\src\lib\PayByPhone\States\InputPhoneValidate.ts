import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { PayByPhoneStateRetryHandler } from "./_Utils";

@Injectable()
export class InputPhoneValidate extends PayByPhoneStateBase {
 @PayByPhoneStateRetryHandler(
    3,
    PayByPhoneState.InputPhoneGather,
    [PayByPhoneState.InputPhoneValidate],
    PayByPhoneState.TransferToAgent,
    "pay-by-phone.max-retry")
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const  { request, twilioResponse, storage } = context;

    const phoneNumberInput = request.Digits;
    const noResponseFromCustomer =
      !phoneNumberInput || phoneNumberInput.length === 0;
    if (noResponseFromCustomer) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.must-enter-ten-digits',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.InputPhoneGather };
    }

    const speakToManager = phoneNumberInput === '0';
    if (speakToManager) {
      return { nextState: PayByPhoneState.TransferToAgent };
    }

    const validPhoneNumber =
      phoneNumberInput.length === 10 && !isNaN(Number(phoneNumberInput));
    if (!validPhoneNumber) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.InputPhoneGather };
    }

    storage.phoneNumber = phoneNumberInput;
    twilioResponse.sayInLocale({
      messageId: 'pay-by-phone.wait-for-account-fetch',
      locale: context.storage.locale
    });
    return { nextState: PayByPhoneState.CustomerByPhoneSearch };
  }
}
