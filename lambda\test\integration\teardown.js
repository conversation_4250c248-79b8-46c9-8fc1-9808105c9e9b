const AWS = require('aws-sdk');
testData = require('../testDataSet.json');
const config = require('../../config/config');
AWS.config.update({ region: 'us-west-2' });
const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
const tableName = config.dynamodb.acctTable;
const locationId = testData.twilio_account.location_id;
const mysql = require('mysql');

module.exports = async () => {
  console.info('Tearing down account after test cases')

  let con = mysql.createConnection({
    host: config.db.connection.host,
    user: config.db.connection.user,
    password: config.db.connection.password,
    database: config.db.connection.database
  });
  
  await con.connect(async function(err) {
    if (err) console.log(err)
    let sql = `DELETE FROM locations WHERE location_id = ${locationId}`;
    await con.query(sql, function (err, result) {
      if (err) console.log(err); else console.log("location deleted ", locationId)
    });

    await con.end()

  });

  // delete twilio account
  var params = {
    TableName: tableName,   
    Key: {
      'id': testData.twilio_account.id 
    }
  };

  await dynamo.delete(params, function(err, data) {
    if (err) {
      console.log("Twilio setup deletion error in dynamodb", err);
    } else {
      console.log("Twilio setup deleted  in dynamodb");
    }
  });
}

