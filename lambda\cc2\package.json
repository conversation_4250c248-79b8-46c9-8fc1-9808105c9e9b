{"name": "lambda-function-gateway-twillio", "version": "1.1.0", "description": "Handle twillio callbacks.", "main": "lambda.js", "license": "Apache-2.0", "config": {"accountId": "************", "s3BucketName": "lambda-cp-4014123", "lambdaFunctionName": "qa-121_twilio", "cloudFormationStackName": "lambdaFunctionGatewayTwillio", "region": "us-west-2", "lambdaMemorySize": 512}, "scripts": {}, "dependencies": {"got": "^8.3.1", "jsonwebtoken": "^8.1.0", "lodash": "^4.17.5", "moment-timezone": "^0.5.21", "twilio": "3.37.0", "redis": "^4.2.0"}, "devDependencies": {"eslint": "^4.18.2", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.9.0"}, "optionalDependencies": {"aws-sdk": "^2.207.0"}}