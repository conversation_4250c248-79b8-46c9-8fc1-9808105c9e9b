import { OverrideByFactoryOptions, Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { LocationService } from './location.service';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RedisService } from './redis.service';
import { of } from 'rxjs';
import { LocationConfiguration } from '../models/location-configuration.model';
import { DomainEventsService } from './domain-events.service';

describe('LocationService', () => {
  let service: LocationService;
  let httpService: HttpService;
  let configService: ConfigService;
  let domainService: DomainEventsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [RedisModule],
      providers: [RedisService, LocationService, HttpService, ConfigService, DomainEventsService],
    })
      .overrideProvider(RedisService)
      .useFactory({
        factory: () => {
          const mockService = {
            getClient: jest.fn(),
          };
          const mockRedis = {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
          };
          mockService.getClient.mockReturnValue(mockRedis);
          return mockService;
        },
      } as OverrideByFactoryOptions)
      .overrideProvider(HttpService)
      .useValue({
        get: jest.fn(),
        post: jest.fn(),
      })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
          if (key === 'API_LOC_URL') return 'http://api.example.com';
          return '';
        }),
      })
      .overrideProvider(DomainEventsService)
      .useValue({
        publish: jest.fn(),
      })
      .compile();

    service = module.get<LocationService>(LocationService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    domainService = module.get<DomainEventsService>(DomainEventsService);
  });

  describe('getLocationConfiguration', () => {
    it('should fetch location configuration with correct parameters', async () => {
      const mockResponse: LocationConfiguration = {} as LocationConfiguration;

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));

      const locationId = 1;
      const requestParams = { formatFields: false };
      await service.getLocationConfiguration(locationId, requestParams);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/locationconfiguration/${locationId}?formatFields=false`,
        {
          headers: {
            Authorization: 'dummy_auth_token',
          },
        }
      );
    });

    it('should fetch location configuration with default parameters when none are provided', async () => {
      const mockResponse = {
        data: {
          status: "OK",
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      const locationId = 1;
      await service.getLocationConfiguration(locationId);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/locationconfiguration/${locationId}`,
        {
          headers: {
            Authorization: 'dummy_auth_token',
          },
        }
      );
    });
  });
});
