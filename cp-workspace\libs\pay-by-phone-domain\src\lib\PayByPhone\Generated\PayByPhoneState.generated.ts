
/**
 * This file was auto-generated by the state-diagram-code generator.
 * DO NOT MODIFY THIS FILE DIRECTLY.
 * To make changes, modify the source diagram file and re-run the generator.
 * Diagram source: /libs/pay-by-phone-domain/src/.diagrams/PayByPhone.state.mmd
 * Generated at: 2025-02-26T20:51:04.842Z
 */
export enum PayByPhoneState {
  LocalePrompt = "LocalePrompt",
  LocaleConfirm = "LocaleConfirm",
  CollectionsPrompt = "CollectionsPrompt",
  CollectionsConfirm = "CollectionsConfirm",
  SayAmountDue = "SayAmountDue",
  TransferToAgent = "TransferToAgent",
  CustomerOptOut = "CustomerOptOut",
  DisconnectCall = "DisconnectCall",
  InputPhoneGather = "InputPhoneGather",
  InputPhoneValidate = "InputPhoneValidate",
  CustomerByPhoneSearch = "CustomerByPhoneSearch",
  UnitsToPayPrompt = "UnitsToPayPrompt",
  ConfirmCustomerInfo = "ConfirmCustomerInfo",
  UnitsToPayPrepayMonthsPrompt = "UnitsToPayPrepayMonthsPrompt",
  UnitsToPayPrepayMonthsSelection = "UnitsToPayPrepayMonthsSelection",
  GetSavedCards = "GetSavedCards",
  PayMethodPrompt = "PayMethodPrompt",
  PayMethodCreditCardPrompt = "PayMethodCreditCardPrompt",
  UnitsToPaySelection = "UnitsToPaySelection",
  UnitsToPayPrepayMonthsConfirm = "UnitsToPayPrepayMonthsConfirm",
  PayMethodSelection = "PayMethodSelection",
  ExistingPayMethodVerify = "ExistingPayMethodVerify",
  PayMethodSecurityCodePrompt = "PayMethodSecurityCodePrompt",
  PayMethodCreditCardValidate = "PayMethodCreditCardValidate",
  PayMethodCreditCardConfirm = "PayMethodCreditCardConfirm",
  PayMethodExpirationPrompt = "PayMethodExpirationPrompt",
  PayMethodExpirationValidate = "PayMethodExpirationValidate",
  PayMethodExpirationConfirm = "PayMethodExpirationConfirm",
  PayMethodSecurityCodeValidate = "PayMethodSecurityCodeValidate",
  PayMethodSecurityCodeConfirm = "PayMethodSecurityCodeConfirm",
  PayMethodPostalCodePrompt = "PayMethodPostalCodePrompt",
  FinalPayAmountPrompt = "FinalPayAmountPrompt",
  PayMethodPostalCodeValidate = "PayMethodPostalCodeValidate",
  PayMethodPostalCodeConfirm = "PayMethodPostalCodeConfirm",
  FinalPayAmountConfirm = "FinalPayAmountConfirm",
  FinalPayAmountSubmitted = "FinalPayAmountSubmitted",
  PaymentSuccessful = "PaymentSuccessful",
  PaymentFailure = "PaymentFailure",
}
