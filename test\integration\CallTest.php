<?php
namespace Test;

class CallTest extends IntegrationCase
{
    public function testCallGeyByIdAction()
    {
        $this->validateSwaggerPath(
            'call',
            'CallGetById',
            '/call/{location_id}/{twilio_id}',
            'get'
        );
        $response = $this->runGET('/call/135/CA046b65e58134c5c7ffd54fa1526ad186');
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/call/135/CA046b65e58134c5c7ffd54fa1526ad186 GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'CallGetById', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'CallGetById', [200, 404, 500]);
        
    }

    /*  API is not implemented yet

    public function testCallListWithoutPagination()
    {
        $this->validateSwaggerPath(
            'call',
            'ListCalls',
            '/call',
            'get'
        );

        $params = $this->getListParams(1);
        $response = $this->runGET('/call', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/call GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'ListCalls', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'ListCalls', [200,401,403,500]);
    }*/

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }

    /**
     * @param integer $case
     * @return array
     */
    private function getListParams($case = 0) :array
    {
        switch ($case) {
            case 1:
                return [
                    'page' => 'false'
                ];
                break;
            case 2:
                return [
                    'page' => 1,
                    'perPage' => 20
                ];
                break;
            default:
                return [];
                break;
        }
    }
}
