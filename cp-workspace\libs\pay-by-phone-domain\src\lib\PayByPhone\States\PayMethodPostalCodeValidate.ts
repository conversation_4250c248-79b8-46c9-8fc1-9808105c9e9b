import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import * as CardValidator from 'card-validator';
import { PaymentToken } from '@cp-workspace/shared';
@Injectable()
export class PayMethodPostalCodeValidate extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. If postal code invalid
     * 1a. Compose TwiML to say it’s invalid
     * 1b. Transition to PayMethodPostalCodePrompt
     * 2. If postal code is valid
     * 2a. Compose TwiML to say postal code and confirm it is correct
     * 2b. Compose gather TwiML to gather the input
     * 2c. Transition to PayMethodPostalCodeConfirm
     */

    const { request, twilioResponse, storage } = context;
    const enteredZip = request.Digits;
    const coreClient = this.services.coreService;
    const allowedDigits = 5;

    if (!enteredZip || !(allowedDigits == enteredZip.length)) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.PayMethodPostalCodePrompt };
    }

    const requestToken: PaymentToken = {
      postalCode: enteredZip,
    };

    storage.paymentToken!.postalCode = (await coreClient.encodePaymentToken(requestToken)).postalCode;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      finishOnKey: '*',
      numDigits: 1,
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.zip-confirm',
      locale: storage.locale,
      i18nOptions: { args: [{ enteredZip: enteredZip.split('').join(' ') }] }
    }]);


    return { nextState: PayByPhoneState.PayMethodPostalCodeConfirm };
  }
}
