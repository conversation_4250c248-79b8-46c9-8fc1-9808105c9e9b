/**
 * This bootstraps the application for local development.
 * For example, the lambda-function-e2e project uses this
 * to run the application locally for end-to-end tests.
 */
import { INestApplication, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

import { AppLocalModule } from './app/app-local.module';

let cachedServer: INestApplication;

export async function bootstrapLocalServer() {
  if (!cachedServer) {
    const app = await NestFactory.create(AppLocalModule);
    const globalPrefix = 'v1';
    app.setGlobalPrefix(globalPrefix);
    const port = process.env.PORT || 3000;
    await app.listen(port);
    Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
    cachedServer = app;
  }
  return cachedServer;
}
