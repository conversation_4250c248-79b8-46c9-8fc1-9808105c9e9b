{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "module": "commonjs", "types": ["node"], "declaration": true, "emitDecoratorMetadata": true, "target": "es6", "composite": true, "baseUrl": ".", "paths": {"@cp-workspace/pay-by-phone-domain": ["../../libs/pay-by-phone-domain/src/index.ts"], "@cp-workspace/shared": ["../../libs/shared/src/index.ts"]}}, "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "../../libs/**/*.spec.ts", "../../libs/**/*.test.ts", "../../libs/**/PayByPhoneTest.module.ts"], "include": ["src/**/*.ts", "../../libs/**/*.ts"]}