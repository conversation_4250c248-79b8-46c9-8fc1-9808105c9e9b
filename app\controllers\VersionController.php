<?php
/**
 * Retieve current git branch
 *
 * PHP version 7.0.1
 *
 * @category VersionController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;

/**
 * Version swagger defination
 *
 * @SWG\Definition(type="object",definition="Version",
 * @SWG\Property(property="items",type="string"),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData")
 * )
 */

/**
 * Retieve current git branch
 *
 * @category VersionController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */

class VersionController extends CallPotential\CPCommon\Controllers\BaseController
{
    use CallPotential\CPCommon\Controllers\SessionTrait;
    use CallPotential\CPCommon\Controllers\NoDataTrait;

    /**
     * Public Endpoint By pass token validation
     *
     * @var mixed
     */
    protected $publicEndpoint = true;

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Version Management"},
     *     path="/version",
     *     description="Returns git branches and tags",
     *     summary="branches and tags",
     *     operationId="GetVersion",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header",
     *     required=false
     * ),
     * @SWG\Response(
     *     response=200,
     *     description="A list of branches and tags",
     *     *@SWG\Schema(ref="#/definitions/Version")
     *     ),
     * @SWG\Response(
     *     response="404",
     *     description="Not Found",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),
     * @SWG\Response(
     *     response=500,
     *     description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return current branch of service
     */
    public function listAction()
    {
        $result = Util::getCurrentVersion();
        $this->response->setJsonContent($result);

        return $this->response;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data for checking delete access
     *
     * @return boolean
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);

        return $this->validSession();
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data checkig read access
     *
     * @return boolean
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return $this->validSession();
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data checking write access
     *
     * @return boolean
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return $this->validSession();
    }
}
