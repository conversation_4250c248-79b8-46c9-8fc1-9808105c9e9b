import {
  PayByPhoneRequestBody,
  PayByPhoneService,
  storageKey,
} from '@cp-workspace/pay-by-phone-domain';
import {
  Controller,
  Post,
  UseInterceptors,
  BadRequestException,
  InternalServerErrorException,
  Header,
  Req,
  HttpStatus,
} from '@nestjs/common';
import { Request } from 'express';
import { PayByPhoneRequestInterceptor } from '../Interceptors/PayByPhoneRequestInterceptor';
import { DataDogService, BugsnagService } from '@cp-workspace/shared';

@Controller('pay-by-phone')
@UseInterceptors(PayByPhoneRequestInterceptor)
export class PayByPhoneController {
  constructor(
    private payByPhoneService: PayByPhoneService,
    private dataDogService: DataDogService,
    private bugsnagService: BugsnagService
  ) {}

  /**
   * When a request is made to the pay-by-phone API endpoint, this method will be called.
   * It will execute the core logic of the pay-by-phone process and return a TwiML HTTP response.
   * @param request The HTTP request
   * @returns A TwiML HTTP response
   */
  @Post()
  @Header('Content-Type', 'text/xml')
  async handlePayByPhoneRequest(@Req() request: Request) {
    try {
      const requestBody = request.body as PayByPhoneRequestBody;
      requestBody.Source = 'request';
      requestBody.StorageKeyPrefix = '';
      this.bugsnagService.leaveBreadcrumb('Handling pay-by-phone request', { callSid: requestBody.CallSid });
      return this.handlePayByPhone(requestBody);
    } catch (error) {
      this.bugsnagService.notify(error);
      throw new InternalServerErrorException('Server error');
    }
  }

  /**
   * When a long running state handler is invoked asynchronously, this method will be called.
   * It will execute the core logic of the pay-by-phone process and store the result in the storage service.
   * @param requestBody Contains the pay-by-phone request body
   * @returns A standard 200 status code to indicate that the async invocation was successful.
   * @remarks The result of the async invocation will be stored in the storage service and another
   * process will observe the status of this async process and take appropriate action.
   */
  async handlePayByPhoneAsyncInvocation(event: { source: string, body: PayByPhoneRequestBody }): Promise<boolean> {
    const requestBody = event.body as PayByPhoneRequestBody;
    requestBody.Source = 'async-invocation';
    requestBody.StorageKeyPrefix = 'in_progress_';
    this.bugsnagService.leaveBreadcrumb('Handling async invocation', { source: event.source });

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let twimlResponse: string, error: any;
    try {
      twimlResponse = await this.handlePayByPhone(requestBody);
    } catch (err) {
      error = err;
      this.bugsnagService.notify(err);
    }

    try {
      /**
       * We will save the twimlResponse and error to the storage service so that
       * another process can observe the status of this async process and
       * take appropriate action.
       */
      const storage = await this.payByPhoneService.storageService.getStorage(storageKey(requestBody));
      storage.stateInProgressResult = {};
      if (error) {
        storage.stateInProgressResult.error = error;
      }
      if (twimlResponse) {
        storage.stateInProgressResult.twimlResponse = twimlResponse;
      }
      await this.payByPhoneService.storageService.saveStorage(storageKey(requestBody), storage);
    } catch (err) {
      this.bugsnagService.notify(err);
      console.error('Error saving storage: ', err);
    }

    /**
     * IMPORTANT: We expect that another process is observing the status
     * of this async invocation AND we don't want AWS to retry this async
     * invocation in the event of an error.  We will return a 200 status
     * code to AWS to indicate that the async invocation was successful.
     *
     * Returning `true` here will result in a 200 status code.
     */
    return true;
  }

  /**
   * This is the core logic of the pay-by-phone process.  It will execute the state machine
   * and return a TwiML string.  Additional state is stored in storage.
   * @param requestBody Contains the pay-by-phone request body
   * @returns A TwiML string
   */
  async handlePayByPhone(requestBody: PayByPhoneRequestBody): Promise<string> {
    const start = Date.now();
    this.bugsnagService.leaveBreadcrumb('Processing pay-by-phone', { callSid: requestBody.CallSid });

    if (!requestBody.CallSid) {
      this.dataDogService.incrementCounter('pay_by_phone.request.total', [`status_code:${HttpStatus.BAD_REQUEST}`]);
      throw new BadRequestException('Bad Request');
    }

    const isFirstRequest = await this.payByPhoneService.isFirstRequest(storageKey(requestBody));
    if (isFirstRequest && (!requestBody.LocationId || !requestBody.TransferToAgentUrl)) {
      this.dataDogService.incrementCounter('pay_by_phone.request.total', [`status_code:${HttpStatus.BAD_REQUEST}`]);
      throw new BadRequestException('Bad Request');
    }

    try {
      const twilioResponse: string = await this.payByPhoneService.handleRequest(requestBody);
      const duration = Date.now() - start;
      this.dataDogService.recordDistribution('pay_by_phone.request.duration', duration);
      this.dataDogService.incrementCounter('pay_by_phone.request.total', [`status_code:${HttpStatus.CREATED}`]);
      return twilioResponse;
    } catch (error) {
      this.bugsnagService.notify(error);
      const duration = Date.now() - start;
      this.dataDogService.recordDistribution('pay_by_phone.request.duration', duration);
      this.dataDogService.incrementCounter('pay_by_phone.request.total', [`status_code:${HttpStatus.INTERNAL_SERVER_ERROR}`]);
      await this.payByPhoneService.logPayByPhoneEnd(requestBody, 'runtime_error');
      console.error('PayByPhoneController caught error: ', error);
      throw new InternalServerErrorException('Server error');
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  logStdErr(message?: any, ...optionalParams: any[]) {
    console.error(message, optionalParams);
  }
}