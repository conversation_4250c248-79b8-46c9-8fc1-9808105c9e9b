name: Swagger Schema Validation
on:
  pull_request:
    branches:
      - "**"
jobs:
  Pipeline:
    runs-on: ubuntu-latest
    steps:
      - name: PHP Setup
        uses: shivammathur/setup-php@v2
        with:
          php-version: "7.4"
          extensions: json, openssl, fileinfo, zip, gd, psr, phalcon-4.0.6

      - uses: actions/checkout@v1

      - name: Set up Composer authentication
        env:
          COMPOSER_AUTH: |
            {
              "github-oauth": {
                "github.com": "${{ secrets.COMPOSER_GITHUB_PAT }}"
              }
            }
        run: composer install --no-interaction --prefer-dist --optimize-autoloader
  
      - name: Swagger Validation
        run: vendor/bin/phpunit --configuration test/swaggerValidation.xml
