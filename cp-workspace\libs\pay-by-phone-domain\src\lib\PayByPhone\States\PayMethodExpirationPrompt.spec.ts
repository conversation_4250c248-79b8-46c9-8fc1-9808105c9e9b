import { PayMethodExpirationPrompt } from './PayMethodExpirationPrompt';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodExpirationPrompt', () => {
  let service: PayMethodExpirationPrompt;

  beforeEach(() => {
    service = new PayMethodExpirationPrompt();
  });

  it('should prompt for credit card expiration date and transition to PayMethodExpirationValidate', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1222',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      finishOnKey: "*",
      timeout: 10
    }, [{
      messageId: "pay-by-phone.enter-expiry",
      locale: context.storage.locale
    }]);
    expect(result.nextState).toBe(PayByPhoneState.PayMethodExpirationValidate);
  });
});
