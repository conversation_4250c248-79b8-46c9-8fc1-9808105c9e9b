{
  "task_routing": {
    "filters": [
      __QUEUE_FILTERS__,
      {
        "targets": [
          {
            "queue": "__OUTBOUND_QUEUE__",
            "expression": "task.agent_id == worker.agent_id AND worker.channel.custom1.assigned_tasks == 0 ",
            "priority": "1000",
            "timeout": "10"
          }
        ],
        "filter_friendly_name": "outbound",
        "expression": "taskchannel == 'custom1' AND direction == 'outbound'"
      }
    ]
  }
}