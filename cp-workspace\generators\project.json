{"name": "generators", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "generators/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/generators", "main": "generators/src/index.ts", "tsConfig": "generators/tsconfig.lib.json", "assets": ["generators/*.md"]}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "generators/jest.config.ts", "passWithNoTests": true, "codeCoverage": true, "testPathPattern": ["generators/src/lib"], "coverageDirectory": "{workspaceRoot}/coverage/generators", "coverageReporters": ["lcov", "text"]}, "dependsOn": ["generators:build"]}}}