<?php
/**
 * Event handler for call logs update
 *
 * @category CallHistoryUpdateEventHandler
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\BaseEventHandler;
use CallPotential\CPCommon\EventHandler;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\LoggerTrait;

/**
 * Event handler for call logs update
 *
 * @category CallHistoryUpdateEventHandler
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class CallHistoryUpdateEventHandler extends BaseEventHandler implements EventHandler
{
    use LoggerTrait;

    /**
     * Event Data
     *
     * @var private
     */
    private $eventData;

    /**
     * Handle send notification to location email address
     *
     * @param array $eventData array
     *
     * @return bool
     */
    public function handle(array $eventData) : bool
    {
        $this->eventData = $eventData;

        return $this->updateCallHistory();
    }

    /**
     * Return success debug message
     *
     * @return string
     */
    public function getSuccessMessage() : string
    {
        return "Call logs updated successfully";
    }

    /**
     * Return failure error message
     *
     * @return string
     */
    public function getFailureMessage() : string
    {
        return "Problem in update call logs";
    }

    /**
     * Update call log records
     *
     * @return bool
     */
    private function updateCallHistory() : bool
    {
        $beforeData = $this->eventData["before"];
        $afterData = $this->eventData["after"];
        $token = $afterData['token'];
        $esLeadId = $afterData['es_lead_id'];
        $esTenantId = $afterData['es_tenant_id'];

        if (!empty($afterData) && $afterData["version_id"]) {
            $model = new CallHistory();
            $model->setIndexSuffix($afterData["datestamp"]);
            $model->updateByIdAndVersion($beforeData['twilio_id'], $afterData);
            $model->setIndexSuffix(null);
            $this->updateReleventCallHistory($afterData);

            //Save Record to Audit log if one of lead id or tenant id is
            //not empty, will get it during api request, which indicate
            //that respective fields are empty in ES records
            if (empty($esLeadId) && empty($esTenantId)) {
                return true;
            }

            $acctClient = ClientFactory::getAcctClient($token);

            $callTypesForAuditLog = [
                'inbound_lead',
                'inbound_lead_payment',
                'inbound_customer',
                'inbound_collection',
                'inbound_payment',
            ];

            $leadCallTypes = [
                'inbound_lead',
                'inbound_lead_payment',
            ];

            $customerCallTypes = [
                'inbound_customer',
                'inbound_collection',
                'inbound_payment',
            ];

            $leadIdChange = false;
            $tenantIdChange = false;

            if (in_array($afterData['call_type'], $leadCallTypes)) {
                $entityType = 'lead';

                if (empty($esLeadId)) {
                    $this->infoMessage("Calllog event data : ". json_encode($this->eventData));
                    $this->infoMessage("Unable to determine valid leadId for audit log entry");

                    return true;
                }

                if ($afterData['es_lead_id'] !== $beforeData['es_lead_id']) {
                    $leadIdChange =  true;
                }
            } elseif (in_array($afterData['call_type'], $customerCallTypes)) {
                $entityType = 'customer';

                if (empty($esTenantId)) {
                    $this->infoMessage("Calllog event data : ". json_encode($this->eventData));
                    $this->infoMessage("Unable to determine valid esTenantId for audit log entry");

                    return true;
                }

                if ($afterData['es_tenant_id'] !== $beforeData['es_tenant_id']) {
                    $tenantIdChange =  true;
                }
            } else {
                return true;
            }

            if (! ($afterData['recording_duration'] > 0
                    && in_array($afterData['call_type'], $callTypesForAuditLog)
                    && ($afterData['call_type'] !== $beforeData['call_type']
                        || $tenantIdChange
                        || $leadIdChange ))) {
                return true;
            }

            $linkType = '';
            $recording_url = '';
            if (!empty($afterData['recording_url'])) {
                $recording_url = $afterData['callService'] . '/recording/' .
                $afterData['twilio_id'];
                $linkType = 'recording';
            }

            $logType = $afterData['channel'] ===
            'video' ? 'inbound_video_call' : 'inbound_call';

            $payload =  [
                'logType' => $logType,
                'logMessage' => '',
                'logOrigin' => 'inbound',
                'entityType' => $entityType,
                'esLeadId' => $esLeadId,
                'esCustomerId' => $esTenantId,
                'esLedgerId' => '',
                'esUnitId' => '',
                'employeeId' => $afterData['employee_id'],
                'employeeName' => $afterData['employee_name'],
                'phoneNumber' => $afterData['call_number'],
                'callDuration' => $afterData['call_duration'],
                'callOutcome' => 'answered',
                'link' => $recording_url,
                'linkText' => '',
                'linkType' => $linkType,
                'message' => '',
                'emailBody' => '',
                'emailSubject' => '',
                'emailTo' => '',
                'emailFrom' => '',
                'accountId' => $afterData['account_id'],
                'locationId' => $afterData['location_id'],
                'meta' => ["callSid" => $afterData['twilio_id']],
                'date' => $afterData['datestamp'],
            ];

            try {
                $acctClient->callAuditLogNew($payload);
            } catch (Exception $e) {
                $logType = $payload['logType'];
                $this->errorMessage("Request payload : ". json_encode($payload));
                $this->errorMessage(
                    "Insert $logType audit log returned in error response ". $e->getMessage()
                );
            }
        }

        return true;
    }

    /**
     * Update relevant call history records
     *
     * @param array $data Param data
     *
     * @return void
     */
    private function updateReleventCallHistory(array $data) : void
    {
        $callClient = ClientFactory::getCallClient($data["token"]);
        $callLogDetails = [];
        $updatedData = [
            'fk_lead_id' => $data['fk_lead_id'],
            'customer_id' => $data['customer_id'],
        ];

        $date = strtotime(date('Y-m-d').'-1 year');
        $params = [
            'callNumber' => $data['call_number'],
            'locationId' => $data['location_id'],
            'listType' => 'unprocessed',
            'startDate' => date('Y-m-d', $date),
            'endDate' => date('Y-m-d'),
        ];

        if (!empty($data['call_number'])) {
            $callLogDetails = (array) $callClient->getCallHistory($params);
        }

        if (!empty($callLogDetails['items'])) {
            foreach ($callLogDetails['items'] as $callLog) {
                $callLog = (array) $callLog;
                if ($callLog['log_id'] === $data['log_id']) {
                    continue;
                }
                $callDetailData = CallDetail::findFirst(
                    [
                        "conditions" => "log_id = ".$callLog['log_id'],
                    ]
                );
                if ($callDetailData) {
                    foreach ($updatedData as $k => $v) {
                        if ($k === "call_type"
                            && $callDetailData->$k !== "inbound"
                            && $v === "inbound"
                        ) {
                            continue;
                        }
                        $callDetailData->$k = $v;
                    }
                    $callDetailData->save();
                }
            }
        }
    }
}
