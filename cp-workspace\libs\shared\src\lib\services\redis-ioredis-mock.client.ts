import { Injectable } from "@nestjs/common";
import Redis from "ioredis-mock";
import { IIoRedisProvider, RedisClientInitialization } from "./redis-ioredis.provider";

@Injectable()
export class IoRedisMockClientProvider implements IIoRedisProvider {
  private redis: any;

  getClient(options?: RedisClientInitialization) {
    if (options?.data) {
      this.redis = new Redis({
        data: options.data,
      });
    } else {
      this.redis = new Redis();
    }
    return this.redis;
  }
  async stopClient() {
    await this.redis.quit();
  }
}
