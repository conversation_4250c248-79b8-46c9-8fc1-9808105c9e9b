<?php
/**
 * CallBilling model
 *
 * @category CallBilling
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * CallBilling model
 *
 * @category CallBilling
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="CallBilling")
 */
class CallBilling extends CallPotential\CPCommon\RestModel
{
    /**
     * Log Id
     *
     * @var int
     *
     * @Primary
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $log_id;

    /**
     * Location Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $location_id;

    /**
     * User Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $user_id;

    /**
     * Duration
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=10, nullable=true)
     */
    protected $duration;

    /**
     * Call duration
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=10, nullable=true)
     */
    protected $call_duration;

    /**
     * Datestamp
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", nullable=true)
     */
    protected $datestamp;

    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return $this
     */
    public function setLogId(int $log_id)
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(int $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $user_id value to set
     *
     * @return $this
     */
    public function setUserId(int $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field duration
     *
     * @param integer $duration value to set
     *
     * @return $this
     */
    public function setDuration(int $duration)
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * Method to set the value of field call_duration
     *
     * @param integer $call_duration value to set
     *
     * @return $this
     */
    public function setCallDuration(int $call_duration)
    {
        $this->call_duration = $call_duration;

        return $this;
    }

    /**
     * Method to set the value of field datestamp
     *
     * @param string $datestamp value to set
     *
     * @return $this
     */
    public function setDatestamp(string $datestamp)
    {
        $this->datestamp = $datestamp;

        return $this;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field user_id
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field duration
     *
     * @return integer
     */
    public function getDuration(): int
    {
        return $this->duration;
    }

    /**
     * Returns the value of field call_duration
     *
     * @return integer
     */
    public function getCallDuration(): int
    {
        return $this->call_duration;
    }

    /**
     * Returns the value of field datestamp
     *
     * @return string
     */
    public function getDatestamp(): string
    {
        return $this->datestamp;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setConnectionService('dbLegacy');
        $this->setSource("call_billing");
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallBilling[]|CallBilling
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallBilling
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
