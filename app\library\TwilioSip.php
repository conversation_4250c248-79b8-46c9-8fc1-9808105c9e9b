<?php
/**
 * TwilioSip library
 *
 * @category TwilioSip
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;
use CallPotential\CallCenter\TwilioBase;

/**
 * TwilioSip library
 *
 * @category TwilioSip
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class TwilioSip extends TwilioBase
{
    /**
     * List IP access controls
     *
     * @return object
     */
    public function listIpAccessControlList()
    {
        $client = $this->getTwilioClient();
        $ipAccessControlList = $client->sip->ipAccessControlLists->read();

        return $ipAccessControlList;
    }

    /**
     * Create IP access controls
     *
     * @param string $friendlyName name
     *
     * @return mixed
     */
    public function createIpAccessControlList(string $friendlyName)
    {
        $client = $this->getTwilioClient();
        $ipAccessControlList = $client->sip->ipAccessControlLists->create($friendlyName);

        return $ipAccessControlList->sid;
    }

    /**
     * Delete IP accoess controls
     *
     * @param string $aclSid acl SID
     *
     * @return mixed
     */
    public function deleteIpAccessControlList(string $aclSid)
    {
        $client = $this->getTwilioClient();
        $result = $client->sip->ipAccessControlLists($aclSid)->delete();

        return $result;
    }

    /**
     * List all IP addresses
     *
     * @param string $aclSid acl SID
     *
     * @return mixed
     */
    public function listAllIpAddress(string $aclSid)
    {
        $client = $this->getTwilioClient();
        $result = $client->sip->ipAccessControlLists($aclSid)
            ->ipAddresses
            ->read();

        return $result;
    }

    /**
     * Get IP address by IP SID
     *
     * @param string $aclSid acl SID
     * @param string $ipSid  IP SID
     *
     * @return string
     */
    public function getIpAddress(string $aclSid, string $ipSid): string
    {
        $client = $this->getTwilioClient();
        $ipAddress = $client->sip
            ->ipAccessControlLists($aclSid)
            ->ipAddresses($ipSid)
            ->fetch();

        return $ipAddress;
    }

    /**
     * Create IP address
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    public function createIpAddress(array $data)
    {
        $client = $this->getTwilioClient();
        $result = $client->sip->ipAccessControlLists($data['ip_acl_sid'])
            ->ipAddresses
            ->create($data['friendlyName'], $data['ipAddress']);

        return $result;
    }

    /**
     * Update IP address
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return mixed
     */
    public function updateIpAddress(array $data)
    {
        $client = $this->getTwilioClient();
        $result = $client->sip->ipAccessControlLists($data['ipAccessControlListSid'])
            ->ipAddresses($data["sid"])
            ->update(
                array("friendlyName" => $data['friendlyName'])
            );

        return $result;
    }

    /**
     * Delete IP address
     *
     * @param string $ipAcl acl
     * @param string $ipSid IP SID
     *
     * @return mixed
     */
    public function deleteIpAddress(string $ipAcl, string $ipSid)
    {
        $client = $this->getTwilioClient();
        $result = $client->sip->ipAccessControlLists($ipAcl)
            ->ipAddresses($ipSid)
            ->delete();

        return $result;
    }
}
