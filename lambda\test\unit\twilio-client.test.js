const twilioMockData = require('./mockData/twilio-mock-data.json');
const mapData = {
    EventType: 'task.created',
    EventDescription : "Task created",
    TaskAttributes: twilioMockData.task_data.task_attributes,
    TaskSid: twilioMockData.task_data.task_sid,
    TaskChannelUniqueName: twilioMockData.task_data.voice_task_channel,
    TaskQueueName: twilioMockData.task_data.task_queue_name,
    TaskAssignmentStatus: twilioMockData.task_data.task_assignment,
}
const {setup} = require("./mockData/mock-twilio-client");

describe("Test sync maps", () => {
    jest.resetModules();
    setup(true);
    const twilioUtils = require('../../routes/utils/twillio')

    test("should mock creating sync map item and return success response", async () => {
        const result = await twilioUtils.sync_task_map(twilioMockData.twilio_account, mapData);

        expect(result).toEqual("<Response>Success.</Response>");
    });

    test("should mock removing sync map item and return success response", async () => {
        const result = await twilioUtils.remove_sync_task_map(twilioMockData.twilio_account, mapData);

        expect(result).toEqual("<Response>Success.</Response>");
    });

    test("sync map functions should return false when twilioAccount object is empty", async () => {
        const syncMapResult = await twilioUtils.sync_task_map({}, mapData);
        expect(syncMapResult).toEqual(false);

        const removeSyncMapResult = await twilioUtils.remove_sync_task_map({}, mapData);
        expect(removeSyncMapResult).toEqual(false);
    });
});

describe("Test sync maps catch errors scenarios", () => {
    it("should return error response after catching any errors", async () => {
        jest.resetModules();
        setup(false);

        const twilioUtils = require('../../routes/utils/twillio')
      
        const syncMapResult = await twilioUtils.sync_task_map(twilioMockData.twilio_account, {});
        expect(syncMapResult).toEqual("<Response>TypeError: Cannot read properties of undefined (reading 'v1')</Response>");


        const removeSyncMapResult = await twilioUtils.remove_sync_task_map(twilioMockData.twilio_account, {});
        expect(removeSyncMapResult).toEqual("<Response>TypeError: Cannot read properties of undefined (reading 'v1')</Response>");
    });
});