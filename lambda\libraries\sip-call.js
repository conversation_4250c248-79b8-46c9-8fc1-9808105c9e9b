const TwilioTaskHelper = require("./shared/twilio-task-helper.js");
const TwilioVoiceHelper = require("./shared/twilio-voice-helper.js");
const RedisHelper = require("./shared/redis-helper.js");
const TwilioConferenceHelper = require("./shared/twilio-conference-helper.js");

function agentConnectedBySip(context) {
  const { taskchannel } = context.taskAttributes;
  const { contact_uri } = context.workerAttributes;

  if (
    !TwilioVoiceHelper.isVoiceChannel(taskchannel) ||
    TwilioTaskHelper.isAgentContactURI(contact_uri) ||
    TwilioVoiceHelper.isCallbackChannel(taskchannel)
  ) {
    return false;
  }
  return true;
}

async function writeWorkerSidToTaskAttributes(context) {
  console.debug("writeWorkerSidToTaskAttributes", context);

  const { twilioAccount, twilioClient, taskAttributes } = context;
  const { TaskSid, WorkerSid } = context.reservationData;

  context.taskAttributes =
    await TwilioTaskHelper.updateTaskAttributesWithWorkerSid(
      twilioClient,
      twilioAccount.workspace_sid,
      TaskSid,
      WorkerSid,
      taskAttributes
    );
}

async function createSipCallConference(context) {
  const {
    accountSid,
    twilioClient,
    redisClient,
    taskAttributes,
    workerAttributes,
    reservationData,
  } = context;
  const { ReservationSid, TaskSid } = reservationData;

  /**
   * Extract information about the communication identifiers 
   * related to the caller (from) and callee (to) and their 
   * respective communication types (phone, sip, etc.)
   */
  context.communicationIdentifiers = 
      TwilioConferenceHelper.formatCommunicationIdentifiers(workerAttributes, taskAttributes);

  const { caller, callee } = context.communicationIdentifiers;

  /**
   * Define the parameters that will be used to add the agent to the conference
   */
  const agentConferenceParams = TwilioConferenceHelper.defineAgentConferenceParams(context);
  console.debug("agentConferenceParams", agentConferenceParams);

  /**
   * Create an outbound call to the agent
   */
  console.debug("Initiating outbound call to SIP agent");
  const agentOutboundCall = await TwilioVoiceHelper.createAgentOutboundCall(
    twilioClient,
    agentConferenceParams,
    TaskSid,
    ReservationSid,
    callee.to,
    caller.from,
  );

  /**
   * SPECIAL SIP HANDLING FOR EXTERNAL SIP ADDRESSES
   * For external SIP addresses, we need to save information to Redis that
   * will allow us to correlate the external webhook information with
   * information about the task reservation.
   */
  if (callee.type === "sip-external") {
    await RedisHelper.saveSipExternalInfoToRedis(
      redisClient,
      accountSid,
      TaskSid,
      ReservationSid,
      taskAttributes.call_sid,
      agentOutboundCall.sid,
      caller,
      callee
    );
  }
  
  await RedisHelper.saveAgentOutboundCallSidToRedis(
    redisClient,
    ReservationSid,
    taskAttributes.call_sid,
    agentOutboundCall.sid
  );

}

// Export
module.exports = {
  agentConnectedBySip,
  writeWorkerSidToTaskAttributes,
  createSipCallConference,
};
