import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventOptions, StatsD } from 'hot-shots';

@Injectable()
export class DataDogService {
  private client: StatsD;

  constructor(protected configService: ConfigService) {
    this.client = new StatsD({
      host: this.configService.get<string>('DD_AGENT_HOST'),
      port: 8125,
      errorHandler: (error) => {
        console.error('StatsD error:', error);
      },
    });
  }

  public logEvent(message: string, text?: string, eventOptions?: EventOptions, tags?: string[]): void {
    this.client.event(message, text, eventOptions, tags);
  }

  public incrementCounter(metricName: string, tags?: string[]): void {
    this.client.increment(metricName, 1, tags);
  }

  public recordGauge(metricName: string, value: number, tags?: string[]): void {
    this.client.gauge(metricName, value, tags);
  }

  public recordDistribution(metricName: string, value: number, tags?: string[]): void {
    this.client.distribution(metricName, value, tags);
  }

  public getBucket(value: number, maxValue: number, numberOfBuckets: number): string {
    const bucketSize = Math.ceil(maxValue / numberOfBuckets);

    for (let i = 1; i <= numberOfBuckets; i++) {
      if (value <= i * bucketSize) {
        return `${(i - 1) * bucketSize + 1}-${i * bucketSize}`;
      }
    }

    return `${maxValue}+`; // Overflow
  }
}