/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { DomainEventsService } from '@cp-workspace/shared';
import { Observable, concatMap, from, mergeMap } from 'rxjs';
import { Request, Response } from 'express';

@Injectable()
export class RequestResponseInterceptor implements NestInterceptor {

  source = "comms-api.http.controller";
  detailType = "HttpRequest";

  constructor(protected readonly logger: DomainEventsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<void> {
    const start = Date.now();
    const http = context.switchToHttp();
    const request = http.getRequest<Request>();
    const { method, originalUrl, headers, body } = request;

    /**
     * NOTE: 
     * If you need to customize the source and detailType, you should subclass
     * this interceptor and override the source and detailType properties.
     */
    return from(
      this.logger.publish({
        source: this.source,
        detailType: `${this.detailType}Start`,
        detail: {
          start,
          req: { method, originalUrl, headers, body },
        },
      })
    ).pipe(
      concatMap(() => next.handle()),
      mergeMap(async (responseBody) => {
        const response = http.getResponse<Response>();
        if (response.statusCode === 201) {
          response.statusCode = 200;
        }
        const end = Date.now();
        const duration = end - start;
        const { statusCode } = response;
        await this.logger.publish({
          source: this.source,
          detailType: `${this.detailType}End`,
          detail: {
            start,
            end,
            duration,
            req: { method, originalUrl, headers, body },
            res: {
              statusCode,
              headers: response.getHeaders(),
              body: responseBody,
            },
          },
        });
        return responseBody;
      })
    );
  }
}
