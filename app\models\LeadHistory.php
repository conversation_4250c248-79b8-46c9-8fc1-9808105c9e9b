<?php
/**
 * LeadHistory model
 *
 * Created by PhpStor<PERSON>.
 * User: cwalker
 * Date: 4/4/17
 * Time: 11:58 PM
 *
 * @category LeadHistory
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Models\S3DataTrait;
use CallPotential\CPCommon\Models\ElasticSearchModel;
use ONGR\ElasticsearchDSL\Search;
use \ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use \ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use \ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\RangeQuery;
use \ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use \ONGR\ElasticsearchDSL\Sort\FieldSort;

/**
 * LeadHistory model
 *
 * @category LeadHistory
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class LeadHistory extends \Phalcon\Di\Injectable
{
    use ElasticSearchModel{
        getIndexName as protected esTraitGetIndexName;
        findById as protected esTraitFindById;
    }
    use S3DataTrait, LoggerTrait, JsonModelTrait;

    /**
     * Id
     *
     * @var string
     *
     * @Column(type="string", length=256, nullable=true)
     */
    protected $id;

    /**
     * Log Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $log_id;

    /**
     * Lead Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $lead_id;

    /**
     * Twilio Id
     *
     * @var string
     *
     * @Column(type="string", length=256, nullable=true)
     */
    protected $twilio_id;

    /**
     * Account Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $account_id;

    /**
     * Recording SID
     *
     * @var string
     *
     * @Column(type="string", length=256, nullable=true)
     */
    protected $recording_sid;

    /**
     * Call number
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     */
    protected $call_number;

    /**
     * Caller name
     *
     * @var string
     *
     * @Column(type="string", length=256 nullable=true)
     */
    protected $caller_name;

    /**
     * Call name
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     */
    protected $call_name;

    /**
     * Datestamp
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $datestamp;

    /**
     * Call type
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $call_type;

    /**
     * Call processed by
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $call_processed_by;

    /**
     * Recording URL
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $recording_url;

    /**
     * Duration
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $duration;

    /**
     * Call destination
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $call_destination;

    /**
     * Rollover index
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $rollover_index;

    /**
     * Answered by
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $answered_by;

    /**
     * Ad Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $ad_id;

    /**
     * Location Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $location_id;

    /**
     * Employee Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $employee_id;

    /**
     * Employee name
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $employee_name;

    /**
     * Is excluded
     *
     * @var int
     *
     * @Column(type="boolean")
     */
    protected $is_excluded;

    /**
     * Customer type
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $customer_type;

    /**
     * Customer name
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    protected $customer_name;

    /**
     * Customer Id
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     */
    protected $customer_id;

    /**
     * Is auto call
     *
     * @var int
     *
     * @Column(type="boolean")
     */
    protected $is_auto_call;

    /**
     * Neighbor location Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $neighbor_location_id;

    /**
     * Grade
     *
     * @var string
     *
     * @Column(type="string", length=1, nullable=true)
     */
    protected $grade;

    /**
     * Halloffame
     *
     * @var int
     *
     * @Column(type="boolean")
     */
    protected $halloffame;

    /**
     * Gradesheet
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $gradesheet;

    /**
     * Gradesheet Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $gradesheet_id;

    /**
     * Customer card
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $customer_card;

    /**
     * Gradesheet points appointed
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $gradesheet_points_appointed;

    /**
     * Gradesheet points possible
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $gradesheet_points_possible;

    /**
     * Manager score
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $manager_score;

    /**
     * Confirm action
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     */
    protected $confirm_action;

    /**
     * Call status
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $call_status;

    /**
     * Queue time
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $queue_time;

    /**
     * Number of events
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $num_events;

    /**
     * Number of agents
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $num_agents;

    /**
     * Number of queues
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $num_queues;

    /**
     * Confirmed by
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $confirmed_by;

    /**
     * Method to set the value of field id
     *
     * @param string $id value to set
     *
     * @return $this
     */
    public function setId(string $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return $this
     */
    public function setLogId(int $log_id)
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field lead_id
     *
     * @param integer $lead_id value to set
     *
     * @return $this
     */
    public function setLeadId(int $lead_id)
    {
        $this->lead_id = $lead_id;

        return $this;
    }

    /**
     * Method to set the value of field twilio_id
     *
     * @param string $twilio_id value to set
     *
     * @return $this
     */
    public function setTwilioId(string $twilio_id)
    {
        $this->twilio_id = $twilio_id;

        return $this;
    }

    /**
     * Method to set the value of field account_id
     *
     * @param integer $account_id value to set
     *
     * @return $this
     */
    public function setAccountId(int $account_id)
    {
        $this->account_id = $account_id;

        return $this;
    }

    /**
     * Method to set the value of field recording_sid
     *
     * @param string $recording_sid value to set
     *
     * @return $this
     */
    public function setRecordingSid(string $recording_sid)
    {
        $this->recording_sid = $recording_sid;

        return $this;
    }

    /**
     * Method to set the value of field call_number
     *
     * @param string $call_number value to set
     *
     * @return $this
     */
    public function setCallNumber(string $call_number)
    {
        $this->call_number = $call_number;

        return $this;
    }

    /**
     * Method to set the value of field caller_name
     *
     * @param string $caller_name value to set
     *
     * @return $this
     */
    public function setCallerName(string $caller_name)
    {
        $this->caller_name = $caller_name;

        return $this;
    }

    /**
     * Method to set the value of field call_name
     *
     * @param string $call_name value to set
     *
     * @return $this
     */
    public function setCallName(string $call_name)
    {
        $this->call_name = $call_name;

        return $this;
    }

    /**
     * Method to set the value of field datestamp
     *
     * @param string $datestamp value to set
     *
     * @return $this
     */
    public function setDatestamp(string $datestamp)
    {
        $this->datestamp = $datestamp;

        return $this;
    }

    /**
     * Method to set the value of field call_type
     *
     * @param string $call_type value to set
     *
     * @return $this
     */
    public function setCallType(string $call_type)
    {
        $this->call_type = $call_type;

        return $this;
    }

    /**
     * Method to set the value of field call_processed_by
     *
     * @param string $call_processed_by value to set
     *
     * @return $this
     */
    public function setCallProcessedBy(string $call_processed_by)
    {
        $this->call_processed_by = $call_processed_by;

        return $this;
    }

    /**
     * Method to set the value of field recording_url
     *
     * @param string $recording_url value to set
     *
     * @return $this
     */
    public function setRecordingUrl(string $recording_url)
    {
        $this->recording_url = $recording_url;

        return $this;
    }

    /**
     * Method to set the value of field duration
     *
     * @param integer $duration value to set
     *
     * @return $this
     */
    public function setDuration(int $duration)
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * Method to set the value of field call_destination
     *
     * @param string $call_destination value to set
     *
     * @return $this
     */
    public function setCallDestination(string $call_destination)
    {
        $this->call_destination = $call_destination;

        return $this;
    }

    /**
     * Method to set the value of field rollover_index
     *
     * @param integer $rollover_index value to set
     *
     * @return $this
     */
    public function setRolloverIndex(int $rollover_index)
    {
        $this->rollover_index = $rollover_index;

        return $this;
    }

    /**
     * Method to set the value of field answered_by
     *
     * @param integer $answered_by value to set
     *
     * @return $this
     */
    public function setAnsweredBy(int $answered_by)
    {
        $this->answered_by = $answered_by;

        return $this;
    }

    /**
     * Method to set the value of field ad_id
     *
     * @param integer $ad_id value to set
     *
     * @return $this
     */
    public function setAdId(int $ad_id)
    {
        $this->ad_id = $ad_id;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(int $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_id
     *
     * @param integer $employee_id value to set
     *
     * @return $this
     */
    public function setEmployeeId(int $employee_id)
    {
        $this->employee_id = $employee_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_name
     *
     * @param string $employee_name value to set
     *
     * @return $this
     */
    public function setEmployeeName(string $employee_name)
    {
        $this->employee_name = $employee_name;

        return $this;
    }

    /**
     * Method to set the value of field is_excluded
     *
     * @param boolean $is_excluded value to set
     *
     * @return $this
     */
    public function setIsExcluded(bool $is_excluded)
    {
        $this->is_excluded = $is_excluded;

        return $this;
    }

    /**
     * Method to set the value of field customer_type
     *
     * @param integer $customer_type value to set
     *
     * @return $this
     */
    public function setCustomerType(int $customer_type)
    {
        $this->customer_type = $customer_type;

        return $this;
    }

    /**
     * Method to set the value of field customer_name
     *
     * @param string $customer_name value to set
     *
     * @return $this
     */
    public function setCustomerName(string $customer_name)
    {
        $this->customer_name = $customer_name;

        return $this;
    }

    /**
     * Method to set the value of field customer_id
     *
     * @param string $customer_id value to set
     *
     * @return $this
     */
    public function setCustomerId(string $customer_id)
    {
        $this->customer_id = $customer_id;

        return $this;
    }

    /**
     * Method to set the value of field is_auto_call
     *
     * @param boolean $is_auto_call value to set
     *
     * @return $this
     */
    public function setIsAutocall(bool $is_auto_call)
    {
        $this->is_auto_call = $is_auto_call;

        return $this;
    }

    /**
     * Method to set the value of field neighbor_location_id
     *
     * @param integer $neighbor_location_id value to set
     *
     * @return $this
     */
    public function setNeighborLocationId(int $neighbor_location_id)
    {
        $this->neighbor_location_id = $neighbor_location_id;

        return $this;
    }

    /**
     * Method to set the value of field grade
     *
     * @param string $grade value to set
     *
     * @return $this
     */
    public function setGrade(string $grade)
    {
        $this->grade = $grade;

        return $this;
    }

    /**
     * Method to set the value of field halloffame
     *
     * @param boolean $halloffame value to set
     *
     * @return $this
     */
    public function setHalloffame(bool $halloffame)
    {
        $this->halloffame = $halloffame;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet
     *
     * @param string $gradesheet value to set
     *
     * @return $this
     */
    public function setGradesheet(string $gradesheet)
    {
        $this->gradesheet = $gradesheet;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_id
     *
     * @param integer $gradesheet_id value to set
     *
     * @return $this
     */
    public function setGradesheetId(int $gradesheet_id)
    {
        $this->gradesheet_id = $gradesheet_id;

        return $this;
    }

    /**
     * Method to set the value of field customer_card
     *
     * @param string $customer_card value to set
     *
     * @return $this
     */
    public function setCustomerCard(string $customer_card)
    {
        $this->customer_card = $customer_card;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_points_appointed
     *
     * @param integer $gradesheet_points_appointed value to set
     *
     * @return $this
     */
    public function setGradesheetPointsAppointed(int $gradesheet_points_appointed)
    {
        $this->gradesheet_points_appointed = $gradesheet_points_appointed;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_points_possible
     *
     * @param integer $gradesheet_points_possible value to set
     *
     * @return $this
     */
    public function setGradesheetPointsPossible(int $gradesheet_points_possible)
    {
        $this->gradesheet_points_possible = $gradesheet_points_possible;

        return $this;
    }

    /**
     * Method to set the value of field manager_score
     *
     * @param integer $manager_score value to set
     *
     * @return $this
     */
    public function setManagerScore(int $manager_score)
    {
        $this->manager_score = $manager_score;

        return $this;
    }

    /**
     * Method to set the value of field confirm_action
     *
     * @param string $confirm_action value to set
     *
     * @return $this
     */
    public function setConfirmAction(string $confirm_action)
    {
        $this->confirm_action = $confirm_action;

        return $this;
    }

    /**
     * Method to set the value of field call_status
     *
     * @param integer $call_status value to set
     *
     * @return $this
     */
    public function setCallStatus(int $call_status)
    {
        $this->call_status = $call_status;

        return $this;
    }

    /**
     * Method to set the value of field queue_time
     *
     * @param integer $queue_time value to set
     *
     * @return $this
     */
    public function setQueueTime(int $queue_time)
    {
        $this->queue_time = $queue_time;

        return $this;
    }

    /**
     * Method to set the value of field num_events
     *
     * @param integer $num_events value to set
     *
     * @return $this
     */
    public function setNumEvents(int $num_events)
    {
        $this->num_events = $num_events;

        return $this;
    }

    /**
     * Method to set the value of field num_agents
     *
     * @param integer $num_agents value to set
     *
     * @return $this
     */
    public function setNumAgents(int $num_agents)
    {
        $this->num_agents = $num_agents;

        return $this;
    }

    /**
     * Method to set the value of field num_queues
     *
     * @param integer $num_queues value to set
     *
     * @return $this
     */
    public function setNumQueues(int $num_queues)
    {
        $this->num_queues = $num_queues;

        return $this;
    }

    /**
     * Method to set the value of field confirmed_by
     *
     * @param integer $confirmed_by value to set
     *
     * @return $this
     */
    public function setConfirmedBy(int $confirmed_by)
    {
        $this->confirmed_by = $confirmed_by;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field lead_id
     *
     * @return integer
     */
    public function getLeadId(): int
    {
        return $this->lead_id;
    }

    /**
     * Returns the value of field twilio_id
     *
     * @return string
     */
    public function getTwilioId(): string
    {
        return $this->twilio_id;
    }

    /**
     * Returns the value of field account_id
     *
     * @return integer
     */
    public function getAccountId(): int
    {
        return $this->account_id;
    }

    /**
     * Returns the value of field recording_sid
     *
     * @return string
     */
    public function getRecordingSid(): string
    {
        return $this->recording_sid;
    }

    /**
     * Returns the value of field call_number
     *
     * @return string
     */
    public function getCallNumber(): string
    {
        return $this->call_number;
    }

    /**
     * Returns the value of field caller_name
     *
     * @return string
     */
    public function getCallerName(): string
    {
        return $this->caller_name;
    }

    /**
     * Returns the value of field call_name
     *
     * @return string
     */
    public function getCallName(): string
    {
        return $this->call_name;
    }

    /**
     * Returns the value of field datestamp
     *
     * @return string
     */
    public function getDatestamp(): string
    {
        return $this->datestamp;
    }

    /**
     * Returns the value of field call_type
     *
     * @return string
     */
    public function getCallType(): string
    {
        return $this->call_type;
    }

    /**
     * Returns the value of field call_processed_by
     *
     * @return string
     */
    public function getCallProcessedBy(): string
    {
        return $this->call_processed_by;
    }

    /**
     * Returns the value of field recording_url
     *
     * @return string
     */
    public function getRecordingUrl(): string
    {
        return $this->recording_url;
    }

    /**
     * Returns the value of field duration
     *
     * @return integer
     */
    public function getDuration(): int
    {
        return $this->duration;
    }

    /**
     * Returns the value of field call_destination
     *
     * @return string
     */
    public function getCallDestination(): string
    {
        return $this->call_destination;
    }

    /**
     * Returns the value of field rollover_index
     *
     * @return integer
     */
    public function getRolloverIndex(): int
    {
        return $this->rollover_index;
    }

    /**
     * Returns the value of field answered_by
     *
     * @return integer
     */
    public function getAnsweredBy(): int
    {
        return $this->answered_by;
    }

    /**
     * Returns the value of field ad_id
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field employee_id
     *
     * @return integer
     */
    public function getEmployeeId(): int
    {
        return $this->employee_id;
    }

    /**
     * Returns the value of field employee_name
     *
     * @return string
     */
    public function getEmployeeName(): string
    {
        return $this->employee_name;
    }

    /**
     * Returns the value of field is_excluded
     *
     * @return integer
     */
    public function getIsExcluded(): bool
    {
        return $this->is_excluded;
    }

    /**
     * Returns the value of field customer_type
     *
     * @return integer
     */
    public function getCustomerType(): int
    {
        return $this->customer_type;
    }

    /**
     * Returns the value of field customer_name
     *
     * @return string
     */
    public function getCustomerName(): string
    {
        return $this->customer_name;
    }

    /**
     * Returns the value of field customer_id
     *
     * @return string
     */
    public function getCustomerId(): int
    {
        return $this->customer_id;
    }

    /**
     * Returns the value of field is_auto_call
     *
     * @return integer
     */
    public function getIsAutocall(): bool
    {
        return $this->is_auto_call;
    }

    /**
     * Returns the value of field neighbor_location_id
     *
     * @return integer
     */
    public function getNeighborLocationId(): int
    {
        return $this->neighbor_location_id;
    }

    /**
     * Returns the value of field grade
     *
     * @return string
     */
    public function getGrade(): string
    {
        return $this->grade;
    }

    /**
     * Returns the value of field halloffame
     *
     * @return integer
     */
    public function getHalloffame(): bool
    {
        return $this->halloffame;
    }

    /**
     * Returns the value of field gradesheet
     *
     * @return string
     */
    public function getGradesheet(): string
    {
        return $this->gradesheet;
    }

    /**
     * Returns the value of field gradesheet_id
     *
     * @return integer
     */
    public function getGradesheetId(): int
    {
        return $this->gradesheet_id;
    }

    /**
     * Returns the value of field customer_card
     *
     * @return string
     */
    public function getCustomerCard(): string
    {
        return $this->customer_card;
    }

    /**
     * Returns the value of field gradesheet_points_appointed
     *
     * @return integer
     */
    public function getGradesheetPointsAppointed(): int
    {
        return $this->gradesheet_points_appointed;
    }

    /**
     * Returns the value of field gradesheet_points_possible
     *
     * @return integer
     */
    public function getGradesheetPointsPossible(): int
    {
        return $this->gradesheet_points_possible;
    }

    /**
     * Returns the value of field manager_score
     *
     * @return integer
     */
    public function getManagerScore(): int
    {
        return $this->manager_score;
    }

    /**
     * Returns the value of field confirm_action
     *
     * @return string
     */
    public function getConfirmAction(): string
    {
        return $this->confirm_action;
    }

    /**
     * Returns the value of field call_status
     *
     * @return integer
     */
    public function getCallStatus(): int
    {
        return $this->call_status;
    }

    /**
     * Returns the value of field queue_time
     *
     * @return integer
     */
    public function getQueueTime(): int
    {
        return $this->queue_time;
    }

    /**
     * Returns the value of field num_events
     *
     * @return integer
     */
    public function getNumEvents(): int
    {
        return $this->num_events;
    }

    /**
     * Returns the value of field num_agents
     *
     * @return integer
     */
    public function getNumAgents(): int
    {
        return $this->num_agents;
    }

    /**
     * Returns the value of field num_queues
     *
     * @return integer
     */
    public function getNumQueues(): int
    {
        return $this->num_queues;
    }

    /**
     * Returns the value of field confirmed_by
     *
     * @return integer
     */
    public function getConfirmedBy(): int
    {
        return $this->confirmed_by;
    }

    /**
     * Constructor to initialize data
     */
    public function __construct()
    {
        $this->index = 'loc-data';
        $this->type = "lead";
        $this->indexSuffix = '';
    }

    /**
     * Get index name
     *
     * @return string
     */
    public function getIndexName(): string
    {
        return $this->index.$this->indexSuffix;
    }

    /**
     * FindById
     *
     * @param string $id     primary key value
     * @param string $parent parent Id if exists
     *
     * @return array
     */
    public function findById(string $id, string $parent = null): array
    {
        unset($parent);
        $idsQuery = new IdsQuery([$id], ['type' => $this->type]);

        $search = new Search();
        $search->addQuery($idsQuery);
        $queryArray = $search->toArray();
        $findData = $this->search($queryArray);

        return $findData;
    }
}
