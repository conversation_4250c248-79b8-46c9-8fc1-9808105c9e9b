<?php

namespace App\Services;

use CallPotential\CPCommon\ClientFactory;
use Twilio\Exceptions\ConfigurationException;
use Twilio\Rest\Api\V2010\Account\Conference\ParticipantInstance;
use Twilio\Rest\Client as TwilioClient;

class TwilioConferenceService
{
    /**
     * @throws ConfigurationException
     */
    public function getTwilioConferenceParticipants(string $accountId, string $friendlyName): ?array
    {
        $mccClient = ClientFactory::getMccClient();
        $twilio_account = $mccClient->getTwilioAccount($accountId);

        $twilio = new TwilioClient($twilio_account->account_sid, $twilio_account->authtoken);

        /**
         * @var \Twilio\Rest\Api\V2010\Account\ConferenceList $conferenceList
         */
        $conference = null;

        foreach ($twilio->conferences->stream() as $conf) {
            if ($conf->friendlyName === $friendlyName) {
                $conference = $conf;
                break;
            }
        }
        if ($conference !== null) {
            $participants = [];
                foreach($conference->participants->read(array('conferenceSid' => $conference->sid)) as $participant) {
                    /**
                     * @var ParticipantInstance $participant
                     */
                    $participants[] = $participant->toArray();
                }
            if ($participants) {
                return $participants;
            }
        }
        return [];
    }
}