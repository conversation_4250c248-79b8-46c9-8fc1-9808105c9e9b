import { Injectable } from "@nestjs/common";
import { DomainEvent, IPublishDomainEvents } from '../models/domain-event.model';
import { DomainEventsIntegrationService } from "../integrations/event-bridge/domain-events.integration.service";

@Injectable()
export class DomainEventsService implements IPublishDomainEvents {

  /**
   * We expose a way to set globa meta data to all domain events.
   * This will be merged with the meta data of the domain event
   * and is useful for settings things like the invocation id, when
   * running in a lambda function.  In that scenario, we want to
   * ensure that all domain events correlate to the same invocation id.
   */
  globalMeta: Record<string, unknown> = {};

  constructor(
    private readonly service: DomainEventsIntegrationService,
  ) { }

  async publish(domainEvent: DomainEvent, suppressGlobalMeta = false): Promise<DomainEvent> {

    /**
     * If there is global meta data, merge it with the domain event meta data.
     */
    if (!suppressGlobalMeta && Object.getOwnPropertyNames(this.globalMeta).length > 0) {
      domainEvent.detail = domainEvent.detail || {};
      domainEvent.detail.meta = { ...domainEvent.detail.meta, ...this.globalMeta };
    }

    /**
     * Publish the domain event.
     */
    return this.service.publish(domainEvent);
  }

  get isEnabled(): boolean {
    return this.service.isEnabled;
  }

  isDomainEvent(event: unknown): event is DomainEvent {
    let isDomainEvent = false;
    const asDomainEvent = event as DomainEvent;
    if (asDomainEvent.source !== undefined
      && asDomainEvent.detailType !== undefined
      && asDomainEvent.detail !== undefined
    ) {
      isDomainEvent = true;
    }
    return isDomainEvent;
  }

}