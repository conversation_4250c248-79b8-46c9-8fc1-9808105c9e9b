import { Injectable } from "@nestjs/common";

export interface AsyncWorkerEvent {
  source: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  body: any;
}

export interface IAsyncWorkersInvoker {
  invokeAsyncWorker(event: AsyncWorkerEvent): Promise<void>;
}

export interface AsyncWorkersServiceOptions {
  enabled?: boolean;
}

@Injectable()
export class IAsyncWorkersProvider {
  getClient(): IAsyncWorkersInvoker {
    throw new Error('IAsyncWorkersInvoker.getClient() not implemented. Did you add `IAsyncWorkersInvoker` to your Nest Module `providers`?');
  }
}
