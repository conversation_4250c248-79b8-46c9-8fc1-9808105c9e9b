import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, ExitPayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { PaymentSuccessful } from './PaymentSuccessful';
import { Locale } from '@cp-workspace/shared';

describe('PaymentSuccessful', () => {
  let paymentSuccessful: PaymentSuccessful;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PaymentSuccessful],
    }).compile();

    paymentSuccessful = module.get<PaymentSuccessful>(PaymentSuccessful);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.PaymentSuccessful,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        paymentSuccessRedirectUrl: 'http://example.com/redirect',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should set the nextState as TransferToAgent and provide a redirect URL on payment Successful', async () => {
      const response: ExitPayByPhoneStateHandlerResponse = await paymentSuccessful.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.DisconnectCall);
      expect(response.redirectUrl).toBe(context.storage.paymentSuccessRedirectUrl);
    });
  });
});
