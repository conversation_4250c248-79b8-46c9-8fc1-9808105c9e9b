import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { DomainEventsService } from '@cp-workspace/shared';
import { Observable } from 'rxjs';
import { CallSidRequestInterceptor } from './CallSidRequestInterceptor';

@Injectable()
export class PayByPhoneRequestInterceptor extends CallSidRequestInterceptor {

  constructor(protected readonly logger: DomainEventsService) {
    super(logger);
    this.source = "comms-api.pay-by-phone.controller";
    this.detailType = "PayByPhoneRequest";
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<void> {
    return super.intercept(context, next);
  }
}
