/* DO NOT EDIT, file generated by nestjs-i18n */
  
/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "pay-by-phone": {
        "collections-prompt-pbp-allowed": string;
        "collections-prompt-pbp-not-allowed": string;
        "opt-out-success": string;
        "locale-select": string;
        "total-balance": string;
        "account-found": string;
        "ask-prepay": string;
        "ask-prepay-zero-balance": string;
        "prepay-selection": string;
        "ask-saved-cc": string;
        "ask-for-number": string;
        "account-not-found": string;
        "must-enter-ten-digits": string;
        "multiple-accounts": string;
        "pay-all": string;
        "account-select": string;
        "start-over": string;
        "speak-manager": string;
        "not-allowed": string;
        "max-retry": string;
        "card-not-found": string;
        "amount-due": string;
        "enter-cc": string;
        "enter-expiry": string;
        "enter-ccv": string;
        "enter-zip": string;
        "confirm-payment": string;
        "invalid-input": string;
        "payment-under-process": string;
        "payment-success": string;
        "payment-fail": string;
        "cc-confirm": string;
        "expiration-confirm": string;
        "security-code-confirm": string;
        "zip-confirm": string;
        "wait-for-account-fetch": string;
        "could-not-find-card": string;
        "num-confirm": string;
        "amount-due-with-card-processing-fee": string;
        "location-has-card-processing-fee": string;
        "payment-fail-due-to-config-issue": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
