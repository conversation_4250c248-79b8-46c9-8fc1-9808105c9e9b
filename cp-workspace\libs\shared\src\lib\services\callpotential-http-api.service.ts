import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { DomainEventsService } from './domain-events.service';
import Bugsnag from '@bugsnag/js';

export abstract class CallPotentialHttpApiService {
    private baseUrl: string;
    private authToken: string;

    constructor(
        protected httpService: HttpService,
        protected configService: ConfigService,
        protected domainEventService: DomainEventsService,
        urlConfigKey: string,
        authTokenOverride?: string,
    ) {
        const authTokenValue = authTokenOverride || configService.get<string>('db.serviceTokens.readWrite'); //config.db.serviceTokens.readWrite
        if(!authTokenValue) {
            throw new Error('db.serviceTokens.readWrite must be defined');
        }
        const baseUrl = this.configService.get<string>(urlConfigKey);
        if (!baseUrl) {
            throw new Error(`${urlConfigKey} must be defined`);
        }

        this.authToken = authTokenValue;
        this.baseUrl = baseUrl;
    }

    protected getHeaders(): Record<string, string> {
        return {
            Authorization: `${this.authToken}`
        };
    }
    protected constructQueryString(params: Record<string, any>): string {
        return Object.entries(params)
            .filter(([_, value]) => value !== undefined)  // Remove undefined params
            .map(([key, value]) => {
                if (Array.isArray(value)) {
                    return value
                        .map(item => `${encodeURIComponent(key)}[]=${encodeURIComponent(item)}`)
                        .join('&');
                }
                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
            })
            .join('&');
    }

    protected async get(endpoint: string, params?: Record<string, any>): Promise<any> {
      const queryString = params && Object.keys(params).length > 0 ? `?${this.constructQueryString(params)}` : '';
      const url = `${this.baseUrl}${endpoint}${queryString}`;
      const headers = this.getHeaders();
      const observable = this.httpService.get(url, { headers });
      const response = await firstValueFrom(observable);

      await this.domainEventService.publish({
        source: 'comms-api.external.request',
        detailType: 'CallPotentialHttpApiServiceGet',
        detail: {
          req: { method: "GET", url, headers },
          res: { statusCode: response.status, headers: response.headers, body: response.data },
        },
      });

      Bugsnag.leaveBreadcrumb('CallPotentialHttpApiServiceGet', {
        source: 'comms-api.external.request',
        method: "GET",
        url,
        headers,
        response: {
          statusCode: response.status,
          headers: response.headers,
          body: response.data,
        },
      });

      return response.data
  }

  protected async post(endpoint: string, body: any): Promise<any> {
        const url = `${this.baseUrl}${endpoint}`;
        const headers = this.getHeaders();
        const observable = this.httpService.post(url, body, { headers: this.getHeaders() });
        const response = await firstValueFrom(observable);

        await this.domainEventService.publish({
          source: 'comms-api.external.request',
          detailType: 'CallPotentialHttpApiServicePost',
          detail: {
            req: { method: "POST", url, headers, body },
            res: { statusCode: response.status, headers: response.headers, body: response.data },
          },
        });

        Bugsnag.leaveBreadcrumb('CallPotentialHttpApiServicePost', {
          source: 'comms-api.external.request',
          method: "POST",
          url,
          headers,
          body,
          response: {
            statusCode: response.status,
            headers: response.headers,
            body: response.data,
          },
        });

        return response;
    }
}