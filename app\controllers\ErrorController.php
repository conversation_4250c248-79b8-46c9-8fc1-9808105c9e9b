<?php
/**
 * Sends error response with status code and status message
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 3/24/17
 * Time: 3:08 PM
 *
 * @category ErrorController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Sends error response with status code and status message
 *
 * @category ErrorController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */

class ErrorController extends \Phalcon\Mvc\Controller
{
     /**
      * Send 404 response
      *
      * @return Phalcon\Http\Response
      */
    public function route404Action(): Phalcon\Http\Response
    {
        $this->response->setStatusCode(404, 'NOT FOUND');
        $url = $this->request->getURI();
        $error = [];
        $error['url'] = $url;
        $error['message'] = "Not Found";
        $this->response->setContent(json_encode($error));

        return $this->response;
    }
}
