var blocked_numbers = [
  '+1266696687', '+18554278193', '+10000000000', '+11111111111',
  '+12222222222', '+13333333333', '+14444444444', '+15555555555',
  '+16666666666', '+17777777777', '+18888888888', '+19999999999'
]

/**
 * Call card type.
 * We have two different call card types now
 * Form and Script
 */
var CALL_CARD_TYPE = {
  'CALLCARD_FORM': 'form',
  'CALLCARD_SCRIPT': 'script'
}

/**
 * Call card type.
 * We have two different call card types now
 * Form and Script
 */
var CALLCARD = {
  'CALLCARD_FORM': 'form',
  'CALLCARD_SCRIPT': 'script'
};

/**
 * Define developers, client emails to be used for sending emails
 */
var EMAIL_DETAILS = {
  'API_LIMIT_EMAIL': '<EMAIL>',
  'NOTIFICATION_EMAIL': '<EMAIL>',
  'EMAILID_PHIL': '<EMAIL>',
  'EMAILID_DEREK': '<EMAIL>',
  'EMAILID_LEAD_ADMIN': '<EMAIL>',
  'EMAILID_LEAD_DUPLICATE': '<EMAIL>',
  'EMAILID_DEV_ONE': '<EMAIL>',
  'SUPPORT_EMAIL': '<EMAIL>',
  'VOICEMAIL_EMAIL': '<EMAIL>',
  'SMS_OPT_OUT_SYSTEM': '<EMAIL>'
}

module.exports = {
  'blocked_numbers': blocked_numbers,
  'CALL_CARD_TYPE': CALL_CARD_TYPE,
  'CALLCARD': CALLCARD,
  'EMAIL_DETAILS': EMAIL_DETAILS,
};
