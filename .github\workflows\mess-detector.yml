name: Mess Detector
on:
  pull_request:
    branches:
      - "**"
      
jobs:
  Pipeline:
    runs-on: ubuntu-latest
    steps:
      - name: Get file changes
        id: files
        uses: callpotential/diffset@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          relevant_php_files: |
            app/**/*.php

      - name: PHP Setup
        uses: shivammathur/setup-php@v2
        with:
          php-version: "7.4"
          extensions: json, openssl, fileinfo, zip, gd, psr, phalcon-4.0.6

      - name: Checkout
        if: steps.files.outputs.relevant_php_files
        uses: actions/checkout@v1

      - name: Set up Composer authentication
        if: steps.files.outputs.relevant_php_files
        env:
          COMPOSER_AUTH: |
            {
              "github-oauth": {
                "github.com": "${{ secrets.COMPOSER_GITHUB_PAT }}"
              }
            }
        run: composer install --no-interaction --prefer-dist --optimize-autoloader
  
      - name: Mess Detector
        if: steps.files.outputs.relevant_php_files
        run: vendor/bin/phpmd ${{ steps.files.outputs.relevant_php_files }} text vendor/callpotential/cpcommon/src/testrules/cp-phpmd.xml
