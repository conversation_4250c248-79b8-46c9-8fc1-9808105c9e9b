import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { CallPotentialHttpApiService } from './callpotential-http-api.service';
import { CustomerExclusion, CustomerExclusionResponse } from '../models/customer-exclusion.model';
import { DomainEventsService } from './domain-events.service';

@Injectable()
export class AccountService extends CallPotentialHttpApiService {
    constructor(
        protected override httpService: HttpService,
        protected override configService: ConfigService,
        protected override domainEventService: DomainEventsService,
    ) {
        super(httpService, configService, domainEventService, 'API_ACCT_URL');
    }

    public async createCustomerExclusion(request: CustomerExclusion): Promise<CustomerExclusionResponse> {
        const endpoint = '/customerexclusion';
        const body = {
            ...request,
            exclusion_date: new Date().toISOString(),
        };
        return (await this.post(endpoint, body)).data as CustomerExclusionResponse;
    }
}
