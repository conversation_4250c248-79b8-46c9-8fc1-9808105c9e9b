import { Ledger } from "./ledger.model";
import { PagingData } from "./paging.model";
export type TenantLedger = {
    ledger_id: number;
    user_id: number;
    location_id: number;
    tenant_id: string;
    unit_id: string;
    paid_thru_date: string;
    amount_owed: string;
    rent_rate: number;
    status: string;
    last_payment_date: string;
    unit_name: string;
    collection_rule_id: string;
    collection_rule_version: string;
    collection_rule_type: string;
    collection_rule_step: number;
    next_collection_date: string;
    auto_mode: string;
    time: string;
    is_removed: number;
    error: string;
    error_message: string;
    last_error_date: string;
    last_updated: string;
    is_assigned: number;
    is_deleted: number;
    movein_rule_id: string;
    movein_rule_version: number;
    movein_rule_type: string;
    next_movein_rule_date: string;
    next_movein_rule_time: string;
    moved_in_date: string;
    notes: object;
};

export type Tenant = {
    _id: string;
    location_id: number;
    user_id: number;
    site_id: number;
    customer_id: string;
    first_name: string;
    last_name: string;
    email: string;
    email2: string;
    address1: string;
    address2: string;
    city: string;
    province: string;
    country: string;
    postal: string;
    is_excluded: number;
    sms_opt_out: number;
    auto_exclude: number;
    additional_api_fields: any;
    last_updated: string;
    excluded_on_request: string;
    ssn: string;
    dob: string;
    mobile: string;
    license: string;
    access_code: string;
    ledgers: Ledger[];
    account_class: string;
    phones: { phone: string; phone_type: string }[];
    is_primary: number;
    extension?: string;
    additional_info: {
        customer_id: string;
        name: string;
        value: string;
        info_type: string;
        date_created: string;
        is_active: string;
    }[];
    properties: object;
    _lastmodified: number;
    attributes: object;
    sLicense: object;
    dDOB: string;
    sAccessCode: object;
    sSSN: object;
    location_name: string;
}

export type TenantDataRequest = {
    locationId: number;
    unit_name?: string;
    filterType?: "All" | "Current" | "Delinquent";
    filterPhone?: string;
    filterEmail?: string;
    filterFirstName?: string;
    filterLastName?: string;
    filterName?: string;
    filterExcludeAlternate?: boolean; //!LocationConfiguration.paybyphone_all
    skipCache?: boolean;
    forceCache?: boolean;
    search?: string;
    page?: number;
    perPage?: number;
    sortBy?: string;
    order?: "ASC" | "DESC";
}

export type TenantDataResponse = TenantDataSuccessfulResponse | TenantDataErrorResponse;

export type TenantDataSuccessfulResponse = {
    items: Tenant[];
    paging: PagingData;
}

export type TenantDataErrorResponse = {}

export type TenantId = Tenant['_id'];