"use strict";
const config = require('./config');
const AWS = require("aws-sdk");
var workspace = class Workspace{
    getWorkspaces(){
        return new Promise(function(resolve){
            AWS.config.update({
              region: process.env.AWS_REGION || 'us-west-2'
            });
            var docClient = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
             
            var params = {
                TableName: config.dynamodb.acctTable
            };
             
            docClient.scan(params, function(err, data){
                if (err) {
                    console.error("Unable to read item. Error JSON:", JSON.stringify(err, null, 2));
                } else {
                    resolve(data);
                }
            });
            
        });
    }
}

module.exports = {
  'Workspace':workspace
};
