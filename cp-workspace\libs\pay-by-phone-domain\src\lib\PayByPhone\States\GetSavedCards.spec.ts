import { Test, TestingModule } from '@nestjs/testing';
import { ApiType, LoopbackPayByPhoneStateHandlerResponse, PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { GetSavedCards } from './GetSavedCards';
import { Locale, SavedCard, Customer, LocationConfiguration, Tenant } from '@cp-workspace/shared';

jest.mock('@cp-workspace/shared', () => {
  const originalModule = jest.requireActual('@cp-workspace/shared');
  return {
    ...originalModule,
    Customer: {
      ...originalModule.Customer,
      getSavedCards: jest.fn(),
    },
  };
});

describe('GetSavedCards', () => {
  let getSavedCards: GetSavedCards;
  let context: PayByPhoneStateContext;
  let mockSurchargePercentage = 0;
  let mockLocationSettingEnabled = true;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GetSavedCards],
    }).compile();

    getSavedCards = module.get<GetSavedCards>(GetSavedCards);
    getSavedCards.services = {
      integrationService: {
        getSurchargePercentage: jest.fn().mockResolvedValue(mockSurchargePercentage),
      },
      coreService: {
        getLocationSetting: jest.fn().mockResolvedValue(mockLocationSettingEnabled),
        payByPhoneSurchargeFullyQualifiedLocationSettingName: 'CustomerPayByPhone::CardProcessingFeeAmount::Isenabled',
      },
      locationService: {
        getLocationConfiguration: jest.fn().mockResolvedValue({ allow_prev_cc: 1 } as LocationConfiguration),
        getLocationDetails: jest.fn().mockResolvedValue({
          api_type: 9,
          user_id: 'user123',
        }),
      } as any,
    } as any;

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.GetSavedCards,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        matchedTenants: [],
        selectedTenant: {} as Tenant,
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0,
        surchargeDetails: {
          surchargeEnabledAPITypes: [ApiType.STOREDGE],
        }
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 123,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should pass filterByLedgers to getSavedCards when selectedUnits are present with 1 matched tenants and a selected tenant', async () => {
      context.storage.matchedTenants = [{ _id: '1' } as Tenant];
      context.storage.selectedTenant = context.storage.matchedTenants[0];
      const savedCards: SavedCard[] = [
        { id: '1', card_name: 'Card 1', card_number: '1234', autopay_enabled: 'true' },
      ];
      (Customer.getSavedCards as jest.Mock).mockResolvedValue(savedCards);
      getSavedCards.services.locationService.getLocationConfiguration = jest
        .fn()
        .mockResolvedValue({ allow_prev_cc: 1 });

      // Set up selectedUnits in storage
      context.storage.selectedUnits = [
        {
          ledger_id: 123,
          first_name: '',
          last_name: '',
          email: '',
          customer_id: '',
          next_collection_date: '',
          location_id: 0,
          location_name: '',
          days_past_due: '',
          next_due_date: '',
          phone: '',
          phone_type: '',
          _id: '',
          next_movein_rule_time: '',
          movein_rule_type: '',
          is_removed: 0,
          error: '',
          collection_rule_version: 0,
          movein_rule_version: 0,
          is_deleted: 0,
          collection_rule_type: '',
          last_error_date: '',
          collection_rule_id: '',
          error_message: '',
          last_updated: '',
          collection_rule_step: '',
          is_assigned: 0,
          auto_mode: '',
          movein_rule_id: '',
          time: '',
          next_movein_rule_date: '',
          unit_id: '',
          paid_thru_date: '',
          amount_owed: '',
          moved_in_date: '',
          moved_out_date: '',
          user_id: 0,
          es_unit_id: '',
          status: '',
          last_payment_date: '',
          rent_rate: 0,
          unit_name: '',
          tenant_id: '',
          properties: {},
          attributes: {},
          tenant_id_es: ''
        },
        {
          ledger_id: 456,
          first_name: '',
          last_name: '',
          email: '',
          customer_id: '',
          next_collection_date: '',
          location_id: 0,
          location_name: '',
          days_past_due: '',
          next_due_date: '',
          phone: '',
          phone_type: '',
          _id: '',
          next_movein_rule_time: '',
          movein_rule_type: '',
          is_removed: 0,
          error: '',
          collection_rule_version: 0,
          movein_rule_version: 0,
          is_deleted: 0,
          collection_rule_type: '',
          last_error_date: '',
          collection_rule_id: '',
          error_message: '',
          last_updated: '',
          collection_rule_step: '',
          is_assigned: 0,
          auto_mode: '',
          movein_rule_id: '',
          time: '',
          next_movein_rule_date: '',
          unit_id: '',
          paid_thru_date: '',
          amount_owed: '',
          moved_in_date: '',
          moved_out_date: '',
          user_id: 0,
          es_unit_id: '',
          status: '',
          last_payment_date: '',
          rent_rate: 0,
          unit_name: '',
          tenant_id: '',
          properties: {},
          attributes: {},
          tenant_id_es: ''
        }
      ];

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(Customer.getSavedCards).toHaveBeenCalledWith(
        context.storage.matchedTenants,
        getSavedCards.services.integrationService,
        ['123', '456'] // Expect ledger_ids to be passed as strings
      );
      expect(response.nextState).toBe(PayByPhoneState.PayMethodPrompt);
    });

    it('should pass empty filterByLedgers when no selectedUnits are present and we have 1 matched tenant', async () => {
      context.storage.matchedTenants = [{ _id: '1' } as Tenant];
      context.storage.selectedTenant = context.storage.matchedTenants[0];
      const savedCards: SavedCard[] = [
        { id: '1', card_name: 'Card 1', card_number: '1234', autopay_enabled: 'true' },
      ];
      (Customer.getSavedCards as jest.Mock).mockResolvedValue(savedCards);
      getSavedCards.services.locationService.getLocationConfiguration = jest
        .fn()
        .mockResolvedValue({ allow_prev_cc: 1 });

      // Ensure selectedUnits is undefined
      context.storage.selectedUnits = undefined;

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(Customer.getSavedCards).toHaveBeenCalledWith(
        context.storage.matchedTenants,
        getSavedCards.services.integrationService,
        [] // Expect empty array when no selectedUnits
      );
      expect(response.nextState).toBe(PayByPhoneState.PayMethodPrompt);
    });

    it('should navigate to PayMethodPrompt when saved cards are available, 1 matched tenant and allow_prev_cc is true', async () => {
      context.storage.matchedTenants = [{ _id: '1' } as Tenant];
      context.storage.selectedTenant = context.storage.matchedTenants[0];
      const savedCards: SavedCard[] = [
        { id: '1', card_name: 'Card 1', card_number: '1234', autopay_enabled: 'true' },
        { id: '2', card_name: 'Card 2', card_number: '5678', autopay_enabled: 'false' },
      ];
      (Customer.getSavedCards as jest.Mock).mockResolvedValue(savedCards);
      getSavedCards.services.locationService.getLocationConfiguration = jest.fn().mockResolvedValue({ allow_prev_cc: 1 });
      
      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PayMethodPrompt);
      expect(context.storage.savedCards).toEqual(savedCards.map(card => ({ ...card, card_number: card.card_number.slice(-4) })));
    });

    it('should navigate to PayMethodCreditCardPrompt when no saved cards are available and allow_prev_cc is true', async () => {
      (Customer.getSavedCards as jest.Mock).mockResolvedValue([]);
      getSavedCards.services.locationService.getLocationConfiguration = jest.fn().mockResolvedValue({ allow_prev_cc: 1 });

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
    });

    it('should navigate to PayMethodCreditCardPrompt when allow_prev_cc is false, regardless of saved cards', async () => {
      const savedCards: SavedCard[] = [
        { id: '1', card_name: 'Card 1', card_number: '1234', autopay_enabled: 'true' },
      ];
      (Customer.getSavedCards as jest.Mock).mockResolvedValue(savedCards);
      getSavedCards.services.locationService.getLocationConfiguration = jest.fn().mockResolvedValue({ allow_prev_cc: 0 });

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
      expect(context.storage.savedCards).toEqual([]);
    });

    it('should navigate to PayMethodCreditCardPrompt when we have more than 1 matched tenant', async () => {
      context.storage.matchedTenants = [{ _id: '1' } as Tenant, { _id: '2' } as Tenant];

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
      expect(context.storage.savedCards).toEqual([]);
    });

    it('should navigate to PayMethodPrompt when we have 1 matched tenant with a saved card', async () => {
      const savedCards: SavedCard[] = [
        { id: '1', card_name: 'Card 1', card_number: '1234', autopay_enabled: 'true' },
      ];
      (Customer.getSavedCards as jest.Mock).mockResolvedValue(savedCards);
      context.storage.matchedTenants = [{ _id: '1' } as Tenant];
      context.storage.selectedTenant = context.storage.matchedTenants[0];

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PayMethodPrompt);
      expect(context.storage.savedCards).toEqual(savedCards);
    });

    it('should navigate to PayMethodCreditCardPrompt when we have 1 matched tenant with no saved cards', async () => {
      (Customer.getSavedCards as jest.Mock).mockResolvedValue([]);
      context.storage.matchedTenants = [{ _id: '1' } as Tenant];

      const response: LoopbackPayByPhoneStateHandlerResponse = await getSavedCards.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
      expect(context.storage.savedCards).toEqual([]);
    });

    describe('when matchedTenants length is greater than zero', () => {
      beforeEach(() => {
        context.storage.matchedTenants = [{ customer_id: '123' }] as any;
      });
  
      describe('when surcharge is enabled for the api type', () => {
        beforeEach(async () => {
          await getSavedCards.handler(context);
        });
        
        it('should call getLocationSetting with proper params', async () => {
          expect(getSavedCards.services.coreService.getLocationSetting).toHaveBeenCalledWith(
            getSavedCards.services.coreService.payByPhoneSurchargeFullyQualifiedLocationSettingName,
            'user123',
            context.storage.locationId
          );
        });

        it('should call getSurchargePercentage with proper params', async () => {
          expect(getSavedCards.services.integrationService.getSurchargePercentage).toHaveBeenCalledWith(context.storage.locationId);
        });

        it('should set isSurchargeEnabledForLocation in storage', async () => {
          expect(context.storage.surchargeDetails?.isSurchargeEnabledForLocation).toBe(mockLocationSettingEnabled);
        });

        it('should set surchargePercentage in storage', async () => {
          expect(context.storage.surchargeDetails?.surchargePercentage).toBe(mockSurchargePercentage);
        });
          
        describe('when surcharge toggle is enabled at crm side', () => {
          beforeAll(() => {
            mockLocationSettingEnabled = true;
          });

          describe('when surcharge percentage is greater than 0', () => {
            beforeAll(() => {
              mockSurchargePercentage = 5;
            });

            it('should say the card processing fee message with percentage', async () => {
              expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({
                messageId: 'pay-by-phone.location-has-card-processing-fee',
                locale: context.storage.locale,
                i18nOptions: { args: [{ surchargePercent: mockSurchargePercentage }] }
              });
            });
          });

          describe('when surcharge percentage is 0', () => {
            beforeAll(() => {
              mockSurchargePercentage = 0;
            });

            it('should not say the card processing fee message', async () => {
              expect(context.twilioResponse.sayInLocale).not.toHaveBeenCalled();
            });
          });
        });

        describe('when surcharge toggle is disabled at crm side', () => {
          beforeAll(() => {
            mockLocationSettingEnabled = false;
          });

          it('should not say the card processing fee message', async () => {
            expect(context.twilioResponse.sayInLocale).not.toHaveBeenCalled();
          });
        });
      });
    });
  });
});
