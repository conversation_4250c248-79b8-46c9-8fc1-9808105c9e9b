const Twilio = require('twilio');
const AWS = require('aws-sdk');
const express = require('express');
const config = require('../config/config');
const moment_tz = require('moment-timezone');
const cpapiClient = require('../libraries/cpapi-client');
const redis = require('redis');
const redisTtl = 1800;
const router = express.Router();
const dataDogService = require('../libraries/shared/datadog-service');

AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });

const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
const tableName = config.dynamodb.acctTable;

Array.prototype.forEachAsync = async function (fn) {
    for (let t of this) { await fn(t) }
};

async function getAccountDetail(accountSid) {
  const params = {
    TableName: tableName,
    IndexName: 'account_sid-index',
    KeyConditionExpression: "account_sid = :sid",
    ExpressionAttributeValues: {
      ":sid": accountSid
    }
  };

  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

router.route('/prepare_pbp_data/:account_sid/:call_sid')
  .post(async function (req, resp) {

    // console.log('prepare_pbp_data', req.params.call_sid);
    let acct = await getAccountDetail(req.params.account_sid);
    const twilio = new Twilio(acct.account_sid, acct.authtoken);

    const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
    await redisClient.connect();

    const call_task_key = `payment_task_${req.params.call_sid}`;
    let pbpData = await redisClient.get(call_task_key);

    if (pbpData && Object.keys(pbpData).length !== 0) {
      pbpData = JSON.parse(pbpData);
    } else {
      await redisClient.disconnect();
      console.error('pbpData data not found.', call_task_key, pbpData);
      resp.send();
    }

    let payState = pbpData.payState;
    let paySubState = pbpData.paySubState;
    let locationId = pbpData.locationId;
    let phoneNumber = pbpData.fromNo;

    const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);
    const locClient = new cpapiClient.locClient(config.db.serviceTokens.readWrite);

    const locData = await locClient
      .getLocationConfiguration(locationId)
      .catch(error => console.log(error, new Error().stack));

    console.debug('pbpData', pbpData);

    let route_utils = require('./utils/twillio');

    switch (payState) {
      case 'customerSearch':
        if (paySubState == 'numberSearch') {

          let dialing_code = await route_utils.get_dialing_code(locationId);

          phoneNumber = route_utils.remove_dialing_code(phoneNumber, dialing_code);
          let valid_number = (phoneNumber.length === 10);

          if (valid_number) {
            pbpData.validNumber = phoneNumber;
            let url = `tenant?locationId=${locationId}&filterPhone=${phoneNumber}&filterActive=true&page=1&filterExcludeAlternate=true`;
            if (locData.paybyphone_all && locData.paybyphone_all == 1) {
              url = `tenant?locationId=${locationId}&filterPhone=${phoneNumber}&filterActive=true&page=1`;
            }
            const tenantData = await intClient
              .getData(url)
              .catch(error => {
                console.log(error, new Error().stack);
              });

            if (!tenantData || !tenantData.items || typeof tenantData.items[0] === 'undefined') {
              pbpData.paySubState = 'manualNumberPrompt';
              break;
            }

            let tenantEsIdList = tenantData.items.map((item) => [item._id, item.first_name + ' ' + item.last_name]).slice(0, 6);
            let ledgerDataList = [];

            await tenantEsIdList.forEachAsync(async (tenantEsId) => {
              const ledgerData = await intClient
                .getData(`tenant/${tenantEsId[0]}/ledger?skipCache=true`)
                .catch(error => console.log(error, new Error().stack));

              let tenantSavedCards = [];
              if (locData.allow_prev_cc && locData.allow_prev_cc == 1) {
                tenantSavedCards = await intClient
                  .getData(`tenant/${tenantEsId[0]}/saved_cards`)
                  .catch(error => console.log(error, new Error().stack));
              }

              let ledgerSavedCard = {
                'savedCard' : false
              };

              if (typeof tenantSavedCards.items !== 'undefined' && typeof tenantSavedCards.items[0] !== 'undefined') {
                pbpData.savedCard = true;
                ledgerSavedCard = {
                  'savedCard' : true,
                  'savedCardId' : tenantSavedCards.items[0].id,
                  'savedCardNumber' : tenantSavedCards.items[0].card_number.substr(-4)
                }
              }

              if (ledgerData && ledgerData.items[0]) {
                ledgerData.items.forEach((ledger) => {
                  ledger.tenantEsId = tenantEsId[0];
                  ledger.tenantName = tenantEsId[1];
                  ledger.savedCards = ledgerSavedCard;
                  ledgerDataList.push(ledger);
                });
              }

            });

            if (ledgerDataList[0]) {
              // Universal api for now has customer_id instead of tenant_id
              if ('customer_id' in ledgerDataList[0]) {
                ledgerDataList[0].tenant_id = ledgerDataList[0].customer_id;
              }

              pbpData.payState = 'customerFound';
              pbpData.paySubState = 'confirmLedger';
              pbpData.tenant_id = ledgerDataList[0].tenant_id;
              pbpData.tenantEsId = ledgerDataList[0].tenantEsId;
              pbpData.allowPrepay = true;
              if (locData.convenience_fee_phone && locData.convenience_fee_phone > 0 ) {
                pbpData.convenienceFeeAmount = locData.convenience_fee_phone
              }

              if (ledgerDataList.length > 1) {
                pbpData.paySubState = 'askUnitSelect';
              }

              // 1 is reserved for all units payment and 9 is for repeat
              // so we place units in following way
              let unitDigits = [2, 3, 4, 5, 6, 7, 8];
              let unitArray = [];

              unitDigits = unitDigits.slice(0, ledgerDataList.length);

              unitDigits.forEach(async (digit, index) => {
                let ledgerItem = ledgerDataList[index];

                let paidThruDate = new Date(ledgerItem.paid_thru_date);
                let nextDueDateFormatted = moment_tz(paidThruDate).add(1, 'days').format('MMMM Do, YYYY');

                // Universal api for now has customer_id instead of tenant_id
                if ('customer_id' in ledgerItem) {
                  ledgerItem.tenant_id = ledgerItem.customer_id;
                }

                unitArray[digit] = {
                  'ledgerId'         : ledgerItem.ledger_id,
                  'tenantId'         : ledgerItem.tenant_id,
                  'unitId'           : ledgerItem.unit_id,
                  'amountDue'        : ledgerItem.amount_owed,
                  'ledgerStatus'     : ledgerItem.status,
                  'name'             : ledgerItem.tenantName,
                  'unitNumber'       : ledgerItem.unit_name,
                  'nextDueDate'      : ledgerItem.paid_thru_date,
                  'dueDateFormatted' : nextDueDateFormatted,
                  'esTenantId'       : ledgerItem.tenantEsId,
                  'esUnitId'         : ledgerItem.es_unit_id,
                  'listIndex'        : digit,
                  'savedCards'       : ledgerItem.savedCards
                }

                let today = moment_tz();
                let daysPastDue = today.diff(paidThruDate, 'days');

                if (ledgerItem.status != 'Current' && locData.exclude_payment_link < daysPastDue) {
                  pbpData.paySubState = 'payByPhoneNotAllowed';
                }

                // Determine to allow prepayment or not when press 1 to pay all units in bulk is used.
                if (ledgerItem.status != 'Current') {
                  pbpData.allowPrepay = false;
                }

              });

              pbpData.unitArray = unitArray;

              let savedCards = [];
              if (locData.allow_prev_cc && locData.allow_prev_cc == 1) {
                savedCards = await intClient
                  .getData(`tenant/${pbpData.tenantEsId}/saved_cards`)
                  .catch(error => console.log(error, new Error().stack));
              }

              if (typeof savedCards.items !== 'undefined' && typeof savedCards.items[0] !== 'undefined') {
                pbpData.savedCard = true;
                pbpData.savedCardId = savedCards.items[0].id;
                pbpData.savedCardNumber = savedCards.items[0].card_number.substr(-4);
              }

            } else {
              pbpData.paySubState = 'manualNumberPrompt';
            }
          } else {
            pbpData.paySubState = 'invalidNumber';
          }
        }
        break;

      case 'customerFound':
        if (paySubState == 'waitForPrepayFetch') {

          if (pbpData.unitSelected && pbpData.unitSelected == 1) {
            let totalPrepayDue = 0;
            await pbpData.unitArray.forEachAsync(async (unitItem) => {
              if (unitItem == null) return;
              let amountDuePayload = [
                {
                  "ledger_id": unitItem.ledgerId,
                  "unit_id": unitItem.esUnitId,
                  "prepay_month": pbpData.prepayMonth
                }
              ];
              let prepayData = await intClient
                .postData(`tenant/${unitItem.esTenantId}/amount_due`, amountDuePayload)
                .catch(error => console.log(error, new Error().stack));

              pbpData.unitArray[unitItem.listIndex].amountDue = parseFloat(prepayData[0].meta.total);
              totalPrepayDue += parseFloat(prepayData[0].meta.total);
            });
            pbpData.prepayAmount = totalPrepayDue;
          } else {
            let amountDuePayload = [
              {
                "ledger_id": pbpData.ledgerId,
                "unit_id": pbpData.unitId,
                "prepay_month": pbpData.prepayMonth
              }
            ];
            const prepayData = await intClient
              .postData(`tenant/${pbpData.esTenantId}/amount_due`, amountDuePayload)
              .catch(error => console.log(error, new Error().stack));
            pbpData.prepayAmount = prepayData[0].meta.total;
          }

          pbpData.paySubState = 'confirmaskPrepay';
        }
        break;

      case 'processPayment':
        if (pbpData.paySubState == 'makePayment') {
          pbpData.payState = 'paymentStatus';
          pbpData.paySubState = 'failure';

          let termList = [pbpData.repeatCC,
            pbpData.repeatCVC,
            pbpData.repeatExpiry,
            pbpData.repeatZip];

          if (pbpData.savedCard && pbpData.useSavedCard) {
            termList = [pbpData.repeatCVC, pbpData.repeatZip];
          }

          const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
          let decodeCardData = await coreClient
            .postData(`encodetoken`, {
              'type': 'decode',
              'terms_list': termList
            })
            .catch(error => console.log(error, new Error().stack));

          let bulkPaymentArr = [];
          let paySuccessArr = [];

          if (pbpData.unitSelected && pbpData.unitSelected == 1) {

            // We only need to submit convenience fee once, so add it to first ledger payment only
            let ledgerForConvenienceFee = pbpData.unitArray[2].ledgerId;

            await pbpData.unitArray.forEachAsync(async (unitItem) => {

              if (unitItem == null) return;
              if (unitItem.amountDue == 0) return;

              let paymentHashGet;
              let paymentHash;
              if (pbpData.paymentHash && pbpData.paymentHashLedgerId === unitItem.ledgerId) {
                paymentHash = pbpData.paymentHash;
                paymentHashGet = pbpData.paymentHashGet;
              } else {
                let paymentHashResponse = await intClient.postData('payment_hash', {
                  customer_id : unitItem.tenantId,
                  location_id : locationId,
                  ledger_id   : unitItem.ledgerId,
                  source      : "call"
                }).catch(error => console.log(error, new Error().stack));

                paymentHash = paymentHashResponse.hash;
                paymentHashGet = await intClient.getData(`payment_hash/${paymentHash}`)
                  .catch(error => console.log(error, new Error().stack));
              }

              // Total amount due will be 0 when we are doing prepayment
              let totalAmountDue = (pbpData.prepayAmount > 0) ? 0 : unitItem.amountDue;

              let paymentData = {
                amount                 : unitItem.amountDue,
                convenience_fee        : false,
                cvc_number             : decodeCardData[1],
                is_auto_pay            : false,
                pay_method             : 'creditcard',
                payment_hash           : paymentHash,
                postal_code            : decodeCardData[3],
                save_cc                : locData.save_cc == 1 ? true : false,
                total_amount_due       : totalAmountDue,
                unit_id                : unitItem.esUnitId
              };

              if (typeof pbpData.prepayMonth !== 'undefined') {
                paymentData.prepay_months = pbpData.prepayMonth;
              }

              if (pbpData.savedCard && pbpData.useSavedCard) {
                paymentData.card_id = pbpData.savedCardId;
                paymentData.cvc_number = decodeCardData[0];
                paymentData.postal_code = decodeCardData[1];
              } else {
                paymentData.card_number = decodeCardData[0];
                paymentData.cvc_number = decodeCardData[1];
                paymentData.exp_month = decodeCardData[2].substring(0, 2);
                paymentData.exp_year = '' + '20' + decodeCardData[2].substring(2);
                paymentData.postal_code = decodeCardData[3];
              }

              if (paymentHashGet && paymentHashGet.items) {
                paymentData.payment_id = paymentHashGet.items.payment_id;

                if (locData.convenience_fee_phone &&
                  locData.convenience_fee_phone > 0 &&
                  (ledgerForConvenienceFee == unitItem.ledgerId)
                ) {
                  paymentData.convenience_fee_amount = locData.convenience_fee_phone
                  paymentData.convenience_fee = true;
                  paymentData.convenience_fee_source = paymentHashGet.items.source;
                }

                bulkPaymentArr.push(paymentData);
              }
            });
          } else {

            const paymentHash = pbpData.paymentHash;
            const paymentHashGet = pbpData.paymentHashGet;

            // Total amount due will be 0 when we are doing prepayment
            let totalAmountDue = (pbpData.prepayAmount > 0) ? 0 : pbpData.totalAmount;

            let paymentData = {
              amount                 : pbpData.totalAmount,
              convenience_fee        : false,
              is_auto_pay            : false,
              pay_method             : 'creditcard',
              payment_hash           : paymentHash,
              save_cc                : locData.save_cc == 1 ? true : false,
              total_amount_due       : totalAmountDue,
              unit_id                : pbpData.unitId
            };

            if (typeof pbpData.prepayMonth !== 'undefined') {
              paymentData.prepay_months = pbpData.prepayMonth;
            }

            if (pbpData.savedCard && pbpData.useSavedCard) {
              paymentData.card_id = pbpData.savedCardId;
              paymentData.cvc_number = decodeCardData[0];
              paymentData.postal_code = decodeCardData[1];
            } else {
              paymentData.card_number = decodeCardData[0];
              paymentData.cvc_number = decodeCardData[1];
              paymentData.exp_month = decodeCardData[2].substring(0, 2);
              paymentData.exp_year = '' + '20' + decodeCardData[2].substring(2);
              paymentData.postal_code = decodeCardData[3];
            }

            if (paymentHashGet && paymentHashGet.items) {
              paymentData.payment_id = paymentHashGet.items.payment_id;

              if (locData.convenience_fee_phone && locData.convenience_fee_phone > 0 ) {
                paymentData.convenience_fee_amount = locData.convenience_fee_phone
                paymentData.convenience_fee = true;
                paymentData.convenience_fee_source = paymentHashGet.items.source;
              }

              bulkPaymentArr.push(paymentData);
            }
          }

          let errorMessages = []; // collect error messages for each payment

          if (locData.allow_bulk_payment && locData.allow_bulk_payment == 1) {
            const makePayment = await intClient.postData(
              `tenant/${pbpData.esTenantId}/payment_bulk`, bulkPaymentArr
            ).catch(error => {
              console.error(error, new Error().stack);
            });

            if (makePayment.success) {
              pbpData.paySubState = 'success';
            } else {
              // Extract error message for bulk payment
              const error_msg = makePayment.error_msg || 'Unknown error';
              errorMessages.push(error_msg);
            }
          } else {
            await bulkPaymentArr.forEachAsync(async (paymentItem) => {
              const makePayment = await intClient.postData(
                `tenant/${pbpData.esTenantId}/payment`, paymentItem
              ).catch(error => {
                console.error(error, new Error().stack);
              });

              if (makePayment.success) {
                paySuccessArr.push(true);
              } else {
                paySuccessArr.push(false);
                // Extract error message for each individual payment
                const error_msg = makePayment.error_msg || 'Unknown error';
                errorMessages.push(error_msg);
              }
            })
            // If all payment are success then payment is success otherwise failure
            pbpData.paySubState = paySuccessArr.every((element) => { return element } );

            if (pbpData.paySubState) {
              pbpData.paySubState = 'success';
            } else {
              pbpData.paySubState = 'failure';
            }
          }

          const locale = pbpData.locale || 'unknown';
          if(pbpData.paySubState === 'success') {
            const paymentAmount = bulkPaymentArr.reduce((total, item) => total + item.amount, 0);
            const paymentAmountBucket = dataDogService.getBucket(paymentAmount, 500, 25);
            dataDogService.incrementCounter('old_pay_by_phone.payment.success', [
              `locale:${locale}`,
              `payment_amount:${paymentAmountBucket}`,
            ]);
          }
          else
          {
            const combinedErrorMessage = errorMessages.join(', ') || 'Unknown error';
            dataDogService.incrementCounter('old_pay_by_phone.payment.failure', [
              `locale:${locale}`,
              `error_msg:${combinedErrorMessage}`
            ]);
          }
        }
    }

    try {
      console.debug('pbpData after processing', pbpData);

      await redisClient.set(call_task_key,
        JSON.stringify(pbpData),
        {EX: redisTtl}
        );
      await redisClient.disconnect();

      await twilio.calls(pbpData.callSid)
        .update({method: 'POST', url: `${config.call_url}twilio/process_next_step/${pbpData.logId}?payment_process=1`})
        .catch(error => console.error(error, new Error().stack));
    } catch (e) {
      console.error(e, new Error().stack);
    }

    resp.send();
  })

module.exports = router
