<?php
/**
 * User model
 *
 * @category User
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Phalcon\Validation;

/**
 * User model
 *
 * @category User
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 *
 * @SWG\Definition(definition="User")
 */
class User extends CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * User ID
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    public $user_id;

    /**
     * Plan ID
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    public $plan_id;

    /**
     * Email Address
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    public $email;

    /**
     * Password
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     */
    public $password;

    /**
     * First Name
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    public $firstname;

    /**
     * Last Name
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    public $lastname;

    /**
     * Phone Number
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    public $phone;

    /**
     * Question1
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $question1;

    /**
     * Question2
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $question2;

    /**
     * Question3
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $question3;

    /**
     * Answer1
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    public $answer1;

    /**
     * Answer2
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    public $answer2;

    /**
     * Answer3
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     */
    public $answer3;

    /**
     * Company Name
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    public $companyname;

    /**
     * Master
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $master;

    /**
     * Signupdate
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $signupdate;

    /**
     * Lastagreement
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $lastagreement;

    /**
     * Agreement_ip
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $agreement_ip;

    /**
     * Creditamount
     *
     * @var float
     *
     * @Column(type="decimal", length=10, nullable=true)
     * @SWG\Property()
     */
    public $creditamount;

    /**
     * Active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $active;

    /**
     * Lastlogin
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $lastlogin;

    /**
     * Sid
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $sid;

    /**
     * Authentication token
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $authtoken;

    /**
     * Howheard
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    public $howheard;

    /**
     * Confirmed
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $confirmed;

    /**
     * Parent ID
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    public $parent_id;

    /**
     * Reports
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $reports;

    /**
     * Tips
     *
     * @var string
     *
     * @Column(type="string", length=2048, nullable=true)
     * @SWG\Property()
     */
    public $tips;

    /**
     * Dashboard
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $dashboard;

    /**
     * Default Location
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    public $default_location;

    /**
     * Default timezone
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true, default = "America/Chicago")
     * @SWG\Property()
     */
    public $default_timezone;

    /**
     * Dm report period
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    public $dm_report_period;

    /**
     * Dm report last update
     *
     * @var string|null
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $dm_report_last_update;

    /**
     * Newlead email notify
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    public $newlead_email_notify;

    /**
     * Stale followup notification
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    public $stale_followup_notification;

    /**
     * Stale followup threshold
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    public $stale_followup_threshold;

    /**
     * Stale followup report last update
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $stale_followup_report_last_update;

    /**
     * Send followup notification
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $send_followup_notification;

    /**
     * Send collection notification
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $send_collection_notification;

    /**
     * Optin mktg
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $optin_mktg;

    /**
     * Is test account
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $is_test_account;

    /**
     * Dm report send day
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    public $dm_report_send_day;

    /**
     * Delayed collection email notify
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    public $delayed_collection_email_notify;

    /**
     * Is agent
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $is_agent;

    /**
     * Agent call sid
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    public $agent_call_sid;

    /**
     * Agent calling sid
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    public $agent_calling_sid;

    /**
     * Is agent call connected
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $is_agent_call_connected;

    /**
     * Agent status
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    public $agent_status;

    /**
     * Inactive reason
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $inactive_reason;

    /**
     * Tc access token
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    public $tc_access_token;

    /**
     * Is user logged in
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $is_logged_in;

    /**
     * Scheduled logout
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    public $scheduled_logout;

    /**
     * Ssession refresh
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    public $session_refresh;

    /**
     * Disable unsubscribe text
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $disable_unsubscribe_txt;

    /**
     * Custom variables
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $custom_variables;

    /**
     * Show admin in emp dropdown
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $show_admin_in_emp_dropdown;

    /**
     * Employee restrict percent
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    public $employee_restrict_percent;

    /**
     * Manager restrict percent
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    public $manager_restrict_percent;

    /**
     * Agent restrict percent
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    public $agent_restrict_percent;

    /**
     * Workspace SId
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    public $workspace_sid;

    /**
     * Worker SID
     *
     * @var string
     *
     * @Column(type="string", length=34, nullable=true)
     * @SWG\Property()
     */
    public $worker_sid;

    /**
     * Enable console log
     *
     * @var int
     *
     * @Column(type="integer", length=3, nullable=true)
     * @SWG\Property()
     */
    public $enable_console_log;

    /**
     * Other option employee
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $other_option_employee;

    /**
     * Other option required
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $other_option_required;

    /**
     * Consolidate invoices
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $consolidate_invoices;

    /**
     * Restrict manager edit employee grade
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $restrict_manager_edit_employee_grade;

    /**
     * Exclusion list type
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    public $exclusion_list_type;

    /**
     * Transcribe api
     *
     * @var string
     *
     * @Column(type="string", length=15, nullable=true)
     * @SWG\Property()
     */
    public $transcribe_api;

    /**
     * Is response note enabled
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $is_response_note_enabled;

    /**
     * Txt consent
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $txt_consent;

    /**
     * Opt in text
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $opt_in_txt;

    /**
     * Enable duplicate lead email notification
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $enable_duplicate_lead_email_notification;

    /**
     * Duplicate lead email notification setup
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $duplicate_lead_email_notification_setup;

    /**
     * Restrict manager edit location grade
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $restrict_manager_edit_location_grade;

    /**
     * Kiosk connection
     *
     * @var int
     *
     * @Column(type="integer", length=3, nullable=true)
     * @SWG\Property()
     */
    public $kiosk_connection;

    /**
     * Sms silence
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $sms_silence;

    /**
     * Disable movein
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $disable_movein;

    /**
     * V2 Worker SID
     *
     * @var string
     *
     * @Column(type="string", length=34, nullable=true)
     * @SWG\Property()
     */
    public $v2_worker_sid;

    /**
     * Urgent notes email notification
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    public $urgent_notes_email_notification;

    /**
     * Reporting Access Configuration at user level
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true, default = "0")
     * @SWG\Property()
     */
    public $is_report_accessible;

    /**
     * Ring central tokens
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    public $ringcentral_tokens;

    /**
     * Method to set the value of field user_id
     *
     * @param string|null $user_id user Id to set
     *
     * @return User
     */
    public function setUserId($user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field plan_id
     *
     * @param string|null $plan_id plan Id to set
     *
     * @return User
     */
    public function setPlanId($plan_id)
    {
        $this->plan_id = $plan_id;

        return $this;
    }

    /**
     * Method to set the value of field email
     *
     * @param string|null $email email address to set
     *
     * @return User
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Method to set the value of field password
     *
     * @param string|null $password password to set
     *
     * @return User
     */
    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Method to set the value of field firstname
     *
     * @param string|null $firstname first name to set
     *
     * @return User
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * Method to set the value of field lastname
     *
     * @param string|null $lastname last name to set
     *
     * @return User
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * Method to set the value of field phone
     *
     * @param string|null $phone phone number to set
     *
     * @return User
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Method to set the value of field question1
     *
     * @param string|null $question1 question 1 to set
     *
     * @return User
     */
    public function setQuestion1($question1)
    {
        $this->question1 = $question1;

        return $this;
    }

    /**
     * Method to set the value of field question2
     *
     * @param string|null $question2 question 2 to set
     *
     * @return User
     */
    public function setQuestion2($question2)
    {
        $this->question2 = $question2;

        return $this;
    }

    /**
     * Method to set the value of field question3
     *
     * @param string|null $question3 question 3 to set
     *
     * @return User
     */
    public function setQuestion3($question3)
    {
        $this->question3 = $question3;

        return $this;
    }

    /**
     * Method to set the value of field answer1
     *
     * @param string|null $answer1 answer1 to set
     *
     * @return User
     */
    public function setAnswer1($answer1)
    {
        $this->answer1 = $answer1;

        return $this;
    }

    /**
     * Method to set the value of field answer2
     *
     * @param string|null $answer2 answer 2 to set
     *
     * @return User
     */
    public function setAnswer2($answer2)
    {
        $this->answer2 = $answer2;

        return $this;
    }

    /**
     * Method to set the value of field answer3
     *
     * @param string|null $answer3 answer 3 to set
     *
     * @return User
     */
    public function setAnswer3($answer3)
    {
        $this->answer3 = $answer3;

        return $this;
    }

    /**
     * Method to set the value of field companyname
     *
     * @param string|null $companyname company name to set
     *
     * @return User
     */
    public function setCompanyname($companyname)
    {
        $this->companyname = $companyname;

        return $this;
    }

    /**
     * Method to set the value of field master
     *
     * @param integer $master master to set
     *
     * @return User
     */
    public function setMaster(int $master)
    {
        $this->master = $master;

        return $this;
    }

    /**
     * Method to set the value of field signupdate
     *
     * @param string|null $signupdate date to set
     *
     * @return User
     */
    public function setSignupdate($signupdate)
    {
        $this->signupdate = $signupdate;

        return $this;
    }

    /**
     * Method to set the value of field lastagreement
     *
     * @param string|null $lastagreement last agreement to set
     *
     * @return User
     */
    public function setLastagreement($lastagreement)
    {
        $this->lastagreement = $lastagreement;

        return $this;
    }

    /**
     * Method to set the value of field agreement_ip
     *
     * @param string|null $agreement_ip agreement ip to set
     *
     * @return User
     */
    public function setAgreementIp($agreement_ip)
    {
        $this->agreement_ip = $agreement_ip;

        return $this;
    }

    /**
     * Method to set the value of field creditamount
     *
     * @param string|null $creditamount credit amount to set
     *
     * @return User
     */
    public function setCreditamount($creditamount)
    {
        $this->creditamount = $creditamount;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active active to set
     *
     * @return User
     */
    public function setActive(int $active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field lastlogin
     *
     * @param string|null $lastlogin last login to set
     *
     * @return User
     */
    public function setLastlogin($lastlogin)
    {
        $this->lastlogin = $lastlogin;

        return $this;
    }

    /**
     * Method to set the value of field sid
     *
     * @param string|null $sid SID to set
     *
     * @return User
     */
    public function setSid($sid)
    {
        $this->sid = $sid;

        return $this;
    }

    /**
     * Method to set the value of field authtoken
     *
     * @param string|null $authtoken auth token to set
     *
     * @return User
     */
    public function setAuthtoken($authtoken)
    {
        $this->authtoken = $authtoken;

        return $this;
    }

    /**
     * Method to set the value of field howheard
     *
     * @param string|null $howheard how heard to set
     *
     * @return User
     */
    public function setHowheard($howheard)
    {
        $this->howheard = $howheard;

        return $this;
    }

    /**
     * Method to set the value of field confirmed
     *
     * @param integer $confirmed confirmed to set
     *
     * @return User
     */
    public function setConfirmed(int $confirmed)
    {
        $this->confirmed = $confirmed;

        return $this;
    }

    /**
     * Method to set the value of field parent_id
     *
     * @param integer $parent_id parent Id to set
     *
     * @return User
     */
    public function setParentId(int $parent_id)
    {
        $this->parent_id = $parent_id;

        return $this;
    }

    /**
     * Method to set the value of field reports
     *
     * @param string|null $reports reports to set
     *
     * @return User
     */
    public function setReports($reports)
    {
        $this->reports = $reports;

        return $this;
    }

    /**
     * Method to set the va|nulllue of field tips
     *
     * @param string $tips tips to set
     *
     * @return User
     */
    public function setTips($tips)
    {
        $this->tips = $tips;

        return $this;
    }

    /**
     * Method to set the value of field dashboard
     *
     * @param integer|null $dashboard dashboard to set
     *
     * @return User
     */
    public function setDashboard($dashboard)
    {
        $this->dashboard = $dashboard;

        return $this;
    }

    /**
     * Method to set the value of field default_location
     *
     * @param string|null $default_location default location to set
     *
     * @return User
     */
    public function setDefaultLocation($default_location)
    {
        $this->default_location = $default_location;

        return $this;
    }

    /**
     * Method to set the value of field default_timezone
     *
     * @param string|null $default_timezone default timezone to set
     *
     * @return User
     */
    public function setDefaultTimezone($default_timezone)
    {
        $this->default_timezone = $default_timezone;

        return $this;
    }

    /**
     * Method to set the value of field dm_report_period
     *
     * @param string|null $dm_report_period report period to set
     *
     * @return User
     */
    public function setDmReportPeriod($dm_report_period)
    {
        $this->dm_report_period = $dm_report_period;

        return $this;
    }

    /**
     * Method to set the value of field dm_report_last_update
     *
     * @param string|null $dm_report_last_update last update date to set
     *
     * @return User
     */
    public function setDmReportLastUpdate($dm_report_last_update)
    {
        $this->dm_report_last_update = $dm_report_last_update;

        return $this;
    }

    /**
     * Method to set the value of field newlead_email_notify
     *
     * @param integer $newlead_email_notify notify to set
     *
     * @return User
     */
    public function setNewleadEmailNotify(int $newlead_email_notify)
    {
        $this->newlead_email_notify = $newlead_email_notify;

        return $this;
    }

    /**
     * Method to set the value of field stale_followup_notification
     *
     * @param integer $stale_followup_notification notify to set
     *
     * @return User
     */
    public function setStaleFollowupNotification(int $stale_followup_notification)
    {
        $this->stale_followup_notification = $stale_followup_notification;

        return $this;
    }

    /**
     * Method to set the value of field stale_followup_threshold
     *
     * @param integer $stale_followup_threshold notify to set
     *
     * @return User
     */
    public function setStaleFollowupThreshold(int $stale_followup_threshold)
    {
        $this->stale_followup_threshold = $stale_followup_threshold;

        return $this;
    }

    /**
     * Method to set the value of field stale_followup_report_last_update
     *
     * @param string|null $stale_followup_report_last_update notify to set
     *
     * @return User
     */
    public function setStaleFollowupReportLastUpdate($stale_followup_report_last_update)
    {
        $this->stale_followup_report_last_update = $stale_followup_report_last_update;

        return $this;
    }

    /**
     * Method to set the value of field send_followup_notification
     *
     * @param integer $send_followup_notification send notification value to set
     *
     * @return User
     */
    public function setSendFollowupNotification(int $send_followup_notification)
    {
        $this->send_followup_notification = $send_followup_notification;

        return $this;
    }

    /**
     * Method to set the value of field send_collection_notification
     *
     * @param integer $send_collection_notification send notification value to set
     *
     * @return User
     */
    public function setSendCollectionNotification(int $send_collection_notification)
    {
        $this->send_collection_notification = $send_collection_notification;

        return $this;
    }

    /**
     * Method to set the value of field optin_mktg
     *
     * @param integer $optin_mktg option to set
     *
     * @return User
     */
    public function setOptinMktg(int $optin_mktg)
    {
        $this->optin_mktg = $optin_mktg;

        return $this;
    }

    /**
     * Method to set the value of field is_test_account
     *
     * @param integer $is_test_account is test account boolen value to set
     *
     * @return User
     */
    public function setIsTestAccount(int $is_test_account)
    {
        $this->is_test_account = $is_test_account;

        return $this;
    }

    /**
     * Method to set the value of field dm_report_send_day
     *
     * @param string|null $dm_report_send_day value to set
     *
     * @return User
     */
    public function setDmReportSendDay($dm_report_send_day)
    {
        $this->dm_report_send_day = $dm_report_send_day;

        return $this;
    }

    /**
     * Method to set the value of field delayed_collection_email_notify
     *
     * @param integer $delayed_collection_email_notify value to set
     *
     * @return User
     */
    public function setDelayedCollectionEmailNotify(int $delayed_collection_email_notify)
    {
        $this->delayed_collection_email_notify = $delayed_collection_email_notify;

        return $this;
    }

    /**
     * Method to set the value of field is_agent
     *
     * @param integer $is_agent is agent to set
     *
     * @return User
     */
    public function setIsAgent(int $is_agent)
    {
        $this->is_agent = $is_agent;

        return $this;
    }

    /**
     * Method to set the value of field agent_call_sid
     *
     * @param string|null $agent_call_sid agent call SID to set
     *
     * @return User
     */
    public function setAgentCallSid($agent_call_sid)
    {
        $this->agent_call_sid = $agent_call_sid;

        return $this;
    }

    /**
     * Method to set the value of field agent_calling_sid
     *
     * @param string|null $agent_calling_sid agent calling SID to set
     *
     * @return User
     */
    public function setAgentCallingSid($agent_calling_sid)
    {
        $this->agent_calling_sid = $agent_calling_sid;

        return $this;
    }

    /**
     * Method to set the value of field is_agent_call_connected
     *
     * @param integer $is_agent_call_connected is call connected value to set
     *
     * @return User
     */
    public function setIsAgentCallConnected(int $is_agent_call_connected)
    {
        $this->is_agent_call_connected = $is_agent_call_connected;

        return $this;
    }

    /**
     * Method to set the value of field agent_status
     *
     * @param string|null $agent_status agent status to set
     *
     * @return User
     */
    public function setAgentStatus($agent_status)
    {
        $this->agent_status = $agent_status;

        return $this;
    }

    /**
     * Method to set the value of field inactive_reason
     *
     * @param string|null $inactive_reason inactive user reason to set
     *
     * @return User
     */
    public function setInactiveReason($inactive_reason)
    {
        $this->inactive_reason = $inactive_reason;

        return $this;
    }

    /**
     * Method to set the value of field tc_access_token
     *
     * @param string|null $tc_access_token thinclient access token to set
     *
     * @return User
     */
    public function setTcAccessToken($tc_access_token)
    {
        $this->tc_access_token = $tc_access_token;

        return $this;
    }

    /**
     * Method to set the value of field is_logged_in
     *
     * @param integer $is_logged_in is user logged in to set
     *
     * @return User
     */
    public function setIsLoggedIn(int $is_logged_in)
    {
        $this->is_logged_in = $is_logged_in;

        return $this;
    }

    /**
     * Method to set the value of field scheduled_logout
     *
     * @param string|null $scheduled_logout schedule logout to set
     *
     * @return User
     */
    public function setScheduledLogout($scheduled_logout)
    {
        $this->scheduled_logout = $scheduled_logout;

        return $this;
    }

    /**
     * Method to set the value of field session_refresh
     *
     * @param string|null $session_refresh session refresh time to set
     *
     * @return User
     */
    public function setSessionRefresh($session_refresh)
    {
        $this->session_refresh = $session_refresh;

        return $this;
    }

    /**
     * Method to set the value of field disable_unsubscribe_txt
     *
     * @param integer $disable_unsubscribe_txt disable unsubscribe text to set
     *
     * @return User
     */
    public function setDisableUnsubscribeTxt(int $disable_unsubscribe_txt)
    {
        $this->disable_unsubscribe_txt = $disable_unsubscribe_txt;

        return $this;
    }

    /**
     * Method to set the value of field custom_variables
     *
     * @param string|null $custom_variables variables to set
     *
     * @return User
     */
    public function setCustomVariables($custom_variables)
    {
        $this->custom_variables = $custom_variables;

        return $this;
    }

    /**
     * Method to set the value of field show_admin_in_emp_dropdown
     *
     * @param integer $show_admin_in_emp_dropdown list admin in employee dropdown to set
     *
     * @return User
     */
    public function setShowAdminInEmpDropdown(int $show_admin_in_emp_dropdown)
    {
        $this->show_admin_in_emp_dropdown = $show_admin_in_emp_dropdown;

        return $this;
    }

    /**
     * Method to set the value of field employee_restrict_percent
     *
     * @param integer $employee_restrict_percent employee restrict percent to set
     *
     * @return User
     */
    public function setEmployeeRestrictPercent(int $employee_restrict_percent)
    {
        $this->employee_restrict_percent = $employee_restrict_percent;

        return $this;
    }

    /**
     * Method to set the value of field manager_restrict_percent
     *
     * @param integer $manager_restrict_percent manager restrict percent to set
     *
     * @return User
     */
    public function setManagerRestrictPercent(int $manager_restrict_percent)
    {
        $this->manager_restrict_percent = $manager_restrict_percent;

        return $this;
    }

    /**
     * Method to set the value of field agent_restrict_percent
     *
     * @param integer $agent_restrict_percent agent restrict percent to set
     *
     * @return User
     */
    public function setAgentRestrictPercent(int $agent_restrict_percent)
    {
        $this->agent_restrict_percent = $agent_restrict_percent;

        return $this;
    }

    /**
     * Method to set the value of field workspace_sid
     *
     * @param string|null $workspace_sid workspace SID to set
     *
     * @return User
     */
    public function setWorkspaceSid($workspace_sid)
    {
        $this->workspace_sid = $workspace_sid;

        return $this;
    }

    /**
     * Method to set the value of field worker_sid
     *
     * @param string|null $worker_sid worker SID to set
     *
     * @return User
     */
    public function setWorkerSid($worker_sid)
    {
        $this->worker_sid = $worker_sid;

        return $this;
    }

    /**
     * Method to set the value of field enable_console_log
     *
     * @param integer|null $enable_console_log enable custom log to set
     *
     * @return User
     */
    public function setEnableConsoleLog($enable_console_log)
    {
        $this->enable_console_log = $enable_console_log;

        return $this;
    }

    /**
     * Method to set the value of field other_option_employee
     *
     * @param integer|null $other_option_employee employee other option to set
     *
     * @return User
     */
    public function setOtherOptionEmployee($other_option_employee)
    {
        $this->other_option_employee = $other_option_employee;

        return $this;
    }

    /**
     * Method to set the value of field other_option_required
     *
     * @param integer|null $other_option_required other required option to set
     *
     * @return User
     */
    public function setOtherOptionRequired($other_option_required)
    {
        $this->other_option_required = $other_option_required;

        return $this;
    }

    /**
     * Method to set the value of field consolidate_invoices
     *
     * @param string|null $consolidate_invoices invoices to set
     *
     * @return User
     */
    public function setConsolidateInvoices($consolidate_invoices)
    {
        $this->consolidate_invoices = $consolidate_invoices;

        return $this;
    }

    /**
     * Method to set the value of field restrict_manager_edit_employee_grade
     *
     * @param string|null $restrict_manager_edit_employee_grade manager employee grade to set
     *
     * @return User
     */
    public function setRestrictManagerEditEmployeeGrade($restrict_manager_edit_employee_grade)
    {
        $this->restrict_manager_edit_employee_grade = $restrict_manager_edit_employee_grade;

        return $this;
    }

    /**
     * Method to set the value of field exclusion_list_type
     *
     * @param string|null $exclusion_list_type list type to set
     *
     * @return User
     */
    public function setExclusionListType($exclusion_list_type)
    {
        $this->exclusion_list_type = $exclusion_list_type;

        return $this;
    }

    /**
     * Method to set the value of field transcribe_api
     *
     * @param string|null $transcribe_api transcribe api to set
     *
     * @return User
     */
    public function setTranscribeApi($transcribe_api)
    {
        $this->transcribe_api = $transcribe_api;

        return $this;
    }

    /**
     * Method to set the value of field is_response_note_enabled
     *
     * @param integer $is_response_note_enabled boolean value to set
     *
     * @return User
     */
    public function setIsResponseNoteEnabled(int $is_response_note_enabled)
    {
        $this->is_response_note_enabled = $is_response_note_enabled;

        return $this;
    }

    /**
     * Method to set the value of field txt_consent
     *
     * @param integer $txt_consent boolean value to set
     *
     * @return User
     */
    public function setTxtConsent(int $txt_consent)
    {
        $this->txt_consent = $txt_consent;

        return $this;
    }

    /**
     * Method to set the value of field opt_in_txt
     *
     * @param integer $opt_in_txt boolean value to set
     *
     * @return User
     */
    public function setOptInTxt(int $opt_in_txt)
    {
        $this->opt_in_txt = $opt_in_txt;

        return $this;
    }

    /**
     * Method to set the value of field enable_duplicate_lead_email_notification
     *
     * @param integer $enable_notification boolean value to set
     *
     * @return User
     */
    public function setEnableDuplicateLeadEmailNotification(
        int $enable_notification
    ) {
        $this->enable_duplicate_lead_email_notification = $enable_notification;

        return $this;
    }

    /**
     * Method to set the value of field duplicate_lead_email_notification_setup
     *
     * @param string|null $notification_setup value to set
     *
     * @return User
     */
    public function setDuplicateLeadEmailNotificationSetup($notification_setup)
    {
        $this->duplicate_lead_email_notification_setup = $notification_setup;

        return $this;
    }

    /**
     * Method to set the value of field restrict_manager_edit_location_grade
     *
     * @param string|null $restrict_manager_edit_location_grade grade to set
     *
     * @return User
     */
    public function setRestrictManagerEditLocationGrade($restrict_manager_edit_location_grade)
    {
        $this->restrict_manager_edit_location_grade = $restrict_manager_edit_location_grade;

        return $this;
    }

    /**
     * Method to set the value of field kiosk_connection
     *
     * @param integer|null $kiosk_connection boolean value to set
     *
     * @return User
     */
    public function setKioskConnection($kiosk_connection)
    {
        $this->kiosk_connection = $kiosk_connection;

        return $this;
    }

    /**
     * Method to set the value of field sms_silence
     *
     * @param integer $sms_silence boolean value to set
     *
     * @return User
     */
    public function setSmsSilence(int $sms_silence)
    {
        $this->sms_silence = $sms_silence;

        return $this;
    }

    /**
     * Method to set the value of field disable_movein
     *
     * @param integer|null $disable_movein Disable movein to set
     *
     * @return $this
     */
    public function setDisableMovein($disable_movein)
    {
        $this->disable_movein = $disable_movein;

        return $this;
    }

    /**
     * Method to set the value of field v2_worker_sid
     *
     * @param string|null $v2_worker_sid worker SID to set
     *
     * @return User
     */
    public function setV2WorkerSid($v2_worker_sid)
    {
        $this->v2_worker_sid = $v2_worker_sid;

        return $this;
    }

    /**
     * Method to set the value of field urgent_notes_email_notification
     *
     * @param string|null $urgent_notes_email_notification value to set
     *
     * @return User
     */
    public function setUrgentNotesEmailNotificationSetup($urgent_notes_email_notification)
    {
        $this->urgent_notes_email_notification = $urgent_notes_email_notification;

        return $this;
    }

    /**
     * Method to set the value of field is_report_accessible
     *
     * @param integer $is_report_accessible notify to set
     *
     * @return User
     */
    public function setIsReportAccessible($is_report_accessible)
    {
        $this->is_report_accessible = $is_report_accessible;

        return $this;
    }

    /**
     * Method to set the value of field duplicate_lead_email_notification_setup
     *
     * @param string|null $ringcentral_tokens value to set
     *
     * @return User
     */
    public function setRingcentralTokens($ringcentral_tokens)
    {
        $this->ringcentral_tokens = $ringcentral_tokens;

        return $this;
    }

    /**
     * Returns the value of field user_id
     *
     * @return string
     */
    public function getUserId(): string
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field plan_id
     *
     * @return string
     */
    public function getPlanId(): string
    {
        return $this->plan_id;
    }

    /**
     * Returns the value of field email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Returns the value of field password
     *
     * @return string
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    /**
     * Returns the value of field firstname
     *
     * @return string
     */
    public function getFirstname(): string
    {
        return $this->firstname;
    }

    /**
     * Returns the value of field lastname
     *
     * @return string
     */
    public function getLastname(): string
    {
        return $this->lastname;
    }

    /**
     * Returns the value of field phone
     *
     * @return string
     */
    public function getPhone(): string
    {
        return $this->phone;
    }

    /**
     * Returns the value of field question1
     *
     * @return string
     */
    public function getQuestion1(): string
    {
        return $this->question1;
    }

    /**
     * Returns the value of field question2
     *
     * @return string
     */
    public function getQuestion2(): string
    {
        return $this->question2;
    }

    /**
     * Returns the value of field question3
     *
     * @return string
     */
    public function getQuestion3(): string
    {
        return $this->question3;
    }

    /**
     * Returns the value of field answer1
     *
     * @return string
     */
    public function getAnswer1(): string
    {
        return $this->answer1;
    }

    /**
     * Returns the value of field answer2
     *
     * @return string
     */
    public function getAnswer2(): string
    {
        return $this->answer2;
    }

    /**
     * Returns the value of field answer3
     *
     * @return string
     */
    public function getAnswer3(): string
    {
        return $this->answer3;
    }

    /**
     * Returns the value of field companyname
     *
     * @return string
     */
    public function getCompanyname(): string
    {
        return $this->companyname;
    }

    /**
     * Returns the value of field master
     *
     * @return int
     */
    public function getMaster(): int
    {
        return $this->master;
    }

    /**
     * Returns the value of field signupdate
     *
     * @return string
     */
    public function getSignupdate(): string
    {
        return $this->signupdate;
    }

    /**
     * Returns the value of field lastagreement
     *
     * @return string|null
     */
    public function getLastagreement()
    {
        return $this->lastagreement;
    }

    /**
     * Returns the value of field agreement_ip
     *
     * @return string
     */
    public function getAgreementIp(): string
    {
        return $this->agreement_ip;
    }

    /**
     * Returns the value of field creditamount
     *
     * @return double
     */
    public function getCreditamount(): double
    {
        return $this->creditamount;
    }

    /**
     * Returns the value of field active
     *
     * @return int
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field lastlogin
     *
     * @return string
     */
    public function getLastlogin(): string
    {
        return $this->lastlogin;
    }

    /**
     * Returns the value of field sid
     *
     * @return string
     */
    public function getSid(): string
    {
        return $this->sid;
    }

    /**
     * Returns the value of field authtoken
     *
     * @return string
     */
    public function getAuthtoken(): string
    {
        return $this->authtoken;
    }

    /**
     * Returns the value of field howheard
     *
     * @return string
     */
    public function getHowheard(): string
    {
        return $this->howheard;
    }

    /**
     * Returns the value of field confirmed
     *
     * @return int
     */
    public function getConfirmed(): int
    {
        return $this->confirmed;
    }

    /**
     * Returns the value of field parent_id
     *
     * @return int
     */
    public function getParentId(): int
    {
        return $this->parent_id;
    }

    /**
     * Returns the value of field reports
     *
     * @return string
     */
    public function getReports(): string
    {
        return $this->reports;
    }

    /**
     * Returns the value of field tips
     *
     * @return string
     */
    public function getTips(): string
    {
        return $this->tips;
    }

    /**
     * Returns the value of field dashboard
     *
     * @return int
     */
    public function getDashboard(): int
    {
        return $this->dashboard;
    }

    /**
     * Returns the value of field default_location
     *
     * @return string
     */
    public function getDefaultLocation(): string
    {
        return $this->default_location;
    }

    /**
     * Returns the value of field default_timezone
     *
     * @return string
     */
    public function getDefaultTimezone(): string
    {
        return $this->default_timezone;
    }

    /**
     * Returns the value of field dm_report_period
     *
     * @return string
     */
    public function getDmReportPeriod(): string
    {
        return $this->dm_report_period;
    }

    /**
     * Returns the value of field dm_report_last_update
     *
     * @return string
     */
    public function getDmReportLastUpdate(): string
    {
        return $this->dm_report_last_update;
    }

    /**
     * Returns the value of field newlead_email_notify
     *
     * @return int
     */
    public function getNewleadEmailNotify(): int
    {
        return $this->newlead_email_notify;
    }

    /**
     * Return the value of field stale_followup_notification
     *
     * @return int
     */
    public function getStaleFollowupNotification(): int
    {
        return $this->stale_followup_notification;
    }

    /**
     * Return the value of field stale_followup_threshold
     *
     * @return int
     */
    public function getStaleFollowupThreshold(): int
    {
        return $this->stale_followup_threshold;
    }

    /**
     * Return the value of field stale_followup_report_last_update
     *
     * @return string|null
     */
    public function getStaleFollowupReportLastUpdate()
    {
        return $this->stale_followup_report_last_update;
    }

    /**
     * Returns the value of field send_followup_notification
     *
     * @return int
     */
    public function getSendFollowupNotification(): int
    {
        return $this->send_followup_notification;
    }

    /**
     * Returns the value of field send_collection_notification
     *
     * @return int
     */
    public function getSendCollectionNotification(): int
    {
        return $this->send_collection_notification;
    }

    /**
     * Returns the value of field optin_mktg
     *
     * @return int
     */
    public function getOptinMktg(): int
    {
        return $this->optin_mktg;
    }

    /**
     * Returns the value of field is_test_account
     *
     * @return int
     */
    public function getIsTestAccount(): int
    {
        return $this->is_test_account;
    }

    /**
     * Returns the value of field dm_report_send_day
     *
     * @return string
     */
    public function getDmReportSendDay(): string
    {
        return $this->dm_report_send_day;
    }

    /**
     * Returns the value of field delayed_collection_email_notify
     *
     * @return int
     */
    public function getDelayedCollectionEmailNotify(): int
    {
        return $this->delayed_collection_email_notify;
    }

    /**
     * Returns the value of field is_agent
     *
     * @return int
     */
    public function getIsAgent(): int
    {
        return $this->is_agent;
    }

    /**
     * Returns the value of field agent_call_sid
     *
     * @return string
     */
    public function getAgentCallSid(): string
    {
        return $this->agent_call_sid;
    }

    /**
     * Returns the value of field agent_calling_sid
     *
     * @return string
     */
    public function getAgentCallingSid(): string
    {
        return $this->agent_calling_sid;
    }

    /**
     * Returns the value of field is_agent_call_connected
     *
     * @return int
     */
    public function getIsAgentCallConnected(): int
    {
        return $this->is_agent_call_connected;
    }

    /**
     * Returns the value of field agent_status
     *
     * @return string
     */
    public function getAgentStatus(): string
    {
        return $this->agent_status;
    }

    /**
     * Returns the value of field inactive_reason
     *
     * @return string
     */
    public function getInactiveReason(): string
    {
        return $this->inactive_reason;
    }

    /**
     * Returns the value of field tc_access_token
     *
     * @return string
     */
    public function getTcAccessToken(): string
    {
        return $this->tc_access_token;
    }

    /**
     * Returns the value of field is_logged_in
     *
     * @return int
     */
    public function getIsLoggedIn(): int
    {
        return $this->is_logged_in;
    }

    /**
     * Returns the value of field scheduled_logout
     *
     * @return string
     */
    public function getScheduledLogout(): string
    {
        return $this->scheduled_logout;
    }

    /**
     * Returns the value of field session_refresh
     *
     * @return string
     */
    public function getSessionRefresh(): string
    {
        return $this->session_refresh;
    }

    /**
     * Returns the value of field disable_unsubscribe_txt
     *
     * @return int
     */
    public function getDisableUnsubscribeTxt(): int
    {
        return $this->disable_unsubscribe_txt;
    }

    /**
     * Returns the value of field custom_variables
     *
     * @return string
     */
    public function getCustomVariables(): string
    {
        return $this->custom_variables;
    }

    /**
     * Returns the value of field show_admin_in_emp_dropdown
     *
     * @return int
     */
    public function getShowAdminInEmpDropdown(): int
    {
        return $this->show_admin_in_emp_dropdown;
    }

    /**
     * Returns the value of field employee_restrict_percent
     *
     * @return int
     */
    public function getEmployeeRestrictPercent(): int
    {
        return $this->employee_restrict_percent;
    }

    /**
     * Returns the value of field manager_restrict_percent
     *
     * @return int
     */
    public function getManagerRestrictPercent(): int
    {
        return $this->manager_restrict_percent;
    }

    /**
     * Returns the value of field agent_restrict_percent
     *
     * @return int
     */
    public function getAgentRestrictPercent(): int
    {
        return $this->agent_restrict_percent;
    }

    /**
     * Returns the value of field workspace_sid
     *
     * @return string
     */
    public function getWorkspaceSid(): string
    {
        return $this->workspace_sid;
    }

    /**
     * Returns the value of field worker_sid
     *
     * @return string|null
     */
    public function getWorkerSid()
    {
        return $this->worker_sid;
    }

    /**
     * Returns the value of field enable_console_log
     *
     * @return int
     */
    public function getEnableConsoleLog(): int
    {
        return $this->enable_console_log;
    }

    /**
     * Returns the value of field other_option_employee
     *
     * @return int
     */
    public function getOtherOptionEmployee(): int
    {
        return $this->other_option_employee;
    }

    /**
     * Returns the value of field other_option_required
     *
     * @return int
     */
    public function getOtherOptionRequired(): int
    {
        return $this->other_option_required;
    }

    /**
     * Returns the value of field consolidate_invoices
     *
     * @return string
     */
    public function getConsolidateInvoices(): string
    {
        return $this->consolidate_invoices;
    }

    /**
     * Returns the value of field restrict_manager_edit_employee_grade
     *
     * @return string
     */
    public function getRestrictManagerEditEmployeeGrade(): string
    {
        return $this->restrict_manager_edit_employee_grade;
    }

    /**
     * Returns the value of field exclusion_list_type
     *
     * @return string
     */
    public function getExclusionListType(): string
    {
        return $this->exclusion_list_type;
    }

    /**
     * Returns the value of field transcribe_api
     *
     * @return string
     */
    public function getTranscribeApi(): string
    {
        return $this->transcribe_api;
    }

    /**
     * Returns the value of field is_response_note_enabled
     *
     * @return int
     */
    public function getIsResponseNoteEnabled(): int
    {
        return $this->is_response_note_enabled;
    }

    /**
     * Returns the value of field txt_consent
     *
     * @return int
     */
    public function getTxtConsent(): int
    {
        return $this->txt_consent;
    }

    /**
     * Returns the value of field opt_in_txt
     *
     * @return int
     */
    public function getOptInTxt(): int
    {
        return $this->opt_in_txt;
    }

    /**
     * Returns the value of field enable_duplicate_lead_email_notification
     *
     * @return int
     */
    public function getEnableDuplicateLeadEmailNotification(): int
    {
        return $this->enable_duplicate_lead_email_notification;
    }

    /**
     * Returns the value of field duplicate_lead_email_notification_setup
     *
     * @return string
     */
    public function getDuplicateLeadEmailNotificationSetup(): string
    {
        return $this->duplicate_lead_email_notification_setup;
    }

    /**
     * Returns the value of field restrict_manager_edit_location_grade
     *
     * @return string
     */
    public function getRestrictManagerEditLocationGrade(): string
    {
        return $this->restrict_manager_edit_location_grade;
    }

    /**
     * Returns the value of field kiosk_connection
     *
     * @return int
     */
    public function getKioskConnection(): int
    {
        return $this->kiosk_connection;
    }

    /**
     * Returns the value of field sms_silence
     *
     * @return int
     */
    public function getSmsSilence(): int
    {
        return $this->sms_silence;
    }

    /**
     * Returns the value of field disable_movein
     *
     * @return integer
     */
    public function getDisableMovein(): int
    {
        return $this->disable_movein;
    }

    /**
     * Returns the value of field v2_worker_sid
     *
     * @return string|null
     */
    public function getV2WorkerSid()
    {
        return $this->v2_worker_sid;
    }

    /**
     * Returns the value of field urgent_notes_email_notification
     *
     * @return string
     */
    public function getUrgentNotesEmailNotificationSetup(): int
    {
        return $this->urgent_notes_email_notification;
    }

    /**
     * Returns the value of field is_report_accessible
     *
     * @return int|null
     */
    public function getIsReportAccessible(): int
    {
        return $this->is_report_accessible;
    }

    /**
     * Method to get the value of field duplicate_lead_email_notification_setup
     *
     * @return string
     */
    public function getRingcentralTokens()
    {
        return $this->ringcentral_tokens;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->setSource("users");
    }
}
