import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { DomainEventsService } from '@cp-workspace/shared';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { RequestResponseInterceptor } from './RequestResponseInterceptor';

@Injectable()
export class CallSidRequestInterceptor extends RequestResponseInterceptor {

  constructor(protected logger: DomainEventsService) {
    super(logger);
    this.source = "comms-api.call-sid.controller";
    this.detailType = "CallSidRequest";
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<void> {
    /**
     * In order to correlate domain events, that are emitted during a
     * domain flow, we need to set a global meta data value that uniquely
     * defines the flow.
     * 
     * Many of our domain flows center around a CallSid. We can use this
     * identifier to correlate domain events that are emitted during these
     * types of domain flows.
     * 
     * This Interceptor can be used with any domain that correlates a flow
     * with a CallSid.
     * 
     * If you need to customize the source and detailType, you should subclass
     * this interceptor and override the source and detailType properties.
     */
    const { body } = context.switchToHttp().getRequest<Request>();
    this.logger.globalMeta = { flowId: body?.CallSid || "MISSING_CALL_SID" };

    return super.intercept(context, next);
  }
}
