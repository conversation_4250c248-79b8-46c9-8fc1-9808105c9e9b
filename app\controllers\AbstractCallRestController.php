<?php
/**
 *  Parent controller of all the controllers in call service
 *
 * @category AbstractCallRestController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Phalcon\Http\Response;
use Phalcon\Mvc\Model\Resultset;
use \Phalcon\Mvc\Model\Query\Builder;
use Phalcon\Paginator\Adapter\QueryBuilder as PaginatorQueryBuilder;
use Elasticsearch\Common\Exceptions\Missing404Exception;
use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\HttpStatusCode;
use CallPotential\CPCommon\AppException;

use \CallPotential\CPCommon\Controllers\ElasticSearchTrait;
use CallPotential\CPCommon\Controllers\SessionTrait;

/**
 * Swagger
 *
 * @SWG\Definition(definition="SuccessResponse",type="object",
 * @SWG\Property(property="status",type="string"),
 * @SWG\Property(property="message",type="string"),
 * @SWG\Property(property="data",type="array",@SWG\Items(type="object"))
 *  )
 */

/**
 * Parent controller of all the controllers in call service
 *
 * @category AbstractCallRestController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
abstract class AbstractCallRestController extends CallPotential\CPCommon\Controllers\BaseController
{
    use SessionTrait, ElasticSearchTrait;

    /**
     * Constructor to initialize data
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();

        if (!is_null($this->relationship)) {
            $this->relationship = $this->relationship;
            $this->parentId = $this->getParamID();
        }
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id)
    {
        $objModel = $this->getModelObject();
        $data = $objModel->findById($id);
        $source = Util::array_get('_source', $data, array());
        $source['_id'] = Util::array_get("_id", $data);

        return $source;
    }

    /**
     * Intermediate function to delete data for delete action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function deleteItem($id)
    {
        $objModel = $this->getModelObject();
        $response = $objModel->deleteById($id, $this->parentId);

        return ((bool) $response === true);
    }

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        try {
            $data = Util::objectToArray($this->request->getJsonRawBody());
            if (Util::existSubArray($data, true)) {
                return $this->bulkCreate();
            }
            if (!is_array($data)) {
                return $this->sendBadRequest();
            }
            $this->infoMessage('create request begin', __METHOD__ . ":" . __LINE__);
            $response = $this->createItem($this->preProcessPostData($data), $this->parentId);
            $this->infoMessage('create request end', __METHOD__ . ":" . __LINE__);
            if (is_array($response)) {
                $this->response->setStatusCode(
                    CallPotential\CPCommon\HttpStatusCode::HTTP_CREATED,
                    CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                        CallPotential\CPCommon\HttpStatusCode::HTTP_CREATED
                    )
                );
                $this->response->setJsonContent($response);
            } else {
                $this->sendBadRequest('Unable to create item');
            }

            return $this->response;
        } catch (AppException $ae) {
            return $this->sendBadRequest($ae->getMessage());
        }
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        $modelName = $this->getModelName();

        try {
            $this->infoMessage(
                $modelName . ' get request begin',
                'CallRestController::getAction:' . __LINE__
            );
            $this->debugMessage(
                $modelName . ' get request for id: ' . $this->getParamID(),
                'CallRestController::getAction:' . __LINE__
            );
            $source = $this->getItem($this->getParamID());
            if (!$this->userHasReadAccess($source)) {
                return $this->sendAccessDeniedResponse('Access Denied');
            }
            $this->response->setStatusCode(
                CallPotential\CPCommon\HttpStatusCode::HTTP_OK,
                CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                    CallPotential\CPCommon\HttpStatusCode::HTTP_OK
                )
            );
            $this->response->setJsonContent($source);
            $this->infoMessage(
                $modelName . ' get request end',
                'CallRestController::getAction:' . __LINE__
            );

            return $this->response;
        } catch (AppException $ae) {
            $this->debugMessage(
                $modelName . ' get request end with error : ' . $ae->getMessage(),
                'CallRestController::getAction:' . __LINE__
            );

            return $this->sendBadRequest($ae->getMessage());
        } catch (Missing404Exception $e) {
            $this->debugMessage(
                $modelName . ' get reqeust end id ' . $this->getParamID() . ' not found.',
                __METHOD__ . ':' . __LINE__
            );

            return $this->sendNotFound();
        } catch (Exception $e) {
            $this->debugMessage(
                $modelName . ' get reqeust end id ' . $this->getParamID() .
                ', An error occured : ' . $e->getMessage(),
                __METHOD__ . ':' .__LINE__
            );

            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Method Http accept: GET
     *
     * @throws Exception
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        $userId = $this->getCurrentAccountId();
        if ((int) $userId) {
            $modelName = $this->getModelName();
            try {
                if ($this->relationship !== null) {
                    $this->debugMessage(
                        $modelName . ' list request for ' . $this->getMessage() . ' begin',
                        'CallRestController::listAction:' . __LINE__
                    );
                    $relations = $this->getModelName()::getRelations();

                    if (!isset($relations[$this->relationship])) {
                        return $this->sendNotFound();
                    }
                    $objectName = $relations[$this->relationship];
                    $relatedObject = new $objectName();
                    $data = $relatedObject->findByParentId($this->getParamID());

                    $this->debugMessage(
                        $this->relationship . ' Load complete : ' . count($data) . ' records',
                        'CallRestController::listAction:' . __LINE__
                    );
                    $this->infoMessage(
                        $modelName . ' list request for ' . $this->getMessage() . ' end',
                        'CallRestController::listAction:' . __LINE__
                    );
                } else {
                    $locationId = $this->request->get('locationId');
                    $this->debugMessage(
                        $modelName . ' list requested for account: ' . $userId .
                         ' Location: ' . $locationId,
                        'CallRestController::listAction:' . __LINE__
                    );
                    $apiOptions = [
                        'skipCache' => filter_var(
                            $this->request->get('skipCache'),
                            FILTER_VALIDATE_BOOLEAN
                        ),
                        'forceCache' => filter_var(
                            $this->request->get('forceCache'),
                            FILTER_VALIDATE_BOOLEAN
                        ),
                    ];
                    IntegrationFactory::$authToken = $this->getRequestAuthToken();
                    $api = IntegrationFactory::getIntegrationAPI($locationId, $apiOptions);
                    if (is_object($api)) {
                        $locModel = IntegrationFactory::getLocation($locationId);
                        if (is_object($locModel) &&
                            $this->userHasReadAccess(['user_id' => $locModel->user_id])) {
                            $this->debugMessage(
                                $modelName . ' list API object Created: ' . get_class($api),
                                'CallRestController::listAction' . __LINE__
                            );
                            $funcName = $this->getMethodName($api);
                            $data = $api->$funcName();
                            $this->debugMessage(
                                $modelName . ' list Load Complete: ' . count($data) . " records",
                                'CallRestController::listAction:' . __LINE__
                            );
                        } else {
                            return $this->sendNotAuthorized();
                        }
                    } else {
                        throw new Exception("Unable to determine location type
                         for location $locationId");
                    }
                }

                $this->response->setStatusCode(
                    CallPotential\CPCommon\HttpStatusCode::HTTP_OK,
                    CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                        CallPotential\CPCommon\HttpStatusCode::HTTP_OK
                    )
                );
                $this->response->setJsonContent(
                    array('status' => "OK", 'items' => $data, 'paging' => false)
                );

                $this->infoMessage(
                    $modelName . ' list request end',
                    'CallRestController::listAction:' . __LINE__
                );

                return $this->response;
            } catch (AppException $ae) {
                $this->debugMessage(
                    $modelName . ' list request end with error : ' . $ae->getMessage(),
                    'CallRestController::listAction:' . __LINE__
                );

                return $this->sendBadRequest($ae->getMessage());
            } catch (Exception $e) {
                $this->debugMessage(
                    $modelName . ' list request end with error : ' . $e->getMessage(),
                    'CallRestController::listAction:' . __LINE__
                );

                return $this->sendErrorResponse($e);
            }
        } else {
            return $this->sendForbidden();
        }
    }

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        try {
            $data = Util::objectToArray($this->request->getJsonRawBody());

            //verify if exist more than one element
            if (Util::existSubArray($data, true)) {
                return $this->bulkSave();
            }
            if (!is_array($data)) {
                return $this->sendBadRequest();
            }
            $this->infoMessage('update request begin', __METHOD__ . ":" . __LINE__);
            $objModel = $this->getModelObject();

            $id = $this->getRelatedItem() ?? $this->getParamID();
            if (!$id) {
                return $this->sendBadRequest();
            }
            $this->debugMessage(
                'update request for id: ' . $id,
                __METHOD__ . ":" . __LINE__
            );
            $current = $objModel->findById($id, $this->parentId);
            $current = Util::array_get('_source', $current);
            $response = $this->updateItem(
                $id,
                $this->preProcessPutData($data),
                $this->parentId
            );
            $this->debugMessage(
                'update response for ' . $id . ': ' . var_export($response, true),
                __METHOD__ . ":" . __LINE__
            );
            $result = Util::array_get('result', $response);
            $responseData = array_merge($current, $data);

            $this->response->setStatusCode(
                CallPotential\CPCommon\HttpStatusCode::HTTP_OK,
                CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                    CallPotential\CPCommon\HttpStatusCode::HTTP_OK
                )
            );
            $this->response->setJsonContent($responseData);
            $this->infoMessage(
                'update request end. result: ' . $result . ' version: ' .
                Util::array_get('_version', $response),
                __METHOD__ . ":" . __LINE__
            );

            return $this->response;
        } catch (Missing404Exception $e) {
            $this->debugMessage(
                'Save reqeust end id ' . $this->getParamID() . ' not found.',
                __METHOD__ . ':' . __LINE__
            );

            return $this->sendNotFound();
        }
    }

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        if ($this->request->getJsonRawBody()) {
            $data = Util::objectToArray($this->request->getJsonRawBody());
            //verify if exist more than one element
            if (Util::existSubArray($data, true)) {
                return $this->doBulkDelete();
            }
            if (!is_array($data)) {
                return $this->sendBadRequest();
            }
        }

        $this->infoMessage('delete request begin', __METHOD__ . ":" . __LINE__);

        $objModel = $this->getModelObject();

        $id = $this->getRelatedItem() ?? $this->getParamID();
        try {
            if (!$id) {
                return $this->sendBadRequest();
            }

            $this->debugMessage('delete request for id: ' . $id, __METHOD__ . ":" . __LINE__);
            /*$current = $objModel->findById($id, $this->parentId);
            $current = Util::array_get('_source', $current);*/
            $response = $objModel->deleteById($id, $this->parentId);
            $this->debugMessage(
                'delete response for unit ' . $id . ': ' . var_export($response, true),
                __METHOD__ . ":" . __LINE__
            );
            $result = Util::array_get('result', $response);

            if ($result === 'deleted' || $result === 'noop') {
                $this->response->setStatusCode(
                    CallPotential\CPCommon\HttpStatusCode::HTTP_NO_CONTENT,
                    CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                        CallPotential\CPCommon\HttpStatusCode::HTTP_NO_CONTENT
                    )
                );
                $this->infoMessage(
                    'delete request end. result: ' . $result . ' version: ' .
                     Util::array_get('_version', $response),
                    __METHOD__ . ":" . __LINE__
                );

                return $this->response;
            } else {
                $this->errorMessage(
                    ' delete: An unknown error has occurred: ' . var_export($response, true),
                    __METHOD__ . ":" . __LINE__
                );

                return $this->sendBadRequest(
                    'An unknown error has occurred: ' . var_export($response, true)
                );
            }
        } catch (Missing404Exception $e) {
            $this->debugMessage(
                'Save reqeust end id ' . $this->getParamID() . ' not found.',
                __METHOD__ . ':' . __LINE__
            );

            return $this->sendNotFound();
        }
    }

    /**
     * Functions insert data in related model.
     *
     * @param mixed  $id       primary key value of the record
     * @param string $relation relation with record
     * @param array  $data     array of POST body / query params in key value pair
     *
     * @return mixed
     */
    public function addRelatedItem($id, string $relation, array $data)
    {
        unset($id, $relation, $data);
        // TODO: Implement addRelatedItem() method.
    }

    /**
     * Functions data from related model.
     *
     * @param mixed  $id         primary key value of the record
     * @param string $relation   relation with record
     * @param mixed  $related_id relation Id
     *
     * @return mixed
     */
    public function deleteRelatedItem($id, string $relation, $related_id)
    {
        unset($id, $relation, $related_id);
        // TODO: Implement deleteRelatedItem() method.
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    public function doBulkCreate()
    {
        $this->infoMessage('bulk create request begin', __METHOD__ . ":" . __LINE__);
        $data = Util::objectToArray($this->request->getJsonRawBody());
        $bAllSuccess = true;
        $bAllError = true;
        $response = array();
        foreach ($data as $rec) {
            if (!$this->userHasWriteAccess($rec)) {
                $response['error'][] = ['message' => 'Write Access Denied', 'data' => $rec];
                $bAllSuccess = false;
            } else {
                try {
                    unset($rec['_id']);
                    $result = $this->createItem($this->preProcessPostData($rec), $this->parentId);
                    $resp = $result['result'];
                    if ($resp === "created") {
                        $this->debugMessage(
                            'bulk add response for unit ' . $result['_id'] .
                            ': ' . var_export($result, true),
                            __METHOD__ . ":" . __LINE__
                        );
                        $response['success'][] = $result['_id'];
                        $bAllError = false;
                    } else {
                        $bAllSuccess = false;
                    }
                } catch (Exception $e) {
                    $bAllSuccess = false;
                    $response['error'][] = $e->getMessage();
                }
            }
        }

        if (count($data) > 0) {
            if ($bAllSuccess) {
                $httpCode = HttpStatusCode::HTTP_OK;
            } elseif ($bAllError) {
                $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
            } else {
                $httpCode = HttpStatusCode::HTTP_OK;
            }
        } else {
            $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
        }

        $response['httpCode'] = $httpCode;

        return $response;
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    public function doBulkSave(): array
    {
        $parent = $this->parentId;
        $this->infoMessage('bulk update request begin', __METHOD__ . ":" . __LINE__);

        $objModel = $this->getModelObject();

        $data = Util::objectToArray($this->request->getJsonRawBody());

        $bAllSuccess = true;
        $bAllError = true;
        $response = array();
        foreach ($data as $rec) {
            $id = Util::array_get('_id', $rec, 0);
            if ($id) {
                $this->debugMessage(
                    'bulk update request for id: ' . $id,
                    __METHOD__ . ":" . __LINE__
                );
                try {
                    $current = $objModel->findById($id, $this->parentId);
                } catch (Exception $e) {
                    $current = false;
                }
                if (!is_array($current)) {
                    $response['error'][] = ['message' => 'Item not found', 'data' => $rec];
                    $bAllSuccess = false;
                } else {
                    $current = Util::array_get('_source', $current);
                    if (!$this->userHasWriteAccess($current)) {
                        $response['error'][] = ['message' => 'Write Access Denied', 'data' => $rec];
                        $bAllSuccess = false;
                    } else {
                        try {
                            unset($rec['_id']);
                            $result = $objModel->updateById($id, $rec, $parent);
                            $resp = $result['result'];
                            if ($resp === "updated" || $resp === 'noop') {
                                $this->debugMessage(
                                    'bulk update response for ' . $id . ': ' .
                                     var_export($result, true),
                                    __METHOD__ . ":" . __LINE__
                                );
                                $response['success'][] = $result['_id'];
                                $bAllError = false;
                            } else {
                                $bAllSuccess = false;
                            }
                        } catch (Exception $e) {
                            $bAllSuccess = false;
                            $response['error'][] = $e->getMessage();
                        }
                    }
                }
            }
        }

        if (count($data) > 0) {
            if ($bAllSuccess) {
                $httpCode = HttpStatusCode::HTTP_OK;
            } elseif ($bAllError) {
                $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
            } else {
                $httpCode = HttpStatusCode::HTTP_OK;
            }
        } else {
            $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
        }

        $response['httpCode'] = $httpCode;

        return $response;
    }

    /**
     * Handles bulk delete requests
     *
     * @return \Phalcon\Http\Response
     */
    public function doBulkDelete(): \Phalcon\Http\Response
    {
        $this->infoMessage('bulk delete request begin', __METHOD__ . ":" . __LINE__);
        $objModel = $this->getModelObject();
        $data = Util::objectToArray($this->request->getJsonRawBody());
        $bAllSuccess = true;
        $bAllError = true;
        $response = array();
        foreach ($data as $rec) {
            $id = Util::array_get('_id', $rec, 0);
            if ($id) {
                $this->debugMessage(
                    'bulk delete request for id: ' . $id,
                    __METHOD__ . ":" . __LINE__
                );
                try {
                    $current = $objModel->findById($id, $this->parentId);
                } catch (Exception $e) {
                    $current = false;
                }
                if (!is_array($current)) {
                    $response['error'][] = ['message' => 'Item not found', 'data' => $rec];
                    $bAllSuccess = false;
                } else {
                    $current = Util::array_get('_source', $current);
                    if (!$this->userHasDeleteAccess($current)) {
                        $response['error'][] = [
                            'message' => 'Delete Access Denied',
                            'data' => $rec,
                        ];
                        $bAllSuccess = false;
                    } else {
                        try {
                            $resp = $objModel->deleteById($id, $this->parentId);
                            if ($resp['result'] === "deleted" || $resp['result'] === 'noop') {
                                $this->debugMessage(
                                    'bulk delete response for ' . $id . ': ' .
                                     var_export($response, true),
                                    __METHOD__ . ":" . __LINE__
                                );
                                $response['success'][] = $id;
                                $bAllError = false;
                            } else {
                                $bAllSuccess = false;
                            }
                        } catch (Exception $e) {
                            $bAllSuccess = false;
                            $response['error'][] = $e->getMessage();
                        }
                    }
                }
            }
        }
        if (count($data) > 0) {
            if ($bAllSuccess) {
                $httpCode = HttpStatusCode::HTTP_OK;
            } elseif ($bAllError) {
                $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
            } else {
                $httpCode = HttpStatusCode::HTTP_OK;
            }
        } else {
            $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
        }

        $this->response->setStatusCode($httpCode, HttpStatusCode::getMessageForCode($httpCode));
        $this->response->setJsonContent($response);

        return $this->response;
    }

    /**
     * Method get model's object.
     *
     * @return object
     */
    protected function getModelObject()
    {
        $modelName = $this->getModelName();

        return new $modelName();
    }
}
