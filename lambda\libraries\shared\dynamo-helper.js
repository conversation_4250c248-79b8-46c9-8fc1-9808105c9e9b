const AWS = require('aws-sdk');
const TwUtils = require("../../routes/utils/twillio");
const config = require("../../config/config");
const cpapiClient = require('../cpapi-client');
const dynamo = new AWS.DynamoDB.DocumentClient({
  endpoint: config.dynamodb.endpoint,
});
const tableName = config.dynamodb.acctTable;

async function getAccountDetail(accountSid) {
  const params = {
    TableName: tableName,
    IndexName: 'account_sid-index',
    KeyConditionExpression: "account_sid = :sid",
    ExpressionAttributeValues: {
      ":sid": accountSid
    }
  };

  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

async function insertTaskActivity(data) {
  return await TwUtils.insert_task_activity(data);
}

async function updateCallData(taskAttributes, workerAttributes, config) {
  try {
    const callClient = new cpapiClient.callClient(
      config.db.serviceTokens.readWrite
    );
    let employeeData = {
      employee_id: workerAttributes.agent_id,
    };
    await callClient.putData(
      `call/${taskAttributes.location_id}/${taskAttributes.call_sid}`,
      employeeData
    );
  } catch (error) {
    console.error(error, new Error().stack);
  }
}

module.exports = {
  getAccountDetail,
  insertTaskActivity,
  updateCallData
};