import { CodeArtifact, saveArtifact, convertCase } from "../common";
import * as fs from 'fs';
import * as path from 'path';

const SUPPORTED_DIAGRAM_TYPES = {
  "stateDiagram-v2": true,
};

interface ParsedStateDiagramLine {
  from: string;
  to: string;
  note: string;
  initial?: boolean;
  terminal?: boolean;
}

interface ParsedDiagram {
  filePath: string;
  name: string;
  type: string;
  md: string;
  sourceLines: string[];
}

interface ParsedStateDiagram extends ParsedDiagram {
  parsedLines: ParsedStateDiagramLine[];
  states: Set<string>;
  initialStates: Map<string, string>;
  terminalStates: Map<string, string>;
}

interface ParsedStateDiagramFlow {
  startState: string;
  endState: string;
  flows: any[];
}

function parseDiagram(
  filePath: string,
  name: string,
  diagram: string,
): ParsedDiagram {
  const lines = diagram.split("\n");
  /**
   * if the first line is "```mermaid", remove this line and the last line "```"
   */
  if (lines[0].trim() === "```mermaid") {
    lines.shift();
    lines.pop();
  }
  /**
   * remove the first line if it is an empty string and keep doing that until the first line is not an empty string
   */
  while (lines[0].trim() === "") {
    lines.shift();
  }
  const diagramType = lines.shift()?.trim() || "";
  console.log("diagramType: ", diagramType);
  if (!(diagramType in SUPPORTED_DIAGRAM_TYPES)) {
    throw new Error(
      `Diagram type ${diagramType} is not supported.`,
    );
  }
  return {
    filePath,
    name,
    type: diagramType || "unknown",
    md: diagram,
    sourceLines: lines,
  };
}

// interface StateWithNote {
//   state: string;
//   note: string;
// }

function parseStateDiagram(
  diagram: ParsedDiagram,
): ParsedStateDiagram {
  const states = new Set<string>();

  const parsedLines: ParsedStateDiagramLine[] = [];
  const initialStates = new Map<string, string>();
  const terminalStates = new Map<string, string>();
  diagram.sourceLines.forEach((line) => {
    const parsedLine = parseStateDiagramLine(line);
    if (parsedLine) {
      
      if (parsedLine.from === "[*]") {
        initialStates.set(parsedLine.to.trim(), parsedLine.note);
        return;
      }

      if (parsedLine.to === "[*]") {
        terminalStates.set(parsedLine.from.trim(), parsedLine.note);
        return;
      } 
      
      if (initialStates.has(parsedLine.from.trim())) {
        parsedLine.initial = true;
      }

      if (terminalStates.has(parsedLine.to.trim())) {
        parsedLine.terminal = true;
      }
      
      states.add(parsedLine.from.trim());
      states.add(parsedLine.to.trim());
      parsedLines.push(parsedLine);
    }
  });

  return {
    ...diagram,
    parsedLines,
    states,
    initialStates,
    terminalStates,
  };

}

function parseStateDiagramLine(line: string) {
  // Regular expression to match different parts of the string
  // This regex handles optional notes and different formats for 'from' and 'to'
  const regex = /(\[.*?\]|[^-]+)\s*-->\s*(\[.*?\]|[^:\s]+)(?:\s*:\s*(.*))?/;

  // Match the line against the regular expression
  const match = line.match(regex);

  // Check if the line matches the pattern
  if (match) {
    // Create the object from the matched groups
    return {
      from: match[1].trim(), // Trim to remove any extra whitespace
      to: match[2].trim(),
      note: match[3] ? match[3].trim() : "", // Handle optional 'note' part
    } as ParsedStateDiagramLine;
  } else {
    // Return null or an error if the line does not match the pattern
    return null;
  }
}

function getAutoGeneratedPreambleCommentBlock(diagramFilePath: string): string {
  return `
/**
 * This file was auto-generated by the state-diagram-code generator.
 * DO NOT MODIFY THIS FILE DIRECTLY.
 * To make changes, modify the source diagram file and re-run the generator.
 * Diagram source: ${diagramFilePath}
 * Generated at: ${new Date().toISOString()}
 */\n`
  ;
}
/**
 * NOTE: This function is rather naive at the moment.  It has these limitations:
 * 1. Transitions must be expressed with `-->`
 * 2. Only supports stateDiagram-v2
 */
function generateEnumFromStateDiagram(
  stateDiagram: ParsedStateDiagram
): CodeArtifact {
  const { name } = stateDiagram;
  const artifactName = `${name}State`;

  let enumString = `export enum ${artifactName} {\n`;
  stateDiagram.states.forEach((state) => {
    if (state === "[*]" || state === "") return;
    enumString += `  ${state} = "${state}",\n`;
  });
  enumString += "}\n";

  /**
   * Add preamble comment block
   */
  enumString = getAutoGeneratedPreambleCommentBlock(stateDiagram.filePath) + enumString;

  const artifact = {
    fileName: `Generated/${artifactName}.generated.ts`,
    codeContent: enumString,
    overwrite: true,
  };

  return artifact;
}

function generateStateHandlers(
  stateDiagram: ParsedStateDiagram
): CodeArtifact {
  const { name } = stateDiagram;
  const artifactName = `${name}StateHandlers`;
  const artifactNameEnum = `${name}State`;
  const mapItems: string[] = [];
  const stubClasses: string[] = [];
  const importLines: string[] = [];
  const lines: string[] = [];

  stateDiagram.states.forEach((state) => {
    if (state === "[*]" || state === "") return;
    mapItems.push(`    [${artifactNameEnum}.${state}]: ${state},`);
    stubClasses.push(`export class ${state} extends ${name}StateBase { }\n`);
    importLines.push(`import { ${state} } from '../States/${state}';`);
  });

  lines.push(getAutoGeneratedPreambleCommentBlock(stateDiagram.filePath));
  lines.push(`import { ${name}StateHandlerMap } from "../${name}.model";`);
  lines.push(`import { ${artifactNameEnum} } from "./${artifactNameEnum}.generated";`);
  importLines.forEach((item) => {
    lines.push(item);
  });
  lines.push(``);

  lines.push(``);
  lines.push(`export function build${name}StateHandlerMap(): ${name}StateHandlerMap {`);
  lines.push(`  return {`);
  mapItems.forEach((item) => {
    lines.push(item);
  });
  lines.push(`  };`);
  lines.push(`}`);
  lines.push(``);

  const artifact = {
    fileName: `Generated/${artifactName}.generated.ts`,
    codeContent: lines.join('\n'),
    overwrite: true,
  };

  return artifact;
}


function generateStateClasses(
  stateDiagram: ParsedStateDiagram
): CodeArtifact[] {
  const { name } = stateDiagram;

  const artifacts: CodeArtifact[] = [];

  const importLines: string[] = [];
  const providerLines: string[] = [];

  stateDiagram.states.forEach((state) => {
    if (state === "[*]" || state === "") return;

    /**
     * For each state, we want to generate:
     * - a class per state, that will contain the implementation of the state handler
     *   - set OVERWRITE to false
     * - a states module provider, this can be overwritten, for now
     */

    const lines: string[] = [];
    lines.push(`import { Injectable } from "@nestjs/common";`);
    lines.push(`import { ${name}StateContext, ${name}StateHandlerResponse, ${name}StateBase } from "../${name}.model";`);
    lines.push(``);
    lines.push(`@Injectable()`);
    lines.push(`export class ${state} extends ${name}StateBase {`);
    lines.push(`  override async handler(context: ${name}StateContext): Promise<${name}StateHandlerResponse> {`);
    lines.push(`    throw new Error("Method not implemented.");`);
    lines.push(`  }`);
    lines.push(`}`);
    lines.push(``);

    artifacts.push({
      fileName: `states/${state}.ts`,
      codeContent: lines.join('\n'),
      overwrite: false
    });

    providerLines.push(`    ${state},`);
    importLines.push(`import { ${state} } from '../States/${state}';`);
  });

  const mlines: string[] = [];
  mlines.push(getAutoGeneratedPreambleCommentBlock(stateDiagram.filePath));
  mlines.push(`import { Module } from '@nestjs/common';`);
  importLines.forEach((line) => {
    mlines.push(line);
  });
  mlines.push(``);
  mlines.push(`@Module({`);
  mlines.push(`  providers: [`);
  providerLines.forEach((line) => {
    mlines.push(line);
  });
  mlines.push(`  ],`);
  mlines.push(`  exports: [`);
  providerLines.forEach((line) => {
    mlines.push(line);
  });
  mlines.push(`  ],`);
  mlines.push(`})`);
  mlines.push(`export class PayByPhoneStatesModule {}`);
  mlines.push(``);

  artifacts.push({
    fileName: `Generated/${name}StatesModule.generated.ts`,
    codeContent: mlines.join('\n'),
    overwrite: true
  });

  return artifacts;
}

/**
 * This function creates a TransitionContext class that can be used to validate
 * a transition from one state to another.  It is based on the state diagram
 * format from mermaid-js.
 */
function generateStateTransitionContext(stateDiagram: ParsedStateDiagram): CodeArtifact {
  const { name, parsedLines } = stateDiagram;
  const transitions = new Map();
  const artifactName = `${name}Transitions`;
  const indexArtifactName = `${name}State`;

  parsedLines.forEach((line) => {
    const label = line.note;
    const { from, to } = line;

    if (from === "[*]" || to === "[*]") return;
    if (!from || !to) return;

    const fromState = from.trim();
    const toState = to.trim();
    const transitionLabel = label ? label.trim() : "";

    if (!transitions.has(fromState)) {
      transitions.set(fromState, new Map());
    }

    transitions.get(fromState)?.set(toState, transitionLabel);
  });

  let mapString =
    `import { ${indexArtifactName} } from "./${indexArtifactName}.generated";\n\n`;
  mapString += `export const ${artifactName} = new Map([\n`;

  transitions.forEach((toStates, fromState) => {
    if (fromState === "*" || fromState === "") return;

    mapString += `  [${indexArtifactName}.${fromState}, new Map([\n`;
    toStates.forEach((label: any, toState: any) => {
      mapString += `    [${indexArtifactName}.${toState}, "${label}"],\n`;
    });
    mapString += "  ])],\n";
  });

  mapString += "]);\n";

  /**
   * Add preamble comment block
   */
  mapString = getAutoGeneratedPreambleCommentBlock(stateDiagram.filePath) + mapString;

  const artifact = {
    fileName: `Generated/${artifactName}.generated.ts`,
    codeContent: mapString,
    overwrite: true,
  };

  return artifact;
}


/**
 * This function creates a set of all possible scenarios from a state diagram.  
 * It is based on the state diagram format from mermaid-js.
 */
function generatePossibleFlowsFromDiagram(stateDiagram: ParsedStateDiagram): CodeArtifact {
  const { parsedLines } = stateDiagram;
  const scenarios: any[] = [];
  for (const initialState of stateDiagram.initialStates.keys()) {
    for (const terminalState of stateDiagram.terminalStates.keys()) {
      scenarios.push({
        startState: initialState,
        endState: terminalState,
        flows: generatePossibleFlows(stateDiagram, parsedLines, initialState, terminalState),
      });
    }
  }
  return {
    fileName: `flows.generated.ts`,
    codeContent: `export const flows = ` + JSON.stringify(scenarios, null, 2) + `;`,
    overwrite: true,
  };
}

function generatePossibleFlows(stateDiagram: ParsedStateDiagram, parsedLines: ParsedStateDiagramLine[], startState: string, terminalState: string): any[] {
  const paths: string[][] = [];
  const visited = new Set<string>();
  const currentPath: any[] = [];

  const dfs = (startState: string, terminalState: string, currentState: string, when: string) => {
    if (currentState === terminalState) {
      currentPath.push({ state: currentState, when });
      paths.push([...currentPath]);
      currentPath.pop();
      return;
    }

    if (visited.has(currentState)) {
      return;
    }

    visited.add(currentState);

    if (currentState === startState) {
      when = stateDiagram.initialStates.get(currentState) || when;
    }

    currentPath.push({ state: currentState, when });

    for (const line of parsedLines) {
      if (line.from === currentState) {
        dfs(startState, terminalState, line.to, line.note);
      }
    }

    currentPath.pop();
    visited.delete(currentState);
  };

  dfs(startState, terminalState, startState, "");

  return paths;
}

/**
 * This function is the main entry point for this generator.  It will process
 * all of the files in the directory and generate the appropriate code artifacts.
 * @param dirPath The path to a directory containing diagram files in the mermaid-js format
 * @param outputPath The root path where the generated code will be written
 */
async function processFilesInDirectory(workspaceRoot: string, dirPath: string, outputPath: string, flowsPath: string) {
  console.log(`Processing files in directory: ${dirPath}`);
  const files = fs.readdirSync(dirPath);
  for (const file of files) {
    const filePath = `${dirPath}/${file}`;
    const relativeRootPath = path.normalize(filePath.replace(workspaceRoot, '')).split(`\\`).join('/');
    const domainEntityName = convertCase(file, true).split('.state.mmd').join('');
    const diagramContent = fs.readFileSync(filePath, 'utf8');

    console.log(`Creating entities for: ${domainEntityName}`);

    const parsedDiagram = parseDiagram(relativeRootPath, domainEntityName, diagramContent);

    switch (parsedDiagram.type) {
      case 'stateDiagram-v2': {
        const parsedStateDiagram = parseStateDiagram(parsedDiagram);

        const codeOutputPath = `${outputPath}/${domainEntityName}`;
        const flowsOutputPath = `${flowsPath}/${domainEntityName}`;

        const stateEnumArtifact = generateEnumFromStateDiagram(parsedStateDiagram);
        await saveArtifact(codeOutputPath, stateEnumArtifact);

        const stateTransitionContextArtifact = generateStateTransitionContext(parsedStateDiagram);
        await saveArtifact(codeOutputPath, stateTransitionContextArtifact);

        const stateHandlersArtifact = generateStateHandlers(parsedStateDiagram);
        await saveArtifact(codeOutputPath, stateHandlersArtifact);

        const stateBaseClassArtifacts = generateStateClasses(parsedStateDiagram);
        for(const artifact of stateBaseClassArtifacts) {
          await saveArtifact(codeOutputPath, artifact);
        }

        const possibleFlowsArtifact = generatePossibleFlowsFromDiagram(parsedStateDiagram);
        await saveArtifact(flowsOutputPath, possibleFlowsArtifact);

        break;
      }
      default:
        throw new Error(`Diagram type ${parsedDiagram.type} is not supported.`);
    }
  }

}

// Get the arguments from the command line
const [workspaceRoot, diagramsPath, outputPath, flowsPath] = process.argv.slice(2);

processFilesInDirectory(workspaceRoot || "./", diagramsPath || "./", outputPath || "./output", flowsPath || "./flows");
