import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneLongRunningState, LoopbackPayByPhoneStateHandlerResponse } from "../PayByPhone.model";
import { PaymentHashRequest, PaymentData, TenantId, TenantPaymentResponse, TenantPaymentErrorResponse, WithResponseMetadata } from "@cp-workspace/shared";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class FinalPayAmountSubmitted extends PayByPhoneLongRunningState {
  constructor() {
    super(PayByPhoneState.FinalPayAmountSubmitted, PayByPhoneState.PaymentFailure);
  }
  override async handler(context: PayByPhoneStateContext): Promise<LoopbackPayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;

    if(
      storage.matchedTenants && 
      storage.matchedTenants?.length > 0 && 
      storage.surchargeDetails?.isSurchargeEnabledForCard &&
      storage.surchargeDetails?.surchargePercentage !== undefined &&
      storage.surchargeDetails.surchargePercentage > 0
    ) {
      const locationDetails = await this.services.locationService.getLocationDetails(storage.locationId);
      if (locationDetails?.api_type && storage.surchargeDetails?.surchargeEnabledAPITypes && (storage.surchargeDetails?.surchargeEnabledAPITypes).includes(locationDetails?.api_type) && locationDetails?.user_id) {
        const isSurchargeEnabledforLocation = await this.services.coreService.getLocationSetting(this.services.coreService.payByPhoneSurchargeFullyQualifiedLocationSettingName, locationDetails.user_id, storage.locationId);
        storage.surchargeDetails.isSurchargeEnabledForLocation = isSurchargeEnabledforLocation as boolean;
        if(!isSurchargeEnabledforLocation) {
          twilioResponse.sayInLocale({
            messageId: 'pay-by-phone.payment-fail-due-to-config-issue',
            locale: storage.locale
          });
          this.services.bugsnagService.notify(new Error('Surcharge not enabled for location at CRM end but surcharge is enabled for the location at FMS end. locationId: ' + storage.locationId + ' accountId: ' + locationDetails.user_id));
          this.services.dataDogService.incrementCounter('pay_by_phone.payment.failure', [
            `locale:${storage.locale}`,
            `error_msg:Surcharge not enabled for location at CRM end but surcharge is enabled for the location at FMS end`
          ]);
          return { nextState: PayByPhoneState.PaymentFailure };
        }
      }
    }
    if(!storage.paymentToken || !storage.selectedUnits || storage.selectedUnits.length === 0 || !storage.locationId) {
      storage.paymentProcessorResponse = "!storage.paymentToken || !storage.selectedUnits || storage.selectedUnits.length === 0 || !storage.locationId";
      return { nextState: PayByPhoneState.PaymentFailure };
    }

    const paymentDataArray: PaymentData[] = [];

    const decodedCard = await this.services.coreService.decodePaymentToken(storage.paymentToken!);
    const locationConfig = await this.services.locationService.getLocationConfiguration(storage.locationId);
    let firstUnit = true;
    const allowBulkPayment = locationConfig?.allow_bulk_payment ? true : false;

    for (const unit of storage.selectedUnits!) {
      const paymentHashRequest: PaymentHashRequest = {
        customer_id: unit.tenant_id,
        location_id: storage.locationId,
        ledger_id: unit.ledger_id,
      };
      const paymentHash = await this.services.integrationService.createPaymentHash(paymentHashRequest);
      const paymentHashDetails = await this.services.integrationService.getPaymentHashDetails(paymentHash.hash);
      if(!paymentHashDetails.items || Object.keys(paymentHashDetails.items).length === 0 || unit.amount_owed === undefined || parseFloat(unit.amount_owed) === 0) {
        storage.paymentProcessorResponse = "!paymentHashDetails.items || Object.keys(paymentHashDetails.items).length === 0 || unit.amount_owed === undefined || parseFloat(unit.amount_owed) === 0";
        return { nextState: PayByPhoneState.PaymentFailure };
      }

      const amountDue = Number(parseFloat(unit.amount_owed).toFixed(2));
      const shouldAddConvenienceFee = storage.convenienceFee && firstUnit;

      const paymentData: PaymentData = {
        amount: amountDue,
        convenience_fee: false,
        is_auto_pay: false,
        pay_method: 'creditcard',
        payment_hash: paymentHash.hash,
        save_cc: locationConfig?.save_cc? true : false,
        total_amount_due: storage.prepayMonths? 0 : amountDue,
        prepay_months: storage.prepayMonths,
        unit_id: unit.es_unit_id,
        cvc_number: decodedCard.securityCode!,
        postal_code: decodedCard.postalCode!,
        payment_id: paymentHashDetails.items.payment_id,
      };

      if(storage.useSavedCardId)
      {
        paymentData.card_id = storage.useSavedCardId;
      }
      else
      {
        paymentData.card_number = decodedCard.cardNumber;
        paymentData.exp_month = decodedCard.expiration!.slice(0, 2);
        paymentData.exp_year = "20" + decodedCard.expiration!.slice(2, 4);
      }

      if(shouldAddConvenienceFee) {
        paymentData.convenience_fee = true;
        paymentData.convenience_fee_amount = storage.convenienceFee;
        paymentData.convenience_fee_source = paymentHashDetails.items.source;
      }

      firstUnit = false;
      paymentDataArray.push(paymentData);
    }

    const paymentResult = await this.paySelectedUnits(storage.selectedUnits[0].tenant_id_es, paymentDataArray, allowBulkPayment);
    storage.paymentProcessorResponse = paymentResult?.data || {};

    if(paymentResult?.data?.success)
    {
      const paymentAmount = paymentDataArray.reduce((total, item) => total + item.amount, 0);
      const paymentAmountBucket = this.services.dataDogService.getBucket(paymentAmount, 500, 25);
      this.services.dataDogService.incrementCounter('pay_by_phone.payment.success', [
        `locale:${storage.locale}`,
        `payment_amount:${paymentAmountBucket}`,
      ]);

      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.payment-success',
        locale: storage.locale
      });
      return { nextState: PayByPhoneState.PaymentSuccessful };
    }

    twilioResponse.sayInLocale({
      messageId: 'pay-by-phone.payment-fail',
      locale: storage.locale
    });

    this.services.dataDogService.incrementCounter('pay_by_phone.payment.failure', [
      `locale:${storage.locale}`,
      `status_code:${paymentResult.status}`,
      `error_msg:${(paymentResult as WithResponseMetadata<TenantPaymentErrorResponse>).data?.error_msg}`
    ]);
    return { nextState: PayByPhoneState.PaymentFailure };
  }

  async paySelectedUnits(tenantId: TenantId, paymentDataArray: PaymentData[], allowBulkPayment: boolean): Promise<WithResponseMetadata<TenantPaymentResponse>> {
    if (!allowBulkPayment) {
      /**
       * It's possible that one or more payments fail. In this case, we want to continue processing payments
       * until all payments have been attempted. We will return a success result if all payments succeed, or
       * a failure result if any payment fails.
       */
      let overallResult: WithResponseMetadata<TenantPaymentResponse> = { data: { success: true }, status: 201 };
      for(const paymentData of paymentDataArray) {
        const result = await this.services.integrationService.makeTenantPayment(tenantId, paymentData);
        if (overallResult.data.success) {
          overallResult = result;
        }
      }
      return overallResult;
    }
    return await this.services.integrationService.makeTenantBulkPayment(tenantId, paymentDataArray);
  }

}
