import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { I18nPath } from "../Generated/i18n.generated";

@Injectable()
export class FinalPayAmountPrompt extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;

    const gatherOptions = {
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    };

    let paymentAmount = storage.totalAmountDue + storage.convenienceFee;
    let messageId : I18nPath = 'pay-by-phone.amount-due';
    if (
      storage.matchedTenants && 
      storage.matchedTenants?.length > 0 &&
      storage.surchargeDetails?.isSurchargeEnabledForCard &&
      storage.surchargeDetails?.surchargePercentage !== undefined &&
      storage.surchargeDetails.surchargePercentage > 0
    ) {
      const locationDetails = await this.services.locationService.getLocationDetails(storage.locationId);
      if (locationDetails?.api_type && storage.surchargeDetails?.surchargeEnabledAPITypes && (storage.surchargeDetails?.surchargeEnabledAPITypes).includes(locationDetails?.api_type) && locationDetails?.user_id) {
        const isSurchargeEnabledforLocation = await this.services.coreService.getLocationSetting(this.services.coreService.payByPhoneSurchargeFullyQualifiedLocationSettingName, locationDetails.user_id, storage.locationId);
        storage.surchargeDetails.isSurchargeEnabledForLocation = isSurchargeEnabledforLocation as boolean;
        if (isSurchargeEnabledforLocation && storage.matchedTenants[0].customer_id) {
          const paymentAmountWithSurcharge = await this.services.integrationService.getTotalWithSurcharge(storage.matchedTenants[0].customer_id, storage.locationId, paymentAmount.toFixed(2));
          if (paymentAmountWithSurcharge > paymentAmount) {
            paymentAmount = paymentAmountWithSurcharge;
            messageId = 'pay-by-phone.amount-due-with-card-processing-fee';
          }
        }
      }
    }

    twilioResponse.gatherWithLocaleSay(gatherOptions, [
      {
        messageId: messageId,
        locale: storage.locale,
        i18nOptions: { args: [{ paymentAmount: paymentAmount.toFixed(2) }] }
      },
      {
        messageId: 'pay-by-phone.confirm-payment',
        locale: storage.locale
      }
    ]);

    return { nextState: PayByPhoneState.FinalPayAmountConfirm };
  }
}
