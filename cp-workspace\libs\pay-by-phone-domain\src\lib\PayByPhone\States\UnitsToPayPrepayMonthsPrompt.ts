import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class UnitsToPayPrepayMonthsPrompt extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;

    twilioResponse.gatherWithLocaleSay({
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.ask-prepay',
      locale: storage.locale
    }]);
    

    return { nextState: PayByPhoneState.UnitsToPayPrepayMonthsSelection };
  }
}
