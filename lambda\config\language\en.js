var lang = {};

lang['call_route_total_balance'] = 'Your total balance due is %s. To make a payment please press 1 otherwise to speak to a manager press 0';
lang['call_route_not_allowed'] = 'Pay by phone is not allowed on this account. Please select new account.';
lang['call_route_account_found'] = 'We have located an account belonging to %s, for unit number %s. Your current balance due is %s and your next scheduled payment date is %s. To make a payment please press one otherwise to speak with a manager, please press zero.';
lang['call_route_ask_prepay'] = 'Please press Zero to pay your total amount due or press the dial pad number representing the number payments you would like to make.  For example, press 1 to pay one month in advance or press 2 to pay months in advance. (Please note the automated system does not accept partial payments)';
lang['call_route_ask_prepay_current_customer'] = 'Cannot pay a 0 balance, please select the number of months you wish to pre pay';
lang['call_route_prepay_selection'] = 'You have selected to pay %s payment for a total of %s. Press one to process your payment or press two to change the amount of the payment.';

lang['call_route_ask_saved_cc'] = 'If you would like to use one of your previously saved credit cards please enter the last 4 digits now or press * to enter a new card';
lang['call_route_missing_response'] = 'Sorry, I didn\'t get your response.';
lang['call_route_account_not_found'] = 'We are unable to locate your account, please enter the phone number associated with your account or press zero followed by pound to speak with a manager.';
lang['call_route_10_digit'] = 'You must enter a 10 digit phone number';
lang['call_route_multiple_accounts'] = 'We located multiple accounts matching your phone number.';
lang['call_route_pay_all'] = 'Please press 1 to pay all units.';
lang['call_route_customer_verify'] = 'Press %s if you are calling about %s in Unit %s.';
lang['call_route_start_over'] = 'Press 9 to start over .';
lang['call_route_speak_manager'] = 'or press 0 followed by hash to speak to a manager.';
lang['call_route_not_allowed_speak_manager'] = 'Pay by phone is not allowed on this account, please select new account by entering the main phone number associated with your account or press 0 followed by hash to speak to a manager.';
lang['call_route_units_not_found'] = 'Sorry no units found. Press 0 followed by hash to speak to a manager.';
lang['call_route_max_tries'] = 'Sorry, Maximum retry attempts reached .';
lang['call_route_card_not_found'] = 'We were unable to find a matching card.';
lang['call_route_charge_confirm'] = 'The total amount that will be charged to your card is %s.';
lang['call_route_enter_cc'] = 'Please enter your 16-digit card number followed by the star sign.';
lang['call_route_enter_expiry'] = 'Please input the expiration date on the card using the two digits for the month and the last two digits of the year, followed by star.';
lang['call_route_enter_ccv'] = 'Please enter the 3 digit security code located in the back of your card, followed by the star.';
lang['call_route_enter_zip'] = 'Please enter the billing zip code associated with the card, followed by star.';
lang['call_route_confirm_payment'] = 'Please press one to confirm the information entered in correct and process your payment.';
lang['call_route_invalid_input'] = 'in valid Input. Try again.';
lang['call_route_processing_error'] = 'There was problem processing payment. Disconnecting...';
lang['call_route_under_process'] = 'Your Payment is being processed. Please wait.';
lang['call_route_payment_success'] = 'Your payment has been processed';
lang['call_route_payment_fail'] = 'We were unable to process your payment. Please wait while we connect you to a manager.';
lang['call_route_num_confirm'] = 'The number you have entered is %s. Press one to confirm the number is correct or press two to re-enter the number.';
lang['call_route_wrong_input_cc'] = 'We\'re sorry, we didn\'t recognize that as a valid card number.';
lang['call_route_wrong_input_date'] = 'We\'re sorry, that isn\'t a valid expiration date.';
lang['call_route_wrong_input_cvc'] = 'We\'re sorry, that isn\'t a valid CVC code.';
lang['call_route_wait_for_account_fetch'] = 'Please wait while we gather your account details.';

module.exports = {
  'lang': lang
}
