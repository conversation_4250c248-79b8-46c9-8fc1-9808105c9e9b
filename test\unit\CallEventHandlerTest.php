<?php

use Tests\Unit\AbstractUnitTest;

include_once './app/events/CallEventHandler.php';

class CallEventHandlerTest extends AbstractUnitTest
{
    public function testUpdateReservationFeeSuccess(): void
    {
        $callEventHandler = $this->getMockedEventHandlerObject();

        $eventData = [
            [
                'updateReservationFee' => true,
                'updateCustomerPayment' => false,
                'updateMysql' => true,
                'oldData' => [
                    'db_log_id' => 23344,
                    'reservation_payment_id' => 123 
                ],
                'newData' => [
                    'db_log_id' => 23344,
                    'reservation_payment_id' => 123 
                ]
            ]
        ];

        $method = self::getMethod('handle');
        $method->invokeArgs($callEventHandler, $eventData);

        $this->assertTrue(
            true,
            "assert value is true or not"
        );
    }

    public function testCustomerPaymentSuccess(): void
    {
        $callEventHandler = $this->getMockedEventHandlerObject();

        $eventData = [
            [
                'updateReservationFee' => false,
                'updateCustomerPayment' => true,
                'updateMysql' => true,
                'oldData' => [
                    'db_log_id' => 23344,
                    'customer_payment_id' => 123 
                ],
                'newData' => [
                    'db_log_id' => 23344,
                    'customer_payment_id' => 123 
                ]
            ]
        ];

        $method = self::getMethod('handle');
        $method->invokeArgs($callEventHandler, $eventData);

        $this->assertTrue(
            true,
            "assert value is true or not"
        );
    }

    public function testCustomerPaymentUpdateException(): void
    {
        $callEventHandler = $this->getMockedEventHandlerObject();

        $eventData = [
            [
                'oldData' => [
                    'db_log_id' => 23344,
                ],
                'newData' => [
                    'db_log_id' => 23344,
                ]
            ]
        ];

        $this->expectException(Exception::class);

        $method = self::getMethod('updateLogIdCustomerPayment');
        $method->invokeArgs($callEventHandler, $eventData);
    }

    public function testReservationFeeUpdateException(): void
    {

        $callEventHandler = $this->getMockedEventHandlerObject();

        $eventData = [
            [
                'oldData' => [
                    'db_log_id' => 23344,
                ],
                'newData' => [
                    'db_log_id' => 23344,
                ]
            ]
        ];

        $this->expectException(Exception::class);

        $method = self::getMethod('updateLogIdReservationFee');
        $method->invokeArgs($callEventHandler, $eventData);
    }

    protected static function getMethod($name)
    {
        $class = new ReflectionClass("CallEventHandler");
        $method = $class->getMethod($name);
        $method->setAccessible(true);
        return $method;
    }

    protected function getMockedEventHandlerObject()
    {
        $callEventHandler = \Mockery::mock('CallEventHandler')->makePartial();
        $callEventHandler->shouldAllowMockingProtectedMethods();

        //bind mock objects to mocked callEventHandler
        $callEventHandler->shouldReceive([
            'updateCallDetailData'   => true,
            'errorMessage' => null
        ]);

        $this->injectClientFactory();

        return $callEventHandler;
    }

    private function injectClientFactory()
    {
        // mock IntClient
        $mockIntFactory = \Mockery::mock('IntClient');
        $mockIntFactory->shouldReceive([
            'updateReservationFee'  => true,
            'updateCustomerPayment' => true,
        ]);
    
        //mock ClientFactory
        $clientFactoryMockObject = \Mockery::mock('ClientFactory');
        $clientFactoryMockObject->shouldReceive([
            'getIntClient' => $mockIntFactory,
        ]);

        $di = \Phalcon\DI\FactoryDefault::getDefault();

        $di->setShared(
            'clientFactory',
            $clientFactoryMockObject
        );
    }
}