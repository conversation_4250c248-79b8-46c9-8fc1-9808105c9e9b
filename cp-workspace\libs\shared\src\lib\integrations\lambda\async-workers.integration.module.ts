import { Modu<PERSON> } from '@nestjs/common';
import { AsyncWorkersServiceOptions, IAsyncWorkersProvider } from '../../models/async-workers.model';
import { AsyncWorkersIntegrationService } from './async-workers.integration.service';
import { AsyncWorkersMockClientProvider } from './async-workers.mock.provider';
import { AsyncWorkersClientProvider } from './async-workers.aws.provider';

export enum AsyncWorkersClientType {
  EXTERNAL = 'EXTERNAL',
  IN_MEMORY = 'IN_MEMORY',
}

export interface AsyncWorkersModuleOptions {
  clientType: AsyncWorkersClientType;
  options?: AsyncWorkersServiceOptions;
}

@Module({})
export class AsyncWorkersIntegrationModule {
  static forRoot(options: AsyncWorkersModuleOptions) {
    options = options || { clientType: AsyncWorkersClientType.IN_MEMORY, options: { enabled: false } };
    if (options?.options) {
      AsyncWorkersIntegrationService.options = options.options;
    }
    if (options.clientType === AsyncWorkersClientType.IN_MEMORY) {
      return {
        module: AsyncWorkersIntegrationModule,
        providers: [
          IAsyncWorkersProvider,
          AsyncWorkersMockClientProvider,
          {
            provide: 'IAsycnWorkersProvider',
            useClass: AsyncWorkersMockClientProvider,
          },
        ],
        exports: [
          IAsyncWorkersProvider,
          AsyncWorkersMockClientProvider,
          'IAsycnWorkersProvider',
        ],
      };
    }
    if (options.clientType === AsyncWorkersClientType.EXTERNAL) {
      return {
        module: AsyncWorkersIntegrationModule,
        providers: [
          IAsyncWorkersProvider,
          AsyncWorkersClientProvider,
          {
            provide: 'IAsycnWorkersProvider',
            useClass: AsyncWorkersClientProvider,
          },
        ],
        exports: [
          IAsyncWorkersProvider,
          AsyncWorkersClientProvider,
          'IAsycnWorkersProvider',
        ],
      };
    }
    return {
      module: AsyncWorkersIntegrationModule,
      providers: [
        IAsyncWorkersProvider,
        AsyncWorkersMockClientProvider,
        AsyncWorkersClientProvider,
        {
          provide: 'IAsycnWorkersProvider',
          useClass: IAsyncWorkersProvider,
        },
      ],
      exports: [
        IAsyncWorkersProvider,
        AsyncWorkersMockClientProvider,
        AsyncWorkersClientProvider,
        'IAsycnWorkersProvider',
      ],
    };
  }
}