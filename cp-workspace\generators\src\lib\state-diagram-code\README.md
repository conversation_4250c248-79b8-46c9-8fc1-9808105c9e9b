# State Diagram Code Generator

## Purpose

The State Diagram Code Generator is a tool that converts Mermaid.js state diagrams into TypeScript code artifacts. It parses state diagrams and generates:

1. TypeScript enums representing the states
2. State handler classes for each state
3. State transition context interfaces
4. Flow analysis for possible state transitions

This generator helps maintain consistency between visual state diagrams and the corresponding code implementation, ensuring that the code accurately reflects the state machine design.

## Usage

You can run the generator using the following command:

```bash
ts-node generators/src/lib/state-diagram-code/generator.ts <workspaceRoot> <diagramsPath> <outputPath> [flowsPath]
```

### Parameters

- `workspaceRoot`: The root directory of your workspace
- `diagramsPath`: The directory containing your Mermaid.js state diagrams
- `outputPath`: The directory where generated code will be saved
- `flowsPath` (optional): The directory where flow analysis will be saved

### Example

```bash
ts-node generators/src/lib/state-diagram-code/generator.ts /path/to/workspace /path/to/workspace/libs/pay-by-phone-domain/src/.diagrams /path/to/workspace/libs/pay-by-phone-domain/src/lib
```

### VS Code Launch Configuration

A launch configuration is available in `.vscode/launch.json` for debugging:

```json
{
  "type": "node",
  "request": "launch",
  "name": "Run State Diagram Code Generator",
  "runtimeExecutable": "ts-node",
  "runtimeArgs": [
    "--inspect-brk",
    "${workspaceFolder}/generators/src/lib/state-diagram-code/generator.ts",
    "${workspaceFolder}",
    "${workspaceFolder}/libs/pay-by-phone-domain/src/.diagrams",
    "${workspaceFolder}/libs/pay-by-phone-domain/src/lib"
  ],
  "skipFiles": ["<node_internals>/**"],
  "preLaunchTask": "tsc: build - generators/tsconfig.json",
  "outFiles": ["${workspaceFolder}/generators/**/*.mjs"]
}
```

## Limitations

The current implementation has some limitations:
1. Transitions must be expressed with `-->`
2. Only supports stateDiagram-v2 format 
