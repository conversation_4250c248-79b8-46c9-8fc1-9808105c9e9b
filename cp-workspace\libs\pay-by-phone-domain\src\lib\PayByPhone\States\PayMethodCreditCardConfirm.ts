import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodCreditCardConfirm extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Read the customer's confirmation selection
     * 2. If they have confirmed the number
     * 2a. Save card number in storage
     * 2b. Transisiton to PayMethodExpirationPrompt
     * 3. If they reject
     * 3a. Reset the surcharge flag if it was set
     * 3b. They want to re-enter their number, so we'll transition back to PayMethodCreditCardPrompt
     */
    const { request, storage } = context;

    const confirmationSelection = request.Digits;

    if (confirmationSelection === '1') {
      return { nextState: PayByPhoneState.PayMethodExpirationPrompt };
    } else {
      if (storage.surchargeDetails) {
        storage.surchargeDetails.isSurchargeEnabledForCard = false; // Reset surcharge flag if they reject the card
      }
      return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
    }
  }
}
