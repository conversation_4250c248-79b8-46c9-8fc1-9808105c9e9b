"use strict";
var main_config = require('../config/config');

var dialCall = class DialCall {
    constructor(config, req_q_data, req_p_data) {
        this.record_call = 1;
        if (config) {
            this.queue_manager = config['queue_manager'];
            // (bool)$config['record'];

            /*
              CCC-180
              The call route setting "Stop Recording on this Step" should 
              take precedence over the recording settings from `config`.
            */
            if (this.queue_manager.current_step.stop_recording && !this.queue_manager.current_step.stop_recording.disabled && this.queue_manager.current_step.stop_recording.checked){
              this.record_call = 0;
            }

            if (this.record_call === 1) {
                this.record_call = (config['record'] === true || config['record'].toString().toLowerCase() === 'true' || config['record'].toString().toLowerCase() === '1') ? "record-from-answer-dual" : 0;
            }

            this.location_id = config['location_id'];
        }
        this.req_q_data = req_q_data;
        this.req_p_data = req_p_data;
    }

    async get_caller_id(location_id) {
        let callNumber = '';
        const cpapiClient = require('../libraries/cpapi-client');
        const acctClient = new cpapiClient.acctClient(main_config.db.serviceTokens.readWrite);
        let trackingNumber = await acctClient.cache.getData(
          `trackingnumber?filterActive=true&filterLocation_id=${location_id}`
          );
        let trackingNumbers = trackingNumber['items'];

        trackingNumbers.forEach((number) => {
            if (number.type !== 'virtual' && callNumber == '') {
                callNumber = number.call_number;
            }
        })

        return callNumber;
    }

    /**
     * Dials to specified phone number
     *
     * @access public
     * @param  int $log_id
     * @param  string $phone_list Comma separated list
     * @return void
     */
    async call(log_id, phone_list, sip_username, sip_password) {
        var self = this;
        // Implement dial functionality
        var options = {
            'action': main_config.call_url + 'twilio/process_next_step/' + log_id,
            'timeout': self.queue_manager.timeout,
            'ringTone': 'us'
        };

        if (self.record_call){
          options.record = self.record_call;
        }

        var dial_sip = false;
        let phones = phone_list.split(';');
        phones.forEach(function(phone) {
            phone = phone.trim();
            if (phone) {
                if (phone.indexOf('@') !== -1) {
                    dial_sip = true;
                    return;
                }
            }
        });

        var caller_id = self.req_p_data['Caller'];
        if (self.req_p_data['Caller'].indexOf('sip') != -1)
        {
            // eslint-disable-next-line no-useless-escape
            var sip_pattern = '/[^\:]*\:([0-9]*)@.*/';
            var from_match = sip_pattern.exec(self.req_p_data['Caller']);

            if (from_match) {
                caller_id = from_match[1];
            }
        }

        if (dial_sip || self.req_p_data['From'].indexOf('sip') === -1) {
            options['callerId'] = caller_id;
        } else {
            let call_number = await self.get_caller_id(self.location_id);
            options['callerId'] = call_number;
        }

        let dial = self.queue_manager.twilio_response.dial(options);
        phones.forEach(function(phone) {
            phone = phone.trim();
            if (phone) {
                if (phone.indexOf('@') !== -1) {
                    var sip_options = {};

                    if (sip_username) {
                        sip_options = {
                            'username': sip_username,
                            'password': sip_password
                        };
                    }

                    dial.sip(phone, sip_options);
                } else {
                    dial.number(phone);
                }
            }
        });
        self.queue_manager.is_output_set = 1;
        return { 'type': 'exit' };
    }

}


module.exports = {
    'DialCall': dialCall
}
