<?php
/**
 * Create or validate call service
 *
 * @category TestCallTask
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Phalcon\Cli\Task;
use Phalcon\Db\Adapter\Pdo\Mysql;
use CallPotential\CPCommon\ClientFactory;

/**
 * Create or validate call service
 *
 * @category TestCallTask
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class TestCallTask extends Task
{
    public $json_data = [];
    public $db;

    /**
     * Prepare Data for Unit Test case
     *
     * @return void
     */
    public function setUpAction()
    {
        $this->initDb();
        $json_data = file_get_contents("././test/testDataSet.json");
        if (!$json_data) {
            echo "Data Json file not found".PHP_EOL;
            exit();
        }
        $this->json_data = json_decode($json_data);

        $this->setupCreatSiteAdminAction(121);  //Create new user
        $this->setupLocationAction(135);  //Create new loction

        $this->setupCreateTwilioAccountAction(); // create twilio account

        $this->setupCallLogDBAction(1);   //Create call log in database
        $this->setupCallLogdynamoDBAction();  //Create call log in dynamo DB
        $this->setupCallHistoryESAction("testcase-callhistory-120-135");
        //Create call history in ES
        $this->setupCallRouteConfigAction("testcase-callroute-121-135");
        // Create call route in ES
    }

    /**
     * Delete all the test case data after unit test case execute
     *
     * @return void
     */
    public function tearDownAction()
    {
        $this->initDb();
        $json_data = file_get_contents("././test/testDataSet.json");
        if (!$json_data) {
            echo "Data Json file not found".PHP_EOL;
            exit();
        }
        $this->json_data = json_decode($json_data);
        $this->teardownTwilioAccountAction(121); // Delete twilio account
        $this->teardownCreatSiteAdminAction(121);  //Delete user
        $this->teardownLocationAction(135);  //Delete location
        $this->teardownCallLogDBAction(1);  //Delete call log from database
        $this->teardownCallLogdynamoDBAction(135);  //Delete call log from dynamo DB
        $this->teardownCallHistoryESAction("testcase-callhistory-120-135");
        //Delete call history from ES
        $this->teardownCallRouteConfigAction("testcase-callroute-121-135");
        // Delete call route in ES
    }

    /**
     * Initialize DB
     *
     * @return void
     */
    public function initDb()
    {
        $host = $_ENV['DB_HOST'] ?? die('DB_HOST not found');
        $username = $_ENV['DB_USER'] ?? die('DB_USER not found');
        $password = $_ENV['DB_PASS'] ?? die('DB_PASS not found');
        $dbname = $_ENV['DB_NAME'] ?? die('DB_NAME not found');

        $this->db = new Mysql(compact('host', 'username', 'password', 'dbname'));
    }

    /**
     * create user to test getById api of location service
     *
     * @param $id to be stored
     *
     * @return void
     */
    public function setupCreatSiteAdminAction($id)
    {
        $siteAdminValues[] = $id;
        $siteAdminValues = array_merge($siteAdminValues, $this->json_data->siteAdmin_values);
        $siteAdminKeys = ($this->json_data->siteAdmin_keys);

        $siteAdminValues = array_merge($siteAdminValues, [md5('testing123')]); // password
        $siteAdminValues = array_merge($siteAdminValues, ["AC".$this->randomString()]);  //sid
        $siteAdminValues = array_merge($siteAdminValues, [$this->randomString()]);  //authtoken
        $siteAdminValues = array_merge(
            $siteAdminValues,
            ["CA".$this->randomString()]
        );  //agent_call_sid
        $siteAdminValues = array_merge(
            $siteAdminValues,
            ["WW".$this->randomString()]
        );  //agent_calling_sid
        $siteAdminValues = array_merge(
            $siteAdminValues,
            ["WS".$this->randomString()]
        );  //workspace_sid
        $siteAdminValues = array_merge(
            $siteAdminValues,
            ["WS".substr($this->randomString(), 0, 32)]
        );  //worker_sid

        try {
            $query = "INSERT INTO users ($siteAdminKeys)
                VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            if ($this->db->execute($query, $siteAdminValues)) {
                echo 'Site Admin created.'.PHP_EOL;

                $query = "INSERT INTO feature_toggles
                (account_id,feature_id,feature_name,feature_value)
                VALUES ($id, 2, 'login-2.0', 1)";

                if ($this->db->execute($query)) {
                    echo 'V2 feature created/enabled.'.PHP_EOL;
                }
            } else {
                throw new Exception('Database error');
            }
        } catch (Exception $e) {
            echo $e->getMessage().PHP_EOL;
        }
    }

    /**
     * delete user which was created to test getById api of location service
     *
     * @param $id to be stored
     *
     * @return void
     */
    public function teardownCreatSiteAdminAction($id)
    {
        try {
            $query = "DELETE FROM feature_toggles WHERE account_id = $id";
            if ($this->db->execute($query)) {
                echo 'V2 feature deleted'.PHP_EOL;

                $query = "DELETE FROM users WHERE user_id = $id";
                if ($this->db->execute($query)) {
                    echo 'Site Admin deleted'.PHP_EOL;
                }
            } else {
                throw new Exception('Database error');
            }
        } catch (Exception $e) {
            echo $e->getMessage().PHP_EOL;
        }
    }

    /**
     * create location to test getById api of location service
     *
     * @param $id to be stored
     *
     * @return void
     */
    public function setupLocationAction($id)
    {
        $locationValues[] = $id;
        $locationValues = array_merge($locationValues, $this->json_data->location_values);
        $locationKeys = ($this->json_data->location_keys);
        try {
            $query = "INSERT INTO `locations` ($locationKeys)
                        VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                        ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                        ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                        ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,
                        ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            if ($this->db->execute($query, $locationValues)) {
                echo 'Location created'.PHP_EOL;
            } else {
                throw new Exception('Database error');
            }
        } catch (Exception $e) {
            echo $e->getMessage().PHP_EOL;
        }
    }

    /**
     * delete location which was created to test getById api of location service
     *
     * @param $id CLI command arguments
     *
     * @return void
     */
    public function teardownLocationAction($id)
    {
        try {
            $query = "DELETE FROM locations WHERE location_id = $id";
            if ($this->db->execute($query)) {
                echo 'Location deleted'.PHP_EOL;
            } else {
                throw new Exception('Database error');
            }
        } catch (Exception $e) {
            echo $e->getMessage().PHP_EOL;
        }
    }

    /**
     * create call log in database
     *
     * @param $id to be stored
     *
     * @return void
     */
    public function setupCallLogDBAction($id)
    {
        try {
            $call_log = CallDetail::findFirst($id);
            if ($call_log) {
                echo 'call_log already created.'.PHP_EOL;

                return;
            }

            $callLog = (array) $this->json_data->call_log_db;
            $model = new CallDetail();
            $model->assign($callLog);
            $response = $model->save($callLog, $id);
            if ($response) {
                echo 'call_log created successfully in DB.'.PHP_EOL;
            } else {
                echo "Error in call_log creation in DB.".PHP_EOL;
            }
        } catch (Exception $e) {
            echo "Error in call_log creation in DB.".PHP_EOL;
        }
    }


    /**
     * Delete call log from database
     *
     * @param $id to delete
     *
     * @return void
     */
    public function teardownCallLogDBAction(int $id)
    {
        $callLog = CallDetail::findFirst($id);
        try {
            if (!empty($callLog)) {
                if ($callLog->delete() === true) {
                    echo 'call_log deleted successfully from DB.'.PHP_EOL;
                } else {
                    echo 'Error in deletion of call_log from DB.'.PHP_EOL;
                }
            }
        } catch (Exception $e) {
            echo 'Exception: error in call_log deletion from DB.'.PHP_EOL;
        }
    }

    /**
     * create call log in dynamoDB
     *
     * @return void
     */
    public function setupCallLogdynamoDBAction()
    {
        try {
            $callLog = (array) $this->json_data->call_log_es;
            $model = new Call();
            $response = $model->add($callLog);
            if ($response) {
                echo 'call_log created successfully in dynamoDB.'.PHP_EOL;
            } else {
                echo "Error in call_log creation in dynamoDB.".PHP_EOL;
            }
        } catch (Exception $e) {
            echo "Error in call_log creation in dynamoDB.".PHP_EOL;
        }
    }


    /**
     * Delete call log from dynamoDB
     *
     * @param int $location_id id to delete
     *
     * @return void
     */
    public function teardownCallLogdynamoDBAction(int $location_id)
    {
        $deleteId = [
            'location_id' => $location_id,
            'twilio_id' => 'CA046b65e58134c5c7ffd54fa1526ad186',
        ];
        $model = new Call();
        $result = $model->deleteById($deleteId);

        try {
            if ($result) {
                echo 'call log deleted successfully from dynamoDB.'.PHP_EOL;
            } else {
                echo 'Error in deletion of call log from dynamoDB.'.PHP_EOL;
            }
        } catch (Exception $e) {
            echo 'Exception: error in call log deletion from dynamoDB.'.PHP_EOL;
        }
    }

    /**
     * create call history in ES
     *
     * @param $id ES ID
     *
     * @return void
     */
    public function setupCallHistoryESAction($id)
    {
        try {
            $callHistory = (array) $this->json_data->call_history;
            $model = new CallHistory();
            $model->setIndexSuffix($callHistory['datestamp']);
            $response = $model->add($callHistory, $id);
            if ($response) {
                echo 'Call History created successfully in ES.'.PHP_EOL;
            } else {
                echo "Error in Call History creation in ES.".PHP_EOL;
            }
        } catch (Exception $e) {
            echo "Exception: Error in Call History creation in ES.".PHP_EOL;
        }
    }


    /**
     * Delete Call History from ES
     *
     * @param $id id to delete
     *
     * @return void
     */
    public function teardownCallHistoryESAction($id)
    {
        $model = new CallHistory();
        $callHistory = (array) $this->json_data->call_history;
        $model->setIndexSuffix($callHistory['datestamp']);
        $result = $model->deleteById($id);

        try {
            if ($result) {
                echo 'Call History deleted successfully from ES.'.PHP_EOL;
            } else {
                echo 'Error in deletion of Call History from ES.'.PHP_EOL;
            }
        } catch (Exception $e) {
            echo 'Exception: error in Call History deletion from ES.'.PHP_EOL;
        }
    }

    /**
     * create Call Route Config
     *
     * @param $id to be stored
     *
     * @return void
     */
    public function setupCallRouteConfigAction($id)
    {

        try {
            $callRoute = (array) $this->json_data->call_route;
            $model = new CallRouteConfig();
            $response = $model->add($callRoute, $id);
            if ($response) {
                echo 'call_route created successfully in ES.'.PHP_EOL;
            } else {
                echo "Error in call_route creation in ES.".PHP_EOL;
            }
        } catch (Exception $e) {
            echo "Exception: Error in call_route creation in ES.".PHP_EOL;
        }
    }


    /**
     * Delete tracking number
     *
     * @param $id CLI command arguments
     *
     * @return void
     */
    public function teardownCallRouteConfigAction($id)
    {
        $model = new CallRouteConfig();
        $result = $model->deleteById($id);
        try {
            if ($result) {
                echo 'call_route_config deleted successfully from ES.'.PHP_EOL;
            } else {
                echo 'Error in deletion of call_route_config from ES.'.PHP_EOL;
            }
        } catch (Exception $e) {
            echo 'Exception: error in call_route_config deletion from ES.'.PHP_EOL;
        }
    }

    /**
     * Create Twilio Account data
     *
     * @return void
     */
    public function setupCreateTwilioAccountAction()
    {
        try {
            $twilio_account = (array) $this->json_data->twilio_account;
            $twilioAccount = new TwilioAccount();
            $createtwilioAccount = $twilioAccount->save($twilio_account);
            if ($createtwilioAccount) {
                echo "TwilioAccount created.".PHP_EOL;
            } else {
                echo "Error in TwilioAccount creation".PHP_EOL;
            }
        } catch (Exception $e) {
            echo 'Exception: error in TwilioAccount creation '.$e->getMessage().PHP_EOL;
        }
    }

    /**
     * Tear down Twilio action
     *
     * @param $id to be stored
     *
     * @return void
     */
    public function teardownTwilioAccountAction($id)
    {
        try {
            $model = new TwilioAccount();
            $model->deleteById($id);
        } catch (Exception $e) {
            echo 'Exception: error in TwilioAccount deletion'.PHP_EOL;
        }
    }

    /**
     * Generate Random String
     *
     * @return void
     */
    private function randomString()
    {
        return rtrim(base64_encode(md5(microtime())), "=");
    }
}
