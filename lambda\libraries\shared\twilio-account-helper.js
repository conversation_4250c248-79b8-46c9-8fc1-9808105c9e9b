const AWS = require('aws-sdk');
const config = require("../../config/config");
const dynamo = new AWS.DynamoDB.DocumentClient({
  endpoint: config.dynamodb.endpoint,
});

async function getTwilioAccount(params) {
  try {
    let twilioAccount = await dynamo.query(params).promise();
    return twilioAccount.Items[0];
  } catch (error) {
    console.error("Error getting Twilio account:", error);
    throw error;
  }
}

module.exports = {
  getTwilioAccount
};