<?php
/**
 * Workspace backup library
 *
 * Created by Ph<PERSON><PERSON>torm.
 * User: cwalker
 * Date: 12/16/17
 * Time: 2:22 PM
 *
 * @category WorkspaceBackup
 * @package  CallCenter
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

namespace CallCenter;

use CallPotential\CallCenter\TwilioSync;
use CallPotential\CallCenter\TwilioTaskRouter;

/**
 * Workspace backup library
 *
 * @category WorkspaceBackup
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class WorkspaceBackup
{
    /**
     * Account Id
     *
     * @var integer|null
     */
    private $accountId = null;

    /**
     * Task router
     *
     * @var object|null
     */
    private $taskRouter = null;

    /**
     * Sync
     *
     * @var object|null
     */
    private $sync = null;

    /**
     * Constructor to initialize data
     *
     * @param integer $accountId account Id
     */
    public function __construct(int $accountId)
    {
        $this->accountId = $accountId;
        $this->taskRouter = new TwilioTaskRouter($this->accountId);
        $this->sync = new TwilioSync($this->accountId);

        return $this;
    }

    /**
     * Workspace to backup in S3
     *
     * @param string $workspaceSid workspace SID
     *
     * @return \stdClass
     */
    public function backupWorkspace(string $workspaceSid): \stdClass
    {
        $currentData = $this->readWorkspace($workspaceSid);
        //echo json_encode($currentData)."\n";
        $core = \CallPotential\CPCommon\ClientFactory::getCoreClient();

        return $core->putS3data(
            'call',
            'twilio',
            $this->accountId,
            'workspace',
            json_encode($currentData),
            $workspaceSid,
            date('Y-m-d h:i:s')
        );
    }

    /**
     * Read workspace to backup
     *
     * @param string $workspaceSid workspace SID
     *
     * @return array
     */
    public function readWorkspace(string $workspaceSid): array
    {
        $data = $this->taskRouter->getWorkspace($workspaceSid);
        unset($data['links']);
        unset($data['url']);
        $workers = $this->taskRouter->getWorkers($workspaceSid);
        foreach ($workers as $w) {
            $worker = $w->toArray();
            unset($worker['links']);
            unset($worker['url']);
            $data['workers'][] = $worker;
        }
        $queues = $this->taskRouter->getQueues($workspaceSid);
        foreach ($queues as $q) {
            $queue = $q->toArray();
            unset($queue['links']);
            unset($queue['url']);
            $data['queues'][] = $queue;
        }
        $activities = $this->taskRouter->getWorkspaceActivities($workspaceSid);
        foreach ($activities as $a) {
            $activity = $a->toArray();
            unset($activity['links']);
            unset($activity['url']);
            $data['activities'][] = $activity;
        }
        $workflows = $this->taskRouter->getWorkflows($workspaceSid);
        foreach ($workflows as $w) {
            $workflow = $w->toArray();
            unset($workflow['links']);
            unset($workflow['url']);
            $data['workflows'][] = $workflow;
        }

        $syncServices = $this->sync->listServices();
        foreach ($syncServices as $service) {
            $s = $service->toArray();
            unset($s['links']);
            unset($s['url']);
            $maps = $this->sync->getMaps($s['sid']);
            foreach ($maps as $map) {
                $m = $map->toArray();
                unset($m['links']);
                unset($m['url']);
                $s['maps'][] = $m;
            }
            $docs = $this->sync->getDocuments($s['sid']);
            foreach ($docs as $doc) {
                $d = $doc->toArray();
                unset($d['links']);
                unset($d['url']);
                $s['documents'][] = $d;
            }
            $data['syncServices'][] = $s;
        }

        return $data;
    }
}
