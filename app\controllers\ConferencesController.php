<?php

use App\Services\TwilioConferenceService;
use CallPotential\CPCommon\Controllers;
use Swagger\Annotations as SWG;

/**
 * Swagger
 *
 */
class ConferencesController extends Controllers\BaseController
{
    use Controllers\SessionTrait;

    /**
     * @var mixed
     */
    private TwilioConferenceService $twilioConferenceService;

    public function initialize()
    {
        parent::initialize();
        $this->twilioConferenceService = $this->getDI()->getShared(TwilioConferenceService::class);
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Participant Data"},
     *     path="/conferences/{friendly_name}/participants",
     *     description="Returns a list of conference participants",
     *     summary="get participants",
     *     operationId="GetListOfCallParticipants",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *       description="Authorization token",
     *       type="string",
     *       name="Authorization",
     *       in="header",
     *       required=true
     *     ),
     * @SWG\Parameter(
     *       description="Twilio ID of Call to fetch",
     *       in="path",
     *       name="friendly_name",
     *       required=true,
     *       type="string"
     *     ),
     * @SWG\Response(
     *       response=200,
     *       description="conference participants detail response"
     *     ),@SWG\Response(
     *       response="403",
     *       description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="404",
     *       description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *       response="500",
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     * @throws \Twilio\Exceptions\ConfigurationException
     */
    public function getListOfCallParticipantsAction(string $friendlyName)
    {
        $accountId = $this->getCurrentAccountId();
        $participants = $this->twilioConferenceService->getTwilioConferenceParticipants($accountId, $friendlyName);
        $data = $this->formatGetResponse($participants);
        $this->response->setJsonContent($data);
        return $this->response;
    }

    public function createItem(array $data, $parentId = null)
    {
        return $this->sendNotImplemented();
    }

    public function updateItem($id, array $data, $parentId = null)
    {
        return $this->sendNotImplemented();
    }

    public function getItem($id)
    {
        return $this->sendNotImplemented();
    }

    public function deleteItem($id)
    {
        return $this->sendNotImplemented();
    }

    public function getListContent()
    {
        return $this->sendNotImplemented();
    }

    protected function userHasReadAccess(array $data)
    {
        return $this->sendNotImplemented();
    }

    protected function userHasWriteAccess(array $data)
    {
        return $this->sendNotImplemented();
    }

    protected function userHasDeleteAccess(array $data)
    {
        return $this->sendNotImplemented();
    }

    protected function doBulkSave(): array
    {
        return [];
    }

    protected function doBulkCreate()
    {
        return $this->sendNotImplemented();
    }
}