const TaskAssignmentStatus = {
  Pending: 'pending',
  Reserved: 'reserved',
  Assigned: 'assigned',
  Completed: 'completed',
  Canceled: 'canceled',
};

const ConnectType = {
  Phone: 'phone',
};

const CallStatus = {
  Queued: 'queued',
  Ringing: 'ringing',
  InProgress: 'in-progress',
  Canceled: 'canceled',
  Completed: 'completed',
  Busy: 'busy',
  Failed: 'failed',
  NoAnswer: 'no-answer',
};

const ReservationStatus = {
  Pending: 'pending',
  Accepted: 'accepted',
  Rejected: 'rejected',
  Timeout: 'timeout',
  Rescinded: 'rescinded',
};

const TaskChannel = {
  Voice: 'voice',
  Chat: 'chat',
  SMS: 'sms',
  Email: 'email',
};

const TaskPriority = {
  Low: 'low',
  Normal: 'normal',
  High: 'high',
};

const RecordingStatus = {
  Completed: 'completed',
  Absent: 'absent',
  InProgress: 'in-progress',
};

  

module.exports = {
  TaskAssignmentStatus,
  ConnectType,
  CallStatus,
  ReservationStatus,
  TaskChannel,
  TaskPriority,
  RecordingStatus
};