import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
@Injectable()
export class PayMethodExpirationConfirm extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Read the customer's confirmation
     * 2. If they have confirmed the expiration date
     * 2a. Transition to PayMethodSecurityCodePrompt
     * 3. If they reject, they want to re-enter the expiration date
     * 3a. Transition to PayMethodExpirationPrompt
     */

    const { request } = context;

    const confirmationSelection = request.Digits;

    if (confirmationSelection === '1') {
      return { nextState: PayByPhoneState.PayMethodSecurityCodePrompt };
    } else {
      return { nextState: PayByPhoneState.PayMethodExpirationPrompt };
    }
  }
}
