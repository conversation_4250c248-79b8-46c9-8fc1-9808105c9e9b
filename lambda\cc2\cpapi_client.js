"use strict";
const config = require('./config');
const got = require('got');
const redis = require('redis');
const acct_url = `${config.API_ACCT_URL}/`;
const int_url = `${config.API_INT_URL}/`;
const loc_url = `${config.API_LOC_URL}/`;
const core_url = `${config.API_CORE_URL}/`;
const call_url = `${config.API_CALL_URL}/`;
const mcc_url = `${config.API_MCC_URL}/`;
const sms_url = `${config.API_SMS_URL}/`;
const email_url = `${config.API_EMAIL_URL}/`;

var apiClient = class apiClient {
  constructor(authToken) {
    if (!authToken) {
      throw new Error('empty authToken!');
    }
    this.url = '';
    this.customLogs = false;
    this.options = {
        headers: { Authorization: authToken }
    };

    let clientObject = this;
    this.cache = {
      async getData(endpoint) {
        try {
          const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
          await redisClient.connect();
          let redisKey = clientObject.url + endpoint;
          let cacheData = await redisClient.get(redisKey);

          if (cacheData && Object.keys(cacheData).length !== 0) {
            await redisClient.disconnect();
            return JSON.parse(cacheData);
          } else {
            cacheData = await clientObject.getData(endpoint)
            await redisClient.set(redisKey, JSON.stringify(cacheData), {EX: 300});
            await redisClient.disconnect();
            return cacheData;
          }
        } catch (e) {
          console.error('ERROR redis cache error', e, new Error().stack);
          return await clientObject.getData(endpoint)
        }
      }
    }
  }

  async getData(endpoint) {
    try {
      let url = this.url + endpoint;
      console.debug('GET request start', url);
      let res = await got.get(url, this.options);
      let data = JSON.parse(res.body);
      console.debug('GET request end', url);
      return data;
    } catch (e) {
      console.error('ERROR getting data', e, new Error().stack);
      return {};
    }
  }

  async postData(endpoint, postData) {
    try {
      let url = this.url + endpoint;

      let options = {...this.options,
        body: JSON.stringify(postData)
      };

      console.debug('POST request start', url);
      let res = await got.post(url, options);
      let data = JSON.parse(res.body);

      if (this.customLogs) {
        console.debug('POST request', postData, 'response', data);
      }

      console.debug('POST request end', url);
      return data;
    } catch (e) {
      if (e.statusCode && e.statusCode === 404) {
        console.log('DEBUG posting data', e, new Error().stack);
        console.log('DEBUG response body', e.response.body);
      } else {
        console.error('ERROR posting data', e, new Error().stack);
        console.error('ERROR response body', e.response.body);
      }
      console.log('postData', postData);
      return {};
    }
  }

  async putData(endpoint, putData) {
    try {
      let url = this.url + endpoint;

      let options = {...this.options,
        body: JSON.stringify(putData)
      };

      console.debug('PUT request start', url);
      let res = await got.put(url, options);
      let data = JSON.parse(res.body);

      if (this.customLogs) {
        console.debug('PUT request', putData, 'response', data);
      }

      console.debug('PUT request end', url);
      return data;
    } catch (e) {
      console.error('ERROR putting data', e, new Error().stack);
      console.error('ERROR response body', e.response.body);
      console.log('putData', putData);
      return {};
    }
  }
}

var locClient = class locClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = loc_url;
  }

  async getLocation(locationId, formatFields=false) {
    try {
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();
      let locData = await redisClient.get(`location_${locationId}`);

      if (locData && Object.keys(locData).length !== 0) {
        await redisClient.disconnect();
        return JSON.parse(locData);
      } else {
        locData = await this.getData(`location/${locationId}?formatFields=${formatFields}`)
        await redisClient.set(`location_${locationId}`, JSON.stringify(locData), {EX: 300});
        await redisClient.disconnect();
        return locData;
      }
    } catch (e) {
      console.error('ERROR redis error', e, new Error().stack);
      return await this.getData(`location/${locationId}?formatFields=${formatFields}`)
    }
  }

  async getLocationConfiguration(locationId, formatFields=false) {
    try {
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();
      let locData = await redisClient.get(`locationconfiguration_${locationId}`);

      if (locData && Object.keys(locData).length !== 0) {
        await redisClient.disconnect();
        return JSON.parse(locData);
      } else {
        locData = await this.getData(`locationconfiguration/${locationId}?formatFields=${formatFields}`)
        await redisClient.set(`locationconfiguration_${locationId}`, JSON.stringify(locData), {EX: 300});
        await redisClient.disconnect();
        return locData;
      }
    } catch (e) {
      return await this.getData(`locationconfiguration/${locationId}?formatFields=${formatFields}`)
    }
  }
}

var intClient = class intClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = int_url;
  }
}

var acctClient = class acctClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = acct_url;
  }
}

var coreClient = class coreClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = core_url;
  }

  async validateSession() {
    return await this.getData('session');
  }
}

var callClient = class callClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = call_url;
    this.customLogs = true;
  }
}

var mccClient = class mccClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = mcc_url;
  }
}

var smsClient = class smsClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = sms_url;
  }
}

var emailClient = class emailClient extends apiClient {
  constructor(authToken) {
    super(authToken);
    this.url = email_url;
  }
}

module.exports = {
  'intClient'  : intClient,
  'locClient'  : locClient,
  'acctClient' : acctClient,
  'coreClient' : coreClient,
  'callClient' : callClient,
  'mccClient'  : mccClient,
  'smsClient'  : smsClient,
  'emailClient': emailClient,
  'apiClient'  : apiClient,
};
