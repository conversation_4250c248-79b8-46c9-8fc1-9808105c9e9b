{"name": "@cp-workspace/source", "version": "0.0.0", "license": "MIT", "scripts": {"unit-test:all": "nx run-many --target=test --all --exclude=lambda-functions-e2e", "bundle-dependencies:create-manifest": "ts-node ./generators/src/lib/dependency-analysis/generator.ts ./ ./dist/apps/lambda-functions/main.js ./dist/apps/lambda-functions/"}, "private": true, "dependencies": {"@aws-sdk/client-eventbridge": "^3.592.0", "@aws-sdk/client-lambda": "^3.600.0", "@bugsnag/js": "^8.2.0", "@bugsnag/plugin-aws-lambda": "^8.2.0", "@bugsnag/plugin-express": "^8.2.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.3.8", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.2", "@nestjs/platform-express": "^10.0.2", "aws-serverless-express": "^3.4.0", "axios": "^1.6.0", "card-validator": "^9.1.0", "express": "^4.19.2", "hot-shots": "^10.0.0", "ioredis": "^5.4.1", "ioredis-mock": "^8.9.0", "nestjs-i18n": "^10.4.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "tslib": "^2.3.0", "twilio": "^5.0.4", "xml2js": "^0.6.2"}, "devDependencies": {"@bugsnag/source-maps": "^2.3.3", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.3.8", "@nx/eslint": "18.3.4", "@nx/eslint-plugin": "18.3.4", "@nx/jest": "18.3.5", "@nx/js": "18.3.5", "@nx/nest": "18.3.5", "@nx/node": "18.3.5", "@nx/web": "18.3.4", "@nx/webpack": "18.3.5", "@nx/workspace": "18.3.4", "@swc-node/register": "~1.8.0", "@swc/core": "^1.4.13", "@swc/helpers": "~0.5.2", "@types/aws-lambda": "^8.10.138", "@types/ioredis-mock": "^8.2.5", "@types/jest": "^29.4.0", "@types/node": "18.16.9", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "core-js": "^3.36.1", "eslint": "~8.57.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.4.1", "nx": "18.3.4", "prettier": "^2.6.2", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.5.1", "ts-node": "10.9.1", "typescript": "~5.4.2", "webpack-cli": "^5.1.4"}}