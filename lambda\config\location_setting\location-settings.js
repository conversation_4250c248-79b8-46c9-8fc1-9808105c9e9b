const cpapiClient = require('../../libraries/cpapi-client');
const config = require('../config');

class LocationSettings {
    static async getLocationSetting(settingName, accountId, locationId) {
        const coreClient = new cpapiClient.coreClient(config.db.serviceTokens.readWrite);
        const endpoint = `locationsetting/${settingName}?AccountId=${accountId}&LocationId=${locationId}`;
        
        try {
            const settingData = await coreClient.cache.getData(endpoint);
            return settingData?.Value;
        } catch (error) {
            console.log(`Error fetching location setting: ${error}`, new Error().stack);
            return null;
        }
    }
}

module.exports = LocationSettings;