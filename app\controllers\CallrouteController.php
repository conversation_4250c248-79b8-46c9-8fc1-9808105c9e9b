<?php
/**
 * CallrouteController
 *
 * @category CallrouteController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\Controllers;
use CallPotential\CPCommon\HttpStatusCode;
use Twilio\Rest\Client;
use CallPotential\CallCenter\TwilioTaskRouter;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallrouteList",
 * @SWG\Property(
 *     property="items",
 *     type="array",
 * @SWG\Items(ref="#/definitions/CallRouteConfig")
 *   ),
 * @SWG\Property(property="paging",ref="#/definitions/PagingData"),
 * @SWG\Property(property="status",type="string")
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallRouteGetById",
 *     allOf = {
 * @SWG\Schema(ref="#/definitions/CallRouteConfig"),
 * @SWG\Schema(
 *      @SWG\Property(property="_lastmodified",type="integer")
 *       )
 *     }
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallRouteBulkResultItem",
 * @SWG\Property(property="message",type="string"),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallRouteConfig"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallRouteBulkResult",
 * @SWG\Property(property="success",type="array",@SWG\Items(type="integer",format="int64")),
 * @SWG\Property(property="error",type="array",@SWG\Items(ref="#/definitions/CallRouteBulkResultItem"))
 * )
 */

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallRouteBulkResponse",
 * @SWG\Property(property="status",type="string",enum={"OK","ERROR","MIXED"}),
 * @SWG\Property(property="data",type="array",@SWG\Items(ref="#/definitions/CallRouteBulkResult"))
 * )
 */

/**
 * CallrouteController
 *
 * @category CallrouteController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class CallrouteController extends AbstractCallRestController
{
    /**
     * Filters to apply while querying for data that typecast fields values passed in GET action
     *
     * @var array
     */
    public $listFilters = [
        "is_active"        => "bool",
    ];

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    public function getModelName(): string
    {
        return "CallRouteConfig";
    }

    /**
     * GetListContent
     *
     * @return mixed
     */
    public function getListContent()
    {
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Config"},
     *     path="/callroute/{id}",
     *     description="Returns an call route based on a single ID",
     *     summary="get call route",
     *     operationId="CallrouteGetById",
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of call route to fetch",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="integer"
     *     ),
     *     produces={
     *         "application/json"
     *     },
     * @SWG\Response(
     *         response=200,
     *         description="call route response",
     * @SWG\Schema(ref="#/definitions/CallRouteGetById")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header"
     *     ),@SWG\Response(
     *         response="404",
     *         description="Data not found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }

        return parent::getAction();
    }

    /**
     * Swagger
     *
     * @SWG\Get(
     *     tags={"Call Config"},
     *     path="/callroute",
     *     description="Returns all call routes from the system that the user has access to",
     *     summary="list call routes",
     *     operationId="ListCallroutes",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="Page Number",
     *         format="int64",
     *         in="query",
     *         name="page",
     *         required=false,
     *         type="integer"
     *     ),
     * @SWG\Parameter(
     *         name="perpage",
     *         in="query",
     *         description="maximum number of results to return",
     *         required=false,
     *         type="integer",
     *         format="int32"
     *     ),
     * @SWG\Parameter(
     *          name="filterIs_active",
     *          in="query",
     *          description="Filter records with active/inactive/both",
     *          type="string",
     *          enum={"true", "false"}
     *     ),
     * @SWG\Parameter(
     *          name="includeConfig",
     *          in="query",
     *          description="If set to true will send data with config field",
     *          type="string",
     *          enum={"true", "false"}
     *      ),
     * @SWG\Response(
     *         response=200,
     *         description="call route response",
     * @SWG\Schema(ref="#/definitions/CallrouteList")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="401",
     *         description="Not Authorized",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response=500,
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }

        $this->infoMessage('list request begin', 'CallrouteController::listAction:'.__LINE__);
        $userId = $this->getCurrentAccountId();
        if (empty($userId)) {
            return $this->sendNotAuthorized();
        }
        $data = [];
        $esQuery = ['match' => ['user_id' => $userId]];
        $result = $this->queryES($esQuery);
        if (!empty($result)) {
            $data = $result;
            $data['_id'] = Util::array_get('_id', $result);
        }
        $this->debugMessage(
            'Load Complete: ' . count($data) . " records",
            'CallrouteController::listAction:' . __LINE__
        );
        $this->response->setStatusCode(
            CallPotential\CPCommon\HttpStatusCode::HTTP_OK,
            CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                CallPotential\CPCommon\HttpStatusCode::HTTP_OK
            )
        );
        $this->response->setJsonContent(
            array('status' => "OK", 'items' => $data, 'paging' => false)
        );
        $this->infoMessage('request end', 'CallrouteController::listAction:' . __LINE__);

        return $this->response;
    }

    /**
     * Swagger
     *
     * @SWG\Post(path="/callroute",
     *   tags={"Call Config"},
     *   summary="Create a new call route",
     *   description="create new call route",
     *   summary="create call route",
     *   operationId="CreateCallroute",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *      description="Auhorization Token",
     *      name="Authorization",
     *      in="header",
     *      type="string",
     *      required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Callroute record",
     *     required=false,
     * @SWG\Schema(ref="#/definitions/CallRouteConfig")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/CallRouteConfig")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *       response=500,
     *       description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Method Http accept: POST (insert)
     *
     * @return Phalcon\Http\Response
     */
    public function createAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }

        try {
            $data = Util::objectToArray($this->request->getJsonRawBody());

            if (isset($data['config']) && is_array($data['config'])) {
                $data['config'] = json_encode($data['config']);
            }

            if (Util::existSubArray($data, true)) {
                return $this->bulkCreate();
            }
            if (!is_array($data)) {
                return $this->sendBadRequest();
            }

            $data = $this->preProcessPostData($data);
            $this->infoMessage('create request begin', __METHOD__ . ":" . __LINE__);
            $response = $this->createItem($data, $this->getCurrentAccountId());
            $this->infoMessage('create request end', __METHOD__ . ":" . __LINE__);
            if (is_array($response)) {
                $this->response->setStatusCode(
                    CallPotential\CPCommon\HttpStatusCode::HTTP_CREATED,
                    CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                        CallPotential\CPCommon\HttpStatusCode::HTTP_CREATED
                    )
                );
                $this->response->setJsonContent($response);
            } else {
                $this->sendBadRequest('Unable to create item');
            }

            return $this->response;
        } catch (AppException $ae) {
            return $this->sendBadRequest($ae->getMessage());
        }
    }

    /**
     * Swagger
     *
     * @SWG\Put(path="/callroute/{id}",
     *   tags={"Call Config"},
     *   summary="Update an existing call route",
     *   description="Update existing call route",
     *   operationId="UpdateCallroute",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *      description="Auhorization Token",
     *      name="Authorization",
     *      in="header",
     *      type="string",
     *      required=true
     *   ),
     * @SWG\Parameter(
     *     description="ID of config type",
     *     in="path",
     *     name="id",
     *     required=true,
     *     type="integer",
     *     format="int64"
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="Config type record",
     *     required=true,
     * @SWG\Schema(ref="#/definitions/CallRouteConfig")
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",@SWG\Schema(ref="#/definitions/CallRouteConfig")
     *   ),
     * @SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     */

    /**
     * Swagger
     *
     * @SWG\Put(path="/callroute",
     *   tags={"Call Config"},
     *   summary="Bulk update",
     *   description="Bulk update for archiving call routes",
     *   operationId="UpdateCallRouteBulk",
     *   consumes={"application/json"},
     *   produces={"application/json"},
     * @SWG\Parameter(
     *      description="Auhorization Token",
     *      name="Authorization",
     *      in="header",
     *      type="string",
     *      required=true
     *   ),
     * @SWG\Parameter(
     *     in="body",
     *     name="body",
     *     description="array of call route records",
     *     required=true,
     * @SWG\Schema(type="array",@SWG\Items(ref="#/definitions/CallRouteConfig"))
     *   ),
     * @SWG\Response(
     *     response="default",
     *     description="successful operation",
     * @SWG\Schema(ref="#/definitions/CallRouteBulkResponse")
     *   ),@SWG\Response(
     *         response="400",
     *         description="Invalid data supplied",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   )
     * )
     * )
     */

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        if (!$this->validSession()) {
            return $this->sendNotAuthorized();
        }

        try {
            $data = Util::objectToArray($this->request->getJsonRawBody());

            if (isset($data['config']) && is_array($data['config'])) {
                $data['config'] = json_encode($data['config']);
            }

            if (Util::existSubArray($data, true)) {
                return $this->bulkSave();
            }
            if (!is_array($data)) {
                return $this->sendBadRequest();
            }

            if ($this->id) {
                $original = $this->getItem($this->id);
                $data = $this->preProcessPutData($data, $original);
                $this->infoMessage('update request begin', __METHOD__ . ":" . __LINE__);
                $response = $this->updateItem($this->id, $data, $this->getCurrentAccountId());
                $this->infoMessage('update request end', __METHOD__ . ":" . __LINE__);
                if (is_array($response)) {
                    $this->response->setStatusCode(
                        CallPotential\CPCommon\HttpStatusCode::HTTP_CREATED,
                        CallPotential\CPCommon\HttpStatusCode::getMessageForCode(
                            CallPotential\CPCommon\HttpStatusCode::HTTP_CREATED
                        )
                    );
                    $this->response->setJsonContent($response);
                } else {
                    $this->sendBadRequest('Unable to update item');
                }

                return $this->response;
            }
        } catch (AppException $ae) {
            return $this->sendBadRequest($ae->getMessage());
        }
    }

    /**
     * Swagger
     *
     * @SWG\Delete(
     *     tags={"Call Config"},
     *     path="/callroute/{id}",
     *     description="deletes a single call route based on the ID supplied",
     *     summary="delete call route",
     *     operationId="DeleteCallroute",
     *     produces={"application/json"},
     * @SWG\Parameter(
     *          description="Authorization token",
     *          type="string",
     *          name="Authorization",
     *          in="header",
     *          required=true
     *     ),
     * @SWG\Parameter(
     *         description="ID of call route to delete",
     *         format="int64",
     *         in="path",
     *         name="id",
     *         required=true,
     *         type="integer"
     *     ),
     * @SWG\Response(
     *         response=204,
     *         description="call route deleted",
     * @SWG\Schema(type="null")
     *     ),@SWG\Response(
     *         response="403",
     *         description="Not Authorized Invalid or missing Authorization header",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     ),@SWG\Response(
     *         response="404",
     *         description="ID Not Found",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *   ),@SWG\Response(
     *         response="500",
     *         description="unexpected error",
     * @SWG\Schema(ref="#/definitions/ErrorResponse")
     *     )
     * )
     */

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        if (!strlen($this->authToken)) {
            return $this->sendNotAuthorized();
        }
        if (!$this->validSession()) {
            return $this->sendForbidden();
        }

        return parent::deleteAction();
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    public function doBulkCreate()
    {
        $data = Util::objectToArray($this->request->getJsonRawBody());

        $response = array();
        $bAllSuccess = true;
        $bAllError = true;

        foreach ($data as $rec) {
            $recData = $this->preProcessPostData($rec);
            $this->infoMessage('create request begin', __METHOD__ . ":" . __LINE__);
            $response = $this->createItem($recData, $this->getCurrentAccountId());
            $this->infoMessage('create request end', __METHOD__ . ":" . __LINE__);
            if (is_array($response)) {
                $bAllError = false;
            } else {
                $bAllSuccess = false;
            }
        }

        if (count($data) > 0) {
            if ($bAllSuccess) {
                $httpCode = HttpStatusCode::HTTP_OK;
                $statusStr = 'OK';
            } elseif ($bAllError) {
                $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
                $statusStr = 'ERROR';
            } else {
                $httpCode = HttpStatusCode::HTTP_OK;
                $statusStr = 'MIXED';
            }
        } else {
            $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
            $statusStr = 'ERROR';
        }

        return array(
            'httpCode' => $httpCode,
            'status' => $statusStr,
            'data' => $response,
        );
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    public function doBulkSave(): array
    {
        $data = Util::objectToArray($this->request->getJsonRawBody());

        $response = array();
        $bAllSuccess = true;
        $bAllError = true;

        foreach ($data as $rec) {
            $original = $this->getItem($rec['_id']);
            $recData = $this->preProcessPutData($rec, $original);
            $this->infoMessage('update request begin', __METHOD__ . ":" . __LINE__);
            $response = $this->updateItem($rec['_id'], $recData, $this->getCurrentAccountId());
            $this->infoMessage('update request end', __METHOD__ . ":" . __LINE__);
            if (is_array($response)) {
                $bAllError = false;
            } else {
                $bAllSuccess = false;
            }
        }

        if (count($data) > 0) {
            if ($bAllSuccess) {
                $httpCode = HttpStatusCode::HTTP_OK;
                $statusStr = 'OK';
            } elseif ($bAllError) {
                $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
                $statusStr = 'ERROR';
            } else {
                $httpCode = HttpStatusCode::HTTP_OK;
                $statusStr = 'MIXED';
            }
        } else {
            $httpCode = HttpStatusCode::HTTP_BAD_REQUEST;
            $statusStr = 'ERROR';
        }

        return array(
            'httpCode' => $httpCode,
            'status' => $statusStr,
            'data' => $response,
        );
    }

    /**
     * Any preprocessing of post data is done here
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    protected function preProcessPostData(array $data): array
    {
        $data['_id'] = $this->getId();
        $date = $this->getDate();

        if (!is_array($data['config'])) {
            $data['config'] = json_decode($data['config']);
        }

        if (!empty($data['is_default'])) {
            $esQuery = ['bool' =>
                            ['must' =>
                                [['match' => ['is_default' => '1']],
                                ['match' => ['user_id' => $this->getCurrentAccountId()]], ],
                            ],
                        ];
            $matchResult = $this->queryES($esQuery);

            foreach ($matchResult as $value) {
                    $value['is_default'] = 0;
                    $value['updated_date'] = $this->getDate();
                    $this->updateItem($value['_id'], $value, $this->getCurrentAccountId());
            }
        }

        $data['workflow_sid'] = $this->saveConfigToTwilio($data);
        $data['created_date'] = $date;
        $data['updated_date'] = $date;
        $data['user_id'] = $this->getCurrentAccountId();
        $data['is_active'] = 1;

        return $data;
    }

    /**
     * Any preprocessing of put data is done here
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param array $original array of original call route record data
     *
     * @return array
     */
    protected function preProcessPutData(array $data, array $original = []): array
    {
        $data['_id'] = $original['_id'];

        if (!empty($data['is_default'])) {
            $esQuery = ['bool' =>
                            ['must' =>
                                [['match' => ['is_default' => '1']],
                                ['match' => ['user_id' => $this->getCurrentAccountId()]], ],
                            ],
                        ];
            $matchResult = $this->queryES($esQuery);

            foreach ($matchResult as $value) {
                if ($value['_id'] !== $data['_id']) {
                    $value['is_default'] = 0;
                    $value['updated_date'] = $this->getDate();
                    $this->updateItem($value['_id'], $value, $this->getCurrentAccountId());
                }
            }
        }

        if (isset($data['config'])) {
            if (!is_array($data['config'])) {
                $data['config'] = json_decode($data['config']);
            }
            $data['workflow_sid'] = $this->saveConfigToTwilio($data);
        }

        $data['updated_date'] = $this->getDate();
        $data['user_id'] = $this->getCurrentAccountId();
        $data = array_merge($original, $data);

        return $data;
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        $accountId = $this->getCurrentAccountId();

        return (array_key_exists('user_id', $data) &&
            (int) $data['user_id'] === $accountId &&
            $accountId > 0
        );
    }

    /**
     * Determine is user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        $accountId = $this->getCurrentAccountId();

        return (array_key_exists('user_id', $data) &&
            (int) $data['user_id'] === $accountId &&
            $accountId > 0
        );
    }

    /**
     * Determine is user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        $accountId = $this->getCurrentAccountId();

        return (array_key_exists('user_id', $data) &&
            (int) $data['user_id'] === $accountId &&
            $accountId > 0
        );
    }

    /**
     * Return date
     *
     * @return string
     */
    private function getDate(): string
    {
        $date = new \DateTime();
        $date->setTimezone(new DateTimeZone('America/Chicago'));

        return $date->format('Y-m-d H:i:s');
    }

    /**
     * Create workflow to twilio
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return string
     */
    private function saveConfigToTwilio(array $data): string
    {
        $config = $this->getWorkflowConfig($data);
        $taskRouter = new TwilioTaskRouter($this->getCurrentAccountId());

        if (!empty($data['workflow_sid'])) {
            $workflow = $taskRouter->updateWorkflow(
                $data['workflow_sid'],
                [
                    'Configuration' => json_encode($config),
                    'TaskReservationTimeout' => $data['cascade_time'],
                ]
            );

            return $workflow['sid'];
        }
        $workflow = $taskRouter->createWorkflow(
            $this->getRequestAuthToken(),
            $data['_id'],
            json_encode($config),
            ['TaskReservationTimeout' => $data['cascade_time']]
        );

        return $workflow['sid'];
    }
    /**
     * Retrieve workflow from twilio
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return array
     */
    private function getWorkflowConfig(array $data): array
    {
        $queues = $this->getUniqueSetOfQueues(Util::arrayToObject($data['config']));

        if (count($queues) > 0) {
            $taskRouter = new TwilioTaskRouter($this->getCurrentAccountId());
            $queueDetails = $taskRouter->getQueues();

            for ($x = 0; $x < count($queueDetails); $x++) {
                if (is_object($queueDetails[$x])) {
                    $queueDetails[$x] = $queueDetails[$x]->toArray();
                }
                $queueDetails[$x] = $this->formatGetResponse($queueDetails[$x]);
            }
        }

        $esQuery = ['match' => ['user_id' => $this->getCurrentAccountId()]];
        $callCenterModel = new CallCenter();
        $callCenterData = $callCenterModel->searchAll($esQuery);
        $callCenterData = $callCenterData[0];

        $timedOutTarget = [
            'queue' => $callCenterData['timedout_queue_sid'],
            'priority' => 1,
            'timeout' => 1,
        ];
        $filters = [];
        foreach ($queues as $queue) {
            // Expression includes queue_id and wait_time to match a unique
            // queue setting in a call_route, since same queue object with
            // same queue_id can have different wait_time making them unique
            // from other similar queue. The $expression allows us to set
            // different timeout for a queue per task basis.
            $expression = "queue_id == {$queue['id']} and timeout == {$queue['wait_time']}";
            $queueTarget = [
                'queue' => $queue['id'],
                'priority' => 1,
                'timeout' => $queue['wait_time'],
            ];

            $targets = array($queueTarget, $timedOutTarget);

            $filters[] = [
                'friendly_name' => $queue['id'],
                'expression' => $expression,
                'targets' => $targets,
            ];
        }

        return [
            'task_routing' => [
                'filters' => $filters,
                'default_filter' => [
                    'queue' => $callCenterData['default_queue_sid'],
                ],
            ],
        ];
    }

    /**
     * Retrieve queues from twilios
     *
     * @param object|null $config call route config
     * @param array       $queues queue list to retrieve
     *
     * @return array
     */
    private function getUniqueSetOfQueues($config = null, $queues = array()): array
    {
        // Check if it is call center step
        if (isset($config->type) && $config->type === 'route_connect'
            && $config->connect_type->selected === 'call_center'
        ) {
            // Add queue info to unique list if not present already
            $queues = $this->addUniqueQueueInfo($queues, $config);
        }

        foreach ($config->children as $step_obj) {
            $queues = $this->getUniqueSetOfQueues($step_obj, $queues);
        }

        return $queues;
    }

    /**
     * Add queues to twilio
     *
     * @param array       $queues queue list to retrieve
     * @param object|null $config call route config
     *
     * @return array
     */
    private function addUniqueQueueInfo($queues = array(), $config = null): array
    {
        // Set queue info to be stored
        $queue = array('id' => $config->queue_type->selected,
            'wait_time' => $config->max_wait_time->value, );

        $index = serialize($queue);

        // Check if the queue info is already there in the $queues unique set
        if (!isset($queues[$index])) {
            // Store queue info in unique queue list
            $queues[$index] = $queue;
        }

        return $queues;
    }

    /**
     * Query ES records
     *
     * @param array $esQuery ES query to match with records
     *
     * @return array
     */
    private function queryES(array $esQuery): array
    {
        $model = $this->getModelObject();

        return $model->searchAll($esQuery);
    }

    /**
     * Create call record Id
     *
     * @return string
     */
    private function getId(): string
    {
        return join(
            '-',
            [
                $this->getCurrentAccountId(),
                time(),
            ]
        );
    }
}
