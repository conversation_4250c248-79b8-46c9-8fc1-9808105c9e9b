<?php
/**
 * script for updating log_id in track_callcenter_tasks table
 *
 * @category UpdateLogIdCCTasksTable
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use Phalcon\Cli\Task;
use \CallPotential\CPCommon\Cli\CliOptions;
use Phalcon\Db\Enum;

/**
 * @category UpdateLogIdCCTasksTableTask
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

class UpdateLogIdCCTasksTableTask extends Task
{
    /**
     * Main action of task
     *
     */
    public function mainAction(): void
    {
        echo "UpdateLogIdCCTasksTableTask CLI operations" . PHP_EOL;
        echo "Command: php app/cli.php UpdateLogIdCCTasksTable update --date=date" . PHP_EOL;
    }

    /**
     * update log_id in track_callcenter_tasks table
     * @param string $date date
     */
    public function updateAction(string $date = ''): void
    {
        $cliParams = array();
        if ($date) {
            array_push($cliParams, $date);
        }

        CliOptions::parseArgs($cliParams);
        $date = CliOptions::getValue('date', date('Y-m-d'));

        //valdate date format
        $dateTime = DateTime::createFromFormat('Y-m-d', $date);
        if (!$dateTime || $dateTime->format('Y-m-d') !== $date) {
            echo $date . " Provided date should be in format 'Y-m-d' " . PHP_EOL;

            return ;
        }

        $workflowList = [
            "InboundCallCenterVoice",
            "InboundLocationVoice",
            "OutboundVoice",
            "CallBackWorkflow",
            "InboundVideo",
        ];

        foreach ($workflowList as $workflow) {
            $this->updateTCT($workflow, $date);
        }
    }

    /**
     * update log_id in track_callcenter_tasks table
     * @param string $workflow workflow name
     * @param string $date     date
     */
    public function updateTCT(string $workflow, string $date): void
    {
        echo PHP_EOL . $workflow . " Script Started ". date('Y-m-d H:i:s') . PHP_EOL;

        $query = "SELECT `log_id` FROM `track_callcenter_tasks` WHERE `workflow` =
        '" .$workflow. "' AND log_id > 10000000000000000 AND DATE(`date_created`) = '".$date."';";

        $query = $this->db->query($query);
        $tctTableResults = $query->fetchAll(Enum::FETCH_ASSOC);

        foreach ($tctTableResults as $value) {
            try {
                $callModel = $this->getCallModel();
                $param = [
                    'log_id' => (int) $value['log_id'],
                ];

                $dynamoCallLog = $callModel->findByQuery($param, "log_id-index");

                if (!empty($dynamoCallLog) && isset($dynamoCallLog[0])) {
                    $dynamoCallLog = $dynamoCallLog[0];

                    echo "Updating log_id for record ". $dynamoCallLog['log_id'] . PHP_EOL;

                    $updatequery = "UPDATE `track_callcenter_tasks`
                    SET log_id = " . $dynamoCallLog['db_log_id'] .
                    " WHERE log_id = '" .$dynamoCallLog['log_id'] ."';";

                    $this->dbLegacy->execute($updatequery);
                }
            } catch (\Exception $e) {
                echo "Error while running UpdateLogIdCCTasksTableTask ". $e->getMessage();
                throw new \Exception($e->getMessage());
            }
        }

        echo  $workflow . " Script Ended ".date('Y-m-d H:i:s') . PHP_EOL;
    }

    /**
     * Return Call class object
     *
     * @return \Call call model
     */
    public function getCallModel(): \Call
    {
        return new \Call();
    }
}
