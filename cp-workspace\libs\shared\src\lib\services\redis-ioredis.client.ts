import { InjectRedis } from "@nestjs-modules/ioredis";
import { Injectable } from "@nestjs/common";
import { Redis } from "ioredis";
import { IIoRedisProvider, RedisClientInitialization } from "./redis-ioredis.provider";

@Injectable()
export class IoRedisClientProvider implements IIoRedisProvider {
  constructor(@InjectRedis() private readonly redis: Redis) { }

  getClient(_options?: RedisClientInitialization) {
    return this.redis;
  }
  async stopClient() {
    await this.redis.quit();
  }
}
