import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AsyncTestController } from './AsyncTest.controller';
import { PayByPhoneRequestInterceptor } from '../Interceptors/PayByPhoneRequestInterceptor';
import { AsyncWorkersClientType, AsyncWorkersIntegrationModule, AsyncWorkersIntegrationService, AsyncWorkersModuleOptions, AsyncWorkersService, DomainEventsService } from '@cp-workspace/shared';

describe('AsyncTestController', () => {
  let controller: AsyncTestController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        AsyncWorkersIntegrationModule.forRoot({
          clientType: AsyncWorkersClientType.IN_MEMORY,
          options: { enabled: true },
        } as AsyncWorkersModuleOptions)
      ],
      controllers: [AsyncTestController],
      providers: [
        PayByPhoneRequestInterceptor, 
        AsyncWorkersService,
        AsyncWorkersIntegrationService,
        {provide: DomainEventsService, useValue: {}}
      ],
    }).compile();

    controller = module.get<AsyncTestController>(AsyncTestController);
  });

  describe('handleAsyncTestRequest', () => {
    it('should throw BadRequestException if request body is empty', async () => {
      const mockRequest = {
        body: null,
      };
      await expect(controller.handleAsyncTestRequest(mockRequest as any))
        .rejects
        .toThrow(BadRequestException);
    });

    it('should return the request body on a successful request', async () => {
      const mockRequest = {
        body: { message: 'Hello, World!' },
      };
      const result = await controller.handleAsyncTestRequest(mockRequest as any);
      expect(result).toEqual(mockRequest.body);
    });
  });
});
