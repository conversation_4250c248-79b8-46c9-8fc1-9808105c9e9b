AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: Initiate outbound call
Resources:
  OutboundCallFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: outbound_call.post
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs6.10
      Timeout: 60
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /outbound_call
            Method: any
  ClientTokenFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: client_token.post
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs6.10
      Timeout: 60
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /client_token
            Method: any
  UpdateAgentStatusFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: update_agent_status.post
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs6.10
      Timeout: 60
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /update_agent_status
            Method: any
  UpdateAgentStatsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: update_agent_stats.post
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs6.10
      Timeout: 120
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /update_agent_stats
            Method: any
  WorksapceStatsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: stats.handler
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs6.10
      Timeout: 120
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /stats
            Method: any
  UpdateDynamoFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: update_dynamodb.handler
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs6.10
      Timeout: 120
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /update_dynamodb
            Method: any
  TwilioConferenceEvent:
    Type: AWS::Serverless::Function
    Properties:
      Handler: conference-event.handler
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs8.10
      Timeout: 60
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /twilio-conference-event
            Method: any
  TwilioOutbound:
    Type: AWS::Serverless::Function
    Properties:
      Handler: outbound.handler
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs8.10
      Timeout: 60
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /twilio-outbound
            Method: any
  TwilioOutboundCallback:
    Type: AWS::Serverless::Function
    Properties:
      Handler: outbound-callback.handler
      Policies: AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          TABLE_NAME: !Ref Table
      Runtime: nodejs8.10
      Timeout: 60
      Events:
        GetResource:
          Type: Api
          Properties:
            Path: /twilio-outbound-callback
            Method: any
