{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@cp-workspace/generators": ["generators/src/index.ts"], "@cp-workspace/lambda-functions-app": ["apps/lambda-functions/src/index.ts"], "@cp-workspace/pay-by-phone-domain": ["libs/pay-by-phone-domain/src/index.ts"], "@cp-workspace/shared": ["libs/shared/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}