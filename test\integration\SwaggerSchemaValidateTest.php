<?php
namespace Test;

class SwaggerSchemaValidateTest extends IntegrationCase
{
    public function testSwaggerSchema()
    {
        $this->isLocalScan = true;
        $this->checkValidSchema('call');
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }
}
