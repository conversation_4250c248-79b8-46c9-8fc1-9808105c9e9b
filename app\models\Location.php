<?php
/**
 * Location model
 *
 * @category Location
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Location model
 *
 * @category Location
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="Location")
 */
class Location extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * Location Id
     *
     * @var string
     *
     * @Primary
     * @Identity
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $location_id;

    /**
     * Location name
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $location_name;

    /**
     * Email address
     *
     * @var string
     *
     * @Column(type="string", length=127, nullable=true)
     * @SWG\Property()
     */
    protected $email;

    /**
     * Address 1
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $address1;

    /**
     * Address 2
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $address2;

    /**
     * City
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $city;

    /**
     * Province
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $province;

    /**
     * Postal code
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $postal;

    /**
     * Country
     *
     * @var string
     *
     * @Column(type="string", length=2, nullable=true)
     * @SWG\Property()
     */
    protected $country;

    /**
     * Latitude
     *
     * @var float
     *
     * @Column(type="double", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $latitude;

    /**
     * Longitude
     *
     * @var float
     *
     * @Column(type="double", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $longitude;

    /**
     * Phone number
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $phone;

    /**
     * Primary SIP setup
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $primary_sip_setup;

    /**
     * Callcard Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $callcard_id;

    /**
     * Customer card Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $customer_card_id;

    /**
     * Location gradesheet Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $location_gradesheet_id;

    /**
     * Employee gradesheet Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $employee_gradesheet_id;

    /**
     * Greeting
     *
     * @var string
     *
     * @Column(type="string", length=2048, nullable=true)
     * @SWG\Property()
     */
    protected $greeting;

    /**
     * Record outgoing
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $record_outgoing;

    /**
     * Record call announcement
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $record_call_announcement;

    /**
     * Whisper
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $whisper;

    /**
     * User Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $user_id;

    /**
     * Active
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $active;

    /**
     * API type
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $api_type;

    /**
     * API credentials
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $api_credentials;

    /**
     * API permissions
     *
     * @var string
     *
     * @Column(type="string", length=2048, nullable=true)
     * @SWG\Property()
     */
    protected $api_permissions;

    /**
     * Reserved vacant
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $reserved_vacant;

    /**
     * Max reservation length
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $max_reservation_length;

    /**
     * Last API update
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     */
    protected $last_api_update;

    /**
     * Rate type
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $rateType;

    /**
     * Date and time when created
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $dateCreated;

    /**
     * Date removed
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $dateRemoved;

    /**
     * Last update
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $lastupdate;

    /**
     * Is marketing source required
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_mktgsrc_required;

    /**
     * Is marketing source editable
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_mktgsrc_editable;

    /**
     * Is units always rentable
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_units_always_rentable;

    /**
     * Play music on hold
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $play_music_on_hold;

    /**
     * Music file
     *
     * @var string
     *
     * @Column(type="string", length=200, nullable=true)
     * @SWG\Property()
     */
    protected $music_file;

    /**
     * Delay
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $delay;

    /**
     * Followup rule Id
     *
     * @var string
     *
     * @Column(type="string", length=127, nullable=true)
     * @SWG\Property()
     */
    protected $followup_rule_id;

    /**
     * Unavailable units
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $unavailable_units;

    /**
     * Hide phone number
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $hide_phone_number;

    /**
     * Round rate
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $round_rate;

    /**
     * Is inquiry type required
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_inquiry_type_req;

    /**
     * Collection rule Id
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $collection_rule_id;

    /**
     * Timezone
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $timezone;

    /**
     * Use daylight saving
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $use_daylight_saving;

    /**
     * Include all phone numbers
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $include_all_phone;

    /**
     * Include all email address
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $include_all_email;

    /**
     * Include all text
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $include_all_text;

    /**
     * Minimum balance
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $minimum_balance;

    /**
     * Reset collection on payment
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $reset_collection_on_payment;

    /**
     * Logo Image
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $logo_image;

    /**
     * Greeting mp3
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $greeting_mp3;

    /**
     * Greeting type
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $greeting_type;

    /**
     * Friendly name
     *
     * @var string
     *
     * @Column(type="string", length=127, nullable=true)
     * @SWG\Property()
     */
    protected $friendly_name;

    /**
     * Autopay text
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $autopay_text;

    /**
     * Exclude payment link
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $exclude_payment_link;

    /**
     * Outbound phone
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $outbound_phone;

    /**
     * Outbound SIP setup
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $outbound_sip_setup;

    /**
     * Is rate restricted
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_rate_restricted;

    /**
     * Is collection only
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    protected $is_collection_only;

    /**
     * Script callcard Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $script_callcard_id;

    /**
     * Overriden script callcard
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $overriden_script_callcard;

    /**
     * Call route config Id
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $call_route_config_id;

    /**
     * Website
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $website;

    /**
     * Hours availability
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $hours_availability;

    /**
     * Tags
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $tags;

    /**
     * Display phone number
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $display_phone;

    /**
     * Location image 1
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $location_image1;

    /**
     * Location image 2
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $location_image2;

    /**
     * Location image 3
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $location_image3;

    /**
     * Batch event counter
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $batch_event_count;

    /**
     * Overide tracking number
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $overide_tracking_number;

    /**
     * Custom variables
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $custom_variables;

    /**
     * Is API call allowed threshold_1
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    protected $is_api_call_allowed_threshold_1;

    /**
     * Card type
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $card_type;

    /**
     * Enable reservation payment
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $enable_reservation_payment;

    /**
     * API call frequency
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $api_call_frequency;

    /**
     * SIP domain SID
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $sip_domain_sid;

    /**
     * Excluded specials
     *
     * @var string
     *
     * @Column(type="string", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $excluded_specials;

    /**
     * Is source required
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_source_required;

    /**
     * Bypass press 1
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $bypass_press_1;

    /**
     * Lead expiration rules
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $lead_expiration_rules;

    /**
     * Enable transcribe
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $enable_transcribe;

    /**
     * Transcribe minimum duration
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    protected $transcribe_minimum_duration;

    /**
     * Transcribe maximum duration
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    protected $transcribe_maximum_duration;

    /**
     * Convenience fee employee
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $convenience_fee_employee;

    /**
     * Convenience fee phone
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $convenience_fee_phone;

    /**
     * Convenience fee user
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $convenience_fee_user;

    /**
     * Card assignments
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $card_assignments;

    /**
     * Is available filter on
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_available_filter_on;

    /**
     * Rental period
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $rental_period;

    /**
     * Email signature
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $esign;

    /**
     * Keypad zone
     *
     * @var int
     *
     * @Column(type="integer", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $keypad_zone;

    /**
     * SL timezone
     *
     * @var int
     *
     * @Column(type="integer", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $sl_timezone;

    /**
     * Movein required fields
     *
     * @var string
     *
     * @Column(type="string", length=500, nullable=true)
     * @SWG\Property()
     */
    protected $movein_required_fields;

    /**
     * Restrict lead to mark rented
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $restrict_lead_to_mark_rented;

    /**
     * Reservation fee settings
     *
     * @var string
     *
     * @Column(type="string", length=500, nullable=true)
     * @SWG\Property()
     */
    protected $reservation_fee_settings;

    /**
     * Is pull reservation cron running
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_pull_reservation_cron_running;

    /**
     * Record payment calls
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $record_payment_calls;

    /**
     * Pay by phone all
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $paybyphone_all;

    /**
     * Sitelink pull units last time tick
     *
     * @var string
     *
     * @Column(type="string", length=200, nullable=true)
     * @SWG\Property()
     */
    protected $sitelink_pull_units_last_time_tick;

    /**
     * Save credit card
     *
     * @var int
     *
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
     */
    protected $save_cc;

    /**
     * Gate code
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $gate_code;

    /**
     * Reset followup workflow
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $reset_followup_workflow;

    /**
     * Select unit upon time vacant
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $select_unit_upon_time_vacant;

    /**
     * Collection rule changed
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $collection_rule_changed;

    /**
     * Rate type credit card
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $rate_type_cc;

    /**
     * Rate type tc
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $rate_type_tc;

    /**
     *
     * @var int
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
    */
    protected $sl_update_followup_date;

    /**
     * Can rent reserved units
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $can_rent_reserved_units;

    /**
     *
     * @var string
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
    */
    protected $movein_rule_id;

    /**
     *
     * @var int
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
    */
    protected $movein_failure_template;

    /**
     *
     * @var int
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
    */
    protected $is_mktg_source_hide;

    /**
     *
     * @var int
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
    */
    protected $is_inquiry_type_hide;

    /**
     *
     * @var int
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
    */
    protected $mktg_source_default;

    /**
     *
     * @var int
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
    */
    protected $inquiry_type_default;

    /**
     *
     * @var int
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
    */
    protected $esign_email_template;

    /**
     *
     * @var int
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
    */
    protected $esign_sms_template;

    /**
     *
     * @var int
     * @Column(type="integer", length=4, nullable=true)
     * @SWG\Property()
    */
    protected $allow_prev_cc;

    /**
     *
     * @var int
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
    */
    protected $txt_to_email;

    /**
     *
     * @var string
     * @Column(type="string", length=127, nullable=true)
     * @SWG\Property()
    */
    protected $other_email;

    /**
     *
     * @var string
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
    */
    protected $sl_gmt_timeoffset;

    /**
     *
     * @var string
     * @Column(type="string", nullable=true)
     * @SWG\Property()
    */
    protected $marketing_source_map;

    /**
     *
     * @var int
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
    */
    protected $downgrade_reservation;

    /**
     * Method to set the value of field location_id
     *
     * @param string $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(string $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field location_name
     *
     * @param string $location_name value to set
     *
     * @return $this
     */
    public function setLocationName(string $location_name)
    {
        $this->location_name = $location_name;

        return $this;
    }

    /**
     * Method to set the value of field email
     *
     * @param string $email value to set
     *
     * @return $this
     */
    public function setEmail(string $email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Method to set the value of field address1
     *
     * @param string $address1 value to set
     *
     * @return $this
     */
    public function setAddress1(string $address1)
    {
        $this->address1 = $address1;

        return $this;
    }

    /**
     * Method to set the value of field address2
     *
     * @param string $address2 value to set
     *
     * @return $this
     */
    public function setAddress2(string $address2)
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * Method to set the value of field city
     *
     * @param string $city value to set
     *
     * @return $this
     */
    public function setCity(string $city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Method to set the value of field province
     *
     * @param string $province value to set
     *
     * @return $this
     */
    public function setProvince(string $province)
    {
        $this->province = $province;

        return $this;
    }

    /**
     * Method to set the value of field postal
     *
     * @param string $postal value to set
     *
     * @return $this
     */
    public function setPostal(string $postal)
    {
        $this->postal = $postal;

        return $this;
    }

    /**
     * Method to set the value of field country
     *
     * @param string $country value to set
     *
     * @return $this
     */
    public function setCountry(string $country)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Method to set the value of field latitude
     *
     * @param float $latitude value to set
     *
     * @return $this
     */
    public function setLatitude(float $latitude)
    {
        $this->latitude = $latitude;

        return $this;
    }

    /**
     * Method to set the value of field longitude
     *
     * @param float $longitude value to set
     *
     * @return $this
     */
    public function setLongitude(float $longitude)
    {
        $this->longitude = $longitude;

        return $this;
    }

    /**
     * Method to set the value of field phone
     *
     * @param string $phone value to set
     *
     * @return $this
     */
    public function setPhone(string $phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Method to set the value of field primary_sip_setup
     *
     * @param string $primary_sip_setup value to set
     *
     * @return $this
     */
    public function setPrimarySipSetup(string $primary_sip_setup)
    {
        $this->primary_sip_setup = $primary_sip_setup;

        return $this;
    }

    /**
     * Method to set the value of field callcard_id
     *
     * @param string $callcard_id value to set
     *
     * @return $this
     */
    public function setCallcardId(string $callcard_id)
    {
        $this->callcard_id = $callcard_id;

        return $this;
    }

    /**
     * Method to set the value of field customer_card_id
     *
     * @param string $customer_card_id value to set
     *
     * @return $this
     */
    public function setCustomerCardId(string $customer_card_id)
    {
        $this->customer_card_id = $customer_card_id;

        return $this;
    }

    /**
     * Method to set the value of field location_gradesheet_id
     *
     * @param string $location_gradesheet_id value to set
     *
     * @return $this
     */
    public function setLocationGradesheetId(string $location_gradesheet_id)
    {
        $this->location_gradesheet_id = $location_gradesheet_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_gradesheet_id
     *
     * @param string $employee_gradesheet_id value to set
     *
     * @return $this
     */
    public function setEmployeeGradesheetId(string $employee_gradesheet_id)
    {
        $this->employee_gradesheet_id = $employee_gradesheet_id;

        return $this;
    }

    /**
     * Method to set the value of field greeting
     *
     * @param string $greeting value to set
     *
     * @return $this
     */
    public function setGreeting(string $greeting)
    {
        $this->greeting = $greeting;

        return $this;
    }

    /**
     * Method to set the value of field record_outgoing
     *
     * @param integer $record_outgoing value to set
     *
     * @return $this
     */
    public function setRecordOutgoing(int $record_outgoing)
    {
        $this->record_outgoing = $record_outgoing;

        return $this;
    }

    /**
     * Method to set the value of field record_call_announcement
     *
     * @param string $record_call_announcement value to set
     *
     * @return $this
     */
    public function setRecordCallAnnouncement(string $record_call_announcement)
    {
        $this->record_call_announcement = $record_call_announcement;

        return $this;
    }

    /**
     * Method to set the value of field whisper
     *
     * @param integer $whisper value to set
     *
     * @return $this
     */
    public function setWhisper(int $whisper)
    {
        $this->whisper = $whisper;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param string $user_id value to set
     *
     * @return $this
     */
    public function setUserId(string $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field active
     *
     * @param integer $active value to set
     *
     * @return $this
     */
    public function setActive(int $active)
    {
        $this->active = $active;

        return $this;
    }

    /**
     * Method to set the value of field api_type
     *
     * @param string $api_type value to set
     *
     * @return $this
     */
    public function setApiType(string $api_type)
    {
        $this->api_type = $api_type;

        return $this;
    }

    /**
     * Method to set the value of field api_credentials
     *
     * @param string $api_credentials value to set
     *
     * @return $this
     */
    public function setApiCredentials(string $api_credentials)
    {
        $this->api_credentials = $api_credentials;

        return $this;
    }

    /**
     * Method to set the value of field api_permissions
     *
     * @param string $api_permissions value to set
     *
     * @return $this
     */
    public function setApiPermissions(string $api_permissions)
    {
        $this->api_permissions = $api_permissions;

        return $this;
    }

    /**
     * Method to set the value of field reserved_vacant
     *
     * @param integer $reserved_vacant value to set
     *
     * @return $this
     */
    public function setReservedVacant(int $reserved_vacant)
    {
        $this->reserved_vacant = $reserved_vacant;

        return $this;
    }

    /**
     * Method to set the value of field max_reservation_length
     *
     * @param integer $max_reservation_length value to set
     *
     * @return $this
     */
    public function setMaxReservationLength(int $max_reservation_length)
    {
        $this->max_reservation_length = $max_reservation_length;

        return $this;
    }

    /**
     * Method to set the value of field last_api_update
     *
     * @param string $last_api_update value to set
     *
     * @return $this
     */
    public function setLastApiUpdate(string $last_api_update)
    {
        $this->last_api_update = $last_api_update;

        return $this;
    }

    /**
     * Method to set the value of field rateType
     *
     * @param integer $rateType value to set
     *
     * @return $this
     */
    public function setRateType(int $rateType)
    {
        $this->rateType = $rateType;

        return $this;
    }

    /**
     * Method to set the value of field dateCreated
     *
     * @param string $dateCreated value to set
     *
     * @return $this
     */
    public function setDateCreated(string $dateCreated)
    {
        $this->dateCreated = $dateCreated;

        return $this;
    }

    /**
     * Method to set the value of field dateRemoved
     *
     * @param string $dateRemoved value to set
     *
     * @return $this
     */
    public function setDateRemoved(string $dateRemoved)
    {
        $this->dateRemoved = $dateRemoved;

        return $this;
    }

    /**
     * Method to set the value of field lastupdate
     *
     * @param string $lastupdate value to set
     *
     * @return $this
     */
    public function setLastupdate(string $lastupdate)
    {
        $this->lastupdate = $lastupdate;

        return $this;
    }

    /**
     * Method to set the value of field is_mktgsrc_required
     *
     * @param integer $is_mktgsrc_required value to set
     *
     * @return $this
     */
    public function setIsMktgsrcRequired(int $is_mktgsrc_required)
    {
        $this->is_mktgsrc_required = $is_mktgsrc_required;

        return $this;
    }

    /**
     * Method to set the value of field is_mktgsrc_editable
     *
     * @param integer $is_mktgsrc_editable value to set
     *
     * @return $this
     */
    public function setIsMktgsrcEditable(int $is_mktgsrc_editable)
    {
        $this->is_mktgsrc_editable = $is_mktgsrc_editable;

        return $this;
    }

    /**
     * Method to set the value of field is_units_always_rentable
     *
     * @param integer $is_units_always_rentable value to set
     *
     * @return $this
     */
    public function setIsUnitsAlwaysRentable(int $is_units_always_rentable)
    {
        $this->is_units_always_rentable = $is_units_always_rentable;

        return $this;
    }

    /**
     * Method to set the value of field play_music_on_hold
     *
     * @param integer $play_music_on_hold value to set
     *
     * @return $this
     */
    public function setPlayMusicOnHold(int $play_music_on_hold)
    {
        $this->play_music_on_hold = $play_music_on_hold;

        return $this;
    }

    /**
     * Method to set the value of field music_file
     *
     * @param string $music_file value to set
     *
     * @return $this
     */
    public function setMusicFile(string $music_file)
    {
        $this->music_file = $music_file;

        return $this;
    }

    /**
     * Method to set the value of field delay
     *
     * @param integer $delay value to set
     *
     * @return $this
     */
    public function setDelay(int $delay)
    {
        $this->delay = $delay;

        return $this;
    }

    /**
     * Method to set the value of field followup_rule_id
     *
     * @param string $followup_rule_id value to set
     *
     * @return $this
     */
    public function setFollowupRuleId(string $followup_rule_id)
    {
        $this->followup_rule_id = $followup_rule_id;

        return $this;
    }

    /**
     * Method to set the value of field unavailable_units
     *
     * @param string $unavailable_units value to set
     *
     * @return $this
     */
    public function setUnavailableUnits(string $unavailable_units)
    {
        $this->unavailable_units = $unavailable_units;

        return $this;
    }

    /**
     * Method to set the value of field hide_phone_number
     *
     * @param integer $hide_phone_number value to set
     *
     * @return $this
     */
    public function setHidePhoneNumber(int $hide_phone_number)
    {
        $this->hide_phone_number = $hide_phone_number;

        return $this;
    }

    /**
     * Method to set the value of field round_rate
     *
     * @param integer $round_rate value to set
     *
     * @return $this
     */
    public function setRoundRate(int $round_rate)
    {
        $this->round_rate = $round_rate;

        return $this;
    }

    /**
     * Method to set the value of field is_inquiry_type_req
     *
     * @param integer $is_inquiry_type_req value to set
     *
     * @return $this
     */
    public function setIsInquiryTypeReq(int $is_inquiry_type_req)
    {
        $this->is_inquiry_type_req = $is_inquiry_type_req;

        return $this;
    }

    /**
     * Method to set the value of field collection_rule_id
     *
     * @param string $collection_rule_id value to set
     *
     * @return $this
     */
    public function setCollectionRuleId(string $collection_rule_id)
    {
        $this->collection_rule_id = $collection_rule_id;

        return $this;
    }

    /**
     * Method to set the value of field timezone
     *
     * @param string $timezone value to set
     *
     * @return $this
     */
    public function setTimezone(string $timezone)
    {
        $this->timezone = $timezone;

        return $this;
    }

    /**
     * Method to set the value of field use_daylight_saving
     *
     * @param integer $use_daylight_saving value to set
     *
     * @return $this
     */
    public function setUseDaylightSaving(int $use_daylight_saving)
    {
        $this->use_daylight_saving = $use_daylight_saving;

        return $this;
    }

    /**
     * Method to set the value of field include_all_phone
     *
     * @param integer $include_all_phone value to set
     *
     * @return $this
     */
    public function setIncludeAllPhone(int $include_all_phone)
    {
        $this->include_all_phone = $include_all_phone;

        return $this;
    }

    /**
     * Method to set the value of field include_all_email
     *
     * @param integer $include_all_email value to set
     *
     * @return $this
     */
    public function setIncludeAllEmail(int $include_all_email)
    {
        $this->include_all_email = $include_all_email;

        return $this;
    }

    /**
     * Method to set the value of field include_all_text
     *
     * @param integer $include_all_text value to set
     *
     * @return $this
     */
    public function setIncludeAllText(int $include_all_text)
    {
        $this->include_all_text = $include_all_text;

        return $this;
    }

    /**
     * Method to set the value of field minimum_balance
     *
     * @param integer $minimum_balance value to set
     *
     * @return $this
     */
    public function setMinimumBalance(int $minimum_balance)
    {
        $this->minimum_balance = $minimum_balance;

        return $this;
    }

    /**
     * Method to set the value of field reset_collection_on_payment
     *
     * @param integer $reset_collection_on_payment value to set
     *
     * @return $this
     */
    public function setResetCollectionOnPayment(int $reset_collection_on_payment)
    {
        $this->reset_collection_on_payment = $reset_collection_on_payment;

        return $this;
    }

    /**
     * Method to set the value of field logo_image
     *
     * @param string $logo_image value to set
     *
     * @return $this
     */
    public function setLogoImage(string $logo_image)
    {
        $this->logo_image = $logo_image;

        return $this;
    }

    /**
     * Method to set the value of field greeting_mp3
     *
     * @param string $greeting_mp3 value to set
     *
     * @return $this
     */
    public function setGreetingMp3(string $greeting_mp3)
    {
        $this->greeting_mp3 = $greeting_mp3;

        return $this;
    }

    /**
     * Method to set the value of field greeting_type
     *
     * @param integer $greeting_type value to set
     *
     * @return $this
     */
    public function setGreetingType(int $greeting_type)
    {
        $this->greeting_type = $greeting_type;

        return $this;
    }

    /**
     * Method to set the value of field friendly_name
     *
     * @param string $friendly_name value to set
     *
     * @return $this
     */
    public function setFriendlyName(string $friendly_name)
    {
        $this->friendly_name = $friendly_name;

        return $this;
    }

    /**
     * Method to set the value of field autopay_text
     *
     * @param string $autopay_text value to set
     *
     * @return $this
     */
    public function setAutopayText(string $autopay_text)
    {
        $this->autopay_text = $autopay_text;

        return $this;
    }

    /**
     * Method to set the value of field exclude_payment_link
     *
     * @param string $exclude_payment_link value to set
     *
     * @return $this
     */
    public function setExcludePaymentLink(string $exclude_payment_link)
    {
        $this->exclude_payment_link = $exclude_payment_link;

        return $this;
    }

    /**
     * Method to set the value of field outbound_phone
     *
     * @param string $outbound_phone value to set
     *
     * @return $this
     */
    public function setOutboundPhone(string $outbound_phone)
    {
        $this->outbound_phone = $outbound_phone;

        return $this;
    }

    /**
     * Method to set the value of field outbound_sip_setup
     *
     * @param string $outbound_sip_setup value to set
     *
     * @return $this
     */
    public function setOutboundSipSetup(string $outbound_sip_setup)
    {
        $this->outbound_sip_setup = $outbound_sip_setup;

        return $this;
    }

    /**
     * Method to set the value of field is_rate_restricted
     *
     * @param integer $is_rate_restricted value to set
     *
     * @return $this
     */
    public function setIsRateRestricted(int $is_rate_restricted)
    {
        $this->is_rate_restricted = $is_rate_restricted;

        return $this;
    }

    /**
     * Method to set the value of field is_collection_only
     *
     * @param integer $is_collection_only value to set
     *
     * @return $this
     */
    public function setIsCollectionOnly(int $is_collection_only)
    {
        $this->is_collection_only = $is_collection_only;

        return $this;
    }

    /**
     * Method to set the value of field script_callcard_id
     *
     * @param string $script_callcard_id value to set
     *
     * @return $this
     */
    public function setScriptCallcardId(string $script_callcard_id)
    {
        $this->script_callcard_id = $script_callcard_id;

        return $this;
    }

    /**
     * Method to set the value of field overriden_script_callcard
     *
     * @param string $overriden_script_callcard value to set
     *
     * @return $this
     */
    public function setOverridenScriptCallcard(string $overriden_script_callcard)
    {
        $this->overriden_script_callcard = $overriden_script_callcard;

        return $this;
    }

    /**
     * Method to set the value of field call_route_config_id
     *
     * @param string $call_route_config_id value to set
     *
     * @return $this
     */
    public function setCallRouteConfigId(string $call_route_config_id)
    {
        $this->call_route_config_id = $call_route_config_id;

        return $this;
    }

    /**
     * Method to set the value of field website
     *
     * @param string $website value to set
     *
     * @return $this
     */
    public function setWebsite(string $website)
    {
        $this->website = $website;

        return $this;
    }

    /**
     * Method to set the value of field hours_availability
     *
     * @param string $hours_availability value to set
     *
     * @return $this
     */
    public function setHoursAvailability(string $hours_availability)
    {
        $this->hours_availability = $hours_availability;

        return $this;
    }

    /**
     * Method to set the value of field tags
     *
     * @param string $tags value to set
     *
     * @return $this
     */
    public function setTags(string $tags)
    {
        $this->tags = $tags;

        return $this;
    }

    /**
     * Method to set the value of field display_phone
     *
     * @param string $display_phone value to set
     *
     * @return $this
     */
    public function setDisplayPhone(string $display_phone)
    {
        $this->display_phone = $display_phone;

        return $this;
    }

    /**
     * Method to set the value of field location_image1
     *
     * @param string $location_image1 value to set
     *
     * @return $this
     */
    public function setLocationImage1(string $location_image1)
    {
        $this->location_image1 = $location_image1;

        return $this;
    }

    /**
     * Method to set the value of field location_image2
     *
     * @param string $location_image2 value to set
     *
     * @return $this
     */
    public function setLocationImage2(string $location_image2)
    {
        $this->location_image2 = $location_image2;

        return $this;
    }

    /**
     * Method to set the value of field location_image3
     *
     * @param string $location_image3 value to set
     *
     * @return $this
     */
    public function setLocationImage3(string $location_image3)
    {
        $this->location_image3 = $location_image3;

        return $this;
    }

    /**
     * Method to set the value of field batch_event_count
     *
     * @param integer $batch_event_count value to set
     *
     * @return $this
     */
    public function setBatchEventCount(int $batch_event_count)
    {
        $this->batch_event_count = $batch_event_count;

        return $this;
    }

    /**
     * Method to set the value of field overide_tracking_number
     *
     * @param string $overide_tracking_number value to set
     *
     * @return $this
     */
    public function setOverideTrackingNumber(string $overide_tracking_number)
    {
        $this->overide_tracking_number = $overide_tracking_number;

        return $this;
    }

    /**
     * Method to set the value of field custom_variables
     *
     * @param string $custom_variables value to set
     *
     * @return $this
     */
    public function setCustomVariables(string $custom_variables)
    {
        $this->custom_variables = $custom_variables;

        return $this;
    }

    /**
     * Method to set the value of field is_api_call_allowed_threshold_1
     *
     * @param integer $is_api_call_allowed_threshold_1 value to set
     *
     * @return $this
     */
    public function setIsApiCallAllowedThreshold1(int $is_api_call_allowed_threshold_1)
    {
        $this->is_api_call_allowed_threshold_1 = $is_api_call_allowed_threshold_1;

        return $this;
    }

    /**
     * Method to set the value of field card_type
     *
     * @param string $card_type value to set
     *
     * @return $this
     */
    public function setCardType(string $card_type)
    {
        $this->card_type = $card_type;

        return $this;
    }

    /**
     * Method to set the value of field enable_reservation_payment
     *
     * @param integer $enable_reservation_payment value to set
     *
     * @return $this
     */
    public function setEnableReservationPayment(int $enable_reservation_payment)
    {
        $this->enable_reservation_payment = $enable_reservation_payment;

        return $this;
    }

    /**
     * Method to set the value of field api_call_frequency
     *
     * @param integer $api_call_frequency value to set
     *
     * @return $this
     */
    public function setApiCallFrequency(int $api_call_frequency)
    {
        $this->api_call_frequency = $api_call_frequency;

        return $this;
    }

    /**
     * Method to set the value of field sip_domain_sid
     *
     * @param string $sip_domain_sid value to set
     *
     * @return $this
     */
    public function setSipDomainSid(string $sip_domain_sid)
    {
        $this->sip_domain_sid = $sip_domain_sid;

        return $this;
    }

    /**
     * Method to set the value of field excluded_specials
     *
     * @param string $excluded_specials value to set
     *
     * @return $this
     */
    public function setExcludedSpecials(string $excluded_specials)
    {
        $this->excluded_specials = $excluded_specials;

        return $this;
    }

    /**
     * Method to set the value of field is_source_required
     *
     * @param integer $is_source_required value to set
     *
     * @return $this
     */
    public function setIsSourceRequired(int $is_source_required)
    {
        $this->is_source_required = $is_source_required;

        return $this;
    }

    /**
     * Method to set the value of field bypass_press_1
     *
     * @param integer $bypass_press_1 value to set
     *
     * @return $this
     */
    public function setBypassPress1(int $bypass_press_1)
    {
        $this->bypass_press_1 = $bypass_press_1;

        return $this;
    }

    /**
     * Method to set the value of field lead_expiration_rules
     *
     * @param string $lead_expiration_rules value to set
     *
     * @return $this
     */
    public function setLeadExpirationRules(string $lead_expiration_rules)
    {
        $this->lead_expiration_rules = $lead_expiration_rules;

        return $this;
    }

    /**
     * Method to set the value of field enable_transcribe
     *
     * @param integer $enable_transcribe value to set
     *
     * @return $this
     */
    public function setEnableTranscribe(int $enable_transcribe)
    {
        $this->enable_transcribe = $enable_transcribe;

        return $this;
    }

    /**
     * Method to set the value of field transcribe_minimum_duration
     *
     * @param integer $transcribe_minimum_duration value to set
     *
     * @return $this
     */
    public function setTranscribeMinimumDuration(int $transcribe_minimum_duration)
    {
        $this->transcribe_minimum_duration = $transcribe_minimum_duration;

        return $this;
    }

    /**
     * Method to set the value of field transcribe_maximum_duration
     *
     * @param integer $transcribe_maximum_duration value to set
     *
     * @return $this
     */
    public function setTranscribeMaximumDuration(int $transcribe_maximum_duration)
    {
        $this->transcribe_maximum_duration = $transcribe_maximum_duration;

        return $this;
    }

    /**
     * Method to set the value of field convenience_fee_employee
     *
     * @param string $convenience_fee_employee value to set
     *
     * @return $this
     */
    public function setConvenienceFeeEmployee(string $convenience_fee_employee)
    {
        $this->convenience_fee_employee = $convenience_fee_employee;

        return $this;
    }

    /**
     * Method to set the value of field convenience_fee_phone
     *
     * @param string $convenience_fee_phone value to set
     *
     * @return $this
     */
    public function setConvenienceFeePhone(string $convenience_fee_phone)
    {
        $this->convenience_fee_phone = $convenience_fee_phone;

        return $this;
    }

    /**
     * Method to set the value of field convenience_fee_user
     *
     * @param string $convenience_fee_user value to set
     *
     * @return $this
     */
    public function setConvenienceFeeUser(string $convenience_fee_user)
    {
        $this->convenience_fee_user = $convenience_fee_user;

        return $this;
    }

    /**
     * Method to set the value of field card_assignments
     *
     * @param string $card_assignments value to set
     *
     * @return $this
     */
    public function setCardAssignments(string $card_assignments)
    {
        $this->card_assignments = $card_assignments;

        return $this;
    }

    /**
     * Method to set the value of field is_available_filter_on
     *
     * @param integer $is_available_filter_on value to set
     *
     * @return $this
     */
    public function setIsAvailableFilterOn(int $is_available_filter_on)
    {
        $this->is_available_filter_on = $is_available_filter_on;

        return $this;
    }

    /**
     * Method to set the value of field rental_period
     *
     * @param string $rental_period value to set
     *
     * @return $this
     */
    public function setRentalPeriod(string $rental_period)
    {
        $this->rental_period = $rental_period;

        return $this;
    }

    /**
     * Method to set the value of field esign
     *
     * @param string $esign value to set
     *
     * @return $this
     */
    public function setEsign(string $esign)
    {
        $this->esign = $esign;

        return $this;
    }

    /**
     * Method to set the value of field keypad_zone
     *
     * @param integer $keypad_zone value to set
     *
     * @return $this
     */
    public function setKeypadZone(int $keypad_zone)
    {
        $this->keypad_zone = $keypad_zone;

        return $this;
    }

    /**
     * Method to set the value of field sl_timezone
     *
     * @param integer $sl_timezone value to set
     *
     * @return $this
     */
    public function setSlTimezone(int $sl_timezone)
    {
        $this->sl_timezone = $sl_timezone;

        return $this;
    }

    /**
     * Method to set the value of field movein_required_fields
     *
     * @param string $movein_required_fields value to set
     *
     * @return $this
     */
    public function setMoveinRequiredFields(string $movein_required_fields)
    {
        $this->movein_required_fields = $movein_required_fields;

        return $this;
    }

    /**
     * Method to set the value of field restrict_lead_to_mark_rented
     *
     * @param integer $restrict_lead_to_mark_rented value to set
     *
     * @return $this
     */
    public function setRestrictLeadToMarkRented(int $restrict_lead_to_mark_rented)
    {
        $this->restrict_lead_to_mark_rented = $restrict_lead_to_mark_rented;

        return $this;
    }

    /**
     * Method to set the value of field reservation_fee_settings
     *
     * @param string $reservation_fee_settings value to set
     *
     * @return $this
     */
    public function setReservationFeeSettings(string $reservation_fee_settings)
    {
        $this->reservation_fee_settings = $reservation_fee_settings;

        return $this;
    }

    /**
     * Method to set the value of field is_pull_reservation_cron_running
     *
     * @param integer $is_pull_reservation_cron_running value to set
     *
     * @return $this
     */
    public function setIsPullReservationCronRunning(int $is_pull_reservation_cron_running)
    {
        $this->is_pull_reservation_cron_running = $is_pull_reservation_cron_running;

        return $this;
    }

    /**
     * Method to set the value of field record_payment_calls
     *
     * @param integer $record_payment_calls value to set
     *
     * @return $this
     */
    public function setRecordPaymentCalls(int $record_payment_calls)
    {
        $this->record_payment_calls = $record_payment_calls;

        return $this;
    }

    /**
     * Method to set the value of field paybyphone_all
     *
     * @param integer $paybyphone_all value to set
     *
     * @return $this
     */
    public function setPaybyphoneAll(int $paybyphone_all)
    {
        $this->paybyphone_all = $paybyphone_all;

        return $this;
    }

    /**
     * Method to set the value of field sitelink_pull_units_last_time_tick
     *
     * @param string $sitelink_pull_units_last_time_tick value to set
     *
     * @return $this
     */
    public function setSitelinkPullUnitsLastTimeTick(string $sitelink_pull_units_last_time_tick)
    {
        $this->sitelink_pull_units_last_time_tick = $sitelink_pull_units_last_time_tick;

        return $this;
    }

    /**
     * Method to set the value of field save_cc
     *
     * @param integer $save_cc value to set
     *
     * @return $this
     */
    public function setSaveCc(int $save_cc)
    {
        $this->save_cc = $save_cc;

        return $this;
    }

    /**
     * Method to set the value of field gate_code
     *
     * @param string $gate_code value to set
     *
     * @return $this
     */
    public function setGateCode(string $gate_code)
    {
        $this->gate_code = $gate_code;

        return $this;
    }

    /**
     * Method to set the value of field reset_followup_workflow
     *
     * @param integer $reset_followup_workflow value to set
     *
     * @return $this
     */
    public function setResetFollowupWorkflow(int $reset_followup_workflow)
    {
        $this->reset_followup_workflow = $reset_followup_workflow;

        return $this;
    }

    /**
     * Method to set the value of field select_unit_upon_time_vacant
     *
     * @param integer $select_unit_upon_time_vacant value to set
     *
     * @return $this
     */
    public function setSelectUnitUponTimeVacant(int $select_unit_upon_time_vacant)
    {
        $this->select_unit_upon_time_vacant = $select_unit_upon_time_vacant;

        return $this;
    }

    /**
     * Method to set the value of field collection_rule_changed
     *
     * @param integer $collection_rule_changed value to set
     *
     * @return $this
     */
    public function setCollectionRuleChanged(int $collection_rule_changed)
    {
        $this->collection_rule_changed = $collection_rule_changed;

        return $this;
    }

    /**
     * Method to set the value of field rate_type_cc
     *
     * @param integer $rate_type_cc value to set
     *
     * @return $this
     */
    public function setRateTypeCc(int $rate_type_cc)
    {
        $this->rate_type_cc = $rate_type_cc;

        return $this;
    }

    /**
     * Method to set the value of field rate_type_tc
     *
     * @param integer $rate_type_tc value to set
     *
     * @return $this
     */
    public function setRateTypeTc(int $rate_type_tc)
    {
        $this->rate_type_tc = $rate_type_tc;

        return $this;
    }

    /**
     * Method to set the value of field sl_update_followup_date
     *
     * @param integer $sl_update_followup_date
     * @return $this
     */
    public function setSlUpdateFollowupDate(int $sl_update_followup_date)
    {
        $this->sl_update_followup_date = $sl_update_followup_date;

        return $this;
    }

    /**
     * Method to set the value of field can_rent_reserved_units
     *
     * @param string $can_rent_reserved_units value to set
     *
     * @return $this
     */
    public function setCanRentReservedUnits(string $can_rent_reserved_units)
    {
        $this->can_rent_reserved_units = $can_rent_reserved_units;

        return $this;
    }

    /**
     * Method to set the value of field movein_rule_id
     *
     * @param string $movein_rule_id
     * @return $this
     */
    public function setMoveinRuleId(string $movein_rule_id)
    {
        $this->movein_rule_id = $movein_rule_id;

        return $this;
    }

    /**
     * Method to set the value of field movein_failure_template
     *
     * @param integer $movein_failure_template
     * @return $this
     */
    public function setMoveinFailureTemplate(int $movein_failure_template)
    {
        $this->movein_failure_template = $movein_failure_template;

        return $this;
    }

    /**
     * Method to set the value of field is_mktg_source_hide
     *
     * @param integer $is_mktg_source_hide
     * @return $this
     */
    public function setIsMktgSourceHide(int $is_mktg_source_hide)
    {
        $this->is_mktg_source_hide = $is_mktg_source_hide;

        return $this;
    }

    /**
     * Method to set the value of field is_inquiry_type_hide
     *
     * @param integer $is_inquiry_type_hide
     * @return $this
     */
    public function setIsInquiryTypeHide(int $is_inquiry_type_hide)
    {
        $this->is_inquiry_type_hide = $is_inquiry_type_hide;

        return $this;
    }

    /**
     * Method to set the value of field mktg_source_default
     *
     * @param integer $mktg_source_default
     * @return $this
     */
    public function setMktgSourceDefault(int $mktg_source_default)
    {
        $this->mktg_source_default = $mktg_source_default;

        return $this;
    }

    /**
     * Method to set the value of field inquiry_type_default
     *
     * @param integer $inquiry_type_default
     * @return $this
     */
    public function setInquiryTypeDefault(int $inquiry_type_default)
    {
        $this->inquiry_type_default = $inquiry_type_default;

        return $this;
    }

    /**
     * Method to set the value of field esign_email_template
     *
     * @param integer $esign_email_template
     * @return $this
     */
    public function setEsignEmailTemplate(int $esign_email_template)
    {
        $this->esign_email_template = $esign_email_template;

        return $this;
    }

    /**
     * Method to set the value of field esign_sms_template
     *
     * @param integer $esign_sms_template
     * @return $this
     */
    public function setEsignSmsTemplate(int $esign_sms_template)
    {
        $this->esign_sms_template = $esign_sms_template;

        return $this;
    }

    /**
     * Method to set the value of field allow_prev_cc
     *
     * @param integer $allow_prev_cc
     * @return $this
     */
    public function setAllowPrevCc(int $allow_prev_cc)
    {
        $this->allow_prev_cc = $allow_prev_cc;

        return $this;
    }

    /**
     * Method to set the value of field txt_to_email
     *
     * @param integer $txt_to_email
     * @return $this
     */
    public function setTxtToEmail(int $txt_to_email)
    {
        $this->txt_to_email = $txt_to_email;

        return $this;
    }

    /**
     * Method to set the value of field other_email
     *
     * @param string $other_email
     * @return $this
     */
    public function setOtherEmail(string $other_email)
    {
        $this->other_email = $other_email;

        return $this;
    }

    /**
     * Method to set the value of field sl_gmt_timeoffset
     *
     * @param string $sl_gmt_timeoffset
     * @return $this
     */
    public function setSlGmtTimeoffset(string $sl_gmt_timeoffset)
    {
        $this->sl_gmt_timeoffset = $sl_gmt_timeoffset;

        return $this;
    }

    /**
     * Method to set the value of field marketing_source_map
     *
     * @param string $marketing_source_map
     * @return $this
     */
    public function setMarketingSourceMap(string $marketing_source_map)
    {
        $this->marketing_source_map = $marketing_source_map;

        return $this;
    }

    /**
     * Method to set the value of field downgrade_reservation
     *
     * @param integer $downgrade_reservation
     * @return $this
     */
    public function setDowngradeReservation(int $downgrade_reservation)
    {
        $this->downgrade_reservation = $downgrade_reservation;

        return $this;
    }

    /**
     * Returns the value of field location_id
     *
     * @return string
     */
    public function getLocationId(): string
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field location_name
     *
     * @return string
     */
    public function getLocationName(): string
    {
        return $this->location_name;
    }

    /**
     * Returns the value of field email
     *
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * Returns the value of field address1
     *
     * @return string
     */
    public function getAddress1(): string
    {
        return $this->address1;
    }

    /**
     * Returns the value of field address2
     *
     * @return string
     */
    public function getAddress2(): string
    {
        return $this->address2;
    }

    /**
     * Returns the value of field city
     *
     * @return string
     */
    public function getCity(): string
    {
        return $this->city;
    }

    /**
     * Returns the value of field province
     *
     * @return string
     */
    public function getProvince(): string
    {
        return $this->province;
    }

    /**
     * Returns the value of field postal
     *
     * @return string
     */
    public function getPostal(): string
    {
        return $this->postal;
    }

    /**
     * Returns the value of field country
     *
     * @return string
     */
    public function getCountry(): string
    {
        return $this->country;
    }

    /**
     * Returns the value of field latitude
     *
     * @return double
     */
    public function getLatitude(): float
    {
        return $this->latitude;
    }

    /**
     * Returns the value of field longitude
     *
     * @return double
     */
    public function getLongitude(): float
    {
        return $this->longitude;
    }

    /**
     * Returns the value of field phone
     *
     * @return string
     */
    public function getPhone(): string
    {
        return $this->phone;
    }

    /**
     * Returns the value of field primary_sip_setup
     *
     * @return string
     */
    public function getPrimarySipSetup(): string
    {
        return $this->primary_sip_setup;
    }

    /**
     * Returns the value of field callcard_id
     *
     * @return string
     */
    public function getCallcardId(): string
    {
        return $this->callcard_id;
    }

    /**
     * Returns the value of field customer_card_id
     *
     * @return string
     */
    public function getCustomerCardId(): string
    {
        return $this->customer_card_id;
    }

    /**
     * Returns the value of field location_gradesheet_id
     *
     * @return string
     */
    public function getLocationGradesheetId(): string
    {
        return $this->location_gradesheet_id;
    }

    /**
     * Returns the value of field employee_gradesheet_id
     *
     * @return string
     */
    public function getEmployeeGradesheetId(): string
    {
        return $this->employee_gradesheet_id;
    }

    /**
     * Returns the value of field greeting
     *
     * @return string
     */
    public function getGreeting(): string
    {
        return $this->greeting;
    }

    /**
     * Returns the value of field record_outgoing
     *
     * @return integer
     */
    public function getRecordOutgoing(): int
    {
        return $this->record_outgoing;
    }

    /**
     * Returns the value of field record_call_announcement
     *
     * @return string
     */
    public function getRecordCallAnnouncement(): string
    {
        return $this->record_call_announcement;
    }

    /**
     * Returns the value of field whisper
     *
     * @return integer
     */
    public function getWhisper(): int
    {
        return $this->whisper;
    }

    /**
     * Returns the value of field user_id
     *
     * @return string
     */
    public function getUserId(): string
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field active
     *
     * @return integer
     */
    public function getActive(): int
    {
        return $this->active;
    }

    /**
     * Returns the value of field api_type
     *
     * @return string
     */
    public function getApiType(): string
    {
        return $this->api_type;
    }

    /**
     * Returns the value of field api_credentials
     *
     * @return string
     */
    public function getApiCredentials(): string
    {
        return $this->api_credentials;
    }

    /**
     * Returns the value of field api_permissions
     *
     * @return string
     */
    public function getApiPermissions(): string
    {
        return $this->api_permissions;
    }

    /**
     * Returns the value of field reserved_vacant
     *
     * @return integer
     */
    public function getReservedVacant(): int
    {
        return $this->reserved_vacant;
    }

    /**
     * Returns the value of field max_reservation_length
     *
     * @return integer
     */
    public function getMaxReservationLength(): int
    {
        return $this->max_reservation_length;
    }

    /**
     * Returns the value of field last_api_update
     *
     * @return string
     */
    public function getLastApiUpdate(): string
    {
        return $this->last_api_update;
    }

    /**
     * Returns the value of field rateType
     *
     * @return integer
     */
    public function getRateType(): int
    {
        return $this->rateType;
    }

    /**
     * Returns the value of field dateCreated
     *
     * @return string
     */
    public function getDateCreated(): string
    {
        return $this->dateCreated;
    }

    /**
     * Returns the value of field dateRemoved
     *
     * @return string
     */
    public function getDateRemoved(): string
    {
        return $this->dateRemoved;
    }

    /**
     * Returns the value of field lastupdate
     *
     * @return string
     */
    public function getLastupdate(): string
    {
        return $this->lastupdate;
    }

    /**
     * Returns the value of field is_mktgsrc_required
     *
     * @return integer
     */
    public function getIsMktgsrcRequired(): int
    {
        return $this->is_mktgsrc_required;
    }

    /**
     * Returns the value of field is_mktgsrc_editable
     *
     * @return integer
     */
    public function getIsMktgsrcEditable(): int
    {
        return $this->is_mktgsrc_editable;
    }

    /**
     * Returns the value of field is_units_always_rentable
     *
     * @return integer
     */
    public function getIsUnitsAlwaysRentable(): int
    {
        return $this->is_units_always_rentable;
    }

    /**
     * Returns the value of field play_music_on_hold
     *
     * @return integer
     */
    public function getPlayMusicOnHold(): int
    {
        return $this->play_music_on_hold;
    }

    /**
     * Returns the value of field music_file
     *
     * @return string
     */
    public function getMusicFile(): string
    {
        return $this->music_file;
    }

    /**
     * Returns the value of field delay
     *
     * @return integer
     */
    public function getDelay(): int
    {
        return $this->delay;
    }

    /**
     * Returns the value of field followup_rule_id
     *
     * @return string
     */
    public function getFollowupRuleId(): string
    {
        return $this->followup_rule_id;
    }

    /**
     * Returns the value of field unavailable_units
     *
     * @return string
     */
    public function getUnavailableUnits(): string
    {
        return $this->unavailable_units;
    }

    /**
     * Returns the value of field hide_phone_number
     *
     * @return integer
     */
    public function getHidePhoneNumber(): int
    {
        return $this->hide_phone_number;
    }

    /**
     * Returns the value of field round_rate
     *
     * @return integer
     */
    public function getRoundRate(): int
    {
        return $this->round_rate;
    }

    /**
     * Returns the value of field is_inquiry_type_req
     *
     * @return integer
     */
    public function getIsInquiryTypeReq(): int
    {
        return $this->is_inquiry_type_req;
    }

    /**
     * Returns the value of field collection_rule_id
     *
     * @return string
     */
    public function getCollectionRuleId(): string
    {
        return $this->collection_rule_id;
    }

    /**
     * Returns the value of field timezone
     *
     * @return string
     */
    public function getTimezone(): string
    {
        return $this->timezone;
    }

    /**
     * Returns the value of field use_daylight_saving
     *
     * @return integer
     */
    public function getUseDaylightSaving(): int
    {
        return $this->use_daylight_saving;
    }

    /**
     * Returns the value of field include_all_phone
     *
     * @return integer
     */
    public function getIncludeAllPhone(): int
    {
        return $this->include_all_phone;
    }

    /**
     * Returns the value of field include_all_email
     *
     * @return integer
     */
    public function getIncludeAllEmail(): int
    {
        return $this->include_all_email;
    }

    /**
     * Returns the value of field include_all_text
     *
     * @return integer
     */
    public function getIncludeAllText(): int
    {
        return $this->include_all_text;
    }

    /**
     * Returns the value of field minimum_balance
     *
     * @return integer
     */
    public function getMinimumBalance(): int
    {
        return $this->minimum_balance;
    }

    /**
     * Returns the value of field reset_collection_on_payment
     *
     * @return integer
     */
    public function getResetCollectionOnPayment(): int
    {
        return $this->reset_collection_on_payment;
    }

    /**
     * Returns the value of field logo_image
     *
     * @return string
     */
    public function getLogoImage(): string
    {
        return $this->logo_image;
    }

    /**
     * Returns the value of field greeting_mp3
     *
     * @return string
     */
    public function getGreetingMp3(): string
    {
        return $this->greeting_mp3;
    }

    /**
     * Returns the value of field greeting_type
     *
     * @return integer
     */
    public function getGreetingType(): int
    {
        return $this->greeting_type;
    }

    /**
     * Returns the value of field friendly_name
     *
     * @return string
     */
    public function getFriendlyName(): string
    {
        return $this->friendly_name;
    }

    /**
     * Returns the value of field autopay_text
     *
     * @return string
     */
    public function getAutopayText(): string
    {
        return $this->autopay_text;
    }

    /**
     * Returns the value of field exclude_payment_link
     *
     * @return string
     */
    public function getExcludePaymentLink(): string
    {
        return $this->exclude_payment_link;
    }

    /**
     * Returns the value of field outbound_phone
     *
     * @return string
     */
    public function getOutboundPhone(): string
    {
        return $this->outbound_phone;
    }

    /**
     * Returns the value of field outbound_sip_setup
     *
     * @return string
     */
    public function getOutboundSipSetup(): string
    {
        return $this->outbound_sip_setup;
    }

    /**
     * Returns the value of field is_rate_restricted
     *
     * @return integer
     */
    public function getIsRateRestricted(): int
    {
        return $this->is_rate_restricted;
    }

    /**
     * Returns the value of field is_collection_only
     *
     * @return integer
     */
    public function getIsCollectionOnly(): int
    {
        return $this->is_collection_only;
    }

    /**
     * Returns the value of field script_callcard_id
     *
     * @return string
     */
    public function getScriptCallcardId(): string
    {
        return $this->script_callcard_id;
    }

    /**
     * Returns the value of field overriden_script_callcard
     *
     * @return string
     */
    public function getOverridenScriptCallcard(): string
    {
        return $this->overriden_script_callcard;
    }

    /**
     * Returns the value of field call_route_config_id
     *
     * @return string
     */
    public function getCallRouteConfigId(): string
    {
        return $this->call_route_config_id;
    }

    /**
     * Returns the value of field website
     *
     * @return string
     */
    public function getWebsite(): string
    {
        return $this->website;
    }

    /**
     * Returns the value of field hours_availability
     *
     * @return string
     */
    public function getHoursAvailability(): string
    {
        return $this->hours_availability;
    }

    /**
     * Returns the value of field tags
     *
     * @return string
     */
    public function getTags(): string
    {
        return $this->tags;
    }

    /**
     * Returns the value of field display_phone
     *
     * @return string
     */
    public function getDisplayPhone(): string
    {
        return $this->display_phone;
    }

    /**
     * Returns the value of field location_image1
     *
     * @return string
     */
    public function getLocationImage1(): string
    {
        return $this->location_image1;
    }

    /**
     * Returns the value of field location_image2
     *
     * @return string
     */
    public function getLocationImage2(): string
    {
        return $this->location_image2;
    }

    /**
     * Returns the value of field location_image3
     *
     * @return string
     */
    public function getLocationImage3(): string
    {
        return $this->location_image3;
    }

    /**
     * Returns the value of field batch_event_count
     *
     * @return integer
     */
    public function getBatchEventCount(): int
    {
        return $this->batch_event_count;
    }

    /**
     * Returns the value of field overide_tracking_number
     *
     * @return string
     */
    public function getOverideTrackingNumber(): string
    {
        return $this->overide_tracking_number;
    }

    /**
     * Returns the value of field custom_variables
     *
     * @return string
     */
    public function getCustomVariables(): string
    {
        return $this->custom_variables;
    }

    /**
     * Returns the value of field is_api_call_allowed_threshold_1
     *
     * @return integer
     */
    public function getIsApiCallAllowedThreshold1(): int
    {
        return $this->is_api_call_allowed_threshold_1;
    }

    /**
     * Returns the value of field card_type
     *
     * @return string
     */
    public function getCardType(): string
    {
        return $this->card_type;
    }

    /**
     * Returns the value of field enable_reservation_payment
     *
     * @return integer
     */
    public function getEnableReservationPayment(): int
    {
        return $this->enable_reservation_payment;
    }

    /**
     * Returns the value of field api_call_frequency
     *
     * @return integer
     */
    public function getApiCallFrequency(): int
    {
        return $this->api_call_frequency;
    }

    /**
     * Returns the value of field sip_domain_sid
     *
     * @return string
     */
    public function getSipDomainSid(): string
    {
        return $this->sip_domain_sid;
    }

    /**
     * Returns the value of field excluded_specials
     *
     * @return string
     */
    public function getExcludedSpecials(): string
    {
        return $this->excluded_specials;
    }

    /**
     * Returns the value of field is_source_required
     *
     * @return integer
     */
    public function getIsSourceRequired(): int
    {
        return $this->is_source_required;
    }

    /**
     * Returns the value of field bypass_press_1
     *
     * @return integer
     */
    public function getBypassPress1(): int
    {
        return $this->bypass_press_1;
    }

    /**
     * Returns the value of field lead_expiration_rules
     *
     * @return string
     */
    public function getLeadExpirationRules(): string
    {
        return $this->lead_expiration_rules;
    }

    /**
     * Returns the value of field enable_transcribe
     *
     * @return integer
     */
    public function getEnableTranscribe(): int
    {
        return $this->enable_transcribe;
    }

    /**
     * Returns the value of field transcribe_minimum_duration
     *
     * @return integer
     */
    public function getTranscribeMinimumDuration(): int
    {
        return $this->transcribe_minimum_duration;
    }

    /**
     * Returns the value of field transcribe_maximum_duration
     *
     * @return integer
     */
    public function getTranscribeMaximumDuration(): int
    {
        return $this->transcribe_maximum_duration;
    }

    /**
     * Returns the value of field convenience_fee_employee
     *
     * @return string
     */
    public function getConvenienceFeeEmployee(): string
    {
        return $this->convenience_fee_employee;
    }

    /**
     * Returns the value of field convenience_fee_phone
     *
     * @return string
     */
    public function getConvenienceFeePhone(): string
    {
        return $this->convenience_fee_phone;
    }

    /**
     * Returns the value of field convenience_fee_user
     *
     * @return string
     */
    public function getConvenienceFeeUser(): string
    {
        return $this->convenience_fee_user;
    }

    /**
     * Returns the value of field card_assignments
     *
     * @return string
     */
    public function getCardAssignments(): string
    {
        return $this->card_assignments;
    }

    /**
     * Returns the value of field is_available_filter_on
     *
     * @return integer
     */
    public function getIsAvailableFilterOn(): int
    {
        return $this->is_available_filter_on;
    }

    /**
     * Returns the value of field rental_period
     *
     * @return string
     */
    public function getRentalPeriod(): string
    {
        return $this->rental_period;
    }

    /**
     * Returns the value of field esign
     *
     * @return string
     */
    public function getEsign(): string
    {
        return $this->esign;
    }

    /**
     * Returns the value of field keypad_zone
     *
     * @return integer
     */
    public function getKeypadZone(): int
    {
        return $this->keypad_zone;
    }

    /**
     * Returns the value of field sl_timezone
     *
     * @return integer
     */
    public function getSlTimezone(): int
    {
        return $this->sl_timezone;
    }

    /**
     * Returns the value of field movein_required_fields
     *
     * @return string
     */
    public function getMoveinRequiredFields(): string
    {
        return $this->movein_required_fields;
    }

    /**
     * Returns the value of field restrict_lead_to_mark_rented
     *
     * @return integer
     */
    public function getRestrictLeadToMarkRented(): int
    {
        return $this->restrict_lead_to_mark_rented;
    }

    /**
     * Returns the value of field reservation_fee_settings
     *
     * @return string
     */
    public function getReservationFeeSettings(): string
    {
        return $this->reservation_fee_settings;
    }

    /**
     * Returns the value of field is_pull_reservation_cron_running
     *
     * @return integer
     */
    public function getIsPullReservationCronRunning(): int
    {
        return $this->is_pull_reservation_cron_running;
    }

    /**
     * Returns the value of field record_payment_calls
     *
     * @return integer
     */
    public function getRecordPaymentCalls(): int
    {
        return $this->record_payment_calls;
    }

    /**
     * Returns the value of field paybyphone_all
     *
     * @return integer
     */
    public function getPaybyphoneAll(): int
    {
        return $this->paybyphone_all;
    }

    /**
     * Returns the value of field sitelink_pull_units_last_time_tick
     *
     * @return string
     */
    public function getSitelinkPullUnitsLastTimeTick(): string
    {
        return $this->sitelink_pull_units_last_time_tick;
    }

    /**
     * Returns the value of field save_cc
     *
     * @return integer
     */
    public function getSaveCc(): int
    {
        return $this->save_cc;
    }

    /**
     * Returns the value of field gate_code
     *
     * @return string
     */
    public function getGateCode(): string
    {
        return $this->gate_code;
    }

    /**
     * Returns the value of field reset_followup_workflow
     *
     * @return integer
     */
    public function getResetFollowupWorkflow(): int
    {
        return $this->reset_followup_workflow;
    }

    /**
     * Returns the value of field select_unit_upon_time_vacant
     *
     * @return integer
     */
    public function getSelectUnitUponTimeVacant(): int
    {
        return $this->select_unit_upon_time_vacant;
    }

    /**
     * Returns the value of field collection_rule_changed
     *
     * @return integer
     */
    public function getCollectionRuleChanged(): int
    {
        return $this->collection_rule_changed;
    }

    /**
     * Returns the value of field rate_type_cc
     *
     * @return integer
     */
    public function getRateTypeCc(): int
    {
        return $this->rate_type_cc;
    }

    /**
     * Returns the value of field rate_type_tc
     *
     * @return integer
     */
    public function getRateTypeTc(): int
    {
        return $this->rate_type_tc;
    }

    /**
     * Returns the value of field sl_update_followup_date
     *
     * @return integer
     */
    public function getSlUpdateFollowupDate(): int
    {
        return $this->sl_update_followup_date;
    }

    /**
     * Returns the value of field can_rent_reserved_units
     *
     * @return string
     */
    public function getCanRentReservedUnits(): string
    {
        return $this->can_rent_reserved_units;
    }

    /**
     * Returns the value of field movein_rule_id
     *
     * @return string
     */
    public function getMoveinRuleId(): string
    {
        return $this->movein_rule_id;
    }

    /**
     * Returns the value of field movein_failure_template
     *
     * @return integer
     */
    public function getMoveinFailureTemplate(): int
    {
        return $this->movein_failure_template;
    }

    /**
     * Returns the value of field is_mktg_source_hide
     *
     * @return integer
     */
    public function getIsMktgSourceHide(): int
    {
        return $this->is_mktg_source_hide;
    }

    /**
     * Returns the value of field is_inquiry_type_hide
     *
     * @return integer
     */
    public function getIsInquiryTypeHide(): int
    {
        return $this->is_inquiry_type_hide;
    }

    /**
     * Returns the value of field mktg_source_default
     *
     * @return integer
     */
    public function getMktgSourceDefault(): int
    {
        return $this->mktg_source_default;
    }

    /**
     * Returns the value of field inquiry_type_default
     *
     * @return integer
     */
    public function getInquiryTypeDefault(): int
    {
        return $this->inquiry_type_default;
    }

    /**
     * Returns the value of field esign_email_template
     *
     * @return integer
     */
    public function getEsignEmailTemplate(): int
    {
        return $this->esign_email_template;
    }

    /**
     * Returns the value of field esign_sms_template
     *
     * @return integer
     */
    public function getEsignSmsTemplate(): int
    {
        return $this->esign_sms_template;
    }

    /**
     * Returns the value of field allow_prev_cc
     *
     * @return integer
     */
    public function getAllowPrevCc(): int
    {
        return $this->allow_prev_cc;
    }

    /**
     * Returns the value of field txt_to_email
     *
     * @return integer
     */
    public function getTxtToEmail(): int
    {
        return $this->txt_to_email;
    }

    /**
     * Returns the value of field other_email
     *
     * @return string
     */
    public function getOtherEmail(): string
    {
        return $this->other_email;
    }

    /**
     * Returns the value of field sl_gmt_timeoffset
     *
     * @return string
     */
    public function getSlGmtTimeoffset(): string
    {
        return $this->sl_gmt_timeoffset;
    }

    /**
     * Returns the value of field marketing_source_map
     *
     * @return string
     */
    public function getMarketingSourceMap(): string
    {
        return $this->marketing_source_map;
    }

    /**
     * Returns the value of field downgrade_reservation
     *
     * @return integer
     */
    public function getDowngradeReservation(): int
    {
        return $this->downgrade_reservation;
    }

    /**
     * Validations and business logic
     *
     * @return boolean
     */
    public function validation(): bool
    {
        $this->validate(
            new Email(
                [
                    'field'    => 'email',
                    'required' => true,
                ]
            )
        );

        if ((bool) $this->validationHasFailed() === true) {
            return false;
        }

        return true;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setSource("locations");
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Locations[]
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return Locations
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
