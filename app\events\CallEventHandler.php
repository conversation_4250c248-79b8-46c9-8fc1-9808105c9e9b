<?php
/**
 * Event handler for call create/update
 *
 * @category CallEventHandler
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\BaseEventHandler;
use CallPotential\CPCommon\EventHandler;
use CallPotential\CPCommon\ClientFactory;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Util;
use Twilio\Rest\Client as TwilioClient;
use \GuzzleHttp\Client as GuzzleClient;
use Phalcon\DI;

/**
 * Event handler for call create/update
 *
 * @category CallEventHandler
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */
class CallEventHandler extends BaseEventHandler implements EventHandler
{
    use LoggerTrait;

    /**
     * clientFactory class instance
     *
     * @var ClientFactory::class
    */
    protected $clientFactory;

    /**
     * Handle send notification to location email address
     *
     * @param array $eventData array
     *
     * @return bool
     */
    public function handle(array $eventData) : bool
    {
        try {
            $this->auditMessage(
                'Executing Call event handler' . ': ' .
                var_export($eventData, true),
                __METHOD__ . ":" . __LINE__
            );

            if (!empty($eventData['updateMysql'])) {
                $this->updateCallDetailData($eventData);

                if (!empty($eventData['updateReservationFee'])) {
                    $this->updateLogIdReservationFee($eventData);
                }
                if (!empty($eventData['updateCustomerPayment'])) {
                    $this->updateLogIdCustomerPayment($eventData);
                }
            }

            if (!empty($eventData['convIntelApi'])) {
                $this->callConvIntelApi($eventData);
            }

            if (!empty($eventData['saveVideoRecording'])) {
                $this->saveVideoRecordinginS3($eventData);
            }

            return true;
        } catch (Exception $e) {
            $this->errorMessage("Error in calling conv intel api: " . $e->getMessage());

            return false;
        }
    }

    /**
     * Update mysql data wiht calldetail endpoint
     *
     * @param array $eventData array
     *
     * @return bool
     */
    public function updateCallDetailData(array $eventData) : bool
    {
        try {
            $mysqlUpdateData = $eventData['newData'];

            if (!empty($mysqlUpdateData['lead_id'])) {
                $mysqlUpdateData['fk_lead_id'] = $mysqlUpdateData['lead_id'];
            }

            if (!empty($mysqlUpdateData['es_tenant_id'])) {
                $mysqlUpdateData['tenant_id'] = Util::getMysqlTenantIdfromES(
                    $mysqlUpdateData['es_tenant_id']
                );
                $mysqlUpdateData['customer_id'] = $mysqlUpdateData['tenant_id'];
            }

            $mysqlUpdateData['update_source'] = 'callController';
            $data = array_merge($eventData['oldData'], $eventData['newData']);

            if (!empty($mysqlUpdateData['call_duration']) && empty($mysqlUpdateData['duration'])) {
                $mysqlUpdateData['duration'] = $mysqlUpdateData['call_duration'];
            }

            $this->auditMessage(
                'event data for calldetail update ' . ': ' .
                var_export($data, true),
                __METHOD__ . ":" . __LINE__
            );

            $callClient = ClientFactory::getCallClient();
            $callClient->updateCallDetails($data['twilio_id'], $mysqlUpdateData);

            return true;
        } catch (GuzzleHttp\Exception\ClientException $e) {
            // Possibly call might not exist on mysql
            $this->errorMessage(
                'update error for ' . var_export($eventData, true) .
                ': ' . var_export($e->getMessage(), true),
                __METHOD__ . ":" . __LINE__
            );

            return false;
        }
    }

    /**
     * Call conversation_intelligence api
     *
     * @param array $eventData array
     *
     * @return bool
     */
    public function callConvIntelApi(array $eventData) : bool
    {
        if (! (empty($eventData['oldData']['recording_url'])
            && ! empty($eventData['newData']['recording_url']))) {
            return false;
        }

        $data = array_merge($eventData['oldData'], $eventData['newData']);
        $isTwilioRecording = (strpos($data['recording_url'], 'api.twilio.com') !== false);

        if (! $isTwilioRecording) {
            return false;
        }

        /** Do not process CI for video calls */
        if (substr($data['twilio_id'], 0, 2) === 'RM') {
            return false;
        }

        $coreClient = ClientFactory::getCoreClient();
        $featureToggle = (array) $coreClient->getFeatureToggle(
            $data['account_id'],
            'conversation_intelligence'
        );

        // Check if conversation_intelligence enabled for account
        if (! ($featureToggle
            && $featureToggle['items']
            && $featureToggle['items'][0]
            && $featureToggle['items'][0]->feature_value
            && (int) $featureToggle['items'][0]->feature_value === 1)) {
            return false;
        }

        $this->updateDynamoData(
            [
                'twilio_id'     => $data['twilio_id'],
                'location_id'   => (int) $data['location_id'],
            ],
            [
                'recording_url' => '',
            ]
        );

        $this->updateMysqlData($data['twilio_id'], ['recording_url' => '']);

        // Adding delay as when we are seeing fetching recording details immediately
        // returns duration -1 as recording is still in process and not available
        // for CI to download
        sleep(10);

        // Get recording details
        $mccClient = ClientFactory::getMccClient();
        $twilio_account = $mccClient->getTwilioAccount($data['account_id']);

        $twilio = new TwilioClient($twilio_account->account_sid, $twilio_account->authtoken);
        $recordingData = $twilio->recordings($data['recording_sid'])->fetch();

        // Wait 5 seconds to let recording get processed at twilio if status is not completed
        if ($recordingData->status !== 'completed') {
            sleep(30);
            $recordingData = $twilio->recordings($data['recording_sid'])->fetch();
        }

        if ($recordingData->source) {
            switch ($recordingData->source) {
                case 'Conference':
                    $callSource = 'call_center';
                    break;
                case 'RecordVerb':
                    $callSource = 'voicemail';
                    break;
                case 'DialVerb':
                    $callSource = 'location';

                    if (strpos($data['recording_url'], 'external_connect') !== false) {
                        $callSource = 'external_connect';
                    }
                    break;
            }
        }

        if (strpos($data['call_type'], 'outbound') !== false) {
            $callSource = 'outbound';
        }

        $payloadData = [
            'RecordingUrl'      => $data['recording_url'],
            'CallSid'           => $data['twilio_id'],
            'RecordingSid'      => $data['recording_sid'],
            'AccountSid'        => $recordingData->accountSid,
            'UserId'            => $data['account_id'],
            'RecordingDuration' => $recordingData->duration,
            'RecordingChannels' => $recordingData->channels,
            'RecordingSource'   => $recordingData->source,
            'RecordingStatus'   => $recordingData->status,
            'SourceEnv'         => $eventData['env'],
            'CallbackEndpoint'  => $eventData['callUrl'],
            'CoreEndpoint'      => $eventData['coreUrl'],
            'LocationEndpoint'  => $eventData['locUrl'],
            'RecordingBucket'   => $eventData['recordingBucket'],
            'LocationId'        => $data['location_id'],
            'CallType'          => $data['call_type'],
            'CallerNumber'      => $data['call_number'],
            'CallerName'        => $data['customer_name'] ?? '',
            'LeadId'            => $data['es_lead_id'] ?? '',
            'Version'           => 'v2',
            'CallSource'        => $callSource,
        ];

        try {
            $guzzleClient = new GuzzleClient();
            $response = $guzzleClient->post($eventData['convIntelApi'], [
                'headers' => [
                    'Authorization' => $eventData['staticToken'],
                    'Content-Type'  => 'application/json',
                ],
                'json' => $payloadData,
            ]);

            $this->debugMessage("conv intel api response: " . $response->getBody());
        } catch (Exception $e) {
            $this->errorMessage("Unable to call conv intel api: " . $e->getMessage());

            $recSidErrorStirng = '/Recordings/' . $data['recording_sid'] . '.json was not found';

            // Remove recording sid as recording does not exist at twilio
            if (strpos($e->getMessage(), $recSidErrorStirng) !== false) {
                $this->updateDynamoData(
                    [
                        'twilio_id'     => $data['twilio_id'],
                        'location_id'   => (int) $data['location_id'],
                    ],
                    [
                        'recording_sid' => '',
                    ],
                );

                $this->updateMysqlData($data['twilio_id'], ['recording_sid' => '']);
            }

            return false;
        }

        return true;
    }

    /**
     * Return success debug message
     *
     * @return string
     */
    public function getSuccessMessage() : string
    {
        return "Success";
    }

    /**
     * Return failure error message
     *
     * @return string
     */
    public function getFailureMessage() : string
    {
        return "Failure";
    }

    /**
     * Update dynamo db data directly
     *
     * @param array $key  Dynamo data key
     * @param array $data Update data
     * @return string
     */
    public function updateDynamoData(array $key, array $data) : bool
    {
        $callDynamoObj = new Call();
        $callDynamoObj->updateById($key, $data);

        return true;
    }

    /**
     * Update mysql db data directly
     *
     * @param string $twilioId Twilio id
     * @param array  $data     Update data
     * @return string
     */
    public function updateMysqlData(string $twilioId, array $data) : bool
    {
        $callMysqlData = CallDetail::findFirst(
            [
                "conditions" => "twilio_id = '" . $twilioId . "'",
            ]
        );
        if ($callMysqlData) {
            foreach ($data as $k => $v) {
                $callMysqlData->$k = $v;
            }
            $callMysqlData->save();
        }

        return true;
    }

     /**
     * Save video call recording in S3 bucket
     * @param array $eventData
     * @return boolean
     */
    private function saveVideoRecordinginS3($eventData)
    {
        $data = $eventData['newData'];
        $oldData = $eventData['oldData'];
        $roomSid = $oldData['twilio_id'];
        $data['call_type'] = $data['call_type'] ?? $oldData['call_type'];

        try {
            if (empty($data['recording_sid'])) {
                return false;
            }
            $di = Di::getDefault();
            $config = $di->getShared('config');

            /** Get Twilio Client */
            $mccClient = ClientFactory::getMccClient();
            $twilio_account = $mccClient->getTwilioAccount($oldData['account_id']);
            $twilio = new TwilioClient($twilio_account->account_sid, $twilio_account->authtoken);

            $paymentCallTypes = $config->application->paymentCallTypes->toArray();
            /** Do not save video recordings for payment calls */
            /** Video recording files for payment calls will be deleted in lambda callback */
            if (in_array($data['call_type'], $paymentCallTypes)) {
                $this->infoMessage(
                    'video recording deleted for payment calls ' . $roomSid
                );

                return false;
            }

            /** create S3 client */
            $key = $config->aws->key;
            $secret = $config->aws->secret;
            $region = $config->aws->region;
            $bucket = $config->aws->buckets->recordings;

            $s3Client = new Aws\S3\S3Client(
                [
                    "credentials" => [
                        "key" => $key,
                        "secret" => $secret,
                    ],
                    "region" => $region,
                    "version" => "latest",
                ]
            );

            /**fetch composition file for composition id (recording_sid) */
            $uri = "https://video.twilio.com/v1/Compositions/" . $data['recording_sid']
            . "/Media?Ttl=3600";
            $response = $twilio->request("GET", $uri);

            if (!isset($response->getContent()["redirect_to"])) {
                $this->infoMessage(
                    'video recording file not found ' . $data['recording_sid']
                );

                return false;
            }

            $mediaLocation = $response->getContent()["redirect_to"];

            $client = new GuzzleHttp\Client();
            $response = $client->get($mediaLocation);

            $bucketKey = $twilio_account->account_sid . '/Recordings/' .
            $data['recording_sid'].'.webm';

            /** upload file in S3 bucket*/
            $result = $s3Client->putObject(array(
                'Bucket' => $bucket,
                'Key'    => $bucketKey,
                'Body'   => $response->getBody(),
                'ACL'    => 'public-read',
                'Content-Type' => 'video/webm',
            ));

            /**Update dynamo db call_logs table for recording_url*/
            $this->updateDynamoData(
                [
                    'twilio_id'     => $oldData['twilio_id'],
                    'location_id'   => (int) $oldData['location_id'],
                ],
                [
                    'recording_url' => $result['ObjectURL'],
                ]
            );

            /**Update mysql call_logs table for recording_url*/
            $callClient = ClientFactory::getCallClient();
            $callClient->updateCallDetails(
                $oldData['twilio_id'],
                ['recording_url' => $result['ObjectURL']]
            );


            /** Delete composition and video recordings from twilio once file uploaded to s3 upload */
            $this->deleteTwilioVideoRecordings($twilio, $data, $roomSid);

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error in saving video recording in S3 and
                deleting twilio recording for composition sid ' .
                $data['recording_sid'] . ' Message ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            return false;
        }
    }


    /**
     * Delete twilio video recordings
     * @param TwilioClient $twilio
     * @param array  $data
     * @param string $roomSid
     * @return boolean
     */
    private function deleteTwilioVideoRecordings(TwilioClient $twilio, $data, $roomSid)
    {
        try {

            /** Delete composition from twilio*/
            $twilio->video->v1->compositions($data['recording_sid'])
            ->delete();

            /**Delete recording medias from twilio for specific room */
            $recordings = $twilio->video->v1->rooms($roomSid)
                        ->recordings
                        ->read([], 1000);

            foreach ($recordings as $record) {
                $twilio->video->v1->recordings($record->sid)
                ->delete();
            }

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error while deleting video recording' .
                $data['recording_sid'] . ' Message ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            return false;
        }
    }

    /**
     * Update log_id in ReservationFee
     * @param array<mixed> $eventData array of eventdata values
     * @return boolean true/false based of success updating reservatoin fees
     */
    private function updateLogIdReservationFee(array $eventData): bool
    {
        try {
            $di = Di::getDefault();
            $this->clientFactory =  $di->getShared('clientFactory');

            $intClient = $this->clientFactory->getIntClient();

            $dbLogId = isset($eventData['oldData']['db_log_id'])
                    ? $eventData['oldData']['db_log_id']
                    : $eventData['newData']['db_log_id'];


            $reservationPaymentId = isset($eventData['newData']['reservation_payment_id'])
            ? $eventData['newData']['reservation_payment_id']
            : $eventData['oldData']['reservation_payment_id'];

            $reservationFeeUpdateData = [
                'log_id' => $dbLogId,
            ];

            $intClient->updateReservationFee(
                $reservationPaymentId,
                $reservationFeeUpdateData
            );

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error while updating log_id in reservationfee ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            throw new \Exception("Error while updating log_id in reservationfee");
        }
    }

    /**
     * Update log_id in CustomerPayment
     * @param array<mixed> $eventData array of eventdata values
     * @return boolean true/false based of success updating customer payments
     */
    private function updateLogIdCustomerPayment(array $eventData): bool
    {
        try {
            $di = Di::getDefault();
            $this->clientFactory =  $di->getShared('clientFactory');

            $intClient = $this->clientFactory->getIntClient();

            $dbLogId = isset($eventData['oldData']['db_log_id'])
            ? $eventData['oldData']['db_log_id']
            : $eventData['newData']['db_log_id'];

            $customerPaymentId = isset($eventData['newData']['customer_payment_id'])
            ? $eventData['newData']['customer_payment_id']
            : $eventData['oldData']['customer_payment_id'];

            $customerPaymentUpdateData = [
                'log_id' => $dbLogId,
            ];
            $intClient->updateCustomerPayment(
                $customerPaymentId,
                $customerPaymentUpdateData
            );

            return true;
        } catch (\Exception $e) {
            $this->errorMessage(
                'Error while updating log_id in customer Payment ' . $e->getMessage(),
                __METHOD__ . ':' . __LINE__
            );

            throw new \Exception("Error while updating log_id in customer Payment");
        }
    }
}
