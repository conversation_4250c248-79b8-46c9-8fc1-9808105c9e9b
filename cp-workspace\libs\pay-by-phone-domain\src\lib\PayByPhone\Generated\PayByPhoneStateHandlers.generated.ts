
/**
 * This file was auto-generated by the state-diagram-code generator.
 * DO NOT MODIFY THIS FILE DIRECTLY.
 * To make changes, modify the source diagram file and re-run the generator.
 * Diagram source: /libs/pay-by-phone-domain/src/.diagrams/PayByPhone.state.mmd
 * Generated at: 2025-02-26T20:51:04.895Z
 */

import { PayByPhoneStateHandlerMap } from "../PayByPhone.model";
import { PayByPhoneState } from "./PayByPhoneState.generated";
import { LocalePrompt } from '../States/LocalePrompt';
import { LocaleConfirm } from '../States/LocaleConfirm';
import { CollectionsPrompt } from '../States/CollectionsPrompt';
import { CollectionsConfirm } from '../States/CollectionsConfirm';
import { SayAmountDue } from '../States/SayAmountDue';
import { TransferToAgent } from '../States/TransferToAgent';
import { CustomerOptOut } from '../States/CustomerOptOut';
import { DisconnectCall } from '../States/DisconnectCall';
import { InputPhoneGather } from '../States/InputPhoneGather';
import { InputPhoneValidate } from '../States/InputPhoneValidate';
import { CustomerByPhoneSearch } from '../States/CustomerByPhoneSearch';
import { UnitsToPayPrompt } from '../States/UnitsToPayPrompt';
import { ConfirmCustomerInfo } from '../States/ConfirmCustomerInfo';
import { UnitsToPayPrepayMonthsPrompt } from '../States/UnitsToPayPrepayMonthsPrompt';
import { UnitsToPayPrepayMonthsSelection } from '../States/UnitsToPayPrepayMonthsSelection';
import { GetSavedCards } from '../States/GetSavedCards';
import { PayMethodPrompt } from '../States/PayMethodPrompt';
import { PayMethodCreditCardPrompt } from '../States/PayMethodCreditCardPrompt';
import { UnitsToPaySelection } from '../States/UnitsToPaySelection';
import { UnitsToPayPrepayMonthsConfirm } from '../States/UnitsToPayPrepayMonthsConfirm';
import { PayMethodSelection } from '../States/PayMethodSelection';
import { ExistingPayMethodVerify } from '../States/ExistingPayMethodVerify';
import { PayMethodSecurityCodePrompt } from '../States/PayMethodSecurityCodePrompt';
import { PayMethodCreditCardValidate } from '../States/PayMethodCreditCardValidate';
import { PayMethodCreditCardConfirm } from '../States/PayMethodCreditCardConfirm';
import { PayMethodExpirationPrompt } from '../States/PayMethodExpirationPrompt';
import { PayMethodExpirationValidate } from '../States/PayMethodExpirationValidate';
import { PayMethodExpirationConfirm } from '../States/PayMethodExpirationConfirm';
import { PayMethodSecurityCodeValidate } from '../States/PayMethodSecurityCodeValidate';
import { PayMethodSecurityCodeConfirm } from '../States/PayMethodSecurityCodeConfirm';
import { PayMethodPostalCodePrompt } from '../States/PayMethodPostalCodePrompt';
import { FinalPayAmountPrompt } from '../States/FinalPayAmountPrompt';
import { PayMethodPostalCodeValidate } from '../States/PayMethodPostalCodeValidate';
import { PayMethodPostalCodeConfirm } from '../States/PayMethodPostalCodeConfirm';
import { FinalPayAmountConfirm } from '../States/FinalPayAmountConfirm';
import { FinalPayAmountSubmitted } from '../States/FinalPayAmountSubmitted';
import { PaymentSuccessful } from '../States/PaymentSuccessful';
import { PaymentFailure } from '../States/PaymentFailure';


export function buildPayByPhoneStateHandlerMap(): PayByPhoneStateHandlerMap {
  return {
    [PayByPhoneState.LocalePrompt]: LocalePrompt,
    [PayByPhoneState.LocaleConfirm]: LocaleConfirm,
    [PayByPhoneState.CollectionsPrompt]: CollectionsPrompt,
    [PayByPhoneState.CollectionsConfirm]: CollectionsConfirm,
    [PayByPhoneState.SayAmountDue]: SayAmountDue,
    [PayByPhoneState.TransferToAgent]: TransferToAgent,
    [PayByPhoneState.CustomerOptOut]: CustomerOptOut,
    [PayByPhoneState.DisconnectCall]: DisconnectCall,
    [PayByPhoneState.InputPhoneGather]: InputPhoneGather,
    [PayByPhoneState.InputPhoneValidate]: InputPhoneValidate,
    [PayByPhoneState.CustomerByPhoneSearch]: CustomerByPhoneSearch,
    [PayByPhoneState.UnitsToPayPrompt]: UnitsToPayPrompt,
    [PayByPhoneState.ConfirmCustomerInfo]: ConfirmCustomerInfo,
    [PayByPhoneState.UnitsToPayPrepayMonthsPrompt]: UnitsToPayPrepayMonthsPrompt,
    [PayByPhoneState.UnitsToPayPrepayMonthsSelection]: UnitsToPayPrepayMonthsSelection,
    [PayByPhoneState.GetSavedCards]: GetSavedCards,
    [PayByPhoneState.PayMethodPrompt]: PayMethodPrompt,
    [PayByPhoneState.PayMethodCreditCardPrompt]: PayMethodCreditCardPrompt,
    [PayByPhoneState.UnitsToPaySelection]: UnitsToPaySelection,
    [PayByPhoneState.UnitsToPayPrepayMonthsConfirm]: UnitsToPayPrepayMonthsConfirm,
    [PayByPhoneState.PayMethodSelection]: PayMethodSelection,
    [PayByPhoneState.ExistingPayMethodVerify]: ExistingPayMethodVerify,
    [PayByPhoneState.PayMethodSecurityCodePrompt]: PayMethodSecurityCodePrompt,
    [PayByPhoneState.PayMethodCreditCardValidate]: PayMethodCreditCardValidate,
    [PayByPhoneState.PayMethodCreditCardConfirm]: PayMethodCreditCardConfirm,
    [PayByPhoneState.PayMethodExpirationPrompt]: PayMethodExpirationPrompt,
    [PayByPhoneState.PayMethodExpirationValidate]: PayMethodExpirationValidate,
    [PayByPhoneState.PayMethodExpirationConfirm]: PayMethodExpirationConfirm,
    [PayByPhoneState.PayMethodSecurityCodeValidate]: PayMethodSecurityCodeValidate,
    [PayByPhoneState.PayMethodSecurityCodeConfirm]: PayMethodSecurityCodeConfirm,
    [PayByPhoneState.PayMethodPostalCodePrompt]: PayMethodPostalCodePrompt,
    [PayByPhoneState.FinalPayAmountPrompt]: FinalPayAmountPrompt,
    [PayByPhoneState.PayMethodPostalCodeValidate]: PayMethodPostalCodeValidate,
    [PayByPhoneState.PayMethodPostalCodeConfirm]: PayMethodPostalCodeConfirm,
    [PayByPhoneState.FinalPayAmountConfirm]: FinalPayAmountConfirm,
    [PayByPhoneState.FinalPayAmountSubmitted]: FinalPayAmountSubmitted,
    [PayByPhoneState.PaymentSuccessful]: PaymentSuccessful,
    [PayByPhoneState.PaymentFailure]: PaymentFailure,
  };
}
