const { INTERNAL_SIP_DOMAINS, formatPhoneNumber, formatSipAddress, isInternalSipAddress, formatCommunicationIdentifiers, defineAgentConferenceParams } = require('../../libraries/shared/twilio-conference-helper');

describe('Twilio Conference Helper', () => {
  describe('formatPhoneNumber', () => {
    it('should format a 10-digit number correctly', () => {
      expect(formatPhoneNumber('1234567890')).toBe('+11234567890');
    });
  
    it('should handle non-digit characters', () => {
      expect(formatPhoneNumber('(*************')).toBe('+11234567890');
    });
  });

  describe('formatSipAddress', () => {
    it('should prefix SIP address correctly', () => {
      const sipAddress = '1234567890';
      const result = formatSipAddress(sipAddress);
      expect(result).toBe('sip:1234567890');
    });

    it('should not prefix SIP address if a prefix already exists', () => {
      const sipAddress = 'sip:1234567890';
      const result = formatSipAddress(sipAddress);
      expect(result).toBe(sipAddress);
    });
  });

  describe('isInternalSipAddress', () => {
    it('should correctly identify all internal SIP addresses', () => {
      for(let i = 0; i < INTERNAL_SIP_DOMAINS.length; i++) {
        const sipAddress = `1234567890@${INTERNAL_SIP_DOMAINS[i]}`;
        const result = isInternalSipAddress(sipAddress);
        expect(result).toBe(true);
      }
    });

    it('should correctly identify external SIP address', () => {
      const sipAddress = '<EMAIL>';
      const result = isInternalSipAddress(sipAddress);
      expect(result).toBe(false);
    });

    it('should handle an invalid SIP address', () => {
      const sipAddress = '1234567890';
      const result = isInternalSipAddress(sipAddress);
      expect(result).toBe(false);
    });

    it('should handle an invalid SIP address', () => {
      const sipAddress = 1234567890;
      const result = isInternalSipAddress(sipAddress);
      expect(result).toBe(false);
    });
  });

  describe('formatCommunicationIdentifiers', () => {
    it('should handle phone type callee', () => {
      const workerAttributes = { contact_uri: '1234567890' };
      const taskAttributes = { tracking_no: '9876543210', caller_id: '<EMAIL>' };

      const result = formatCommunicationIdentifiers(workerAttributes, taskAttributes);

      expect(result).toEqual({
        callee: {
          to: '+11234567890',
          type: 'phone'
        },
        caller: {
          from: '9876543210',
          type: 'phone'
        }
      });
    });

    it('should handle internal SIP type callee', () => {
      const calleeSip = `sip:1234567890@${INTERNAL_SIP_DOMAINS[0]}`;
      const workerAttributes = { contact_uri: calleeSip };
      const taskAttributes = { tracking_no: '9876543210', caller_id: '<EMAIL>' };

      const result = formatCommunicationIdentifiers(workerAttributes, taskAttributes);
  
      expect(result).toEqual({
        callee: {
          to: calleeSip,
          type: 'sip-internal'
        },
        caller: {
          from: '<EMAIL>',
          type: 'sip'
        }
      });
    });
  
    it('should handle external SIP type callee', () => {
      const workerAttributes = { contact_uri: 'sip:<EMAIL>' };
      const taskAttributes = { tracking_no: '9876543210', caller_id: '<EMAIL>' };
  
      const result = formatCommunicationIdentifiers(workerAttributes, taskAttributes);
  
      expect(result).toEqual({
        callee: {
          to: 'sip:<EMAIL>',
          type: 'sip-external'
        },
        caller: {
          from: '<EMAIL>',
          type: 'sip'
        }
      });
    });

    it('should handle invalid SIP address', () => {
      const workerAttributes = { contact_uri: 'invalid@address' };
      const taskAttributes = { tracking_no: '9876543210', caller_id: '+' };

      const result = formatCommunicationIdentifiers(workerAttributes, taskAttributes);

      expect(result).toEqual({
        callee: {
          to: 'sip:invalid@address',
          type: 'sip-external'
        },
        caller: {
          from: '9876543210',
          type: 'phone'
        }
      });
    });
  });
});