import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Bugsnag from '@bugsnag/js';
import BugsnagPluginExpress from '@bugsnag/plugin-express';
import BugsnagPluginAwsLambda from '@bugsnag/plugin-aws-lambda';
import { RequestHandler, ErrorRequestHandler } from 'express';
import { <PERSON><PERSON> } from 'aws-lambda';

interface BugsnagExpressMiddleware {
  requestHandler: RequestHandler;
  errorHandler: ErrorRequestHandler;
}

@Injectable()
export class BugsnagService {
  private readonly logger = new Logger(BugsnagService.name);

  constructor(protected configService: ConfigService) {
    // Initialize Bugsnag
    Bugsnag.start({
      apiKey: this.configService.get<string>('BUGSNAG_API_KEY') || '',
      plugins: [BugsnagPluginExpress, BugsnagPluginAwsLambda],
      releaseStage: this.configService.get<string>('NODE_ENV') || 'development',
      appVersion: '1.0.0',
    });
  }

  /**
   * Get the Express middleware for request and error handling
   * @returns Middleware object with requestHandler and errorHandler, or undefined if the plugin isn’t available
   */
  getExpressMiddleware(): BugsnagExpressMiddleware | undefined {
    const expressPlugin = Bugsnag.getPlugin('express');
    if (!expressPlugin) {
      this.logger.error('Express plugin is not available');
      return undefined;
    }
    return expressPlugin;
  }

  /**
   * Get the Bugsnag handler for AWS Lambda
   */
  getLambdaHandler(): (handler: Handler) => Handler {
    const awsLambdaPlugin = Bugsnag.getPlugin('awsLambda');
    if (!awsLambdaPlugin) {
      this.logger.error('AWS Lambda plugin is not available');
      return (handler: Handler) => handler;
    }
    return awsLambdaPlugin.createHandler();
  }

  /**
   * Method to leave a breadcrumb in Bugsnag with message and optional metadata
   * @param {string} message
   * @param {any} metadata
   */
  leaveBreadcrumb(message: string, metadata?: any) {
    Bugsnag.leaveBreadcrumb(message, metadata);
  }

  /**
   * Method to capture an error and notify Bugsnag
   * @param {Error} error
   * @param {any} options
   */
  notify(error: Error, options?: any) {
    Bugsnag.notify(error, options);
  }

  /**
   * Method to set the user in Bugsnag reports
   * @param {string} id
   * @param {string} email
   * @param {string} name
   */
  setUser(id: string, email?: string, name?: string) {
    Bugsnag.setUser(id, email, name);
  }

  /**
   * Method to add custom metadata in Bugsnag reports
   * @param {string} section
   * @param {any} data
   */
  addMetadata(section: string, data: any) {
    Bugsnag.addMetadata(section, data);
  }

  /**
   * Method to clear custom metadata in Bugsnag reports
   * @param {string }section
   */
  clearMetadata(section: string) {
    Bugsnag.clearMetadata(section);
  }
}
