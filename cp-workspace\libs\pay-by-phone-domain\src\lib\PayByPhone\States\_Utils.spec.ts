import { PayByPhoneStateRetryHandler } from './_Utils';
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

describe('PayByPhoneStateRetryHandler', () => {
  const mockTwilioResponse = {
    sayInLocale: jest.fn()
  };
  const maxRetries = 3;
  const promptState = PayByPhoneState.CollectionsPrompt;
  const confirmState = PayByPhoneState.CollectionsConfirm;
  const maxRetryState = PayByPhoneState.TransferToAgent;
  const maxRetryMessageKey = 'pay-by-phone.max-retry';

  const originalMethod = jest.fn();
  originalMethod.mockResolvedValue({ nextState: PayByPhoneState.CollectionsConfirm });

  const target = {
    handleState: jest.fn()
  };

  const propertyKey = 'handleState';
  const descriptor = {
    value: originalMethod
  };

  it('should call the original method and not modify the retryCount if retries are not exceeded and the next state is a confirmState', async () => {
    const retryHandler = PayByPhoneStateRetryHandler(maxRetries, promptState, [confirmState], maxRetryState);
    retryHandler(target, propertyKey, descriptor);

    const context = {
      storage: {
        retryCount: 2,
        locale: 'en-US'
      },
      twilioResponse: mockTwilioResponse
    };

    await descriptor.value(context);
    expect(originalMethod).toHaveBeenCalledWith(context);
    expect(context.storage.retryCount).toBe(2);
    expect(mockTwilioResponse.sayInLocale).not.toHaveBeenCalled();
  });
  
  it('should transition to maxRetryState when retry limit is exceeded', async () => {
    const retryHandler = PayByPhoneStateRetryHandler(maxRetries, promptState, [confirmState], maxRetryState, maxRetryMessageKey);
    retryHandler(target, propertyKey, descriptor);

    const context = {
      storage: {
        retryCount: maxRetries,
        locale: 'en-US'
      },
      twilioResponse: mockTwilioResponse
    };

    const response = await descriptor.value(context);
    expect(response).toEqual({ nextState: maxRetryState });
  });

  it('should reset retryCount when transitioning to a non-cyclical state', async () => {
    originalMethod.mockReturnValue({ nextState: PayByPhoneState.PaymentFailure });
    const retryHandler = PayByPhoneStateRetryHandler(maxRetries, promptState, [confirmState], maxRetryState);
    retryHandler(target, propertyKey, descriptor);

    const context = {
      storage: {
        retryCount: 1,
        locale: 'en-US'
      },
      twilioResponse: mockTwilioResponse
    };

    await descriptor.value(context);
    expect(context.storage.retryCount).toBe(0);
  });
});
