import { PayMethodPrompt } from './PayMethodPrompt';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodPrompt', () => {
  let service: PayMethodPrompt;

  beforeEach(() => {
    service = new PayMethodPrompt();
  });

  it('should transition to PayMethodSelection', async () => {
    const context = {
      twilioResponse: {
        sayInLocale: jest.fn(),
        gatherWithLocaleSay: jest.fn(),
      },
      storage: {
        savedCards: [{ card_number: '1111123412341234' }],
        locale: 'en',
      },
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      finishOnKey: '*',
      numDigits: 4,
      timeout: 10
    }, [{
      messageId: "pay-by-phone.ask-saved-cc",
      locale: context.storage.locale
    }]);
    expect(result).toEqual({
      nextState: PayByPhoneState.PayMethodSelection,
    });
  });
});
