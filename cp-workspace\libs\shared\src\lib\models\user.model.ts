export type User = {
    user_id: number;
    plan_id: number;
    email: string;
    password: string;
    firstname: string;
    lastname: string;
    phone: string;
    question1: string;
    question2: string;
    question3: string;
    answer1: string;
    answer2: string;
    answer3: string;
    companyname: string;
    master: number;
    signupdate: string;
    lastagreement: string;
    agreement_ip: string;
    creditamount: number;
    active: number;
    lastlogin: string;
    sid: string;
    authtoken: string;
    howheard: string;
    confirmed: number;
    parent_id: number;
    reports: string;
    tips: string;
    dashboard: number;
    default_location: number;
    default_timezone: string;
    dm_report_period: string;
    dm_report_last_update: string;
    newlead_email_notify: number;
    stale_followup_notification: number;
    stale_followup_threshold: number;
    stale_followup_report_last_update: string;
    send_followup_notification: number;
    send_collection_notification: number;
    optin_mktg: number;
    is_test_account: number;
    dm_report_send_day: string;
    delayed_collection_email_notify: number;
    is_agent: number;
    agent_call_sid: string;
    agent_calling_sid: string;
    is_agent_call_connected: number;
    agent_status: string;
    inactive_reason: string;
    tc_access_token: string;
    is_logged_in: number;
    scheduled_logout: string;
    session_refresh: string;
    disable_unsubscribe_txt: number;
    custom_variables: string;
    show_admin_in_emp_dropdown: number;
    employee_restrict_percent: number;
    manager_restrict_percent: number;
    agent_restrict_percent: number;
    workspace_sid: string;
    worker_sid: string;
    enable_console_log: number;
    other_option_employee: number;
    other_option_required: number;
    consolidate_invoices: string;
    restrict_manager_edit_employee_grade: string;
    exclusion_list_type: string;
    transcribe_api: string;
    is_response_note_enabled: number;
    txt_consent: number;
    opt_in_txt: number;
    enable_duplicate_lead_email_notification: number;
    duplicate_lead_email_notification_setup: string;
    restrict_manager_edit_location_grade: string;
    kiosk_connection: number;
    sms_silence: number;
    disable_movein: number;
    impersonated_by: number;
    changed_by: number;
    v2_worker_sid: string;
    urgent_notes_email_notification: number;
    is_report_accessible: number;
    ringcentral_tokens: string;
    delete_lead_email_notify: number;
};

export type AdminUser = {
    id: number;
    name: string;
    role_name: string;
    email: string;
    role: number;
};