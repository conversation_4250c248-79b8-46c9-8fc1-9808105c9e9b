'use strict';

var main_config = require('../config/config');
var twilioQueueTableDynamo = main_config.dynamodb.twilioQueueTable;
const AWS = require("aws-sdk");
const cpapiClient = require('../libraries/cpapi-client');
const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: main_config.dynamodb.endpoint});

// replace with cpapi-mcc queue call
var CallRouteQueueModel = class CallRouteQueueModel {
  async list_dynamodb(account_id, queue_sid) {
    let params = {
      TableName: twilioQueueTableDynamo,
    };

    params['KeyConditionExpression'] = 'account_id = :account_id AND queue_sid = :queue_sid';
    params['ExpressionAttributeValues'] = { ':account_id': account_id, ':queue_sid': queue_sid };

    try {
      const data = await dynamo.query(params).promise();
      if (data.Items.length === 0) return {};

      return data.Items[0];
    } catch (e) {
      console.error('Error querying dynamo:', e, new Error().stack);
      return {};
    }
  }
};

var TrackingNumberModel = class TrackingNumberModel {
  async get_tracking_number(call_number, account_id) {
    const acctClient = new cpapiClient.acctClient(main_config.db.serviceTokens.readWrite);
    let trackingNumber = await acctClient.cache.getData(
      `trackingnumber?filterActive=true&filterCall_number=${call_number}&filterUser_id=${account_id}`
      );

    if (trackingNumber && trackingNumber.items && trackingNumber.items.length > 0) {
      trackingNumber = trackingNumber['items'][0].trackingnumber;
    }
    return trackingNumber;
  }
};

module.exports = {
  CallRouteQueueModel: CallRouteQueueModel,
  TrackingNumberModel: TrackingNumberModel
};
