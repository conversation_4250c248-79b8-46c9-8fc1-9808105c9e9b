"use strict";
var twilio = require('twilio');
var _ = require('lodash');
const redis = require('redis');
var main_config = require('../config/config');
const console_c = require('../config/logger').console;
var CallLogModel = require('../models/call-log');
const CommonMethod = require('../libraries/common-methods').CommonMethod;
const cpapiClient = require('../libraries/cpapi-client');
const util = new CommonMethod();

Array.prototype.forEachAsync = async function (fn) { 
    for (let t of this) { await fn(t) }
}

var queueManager = class QueueManager {

  constructor(config) {
    this.twilio = null;
    this.queue = null;
    this.twilio_response = null;
    this.override_twilio_response = null;
    this.timeout = null;
    this.log_id = null;
    this.log_detail = null;
    this.wait_music_url = null;
    this.notify_caller_status = null;
    this.current_step = null;
    this.is_header_sent = 0;
    this.is_output_set = 0;
    this.account_sid = '';
    this.account_sid = 0;
    this.auth_token = '';
    this.workspace_sid = '';
    this.workflow_sid = '';

    // $config is passed when used inside Call_route.php library
    // To use functions with other code, $config is not necessary
    if (config) {
      this.twilio = twilio(config['account_sid'], config['auth_token']);
      this.account_sid = config['account_sid'];
      this.auth_token = config['auth_token'];
      this.workspace_sid = config['workspace_sid'];
      this.workflow_sid = config['workflow_sid'];
      this.account_id = config['account_id'];

      // Create response object
      this.twilio_response = new twilio.twiml.VoiceResponse();
    }
  }

  /**
   * Sets the timeout and log_id for call
   * @access public
   * @param  int $log_id
   * @param  int $timeout
   * @return void
   */
  set_call_info(log_id, timeout) {
    this.log_id = log_id;
    this.timeout = timeout;
  }

  async mark_call_route_complete() {
    let log_info = await new CallLogModel().list_dynamodb(this.log_id)

    let call_conds = _.pick(log_info, ['twilio_id', 'location_id', 'log_id']);
    let call_info = {
      'is_route_complete': 1
    }

    let res_list = await new CallLogModel().update_dynamodb(call_conds, call_info);

    if (this.log_detail && this.log_detail.is_route_complete) {
      this.log_detail['is_route_complete'] = 1;
    }

    return res_list;
  }

  leave_call() {
    this.twilio_response.leave();
    var t_client_resp = this.twilio_response.toString();
    return t_client_resp;
  }

  /**
   * Sends twilio response to the caller in wait
   *
   * @access public
   * @param  void
   * @return void
   */
  wait_in_queue(post_input) {
    /*
      In order to capture the caller pressing 9, you must return a <Gather/> response 
      so Twilio listens.
    */

    const gather_action = `${main_config.call_url}twilio/process_next_step/${this.log_id}?wait_in_queue=1`;
    const gather = this.twilio_response.gather({'numDigits': 1, 'action': gather_action});

    console_c.log('PRESS_9_SUPPORT', gather_action, gather.toString());

    if (this.notify_caller_status) {
      gather.say('You are number ' + post_input['QueuePosition'] + ' in the queue. Please hold.');
    }

    if (this.wait_music_url) {
      gather.play(this.wait_music_url);
    }

    return true;
  }

  /**
   * Places a call into a queue
   *
   * @access public
   * @param  int $queue_id
   * @param  array $call_info
   * @return void
   */
  async queue_call(queueId, callInfo) {
    let actionUrl = `${main_config.call_url}twilio/process_next_step/${this.log_id}?`;
    actionUrl += encodeURIComponent(`leave_queue=1`);

    let waitUrl = `${main_config.call_url}twilio/process_next_step/${this.log_id}?`;
    waitUrl += encodeURIComponent(`wait_in_queue=1`);

    // Place incoming caller in location Queue
    const enqueueParams = {
      action: actionUrl,
      waitUrl,
    };

    this.is_output_set = 1;
    this.timeout = parseInt(this.timeout, 10) + 1;
    const cardPopInfo = await this.getCardPopInfo(callInfo.call_number, callInfo.location_id);

    // Set task attributes for wds task creation for this call
    const taskAttributes = {
     call_sid: callInfo.twilio_call_sid,
     type: 'call',
     direction: 'inbound',
     taskchannel: 'voice',
     caller_id: callInfo.call_number,
     caller_name: util.escapeTwilioChars(callInfo.caller_name),
     tracking_no: callInfo.call_name,
     ad_name: util.escapeTwilioChars(callInfo.ad_name),
     date_time: callInfo.datestamp,
     call_route_event: 'call_center',
     timeout: this.timeout,
     queue_id: queueId,
     log_id: this.log_id,
     location_id: callInfo.location_id,
     location_name: util.escapeTwilioChars(callInfo.location_name),
     customer_name: util.escapeTwilioChars(callInfo.customer_name),
     customer_type: callInfo.customer_type,
     card_type: callInfo.lead_card_type,
     user_id: callInfo.parent_user_id,
     config_step_uid: callInfo.config_step_uid,
     is_sip_call: callInfo.is_sip_call,
     queue_sid: queueId,
     tenant_id: cardPopInfo.tenantId,
     lead_id: cardPopInfo.leadId,
     disposition: cardPopInfo.disposition,
     first_name: util.escapeTwilioChars(cardPopInfo.first_name),
     last_name: util.escapeTwilioChars(cardPopInfo.last_name),
    };

    /*
     CPIC-1051
     Get SID for the workflow referred to as:
     - "InboundCallCenterVoice" (in Twilio)
     - "cc_inbound_workflow" (in CP Account record -- set inside of `getCardPopInfo()`)
     - "workflow" (in cardPopInfo object)
    */
    enqueueParams.workflowSid = cardPopInfo.workflow;

    // Queue name is not required for call center queueing.
    const enq = this.twilio_response.enqueue(enqueueParams, '');
    const priority = this.current_step.priority ? this.current_step.priority.value : 1;
    enq.task(
     {
       priority: `${priority}`, // twilio require this value as string
       timeout: this.timeout,
     },
     encodeURIComponent(JSON.stringify(taskAttributes))
    );

    Promise.resolve(false);
   }

  async getCardPopInfo(number, locationId) {
    let get_dialing_code = require('../routes/utils/twillio').get_dialing_code;
    let remove_dialing_code = require('../routes/utils/twillio').remove_dialing_code;
    let disposition = 'Unknown',
      tenantId = '',
      leadId = '',
      workflow = '',
      first_name = '',
      last_name = '';

    const cpapiClient = require('./cpapi-client');
    const mccClient = new cpapiClient.mccClient(main_config.db.serviceTokens.readWrite);
    const intClient = new cpapiClient.intClient(main_config.db.serviceTokens.readWrite);

    const twilioAccount = await mccClient.cache.getData(`twilio/account/${this.account_id}`);
    if (twilioAccount != {}) {
      workflow = twilioAccount.cc_inbound_workflow;
    }
    let dialCode = await get_dialing_code(locationId);

    number = remove_dialing_code(number, dialCode);
    const dispositionPayload = {
      "phone": number,
      "location_id": locationId
    }
    const dispositionData = await intClient.postData('disposition', dispositionPayload);

    if (dispositionData && dispositionData['items']) {
      let tenants = dispositionData['items'];
      if (tenants.length > 0) {
        let record = tenants[0];
        if (record.type == 'Delinquent Customer' || record.type == 'Current Customer' || record.type == 'Previous Customer') {
          tenantId = record._id;
        } else {
          leadId = record._id;
        }
        disposition = record.type;
        first_name = record.first_name;
        last_name = record.last_name;
      }
    }

    return {
      leadId: leadId,
      tenantId: tenantId,
      disposition: disposition,
      workflow: workflow,
      first_name: first_name,
      last_name: last_name
    };
  }

  async leave_queue(req_p_data) {
    /*
      CPAPI-2025
      This function was revised to adopt a clearer approach to writing
      asynchronous code inside of `async/await` functions.

      This helps make the code more legible and easier to follow the flow
      of execution.

      This function implicitly returns a Promise as a result of marking it
      as an `async` function.
    */
    console_c.log("# inside leave_queue ########");

    var queue_result = req_p_data['QueueResult'];
    console_c.log("# QUEUE_MANAGER - queue_result # ", queue_result);

    // 'redirected' in case of manual rest API leave request.
    if ('redirected' === queue_result || 'redirected-from-bridged' === queue_result) {
      console_c.log("MANUAL API LEAVE REQUEST");
      return true; // exit
    }

    try {

      switch (queue_result) {
        case 'leave':
          var redirect_url = main_config.call_url +
            'twilio/process_next_step/' + this.log_id +
            '?next_step';
          this.twilio_response.redirect(redirect_url);
          break;
        case 'hangup':
        case 'system-error':
        case 'error':
          // If call is not redirected, then in all other cases update call is_route_complete
          // since the call ends at this step.
          await this.mark_call_route_complete();
  
          if ('hangup' === queue_result) {
            this.twilio_response.hangup();
  
            // CCC-162 On customer hangup, agent keeps ringing and does not disconnect
            // We will disconnect the call by ending the confernece
            // Get task sid which is same as conference name 
            // end the conference so the agent call will also get disconnected
            try{
  
              const redisClient = redis.createClient({'url': `redis://${main_config.redis.host}`});
              await redisClient.connect();

              // Key being set from task_created event in workspace-event-handler
              let taskSid = await redisClient.get(`g5_${req_p_data.CallSid}`);
              const task = await this.twilio.taskrouter.workspaces(this.workspace_sid)
                .tasks(taskSid).fetch();

              if (task && task.sid) {   
                try{
                  let reservations = await this.twilio.taskrouter.workspaces(this.workspace_sid)
                    .tasks(task.sid)
                    .reservations
                    .list({limit: 20});

                  await reservations.forEachAsync(async (res) => {
                    let call_key = 'voipPhoneSid-' + res.sid;
                    const callSid = await redisClient.get(call_key);
                    if (callSid) {
                      await this.twilio.calls(callSid)
                        .update({status: 'completed'})
                        .catch((e) => {
                          console.error(e, new Error().stack);
                        });
                    }
                  });

                  const conferences = await this.twilio.conferences.list({ friendlyName: task.sid, limit: 1 });
                  if (conferences.length > 0) {
                    for (let i = 0; i < conferences.length; i++) {
                      const c = conferences[i];
                      console_c.log('Conference details', c);
                      try {
                        const conference = await this.twilio.conferences(c.sid).update({ status: 'completed' });
                        console_c.log('conference completed', conference.friendlyName)
                      } catch(e) {
                        console.log('Error in ending conference', e);
                      }
                    }
                  }
                }catch(e){
                  console.log('Error in fetching conference details', e);
                }
              }
              await redisClient.disconnect();

            }catch(e){
              console.log('Error in fetching task details', e);
            }
  
          }
  
          break;
  
        case 'bridging-in-process':
          // Update the next step after agent disconnects caller after
          // connecting with queued call.
          console_c.log("# bridging-in-process #", this.log_detail);
          await new CallLogModel().queue_save_next_step(this.log_detail);
          break;
  
        case 'bridged':
          await this.mark_call_route_complete();
          break;
  
        default:
          if (req_p_data['callback_request']) {
            req_p_data['callback_request'] = false;
            await this.cancel_task(req_p_data['self_call_info'], this);
            await this.schedule_callback(req_p_data['self_call_info'], this);
            let mp3_url = main_config.CP_CDN_URL + 'uploads/voice_broadcast/mp3/callback_play.mp3';
            this.twilio_response.play(mp3_url);
            this.twilio_response.hangup();
          }
      }

    }catch(err){

      console.log(err, new Error().stack);
      // And mark the call route process to be complete
      this.twilio_response.hangup();
      console_c.log("# call route complete #");
      await this.mark_call_route_complete();
      
    }

    return true;

  }

  async cancel_task(call_info, self) {

    try {

      const redisClient = redis.createClient({'url': `redis://${main_config.redis.host}`});
      await redisClient.connect();

      // Key being set from task_created event in workspace-event-handler
      let taskSid = await redisClient.get(`g5_${call_info['twilio_call_sid']}`);
      await redisClient.disconnect();

      let task = await self.twilio.taskrouter.workspaces(self.workspace_sid).tasks(taskSid).fetch();
      let tqs = '';

      try {
        task = await task.update({
          assignmentStatus: 'canceled',
          reason: 'Scheduled Callback'
        });

        self.taskAttributes = JSON.parse(task.attributes);
        self.taskQueueSid = task.taskQueueSid;
        tqs = task.taskQueueSid;

        let reservations = await self.twilio.taskrouter.workspaces(self.workspace_sid)
          .tasks(task.sid)
          .reservations
          .list({limit: 20});

        const redisClient = redis.createClient({'url': `redis://${main_config.redis.host}`});
        await redisClient.connect();

        await reservations.forEachAsync(async (res) => {
          let call_key = 'voipPhoneSid-' + res.sid;
          const callSid = await redisClient.get(call_key);
          if (callSid) {
            await self.twilio.calls(callSid)
              .update({status: 'completed'})
              .catch((e) => {
                console.error(e, new Error().stack);
              });
          }
        });
      } catch (e) {

        self.taskQueueSid = task.taskQueueSid;
        self.taskAttributes = JSON.parse(task.attributes);
        console.log(e, new Error().stack);
        tqs = '';

      }

      return tqs;

    } catch (e) {
      console.error(e, new Error().stack);
      return '';

    }

  }

  async schedule_callback(call_info, self) {
    try {
      const mccClient = new cpapiClient.mccClient(main_config.db.serviceTokens.readWrite);
      const ccAccount = await mccClient.cache.getData(`callcenter/${call_info['account_id']}`);
      let workflow = (ccAccount.v2_callback_workflow_sid) ? ccAccount.v2_callback_workflow_sid : self.workflow_sid;
      let queue = (ccAccount.callback_queue_sid && ccAccount.callback_queue_sid.startsWith('WQ')) 
        ? ccAccount.callback_queue_sid : self.taskQueueSid;
      let expire = (ccAccount.callback_expiration_time) ? ccAccount.callback_expiration_time : 36300;
      let callRouteConfigId = 0;

      let call_name = call_info.call_name.substring(1);
      const locClient = new cpapiClient.locClient(main_config.db.serviceTokens.readWrite);
      const acctClient = new cpapiClient.acctClient(main_config.db.serviceTokens.readWrite);

      let trackingNumber = await acctClient.cache.getData(`trackingnumber?filterActive=true&filterCall_number=${call_name}&filterLocation_id=${call_info.location_id}`);
      trackingNumber = trackingNumber['items'][0].trackingnumber;

      if (!trackingNumber.call_route_config_id) {
        const locData = await locClient.getLocationConfiguration(call_info.location_id);
        callRouteConfigId = locData.call_route_config_id;
      } else {
        callRouteConfigId = trackingNumber.call_route_config_id;
      }
      const callRouteConfig = await acctClient.cache.getData(`callroute/${callRouteConfigId}`);

      const callback_start_time = ccAccount.callback_start_time != undefined ? ccAccount.callback_start_time : "00:00";
      const callback_end_time = ccAccount.callback_end_time != undefined ? ccAccount.callback_end_time : "23:59";

      let task_attributes = {
        type: 'callback',
        taskchannel: 'Callback',
        original_call_sid: call_info['twilio_call_sid'],
        caller_id: call_info['call_number'],
        tracking_no: call_info['call_name'],
        ad_name: util.escapeTwilioChars(call_info['ad_name']),
        date_time: call_info['datestamp'],
        call_route_id: callRouteConfigId['config_id'],
        call_route_event: 'call_center',
        timeout: expire,
        config_step_uid: 'callback',
        queue_id: queue,
        queue_sid: queue,
        log_id: self.log_id,
        location_id: call_info['location_id'],
        location_name: util.escapeTwilioChars(call_info['location_name']),
        customer_name: util.escapeTwilioChars(call_info['customer_name']),
        customer_type: call_info['customer_type'],
        user_id: call_info['parent_user_id'],
        cascade_time: callRouteConfig['cascade_time'],
        disposition: self.taskAttributes.disposition,
        tenant_id: self.taskAttributes.tenant_id,
        lead_id: self.taskAttributes.lead_id,
        first_name: util.escapeTwilioChars(self.taskAttributes.first_name),
        last_name: util.escapeTwilioChars(self.taskAttributes.last_name),
        is_sip_call: self.taskAttributes.is_sip_call,
        caller_name: util.escapeTwilioChars(self.taskAttributes.caller_name),
        callback_start_time: callback_start_time.replace(":", ""),
        callback_end_time: callback_end_time.replace(":", ""),
      };

      await self.twilio.taskrouter
        .workspaces(self.workspace_sid)
        .tasks
        .create({ attributes: JSON.stringify(task_attributes), workflowSid: workflow, taskChannel: "callback", timeout: expire })
        .then(task => {
          console.log('Callback task created', task.sid);
        })
        .catch((e) => {
          console.log(e, new Error().stack);
        });

    } catch (error) {

      console_c.log("# encode POST error #", error);

    }

  }

}


module.exports = {
  'QueueManager': queueManager
}
