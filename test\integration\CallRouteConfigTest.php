<?php
namespace Test;

class CallRouteConfigTest extends IntegrationCase
{
    public function testCallRouteConfigGeyByIdAction()
    {
        $this->validateSwaggerPath(
            'call',
            'CallrouteGetById',
            '/callroute/{id}',
            'get'
        );

        $response = $this->runGET('/callroute/testcase-callroute-121-135', true);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/callroute/testcase-callroute-121-135 GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'CallrouteGetById', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'CallrouteGetById', [200, 404, 500]);   
    }

    public function testCallRouteConfigListWithoutPagination()
    {
        $this->validateSwaggerPath(
            'call',
            'ListCallroutes',
            '/callroute',
            'get'
        );

        $params = $this->getListParams(1);
        $response = $this->runGET('/callroute', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/callroute GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'ListCallroutes', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'ListCallroutes', [200,401,403,500]);
    }

    public function testCallRouteConfigListWithPagination()
    {
        $this->validateSwaggerPath(
            'call',
            'ListCallroutes',
            '/callroute',
            'get'
        );

        $params = $this->getListParams(2);
        $response = $this->runGET('/callroute', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/callroute GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'ListCallroutes', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'ListCallroutes', [200,401,403,500]);
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }

    /**
     * @param integer $case
     * @return array
     */
    private function getListParams($case = 0) :array
    {
        switch ($case) {
            case 1:
                return [
                    'page' => 'false',
                    'filterIs_active' => 'true'
                ];
                break;
            case 2:
                return [
                    'page' => 1,
                    'perPage' => 20,
                    'filterIs_active' => 'true'
                ];
                break;
            default:
                return [];
                break;
        }
    }
}
