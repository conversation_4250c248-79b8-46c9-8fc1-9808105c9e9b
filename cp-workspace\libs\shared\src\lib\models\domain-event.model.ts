import { Injectable } from "@nestjs/common";

export interface DomainEvent {
  source: string;
  detailType: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  detail: any;
}

export interface IPublishDomainEvents {
  publish(domainEvent: DomainEvent): Promise<DomainEvent>;
}

export interface DomainEventServiceOptions {
  enabled?: boolean;
}

@Injectable()
export class IPublishDomainEventsProvider {
  getClient(): IPublishDomainEvents {
    throw new Error('IPublishDomainEventsProvider.getClient() not implemented. Did you add `IPublishDomainEventsProvider` to your Nest Module `providers`?');
  }
}
