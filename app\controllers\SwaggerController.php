<?php
/**
 * Swagger library related methods
 *
 * @category SwaggerController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * Swagger
 *
 * @SWG\Definition(definition="ErrorResponse",type="object",required={"messages"},
 * @SWG\Property(property="status",type="string"),
 * @SWG\Property(property="messages",type="array",@SWG\Items(type="string")),
 * @SWG\Property(property="file",type="string"),
 * @SWG\Property(property="line",type="integer",format="int32"),
 * @SWG\Property(property="trace",type="array",@SWG\Items(type="string"))
 * )
 */

 /**
  * Swagger
  *
  * @SWG\Definition(definition="PagingData",type="object",
  * @SWG\Property(property="currentPage",type="integer",format="int64"),
  * @SWG\Property(property="nextPage",type="integer",format="int64"),
  * @SWG\Property(property="prevPage",type="integer",format="int64"),
  * @SWG\Property(property="totalItems",type="integer",format="int64"),
  * @SWG\Property(property="totalPages",type="integer",format="int64"),
  * @SWG\Property(property="firstPage",type="integer",format="int64"),
  * @SWG\Property(property="lastPage",type="integer",format="int64"),
  *
  * )
  */

 use CallPotential\CPCommon\HttpStatusCode;

/**
 * Swagger library related methods
 *
 * @category SwaggerController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class SwaggerController extends \Phalcon\Mvc\Controller
{
    use \CallPotential\CPCommon\Controllers\ResponseTrait;
    /**
     * App path
     *
     * @var string
     */
    private $appPath;
    /**
     * Json path
     *
     * @var string
     */
    private $jsonPath;

    /**
     * Constructor to initialize data
     *
     * @return void
     */
    public function initialize()
    {
        $this->appPath = $this->getDI()->get('config')->application->appDir;
        $this->jsonPath = BASE_PATH.'/public/swagger-ui/temp.json';
    }

    /**
     * Intermediate function to prepare data for create action
     *
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function createItem(array $data, $parentId = null)
    {
        unset($data, $parentId);
        // TODO: Implement createItem() method.
    }

    /**
     * Intermediate function to prepare data for update action
     *
     * @param mixed $id       primary key value of record
     * @param array $data     array of POST body / query params in key value pair
     * @param mixed $parentId parent id in case of ES parent child relation null otherwise
     *
     * @return mixed
     */
    public function updateItem($id, array $data, $parentId = null)
    {
        unset($id, $data, $parentId);
        // TODO: Implement updateItem() method.
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function listAction()
    {
        try {
            if (!file_exists($this->jsonPath)) {
                $swagger = $this->doSwaggerScan();
                header('Content-Type: application/json');
                echo $swagger;
            } else {
                header('Content-Type: application/json');
                readfile($this->jsonPath);
                exit();
            }
        } catch (Exception $e) {
            return $this->sendErrorResponse($e);
        }
    }

    /**
     * Method Http accept: PUT (update)
     *
     * @return Phalcon\Http\Response
     */
    public function saveAction(): Phalcon\Http\Response
    {
        $swagger = $this->doSwaggerScan();
        file_put_contents($this->jsonPath, $swagger);
        header('Content-Type: application/json');
        echo $swagger;
        exit();
    }
    /**
     * SearchAction
     *
     * @return Phalcon\Http\Response
     */
    public function searchAction(): Phalcon\Http\Response
    {
        return $this->sendNotImplemented();
    }

    /**
     * Method Http accept: DELETE
     *
     * @return Phalcon\Http\Response
     */
    public function deleteAction(): Phalcon\Http\Response
    {
        if (file_exists($this->jsonPath)) {
            unlink($this->jsonPath);
        }

        $this->response->setStatusCode(
            HttpStatusCode::HTTP_NO_CONTENT,
            HttpStatusCode::getMessageForCode(HttpStatusCode::HTTP_NO_CONTENT)
        );
        $this->response->setJsonContent(array('status' => "OK"));
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        unset($data);

        return false;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Scan controllers from the path
     *
     * @return mixed
     */
    private function doSwaggerScan()
    {
        // ['exclude'=>['config','library','migrations']]
        return \Swagger\scan($this->appPath);
    }
}
