import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, ExitPayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class PaymentSuccessful extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<ExitPayByPhoneStateHandlerResponse> {
    const { storage } = context;

    return { 
      nextState: PayByPhoneState.DisconnectCall,
      redirectUrl: storage.paymentSuccessRedirectUrl,
    };
  }
}
