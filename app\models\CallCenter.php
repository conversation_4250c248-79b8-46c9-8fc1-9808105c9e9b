<?php
/**
 * CallCenter model
 *
 * @category CallCenter
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\JsonModelTrait;
use CallPotential\CPCommon\LoggerTrait;
use CallPotential\CPCommon\Models\DynamoModel;

/**
 * CallCenter model
 *
 * @category CallCenter
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="CallCenter")
 */
class CallCenter extends \Phalcon\Di\Injectable
{
    use DynamoModel, LoggerTrait;

    /**
     * Primary key Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $id;

    /**
     * User Id
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $user_id;

    /**
     * Inactivity time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $inactivity_time;

    /**
     * Timedout queue SID
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $timedout_queue_sid;

    /**
     * Default queue SID
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $default_queue_sid;

    /**
     * On cascade inactive
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $on_cascade_inactive;

    /**
     * On reject inactive
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $on_reject_inactive;

    /**
     * Cooldown period
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $cooldown_period;

    /**
     * Twilio function URL
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $twilio_function_url;

    /**
     * Chat reservation timeout
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $chat_reservation_timeout;

    /**
     * Chat queue SID
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $chat_queue_sid;

    /**
     * Sms reservation timeout
     *
     * @var int
     *
     * @SWG\Property()
     * @Column(type="integer", length=20, nullable=true)
     */
    protected $sms_reservation_timeout;

    /**
     * Sms queue SID
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $sms_queue_sid;

    /**
     * Created date time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $created;

    /**
     * Update date time
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $updated;

    /**
     * Properties
     *
     * @var string
     *
     * @SWG\Property()
     * @Column(type="string", length=255, nullable=true)
     */
    protected $properties;

    /**
     * Method to set the value of field id
     *
     * @param integer $id value to set
     *
     * @return $this
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $user_id value to set
     *
     * @return $this
     */
    public function setUserId(int $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field inactivity_time
     *
     * @param string $inactivity_time value to set
     *
     * @return $this
     */
    public function setInactivityTime(string $inactivity_time)
    {
        $this->inactivity_time = $inactivity_time;

        return $this;
    }

    /**
     * Method to set the value of field timedout_queue_sid
     *
     * @param string $timedout_queue_sid value to set
     *
     * @return $this
     */
    public function setTimedoutQueueSid(string $timedout_queue_sid)
    {
        $this->timedout_queue_sid = $timedout_queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field default_queue_sid value to set
     *
     * @param string $default_queue_sid value to set
     *
     * @return $this
     */
    public function setDefaultQueueSid(string $default_queue_sid)
    {
        $this->default_queue_sid = $default_queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field on_cascade_inactive
     *
     * @param integer $on_cascade_inactive value to set
     *
     * @return $this
     */
    public function setOnCascadeInactive(int $on_cascade_inactive)
    {
        $this->on_cascade_inactive = $on_cascade_inactive;

        return $this;
    }

    /**
     * Method to set the value of field on_reject_inactive
     *
     * @param integer $on_reject_inactive value to set
     *
     * @return $this
     */
    public function setOnRejectInactive(int $on_reject_inactive)
    {
        $this->on_reject_inactive = $on_reject_inactive;

        return $this;
    }

    /**
     * Method to set the value of field cooldown_period
     *
     * @param integer $cooldown_period value to set
     *
     * @return $this
     */
    public function setCooldownPeriod(int $cooldown_period)
    {
        $this->cooldown_period = $cooldown_period;

        return $this;
    }

    /**
     * Method to set the value of field twilio_function_url
     *
     * @param string $twilio_function_url value to set
     *
     * @return $this
     */
    public function setTwilioFunctionUrl(string $twilio_function_url)
    {
        $this->twilio_function_url = $twilio_function_url;

        return $this;
    }

    /**
     * Method to set the value of field chat_reservation_timeout
     *
     * @param integer $chat_reservation_timeout value to set
     *
     * @return $this
     */
    public function setChatReservationTimeout(int $chat_reservation_timeout)
    {
        $this->chat_reservation_timeout = $chat_reservation_timeout;

        return $this;
    }

    /**
     * Method to set the value of field chat_queue_sid
     *
     * @param string $chat_queue_sid value to set
     *
     * @return $this
     */
    public function setChatQueueSid(string $chat_queue_sid)
    {
        $this->chat_queue_sid = $chat_queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field sms_reservation_timeout
     *
     * @param integer $sms_reservation_timeout value to set
     *
     * @return $this
     */
    public function setSmsReservationTimeout(int $sms_reservation_timeout)
    {
        $this->sms_reservation_timeout = $sms_reservation_timeout;

        return $this;
    }

    /**
     * Method to set the value of field sms_queue_sid
     *
     * @param string $sms_queue_sid value to set
     *
     * @return $this
     */
    public function setSmsQueueSid(string $sms_queue_sid)
    {
        $this->sms_queue_sid = $sms_queue_sid;

        return $this;
    }

    /**
     * Method to set the value of field created
     *
     * @param string $created value to set
     *
     * @return $this
     */
    public function setCreated(string $created)
    {
        $this->created = $created;

        return $this;
    }

    /**
     * Method to set the value of field updated
     *
     * @param string $updated value to set
     *
     * @return $this
     */
    public function setUpdated(string $updated)
    {
        $this->updated = $updated;

        return $this;
    }

    /**
     * Method to set the value of field properties
     *
     * @param string $properties value to set
     *
     * @return $this
     */
    public function setProperties(string $properties)
    {
        $this->properties = $properties;

        return $this;
    }

    /**
     * Returns the value of field id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns the value of field user_id
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field inactivity_time
     *
     * @return string
     */
    public function getInactivityTime(): string
    {
        return $this->inactivity_time;
    }

    /**
     * Returns the value of field timedout_queue_sid
     *
     * @return string
     */
    public function getTimedoutQueueSid(): string
    {
        return $this->timedout_queue_sid;
    }

    /**
     * Returns the value of field default_queue_sid
     *
     * @return string
     */
    public function getDefaultQueueSid(): string
    {
        return $this->default_queue_sid;
    }

    /**
     * Returns the value of field on_cascade_inactive
     *
     * @return integer
     */
    public function getOnCascadeInactive(): int
    {
        return $this->on_cascade_inactive;
    }

    /**
     * Returns the value of field on_reject_inactive
     *
     * @return integer
     */
    public function getOnRejectInactive(): int
    {
        return $this->on_reject_inactive;
    }

    /**
     * Returns the value of field cooldown_period
     *
     * @return integer
     */
    public function getCooldownPeriod(): int
    {
        return $this->cooldown_period;
    }

    /**
     * Returns the value of field twilio_function_url
     *
     * @return string
     */
    public function getTwilioFunctionUrl(): string
    {
        return $this->twilio_function_url;
    }

    /**
     * Returns the value of field chat_reservation_timeout
     *
     * @return integer
     */
    public function getChatReservationTimeout(): int
    {
        return $this->chat_reservation_timeout;
    }

    /**
     * Returns the value of field chat_queue_sid
     *
     * @return string
     */
    public function getChatQueueSid(): string
    {
        return $this->chat_queue_sid;
    }

    /**
     * Returns the value of field sms_reservation_timeout
     *
     * @return integer
     */
    public function getSmsReservationTimeout(): int
    {
        return $this->sms_reservation_timeout;
    }

    /**
     * Returns the value of field sms_queue_sid
     *
     * @return string
     */
    public function getSmsQueueSid(): string
    {
        return $this->sms_queue_sid;
    }

    /**
     * Returns the value of field created
     *
     * @return string
     */
    public function getCreated(): string
    {
        return $this->created;
    }

    /**
     * Returns the value of field updated
     *
     * @return string
     */
    public function getUpdated(): string
    {
        return $this->updated;
    }

    /**
     * Returns the value of field properties
     *
     * @return string
     */
    public function getProperties(): string
    {
        return $this->properties;
    }

    /**
     * Constructor to initialize data
     */
    public function __construct()
    {
        $this->table = 'callcenter_setup';
    }

    /**
     * Clear model object values
     *
     * @return void
     */
    public function clear()
    {
        $this->user_id = null;
        $this->inactivity_time = null;
        $this->timedout_queue_sid = null;
        $this->default_queue_sid = null;
        $this->on_cascade_inactive = null;
        $this->on_reject_inactive = null;
        $this->cooldown_period = null;
        $this->properties = null;
        $this->twilio_function_url = null;
    }
}
