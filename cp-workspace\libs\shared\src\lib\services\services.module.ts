import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AccountService } from './account.service';
import { CoreService } from './core.service';
import { DataDogService } from './datadog.service';
import { IntegrationService } from './integration.service';
import { LocationService } from './location.service';
import { HttpModule } from '@nestjs/axios';
import { IoRedisModule, IoRedisModuleOptions } from './redis-ioredis.module';
import { RedisService } from './redis.service';
import { DomainEventsIntegrationModule, DomainEventsModuleOptions } from '../integrations/event-bridge/domain-events.integration.module';
import { DomainEventsService } from './domain-events.service';
import { DomainEventsIntegrationService } from '../integrations/event-bridge/domain-events.integration.service';
import { AsyncWorkersIntegrationModule, AsyncWorkersModuleOptions } from '../integrations/lambda/async-workers.integration.module';
import { AsyncWorkersService } from './async-workers.service';
import { AsyncWorkersIntegrationService } from '../integrations/lambda/async-workers.integration.service';
import { BugsnagService } from './bugsnag.service';

export interface SharedServicesModuleOptions {
  redisOptions: IoRedisModuleOptions;
  domainEventsOptions: DomainEventsModuleOptions;
  asyncWorkersOptions: AsyncWorkersModuleOptions
}

@Module({})
export class SharedServicesModule {
  static forRoot(options: SharedServicesModuleOptions) {
    return {
      module: SharedServicesModule,
      imports: [
        HttpModule,
        ConfigModule,
        IoRedisModule.forRoot(options.redisOptions as IoRedisModuleOptions),
        DomainEventsIntegrationModule.forRoot(options.domainEventsOptions as DomainEventsModuleOptions),
        AsyncWorkersIntegrationModule.forRoot(options.asyncWorkersOptions as AsyncWorkersModuleOptions),
      ],
      providers: [
        AccountService,
        CoreService,
        DataDogService,
        IntegrationService,
        LocationService,
        RedisService,
        DomainEventsService,
        DomainEventsIntegrationService,
        AsyncWorkersService,
        AsyncWorkersIntegrationService,
        BugsnagService,
      ],
      exports: [
        AccountService,
        CoreService,
        DataDogService,
        IntegrationService,
        LocationService,
        RedisService,
        IoRedisModule,
        DomainEventsIntegrationModule,
        DomainEventsIntegrationService,
        DomainEventsService,
        AsyncWorkersIntegrationModule,
        AsyncWorkersIntegrationService,
        AsyncWorkersService,
        BugsnagService,
      ],
    };
  }
}