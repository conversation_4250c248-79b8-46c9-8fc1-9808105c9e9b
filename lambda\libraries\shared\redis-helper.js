async function saveAgentOutboundCallSidToRedis(
  redisClient,
  reservationSid,
  callSid,
  agentOutboundCallSid
) {
  let callKey = "voipPhoneSid-" + reservationSid;
  let callKeyValue = agentOutboundCallSid;

  let redisTtl = callSid === agentOutboundCallSid ? 60 : 300;

  await redisClient.set(callKey, callKeyValue, "EX", redisTtl);
}

/**
 * For agent calls to external SIP addresses, we need to save information
 * about the task reservation so that we will be able accept the call.
 * 
 * We will receive data from the external SIP system at webhook.  This webhook
 * expects to receive the following information from the external SIP system:
 * - phone number of the caller
 *   - NOTE: in the case of a SIP call, this will be equal to the original caller
 *     to our tracking number (aka, the inbound caller_id)
 * - phone number of the callee.  For Fonality, this will be a 4 digit extension.
 * 
 * We will use this information to construct a Redis key in the format:
 *  agentSIP-<caller_id>-<callee-extension>
 * 
 * The way that we compute the callee extension is:
 * - split the SIP address on the @ symbol
 * - take the last 4 digits of the left part of the split
 */
async function saveSipExternalInfoToRedis(
  redisClient,
  accountSid,
  taskSid,
  reservationSid,
  customerCallSid,
  agentCallSid,
  caller,
  callee
) {

  let callerId = caller.from;
  let calleeExtension = callee.to.split("@")[0].slice(-4);

  let callKey = `agentSIP-${callerId}-${calleeExtension}`;
  let callKeyValue = JSON.stringify({
    accountSid,
    taskSid, 
    reservationSid, 
    customerCallSid,
    agentCallSid,
    caller,
    callee
  });
  console.debug("saveSipExternalInfoToRedis", callKey, callKeyValue);

  await redisClient.set(callKey, callKeyValue, "EX", 60);
}

module.exports = {
  saveAgentOutboundCallSidToRedis,
  saveSipExternalInfoToRedis
};