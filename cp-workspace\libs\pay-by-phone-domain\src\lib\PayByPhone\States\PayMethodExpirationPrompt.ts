import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodExpirationPrompt extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Compose TwiML to ask for credit card expiration date
     * 2. Compose TwiML that will gather the CC expiration date
     * 3. Transition to PayMethodExpirationValidate
     */

    const { twilioResponse, storage } = context;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      timeout: 10,
      finishOnKey: '*',
    }, [{
      messageId: 'pay-by-phone.enter-expiry',
      locale: storage.locale
    }]);


    return { nextState: PayByPhoneState.PayMethodExpirationValidate };
  }
}
