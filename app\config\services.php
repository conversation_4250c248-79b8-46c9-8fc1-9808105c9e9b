<?php

use Bugsnag\Client as BugsnagClient;
use CallPotential\CPCommon\ClientFactory;
use Phalcon\Url as UrlResolver;
use Phalcon\Cache\AdapterFactory;
use Phalcon\Storage\SerializerFactory;
use Phalcon\Mvc\Model\Metadata\Memory as MetaDataAdapter;
use Phalcon\Mvc\Model\MetaData\Strategy\Annotations as StrategyAnnotations;
use Phalcon\Session\Adapter\Files as SessionAdapter;
use Pbx\PbxAccount;

/**
 * Shared configuration service
 */
$di->setShared('config', function () {
    return include APP_PATH . "/config/config.php";
});

/**
 * Registering a router
 */
$di->set('router', function () {
    return include 'routes.php';
});

/**
 * The URL component is used to generate all kind of urls in the application
 */
$di->setShared('url', function () {
    $config = $this->getConfig();

    $url = new UrlResolver();
    $url->setBaseUri($config->application->baseUri);

    return $url;
});


$di->set('view', function () {
    $view = new \Phalcon\Mvc\View();
    $view->disable();

    return $view;
});

/**
 * Database connection is created based in the parameters defined in the configuration file
 */
$di->setShared('db', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host'     => $config->database->host,
        'username' => $config->database->username,
        'password' => $config->database->password,
        'dbname'   => $config->database->dbname,
        'charset'  => $config->database->charset,
        'persistent' => true,
        "options"    => [\PDO::ATTR_PERSISTENT => 1],
    ]);

    return $connection;
});

/**
 * Database replica (read only) connection is created based in the
 * parameters defined in the configuration file
 */
$di->setShared('dbReplica', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host'     => $config->replica->host,
        'username' => $config->replica->username,
        'password' => $config->replica->password,
        'dbname'   => $config->replica->dbname,
        'charset'  => $config->replica->charset,
        'persistent' => true,
        "options"    => [\PDO::ATTR_PERSISTENT => 1],
    ]);

    return $connection;
});

/**
 * Legacy database (read only) connection is created based in the parameters
 *  defined in the configuration file
 */
$di->setShared('dbLegacy', function () {
    $config = $this->getConfig();

    $class = 'Phalcon\Db\Adapter\Pdo\\' . $config->database->adapter;
    $connection = new $class([
        'host'     => $config->legacy->host,
        'username' => $config->legacy->username,
        'password' => $config->legacy->password,
        'dbname'   => $config->legacy->dbname,
        'charset'  => $config->legacy->charset,
        'persistent' => true,
        "options"    => [\PDO::ATTR_PERSISTENT => 1],
    ]);

    return $connection;
});

/**
 * Database connection is created based in the parameters defined in the configuration file
 */
$di->setShared('elasticsearch', function () {
    $config = $this->getConfig();
    $hosts = array();
    foreach ($config->elasticsearch->hosts as $host) {
        $hosts[] = (string) $host;
    }
    $connectionPool = '\Elasticsearch\ConnectionPool\StaticNoPingConnectionPool';
    //$connectionPool = '\Elasticsearch\ConnectionPool\SimpleConnectionPool';
    $client = \Elasticsearch\ClientBuilder::create()
        ->setHosts($hosts)
        ->setConnectionPool($connectionPool)
        ->build();

    return $client;
});

$di->setShared("dynamoDB", function () {
    $config = $this->getConfig();
    $dynamoConfig = [
        'version' => $config->dynamodb['version'],
        'region' => $config->dynamodb['region'],
        'endpoint' => $config->dynamodb['endpoint'],
        'credentials' => [
            'key' => $config->dynamodb['key'],
            'secret' => $config->dynamodb['secret'],
        ],
    ];

    $sdk = new \Aws\Sdk($dynamoConfig);

    return $sdk->createDynamoDb();
});

/**
 * If the configuration specify the use of metadata adapter use it or use memory otherwise
 */
$di->setShared('modelsMetadata', function () {
    $metadata = new MetaDataAdapter();

    $metadata->setStrategy(
        new StrategyAnnotations()
    );

    return $metadata;
});

/**
 * Start the session the first time some component request the session service
 */
$di->setShared('session', function () {
    $session = new SessionAdapter();
    $session->start();

    return $session;
});

$di->setShared('pbxAccount', function () {
    $config = $this->getConfig();
    $pbx = new PbxAccount($config->pbxUrl);

    return $pbx;
});

$di->setShared(
    'statecodes',
    function () {
        return include APP_PATH . "/config/statecodes.php";
    }
);

// Memcache for badge counter
$di->setShared(
    'badgeCache',
    function () {
        $config = $this->getConfig();
        $serializerFactory = new SerializerFactory();
        $adapterFactory = new AdapterFactory($serializerFactory);

        $options = [
            'defaultSerializer' => 'Json',
            'lifetime'          => 7200,
            'servers'           => [
                0 => [
                    'host'   => $config->application->memcache->memcacheHost,
                    'port'   => $config->application->memcache->memcachePort,
                ],
            ],
        ];

        $adapter = $adapterFactory->newInstance('libmemcached', $options);

        return new Phalcon\Cache($adapter);
    }
);

/**
 * shared ClientFactory class
 */
$di->setShared(
    'clientFactory',
    function () {
        return new ClientFactory();
    }
);

$di->setShared(
    BugsnagClient::class,
    function () {
        $config = $this->getConfig();
        $bugsnag = BugsnagClient::make($config->bugsnag['apiKey']);
        Bugsnag\Handler::register($bugsnag);
        
        return $bugsnag;
    }
);
