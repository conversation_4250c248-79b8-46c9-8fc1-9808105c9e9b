import { PayMethodSelection } from './PayMethodSelection';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodSelection', () => {
  let service: PayMethodSelection;

  beforeEach(() => {
    service = new PayMethodSelection();
  });

  it('should transition to ExistingPayMethodVerify if customer wishes to use existing pay method', async () => {
    const context = {
      request: {
        Digits: '1234',
        CallSid: '123',
      },
      twilioResponse: {},
      storage: {},
    } as any;

    const result = await service.handler(context);

    expect(result).toEqual({
      nextState: PayByPhoneState.ExistingPayMethodVerify,
    });
  });

  it('should transition to PayMethodCreditCardPrompt if customer wishes to use new CC', async () => {
    const context = {
      request: {
        Digits: '0',
      },
      twilioResponse: {sayInLocale: jest.fn()},
      storage: {},
    } as any;

    const result = await service.handler(context);

    expect(result).toEqual({
      nextState: PayByPhoneState.PayMethodCreditCardPrompt,
    });
  });

  it('should re-prompt for input if customer input is invalid', async () => {
    const context = {
      request: {
        Digits: '1',
      },
      twilioResponse: {
        sayInLocale: jest.fn(),
      },
      storage: {
        locale: 'en',
      },
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({
      messageId: 'pay-by-phone.invalid-input',
      locale: context.storage.locale
    });
    expect(result).toEqual({
      nextState: PayByPhoneState.PayMethodPrompt,
    });
  });
});
