<?php
/**
 * MiscController
 *
 * @category MiscController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\HttpStatusCode;

/**
 * MiscController
 *
 * @category MiscController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class MiscController extends \Phalcon\Mvc\Controller
{
    /**
     * OptionsAction
     *
     * @return Phalcon\Http\Response
     */
    public function optionsAction(): Phalcon\Http\Response
    {
        $this->response->setStatusCode(
            HttpStatusCode::HTTP_OK,
            HttpStatusCode::getMessageForCode(HttpStatusCode::HTTP_OK)
        );

        return $this->response;
    }
}
