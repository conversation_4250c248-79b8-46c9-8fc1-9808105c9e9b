import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import * as CardValidator from 'card-validator';
import { PaymentToken } from '@cp-workspace/shared';
@Injectable()
export class PayMethodExpirationValidate extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Read the CC expiration date input
     * 2. If expiration date is invalid
     * 2a. Compose TwiML to instruct customer to try again
     * 2b. Transition to PayMethodExpirationPrompt
     * 3. If expiration date is valid
     * 3a. Compose TwiML to ask for confirmation
     * 3b. Compose TwiML to gather the input
     * 3c. Transition to PayMethodExpirationConfirm
     */

    const { request, twilioResponse, storage } = context;
    const coreClient = this.services.coreService;
    const enteredExpiration = request.Digits;

    if (!enteredExpiration || !CardValidator.expirationDate(enteredExpiration).isValid) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.PayMethodExpirationPrompt };
    }

    const requestToken: PaymentToken = {
      expiration: enteredExpiration,
    };

    storage.paymentToken!.expiration = (await coreClient.encodePaymentToken(requestToken)).expiration;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      numDigits: 1,
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.expiration-confirm',
      locale: storage.locale,
      i18nOptions: { args: [{ enteredDate: enteredExpiration.split('').join(' ') }] }
    }]);

    return { nextState: PayByPhoneState.PayMethodExpirationConfirm };
  }
}
