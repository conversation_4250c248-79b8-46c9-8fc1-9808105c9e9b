/**
 * After updating serverless-express from v2 to v4
 * it broke path mapping with express js for our api gateway path mapping
 * So creating custom request/response mapper for serverss-express
 * It is modification of default livrary mapping 
 * https://github.com/vendia/serverless-express/blob/mainline/src/event-sources/utils.js
 * 
 * Only change is 'path = event.path' in  getPathWithQueryStringParams
 *  
 */

const url = require('url')

function getPathWithQueryStringParams ({
  event,
  query = event.multiValueQueryStringParameters,
  path = event.path,
  // NOTE: Strip base path for custom domains
  stripBasePath = '',
  replaceRegex = new RegExp(`^${stripBasePath}`)
}) {
  console.debug('path', path);
  return url.format({
    pathname: path.replace(replaceRegex, ''),
    query
  })
}

function getCommaDelimitedHeaders ({ headersMap, separator = ',', lowerCaseKey = false }) {
  const commaDelimitedHeaders = {}

  Object.entries(headersMap)
    .forEach(([headerKey, headerValue]) => {
      const newKey = lowerCaseKey ? headerKey.toLowerCase() : headerKey
      if (Array.isArray(headerValue)) {
        commaDelimitedHeaders[newKey] = headerValue.join(separator)
      } else {
        commaDelimitedHeaders[newKey] = headerValue
      }
    })

  return commaDelimitedHeaders
}

function getEventBody ({
  event,
  body = event.body,
  isBase64Encoded = event.isBase64Encoded
}) {
  return Buffer.from(body, isBase64Encoded ? 'base64' : 'utf8')
}

function getMultiValueHeaders ({ headers }) {
  const multiValueHeaders = {}

  Object.entries(headers).forEach(([headerKey, headerValue]) => {
    const headerArray = Array.isArray(headerValue) ? headerValue.map(String) : [String(headerValue)]

    multiValueHeaders[headerKey.toLowerCase()] = headerArray
  })

  return multiValueHeaders
}

function requestMapper ({ event }) {
  let headers = {}

  let method = event.httpMethod
  let path = getPathWithQueryStringParams({ event })
  if (event.multiValueHeaders) {
    headers = getCommaDelimitedHeaders({ headersMap: event.multiValueHeaders, lowerCaseKey: true })
  } else if (event.headers) {
    headers = event.headers
  }

  let body

  if (event.body) {
    body = getEventBody({ event })
    const { isBase64Encoded } = event
    headers['content-length'] = Buffer.byteLength(body, isBase64Encoded ? 'base64' : 'utf8')
  }

  const remoteAddress = (event && event.requestContext && event.requestContext.identity && event.requestContext.identity.sourceIp) || ''

  return {
    method,
    headers,
    body,
    remoteAddress,
    path
  }
}


function responseMapper ({
  statusCode,
  body,
  headers,
  isBase64Encoded
}) {

  const multiValueHeaders = getMultiValueHeaders({ headers })
  const transferEncodingHeader = multiValueHeaders['transfer-encoding']

  // chunked transfer not currently supported by API Gateway
  if (transferEncodingHeader && transferEncodingHeader.includes('chunked')) {
    multiValueHeaders['transfer-encoding'] = transferEncodingHeader.filter(headerValue => headerValue !== 'chunked')
  }

  return {
    statusCode,
    body,
    multiValueHeaders,
    isBase64Encoded
  }
}


module.exports = {
  requestMapper,
  responseMapper,
};
