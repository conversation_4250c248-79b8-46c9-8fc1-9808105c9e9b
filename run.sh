#!/usr/bin/env bash

export CP_CORE_URL='__SERVICE_CORE_URL__'
export CP_TEST_USER='<EMAIL>'
export CP_TEST_PASSWORD='testing123'
#export CP_TEST_TOKEN='__STATIC_RW_TOKEN__'
export DB_HOST='__DBHOST__'
export DB_USER='__DBUSER__'
export DB_PASS='__DBPASS__'
export DB_NAME='__DBNAME__'

export startDate='2018-08-01'
export endDate='2018-08-20'
export callNumber='+12568057965'
export employeeId=1
export callType="inbound"

php app/cli.php TestCall setUp

vendor/bin/codecept build

vendor/bin/codecept run integration --xml --coverage --coverage-xml --coverage-html

php app/cli.php TestCall tearDown

mkdir test/_output/coverage/lambda/
cd lambda
npm run test:integration
cp Reports/junit-call-lambda.xml ../test/_output/.
# Talk to Sachin, file is missing
# either remove line, or fix name
#cp coverage/clover.xml ../test/_output/.
cp -r coverage/. ../test/_output/coverage/lambda/.
cd ..

vendor/bin/phpunit --configuration phpunit_unit.xml --coverage-html ~/Reports/cp-int/unit
