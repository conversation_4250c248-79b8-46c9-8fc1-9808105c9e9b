import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { CollectionsConfirm } from './CollectionsConfirm';
import { Locale } from '@cp-workspace/shared';

describe('CollectionsConfirm', () => {
  let collectionsConfirm: CollectionsConfirm;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CollectionsConfirm],
    }).compile();

    collectionsConfirm = module.get<CollectionsConfirm>(CollectionsConfirm);

    collectionsConfirm.services = {
      integrationService: {
        getLedgerData: jest.fn().mockImplementation((tenantId) => Promise.resolve({
          items: [
            {tenant_id_es: tenantId}
          ]
        })),
        getTenantById: jest.fn().mockImplementation((tenantId) => Promise.resolve({
          _id: tenantId,
          name: 'Test Tenant',
          phone: '**********',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'Tenant'
        }))
      }

    } as any;
    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.CollectionsConfirm,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request:
      {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should return next state as CollectionsPrompt when no response from customer', async () => {
      context.request.Digits = undefined;

      const response: PayByPhoneStateHandlerResponse = await collectionsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.CollectionsPrompt);
    });

    it('should return next state as CollectionsPrompt when invalid response from customer', async () => {
      context.request.Digits = '8';

      const response: PayByPhoneStateHandlerResponse = await collectionsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.CollectionsPrompt);
    });

    describe('when make payment response is selected and pay by phone is allowed', () => {

      it('should return next state as TransferToAgent when tenant is not found', async () => {
        context.request.Digits = '1';
        context.storage.tenantId = '426';
  
        const response: PayByPhoneStateHandlerResponse = await collectionsConfirm.handler(context);
  
        expect(response.nextState).toBe(PayByPhoneState.SayAmountDue);
      });
  
      it('should return next state as SayAmountDue when tenant is found', async () => {
        context.request.Digits = '1';
        context.storage.tenantId = '213';
  
        const response: PayByPhoneStateHandlerResponse = await collectionsConfirm.handler(context);
  
        expect(response.nextState).toBe(PayByPhoneState.SayAmountDue);
      });

    });

    it('should return next state as TransferToAgent when transfer to agent response is selected', async () => {
      context.request.Digits = '2';

      const response: PayByPhoneStateHandlerResponse = await collectionsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.TransferToAgent);
    });

    it('should return next state as CustomerOptOut when opt out response is selected', async () => {
      context.request.Digits = '9';

      const response: PayByPhoneStateHandlerResponse = await collectionsConfirm.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.CustomerOptOut);
    });
  });
});
