import { OverrideByFactoryOptions, Test, TestingModule } from '@nestjs/testing';
import { I18nTranslationService } from './I18nTranslation.service';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PayByPhoneServicesModule, PayByPhoneServicesModuleOptions } from './PayByPhoneServices.module';
import { AsyncWorkersClientType, DomainEventsClientType, IoRedisClientType, LocationService, SharedServicesModuleOptions } from '@cp-workspace/shared';


export class PayByPhoneTest {

  static async createTestingModule(providers: any[] = []) {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        PayByPhoneServicesModule.forRoot({
          sharedServicesOptions: {
            redisOptions: {
              clientType: IoRedisClientType.IN_MEMORY,
            },
            domainEventsOptions: {
              clientType: DomainEventsClientType.IN_MEMORY,
              options: { enabled: true },
            },
            asyncWorkersOptions: {
              clientType: AsyncWorkersClientType.IN_MEMORY,
              options: { enabled: true },
            },
          } as SharedServicesModuleOptions
        } as PayByPhoneServicesModuleOptions),
      ],
      providers
    })
    .overrideProvider(I18nTranslationService)
    .useValue({
      translate: jest.fn().mockReturnValue('Test Translation'),
    })
    .overrideProvider(HttpService)
    .useValue({
      get: jest.fn(),
      post: jest.fn(),
    })
    .overrideProvider(LocationService)
    .useFactory({
      factory: () => {
        const mockService = {
          getLocationConfiguration: jest.fn(),
        };
        mockService.getLocationConfiguration.mockResolvedValue({
          locales: ['en-US', 'es-MX'],
          location_id: 123,
          tenant_id: 456,
          tenant_name: 'Test Tenant',
          tenant_phone: '************',
          tenant_email: '<EMAIL>'
        });
        return mockService;
      },
    } as OverrideByFactoryOptions)
    .overrideProvider(ConfigService)
    .useValue({
      get: jest.fn((key: string) => {
        if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
        if (key === 'API_INT_URL') return 'http://api.example.com';
        if (key === 'API_ACCT_URL') return 'http://api.example.com';
        if (key === 'API_CORE_URL') return 'http://api.example.com';
        if (key === 'API_LOC_URL') return 'http://api.example.com';
        if (key === 'DD_API_KEY') return 'dummy_key';
        if (key === 'BUGSNAG_API_KEY') return '616b106fc4dba0412968d8c0e91995be';
        return '';
      }),
    })
    .compile();

    return module;
  }

}
