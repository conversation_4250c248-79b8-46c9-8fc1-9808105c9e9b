import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, ExitPayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { DisconnectCall } from './DisconnectCall';
import { Locale } from '@cp-workspace/shared';

describe('DisconnectCall', () => {
  let disconnectCall: DisconnectCall;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DisconnectCall],
    }).compile();

    disconnectCall = module.get<DisconnectCall>(DisconnectCall);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.DisconnectCall,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn(), hangup: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should execute hangup and return DisconnectCall as next state', async () => {
      const response: ExitPayByPhoneStateHandlerResponse = await disconnectCall.handler(context);

      expect(context.twilioResponse.hangup).toHaveBeenCalled();
      expect(response.nextState).toBe(undefined);
    });
  });
});
