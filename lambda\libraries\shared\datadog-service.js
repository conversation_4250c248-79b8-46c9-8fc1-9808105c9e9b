const StatsD = require('hot-shots');

class DataDogService {
  constructor() {
    if (!DataDogService.instance) {
      this.client = new StatsD({
        host: process.env.DD_AGENT_HOST,
        port: 8125,
        errorHandler: (error) => {
          console.error('StatsD error:', error);
        },
      });
      DataDogService.instance = this;
    }

    return DataDogService.instance;
  }

  incrementCounter(metric, tags = []) {
    this.client.increment(metric, 1, tags);
  }

  recordDistribution(metric, value, tags = []) {
    this.client.distribution(metric, value, tags);
  }

  getBucket(value, maxValue, numberOfBuckets) {
    const bucketSize = Math.ceil(maxValue / numberOfBuckets);

    for (let i = 1; i <= numberOfBuckets; i++) {
      if (value <= i * bucketSize) {
        return `${(i - 1) * bucketSize + 1}-${i * bucketSize}`;
      }
    }

    return `${maxValue}+`; // Overflow
  }
}

const instance = new DataDogService();
Object.freeze(instance);

module.exports = instance;