import { Injectable } from "@nestjs/common";
import { AsyncWorkerEvent, IAsyncWorkersInvoker, IAsyncWorkersProvider } from "../../models/async-workers.model";

@Injectable()
export class AsyncWorkersMockClientProvider implements IAsyncWorkersProvider {
  getClient(): IAsyncWorkersInvoker {
    return new MockLambdaClientService();
  }
}

class MockLambdaClientService implements IAsyncWorkersInvoker {
  // TODO: Address what happens here for local/e2e/unit tests
  events: AsyncWorkerEvent[] = [];
  async invokeAsyncWorker(event: AsyncWorkerEvent): Promise<void> {
    console.log('ASYNC WORKER EVENT:\n', JSON.stringify(event, null, 2));
    this.events.push(event);
  }
}