<?php

use Tests\Unit\AbstractUnitTest;

include './app/controllers/CalldetailController.php';

class CalldetailControllerTest extends AbstractUnitTest
{
    public $allowedMockMethods = [
        "validSession",
        "infoMessage",
    ];

    private $postData = [
        'call_type'     => 'inbound',
        'call_number'   => 2386546646,
        'call_name'     => 9986346343,
        'datestamp'      => '2023-12-12',
        'location_id'   => 462,
        'twilio_id'     => 'CA23kjdg57dtgdj87svb3h39n8765yfh54sft',
    ];

    protected static function getMethod($name)
    {
        $class = new ReflectionClass('CalldetailController');
        $method = $class->getMethod($name);
        $method->setAccessible(true);
        return $method;
    }

    protected static function getPhalconResponseMethod($name)
    {
        $class = new ReflectionClass('\\Phalcon\\Http\\Response');
        $method = $class->getMethod($name);
        $method->setAccessible(true);
        return $method;
    }

    protected function getPhalconMockRequest($requestData = [], $methods = [], $locationIdString='3775')
    {
        $mockRequest = $this->createStub("\\Phalcon\\Http\\Request", $methods);
        foreach ($methods as $method) {
            $mockRequest
                ->method($method)
                ->will($this->returnValue($requestData));
        }

        $mockRequest
            ->method('get')
            ->will($this->returnCallback(function ($arg) use ($locationIdString) {
                if ('locationId' === $arg) {
                    return $locationIdString;
                }
            }));

        return $mockRequest;
    }

    protected function assertStatusCode($response, $code)
    {
        $method = self::getPhalconResponseMethod("getStatusCode");
        $result = $method->invokeArgs($response, []);
        $this->assertEquals($code, $result);
    }

    protected function assertContent($response, $statusString)
    {
        $method = self::getPhalconResponseMethod("getContent");
        $result = $method->invokeArgs($response, []);
        $this->assertEquals($statusString, json_decode($result)->status);
    }

    public function testValidateCallTypeIsValid()
    {
        $mockRequest = $this->getPhalconMockRequest($this->postData, ['getJsonRawBody']);

        $dispatcher = \Mockery::mock('dispatcher')->makePartial();

        $dispatcher->shouldReceive([
            'getActionName' => 'create'
        ]);

        $callDetailController = $this->getMockedControllerObject($mockRequest);

        $callDetailController->dispatcher = $dispatcher;

        $response = $callDetailController->validateCreate($this->postData);

        $this->assertEmpty($response);
    }

    public function testValidateCallTypeIsInvalid()
    {
        $this->postData['call_type'] = 'test';

        $mockRequest = $this->getPhalconMockRequest($this->postData, ['getJsonRawBody']);

        $dispatcher = \Mockery::mock('dispatcher')->makePartial();

        $dispatcher->shouldReceive([
            'getActionName' => 'create'
        ]);

        $callDetailController = $this->getMockedControllerObject($mockRequest);

        $callDetailController->dispatcher = $dispatcher;

        $response = $callDetailController->validateCreate($this->postData);

        $this->assertEquals(', Invalid call type', $response);
    }

    public function testValidateCustomCallTypeIsvalid()
    {
        $this->postData['call_type'] = 'inbound_nolead_xxx';

        $mockRequest = $this->getPhalconMockRequest($this->postData, ['getJsonRawBody']);

        $dispatcher = \Mockery::mock('dispatcher')->makePartial();

        $dispatcher->shouldReceive([
            'getActionName' => 'create'
        ]);

        $callDetailController = $this->getMockedControllerObject($mockRequest);

        $callDetailController->dispatcher = $dispatcher;

        $response = $callDetailController->validateCreate($this->postData);

        $this->assertEmpty($response);
    }

    protected function getMockedControllerObject($mockRequest, $mockMethods = [])
    {
        $callDetailController = \Mockery::mock('CalldetailController')->makePartial();
        $callDetailController->di->set('request', $mockRequest, true);
        $callDetailController->shouldAllowMockingProtectedMethods();

        foreach ($mockMethods as $method => $value) {
            if (!in_array($method, $this->allowedMockMethods)) {
                continue;
            }

            $callDetailController->allows($method)->andReturns($value);
        }

        return $callDetailController;
    }
}