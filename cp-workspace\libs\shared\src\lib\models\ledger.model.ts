import { PagingData } from "./paging.model";
export type Ledger = {
    first_name: string;
    last_name: string;
    email: string;
    customer_id: string;
    next_collection_date: string;
    location_id: number;
    location_name: string;
    days_past_due: string;
    next_due_date: string;
    phone: string;
    phone_type: string;
    _id: string;
    next_movein_rule_time: string;
    movein_rule_type: string;
    is_removed: number;
    error: string;
    collection_rule_version: number;
    movein_rule_version: number;
    is_deleted: number;
    collection_rule_type: string;
    last_error_date: string;
    collection_rule_id: string;
    error_message: string;
    last_updated: string;
    collection_rule_step: string;
    is_assigned: number;
    auto_mode: string;
    movein_rule_id: string;
    time: string;
    next_movein_rule_date: string;

    ledger_id: number;
    unit_id: string;
    paid_thru_date: string;
    amount_owed: string;
    moved_in_date: string;
    moved_out_date: string;
    user_id: number;
    es_unit_id: string;
    status: string;
    last_payment_date: string;
    rent_rate: number;
    unit_name: string;
    tenant_id: string;
    properties: object;
    attributes: object;
    
    tenant_id_es: string;
};

export type LedgerDataRequest = {
    skipCache?: boolean;
    includeMovedOut?: boolean;
}

export type LedgerDataResponse = {
    status: "OK" | "ERROR" | "MIXED";
    items: Ledger[];
    paging: PagingData;
}

export type LedgerId = Ledger['ledger_id'];
export type UnitId = Ledger['es_unit_id'];
export type CustomerId = Ledger['customer_id'] | Ledger['tenant_id'];