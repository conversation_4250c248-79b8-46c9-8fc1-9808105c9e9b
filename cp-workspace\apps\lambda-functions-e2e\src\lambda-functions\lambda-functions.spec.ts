/**
 * Since we're using supertest, we need to bootstrap the server
 * before running the tests. This is similar to how we bootstrap
 * the server in the main.ts file.
 */
// eslint-disable-next-line @nx/enforce-module-boundaries
import { bootstrapLocalServer } from '@cp-workspace/lambda-functions-app';
import request from 'supertest';
import { INestApplication } from '@nestjs/common';
import xml2js from 'xml2js';
import {
  PayByPhoneRequestBody,
  PayByPhoneResponse,
} from '@cp-workspace/pay-by-phone-domain';

let app: INestApplication;

beforeAll(async () => {
  process.env['I18N_E2E_TRANSLATIONS_PATH'] = '../../i18n';
  app = await bootstrapLocalServer();
});

afterAll(async () => {
  await app.close();
});

describe('POST /v1/echo', () => {

  it('should return 200 status code and the request body', async () => {
    const requestBody = { message: 'Hello, World!' };
    const response = await request(app.getHttpServer())
      .post('/v1/echo')
      .send(requestBody)
      .expect(200);

    expect(response.body).toEqual(requestBody);
  }, 10 * 60 * 1000);

});

describe('POST /v1/pay-by-phone', () => {
  const parser = new xml2js.Parser();
  const requestBody: PayByPhoneRequestBody = {
    CallSid: 'CA0693c6d35e1f1f6af4156d861ce517a7b',
    LocationId: 462,
    TransferToAgentUrl: '/transfer-to-agent',
  };
  const redirect = '/v1/pay-by-phone';

  describe('Customer Search', () => {
    it('Should not be able to locate users account', async () => {
      const response = await request(app.getHttpServer())
        .post('/v1/pay-by-phone')
        .send(requestBody)
        .expect(201);

      const parsedResponse = await parser.parseStringPromise(response.text);
      expect(parsedResponse.Response.Say).toBeDefined();
      expect(parsedResponse.Response.Redirect).toBeDefined();
    });

    it('Should prompt user to enter 10 digit number associated with account', async () => {
      const response: PayByPhoneResponse = await request(app.getHttpServer())
        .post(redirect)
        .send(requestBody)
        .expect(201);

      const parsedResponse = await parser.parseStringPromise(response.text);
      expect(parsedResponse.Response.Gather).toBeDefined();
      expect(parsedResponse.Response.Say).toBeDefined();
      expect(parsedResponse.Response.Redirect).toBeDefined();

      // Update request for next state...
      requestBody.Digits = '**********';
    });

    it('Should gather account details', async () => {
      const response: PayByPhoneResponse = await request(app.getHttpServer())
        .post(redirect)
        .send(requestBody)
        .expect(201);

      const parsedResponse = await parser.parseStringPromise(response.text);
      expect(parsedResponse.Response.Say).toBeDefined();
      expect(parsedResponse.Response.Redirect).toBeDefined();
    });
  });

  // describe('Customer Found', () => {
  //   it('placeholder', async () => {
  //     const response = await request(app.getHttpServer())
  //       .post('/v1/pay-by-phone')
  //       .send(requestBody)
  //       .expect(201);

  //     // Parse the XML response
  //     const parsedResponse = await xml2js.parseStringPromise(response.text);

  //     // Now you can make assertions on the parsedResponse object
  //     expect(parsedResponse.Response.Gather[0].$.numDigits).toBe('10');
  //     expect(parsedResponse.Response.Say[0]._).toBe(
  //       'Please enter the main phone number associated with your account. Press 0 followed by the pound key to speak with a manager.'
  //     );
  //   });
  // });

  // describe('Gather CC Details', () => {
  //   it('placeholder', async () => {
  //     const response = await request(app.getHttpServer())
  //       .post('/v1/pay-by-phone')
  //       .send(requestBody)
  //       .expect(201);

  //     // Parse the XML response
  //     const parsedResponse = await xml2js.parseStringPromise(response.text);

  //     // Now you can make assertions on the parsedResponse object
  //     expect(parsedResponse.Response.Gather[0].$.numDigits).toBe('10');
  //     expect(parsedResponse.Response.Say[0]._).toBe(
  //       'Please enter the main phone number associated with your account. Press 0 followed by the pound key to speak with a manager.'
  //     );
  //   });
  // });

  // describe('Process Payment', () => {
  //   it('placeholder', async () => {
  //     const response = await request(app.getHttpServer())
  //       .post('/v1/pay-by-phone')
  //       .send(requestBody)
  //       .expect(201);

  //     // Parse the XML response
  //     const parsedResponse = await xml2js.parseStringPromise(response.text);

  //     // Now you can make assertions on the parsedResponse object
  //     expect(parsedResponse.Response.Gather[0].$.numDigits).toBe('10');
  //     expect(parsedResponse.Response.Say[0]._).toBe(
  //       'Please enter the main phone number associated with your account. Press 0 followed by the pound key to speak with a manager.'
  //     );
  //   });
  // });
});
