const AWS = require('aws-sdk');
const _ = require('lodash');
const {
  ClientCapability,
} = require('twilio').jwt;
const config = require('./config');

AWS.config.update({
  region: process.env.AWS_REGION || 'us-west-2'
});
const twilioTwiml = config.twilio.twiml_app_sid;

exports.post = (event, context, callback) => {

  if (event.source === 'aws.events') {
    // keep warm!
    context.done();
    return;
  }

  var getUserID = function() {
    return new Promise((resolve, reject) => {
      const https = require('https');

      var options = {
        hostname: config.env + '-core.callpotential.com',
        port: 443,
        path: '/session',
        method: 'GET',
        headers: {
          'Authorization': event.body.authToken
        }
      };

      var req = https.request(options, (res) => {
        if (res.statusCode.toString().startsWith('4')) {
          reject(res.messages);
        }
        let result = '';
        res.setEncoding('utf8');
        res.on('data', (d) => {
          result += d;
        });
        res.on('end', () => {
          if (JSON.parse(result).status == 'ERROR') {
            return showError(401, JSON.parse(result).messages[0]);
          }
          resolve(result);
        });
      });

      req.on('error', (e) => {
        reject(e);
      });
      req.end();
    });
  }

  var queryDynamo = function(user_id) {
    var docClient = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
    var params = {
      TableName: config.dynamodb.acctTable,
      KeyConditionExpression: 'id = :id',
      ExpressionAttributeValues: {
        ':id': user_id
      }
    };

    docClient.query(params, function(err, data) {
      if (err || data.Count == 0) {
        console.error('Unable to query or no record found. Error:', JSON.stringify(err, null, 2));
      } else {
        generateToken(data);
      }
    });
  }

  getUserID().then((d) => {
    d = JSON.parse(d);
    let user_id = d.user.parent_id;
    if (d.user.parent_id == '0') {
      user_id = d.user.user_id;
    }
    queryDynamo(user_id);
  }).catch((e) => {
    console.log('error for reject getting user_id ', e);
  });

  function generateToken(data) {
    const twilioAccount = data.Items[0];
    const {
      clientName,
      ttl,
    } = (typeof event.body === 'string') ? JSON.parse(event.body): event.body;
    // console.log(`Client Name: ${clientName}, TTL: ${ttl}`);

    const accountSid = twilioAccount.account_sid;
    if (_.isEmpty(accountSid) || _.isEmpty(clientName)) {
      const error = 'A required parameter was not specified.';
      // console.log(`${error}: [clientName: ${clientName}, accountSid: ${accountSid}]`);
      return callback(null, createResponse(400, {
        error
      }));
    }

    const capability = new ClientCapability({
      accountSid: twilioAccount.account_sid,
      authToken: twilioAccount.authtoken,
      ttl: parseInt(ttl), // default in library is 3600
    });
    capability.addScope(new ClientCapability.IncomingClientScope(clientName));
    capability.addScope(new ClientCapability.OutgoingClientScope({
      applicationSid: twilioTwiml,
    }));

    const token = capability.toJwt();
    return callback(null, createResponse(200, {
      token
    }));
  }

  function createResponse(statusCode, body) {
    return {
      statusCode,
      body: JSON.stringify(body),
    };
  }

  function showError(statusCode, body) {
    return callback(null, {
      body,
      statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'OPTIONS POST GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  }
};


function no_op(event, context) {
  // console.log(event)
  context.done()
}

exports.no_op = no_op;
