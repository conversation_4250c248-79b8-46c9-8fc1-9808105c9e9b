const twilioUtils = require('../../routes/utils/twillio');
const twilioMockData = require('./mockData/twilio-mock-data.json');
const CallLogModel = require("../../models/call-log");
const {LocationModel} = require("../../models/location");
const { cpapiClient } = require('../../libraries/cpapi-client');

const taskData = {
    EventType: 'task.canceled',
    EventDescription : "Task canceled",
    Reason : "hangup",
    TaskAttributes: JSON.stringify(twilioMockData.task_data_video.task_attributes),
    TaskSid: twilioMockData.task_data_video.task_sid,
    TaskAge : 20,
    TaskChannelUniqueName: 'video',
    TaskQueueName: twilioMockData.task_data_video.task_queue_name,
    TaskAssignmentStatus: twilioMockData.task_data_video.task_assignment,
}
const reservationData = {
    EventType: 'reservation.canceled',
    EventDescription : "Reservation created",
    TaskAttributes: JSON.stringify(twilioMockData.task_data.task_attributes),
    TaskSid: twilioMockData.task_data.task_sid,
    TaskAge : 20,
    TaskChannelUniqueName: 'voice'
}

jest.mock('../../models/call-log');
jest.mock('../../models/location');
jest.mock('../../libraries/cpapi-client');

describe('Twilio', () => {
    
    describe("Test process_inbound_call_destination_data", () => {
        let desination_data = {
            trackingnumber_id: 57081,
            location_id: 3775,
            record: 1,
            ad_id: 3043,
            call_number: '***********',
            user_id: 235,
            active: 1,
            twilio_phone_sid: 'PN6329dc0cf3a1ab46dbe570694f3737ce',
            dateCreated: '2018-07-13 06:39:21',
            dateRemoved: '0000-00-00 00:00:00',
            type: 'local',
            archived_by: 0,
            exclude_number: 0,
            call_route_config_id: 11145,
            callcard_id: 161,
            location_name: '*StorEDGE',
            postal: '60544',
            country: 'US',
            latitude: 41.607691,
            longitude: -88.205447,
            phone: '<EMAIL>',
            api_type: 9,
            delay: 120,
            timezone: 'America/Chicago',
            use_daylight_saving: 1,
            logo_image: 'pn3zijy37geu4hr67nolfkzth.jpg',
            friendly_name: 'StorEDGE',
            website: '', 
            overide_tracking_number: '***********',
            custom_variables: '',
            is_api_call_allowed_threshold_1: 0,
            api_call_frequency: 5000,
            location_external_id: 'a64137b2489ca88aa7d9e875f77b3e37',
            version_id: 10000218803,
            unit_rates: [ { name: 'Rent Rate', field: 'rent_rate' } ],
            callRouteConfig: {
                config_id: 11144,
                user_id: 235,
                config: '{"children":[{"name":{"type":"input","label":"Name","value":"RouteConnect","disabled":false},"connect_type":{"type":"select","label":"Connect Type","options":[{"name":"Call Center","value":"call_center"},{"name":"Location","value":"location"},{"name":"Dial","value":"dial"}],"selected":"call_center"},"queue_type":{"type":"select","label":"Queue Name","options":[],"selected":"WQa2e718dbb979114111a6148e47c76632"},"dial_to":{"type":"input","label":"Dial","value":""},"sip_username":{"type":"input","label":"Username","value":""},"sip_password":{"type":"input","label":"Password","value":""},"max_wait_time":{"type":"input","label":"Wait Time (sec)","value":"15"},"skip_step":{"type":"checkbox","label":"Skip this step outside business hours","checked":false,"disabled":false},"skip_outside_callcenterhr":{"type":"checkbox","label":"Skip This Step Outside Call Center Hours","checked":false,"disabled":false},"children":[],"stop_recording":{"type":"checkbox","label":"Stop recording on this step","checked":false,"disabled":false},"priority":{"value":"1"},"uuid":"84eed08f-691f-4582-97d2-cf26add93a73","type":"route_connect","pos":{"top":457,"left":1120},"parent_uuid":"root_node","connector_label":"RouteConnect","is_label_required":false,"lead_card":{"type":"radio","name":"84eed08f-691f-4582-97d2-cf26add93a73lead_card_name","label":"Lead Card","options":[{"name":"Form","value":"form"},{"name":"Script","value":"script"}]}}],"uuid":"root_node","type":"root_node","pos":{"top":230,"left":1110},"parent_uuid":null,"name":"Inbound Call","connector_label":"Root Connection","is_label_required":false}',
                name: 'testing_route',
            }
        };
        let data = {
            Called: '+***********',
            ToState: 'PA',
            CallerCountry: 'US',
            Direction: 'inbound',
            CallerState: 'PA',
            ToZip: '19110',
            CallSid: 'CA4b9b6b6c7dad7f8aeb36061dd25e1735',
            To: '+***********',
            CallerZip: '15219',
            ToCountry: 'US',
            CalledZip: '19110',
            ApiVersion: '2010-04-01',
            CalledCity: 'PHILADELPHIA',
            CallStatus: 'ringing',
            From: '+***********',
            AccountSid: '**********************************',
            CalledCountry: 'US',
            CallerCity: 'PITTSBURGH',
            ToCity: 'PHILADELPHIA',
            FromCountry: 'US',
            Caller: '+***********',
            FromCity: 'PITTSBURGH',
            CalledState: 'PA',
            FromZip: '15219',
            FromState: 'PA'
        };

        let call_number = '+***********';
        let intClient;
        beforeEach(() => {
            callLogModel = new CallLogModel();
            intClient = {
                postData: jest.fn(),
            };
            LocationModel.prototype.get_location_by_id = jest.fn().mockResolvedValue('US');
            CallLogModel.prototype.put_dynamodb = jest.fn().mockResolvedValue({log_id: 2386533});
            intClient.postData.mockResolvedValue({items: []});            
        });

        afterAll(() => {
            jest.clearAllMocks()
        })
        it('should call create_call_log when process_inbound_call_destination_data is triggered', async () => {
            CallLogModel.prototype.create_call_log.mockResolvedValue({db_log_id: 1235333});
            await twilioUtils.process_inbound_call_destination_data(desination_data, data, call_number);
            expect(CallLogModel.prototype.create_call_log).toHaveBeenCalled();
        })

        it('should fail when create_call_log return null', async () => {
            CallLogModel.prototype.create_call_log.mockResolvedValue(null);
            const response = await twilioUtils.process_inbound_call_destination_data(desination_data, data, call_number);
            expect(response).toBe("<Response><Say>Something wrong with processing calldetail.</Say></Response>");
        })

        it('should fail when put_dynamodb return null', async () => {
            CallLogModel.prototype.put_dynamodb = jest.fn().mockImplementation(() => '');
            const response = await twilioUtils.process_inbound_call_destination_data(desination_data, data, call_number);
            expect(response).toBe("<Response><Say>Something wrong with processing call.</Say></Response>");
        })

        it('should fail when put_dynamodb return empty {}', async () => {
            CallLogModel.prototype.put_dynamodb = jest.fn().mockImplementation(() => {});
            const response = await twilioUtils.process_inbound_call_destination_data(desination_data, data, call_number);
            expect(response).toBe("<Response><Say>Something wrong with processing call.</Say></Response>");
        })
    })

    describe("Test Twilio Utils Functions", () => {
        
        test("should test update_callcener_task for video channel abandoned call and returns true", async () => {
            jest.resetModules();

            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
            
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        return {
                            getData : () => { return {
                                items : [ {is_task : 1} ]
                            }},
                            putData : () => {return }
                        };
                    }),
                };
            });
            const result = await twilioUtils.update_callcener_task(taskData);

            expect(result).toBe(true);
        });

        test("should test update_callcener_task for video channel rolled over call and returns true", async () => {
            jest.resetModules();

            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
            
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        return {
                            getData : () => { return {
                                items : [ {is_task : 1} ]
                            }},
                            putData : () => {return }
                        };
                    }),
                };
            });
            taskData.Reason = "Task TTL Exceeded"; 
            const result = await twilioUtils.update_callcener_task(taskData);

            expect(result).toBe(true);
        });

        test("should test update_callcener_task for video channel and return false if any exception occurs", async () => {
            jest.resetModules();

            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        throw new Error();
                    }),
                };
            });

            const result = await twilioUtils.update_callcener_task(taskData);
            expect(result).toBe(false);
        });

        test("should test insert_task_activity function and return true after successful processing and saving data", async () => {
            jest.resetModules();
        
            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
            
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        return {
                            postData : () => { return {
                                'log_id' : 1234
                            }},
                        };
                    }),
                };
            });
            
            const result = await twilioUtils.insert_task_activity(reservationData);
            
            expect(result).toBe(true);
        });


        test("should test insert_task_activity function and return false after failing", async () => {
            jest.resetModules();
        
            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
            
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        return {
                            postData : () => { throw new Error()},
                        };
                    }),
                };
            });

            const result = await twilioUtils.insert_task_activity(reservationData);
            
            expect(result).toBe(false);
        });

        test('should handle voice channel task activity correctly', async () => {
            jest.resetModules();
            //add mockPutData
            const mockPutData = jest.fn();
            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
            
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        return {
                            getData : () => { return {
                                items : [ {is_task : 1} ]
                            }},
                            postData : () => { return {
                                'log_id' : 1234
                            }},
                            putData : mockPutData
                        };
                    }),
                };
            });
            const testData = {
                TaskSid: 'TestTaskSid',
                Timestamp: 123,
                TaskAttributes: JSON.stringify({
                    log_id: 'TestLogId',
                    location_id: 'TestLocationId',
                    call_sid: 'TestCallSid',
                    queue_sid: 'TestQueueSid',
                    config_step_uid: 'TestConfigStepUid'
                }),
                WorkerAttributes: JSON.stringify({
                    agent_id: 'TestAgentId'
                }),
                EventType: 'reservation.accepted',
                TaskChannelUniqueName: 'voice'
            };

            const result = await twilioUtils.insert_task_activity(testData);
            expect(result).toBe(true);
            expect(mockPutData).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({
                'employee_id': 'TestAgentId'
            }));
        });

        test('should handle custom1 channel task activity correctly', async () => {
            jest.resetModules();
            const mockPutData = jest.fn();
            jest.doMock('../../libraries/cpapi-client', () => {
                const originalModule = jest.requireActual('../../libraries/cpapi-client');
            
                return {
                    __esModule: true,
                    ...originalModule,
                    callClient: jest.fn().mockImplementation( () => {
                        return {
                            getData : () => { return {
                                items : [ {is_task : 1} ]
                            }},
                            postData : () => { return {
                                'log_id' : 1234
                            }},
                            putData : mockPutData
                        };
                    }),
                };
            });

            const testData = {
                TaskSid: 'TestTaskSidCustom',
                Timestamp: 123,
                TaskAttributes: JSON.stringify({
                    log_id: 'TestLogIdCustom',
                    dbLogId: 'TestDbLogId',
                    location_id: 'TestLocationIdCustom',
                    call_sid: 'TestCallSidCustom',
                    queue_sid: 'TestQueueSidCustom',
                    config_step_uid: 'TestConfigStepUidCustom'
                }),
                WorkerAttributes: JSON.stringify({
                    agent_id: 'TestAgentIdCustom'
                }),
                EventType: 'reservation.accepted',
                TaskChannelUniqueName: 'custom1'
            };

            const result = await twilioUtils.insert_task_activity(testData);
            expect(result).toBe(true);
            expect(mockPutData).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({
                'employee_id': 'TestAgentIdCustom'
            }));
        });
    });

});
