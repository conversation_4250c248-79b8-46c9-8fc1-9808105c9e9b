{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Run State Diagram Code Generator",
      "runtimeExecutable": "ts-node",
      "runtimeArgs": [
        "--inspect-brk",
        "${workspaceFolder}/generators/src/lib/state-diagram-code/generator.ts",
        "${workspaceFolder}",
        "${workspaceFolder}/libs/pay-by-phone-domain/src/.diagrams",
        "${workspaceFolder}/libs/pay-by-phone-domain/src/lib"
      ],
      "skipFiles": ["<node_internals>/**"],
      "preLaunchTask": "tsc: build - generators/tsconfig.json",
      "outFiles": ["${workspaceFolder}/generators/**/*.mjs"]
    }
  ]
}
