import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { EventLoggerController } from './EventLogger.controller';
import { DomainEventsClientType, DomainEventsIntegrationModule, DomainEventsIntegrationService, DomainEventsService } from '@cp-workspace/shared';

describe('EventLoggerController', () => {
  let controller: EventLoggerController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [DomainEventsIntegrationModule.forRoot({
        clientType: DomainEventsClientType.IN_MEMORY,
        options: { enabled: true },
      }),],
      providers: [DomainEventsService, DomainEventsIntegrationService],
      controllers: [EventLoggerController],
    }).compile();

    controller = module.get<EventLoggerController>(EventLoggerController);
  });

  describe('handleEventLoggerRequest', () => {

    describe('when domain events feature is enabled', () => {

      it('should throw BadRequestException if request body is empty', async () => {
        const mockRequest = {
          body: null,
        };
        await expect(controller.handleEventLoggerRequest(mockRequest as any))
          .rejects
          .toThrow(BadRequestException);
      });

      it('should throw BadRequestException if request body is not a DomainEvent', async () => {
        const mockRequest = {
          body: { message: 'Hello, World!' },
        };
        await expect(controller.handleEventLoggerRequest(mockRequest as any))
          .rejects
          .toThrow(BadRequestException);
      });

      it('should return nothing when request body is a DomainEvent', async () => {
        jest.spyOn(controller.logger, 'publish');

        const mockRequest = {
          body: { 
            source: 'test', 
            detailType: 'test',
            detail: { message: 'Hello, World!' } 
          },
        };
        const result = await controller.handleEventLoggerRequest(mockRequest as any);
        expect(controller.logger.publish).toHaveBeenCalledWith(mockRequest.body, true);
        expect(result).toBeUndefined();
      });

    });

    describe('when domain events feature is disabled', () => {

      it('should return nothing', async () => {
        DomainEventsIntegrationService.options = { enabled: false };
        const mockRequest = {
          body: { message: 'Hello, World!' },
        };
        const result = await controller.handleEventLoggerRequest(mockRequest as any);
        expect(result).toBeUndefined();
      });
  
    });

  });
});
