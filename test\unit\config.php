<?php

return new \Phalcon\Config([
    'application' => [
        'staticToken' => [
            'readOnly' => 'VYZQ3XScyB9LDryvjqxDi6nF3Hw349JB3QsTN1hatxorEu1VMqq9QUEFSrTVyGS6',
            'readWrite' => 'ye1w2iqF3BXihixGzw6efFF3W9edRmoKbwMqOhfBuNDLzsyWz6vxiPgyzKhMjRDr'
        ],
        'services' => [
            'core' => 'http://local-core.services.com',
            'loc' => 'http://local-loc.services.com',
            'acct' => 'http://local-acct.services.com',
            'int' => 'http://local-int.services.com',
            'call' => 'http://local-call.services.com',
            'chat' => 'http://local-chat.services.com',
            'sms' => 'http://local-sms.services.com',
            'email' => 'http://local-email.services.com',
            'mcc' => 'http://local-mcc.services.com',
            'eval' => 'http://local-eval.services.com',
        ],
        'paymentCallTypes' => [
            'inbound_payment',
            'inbound_lead_payment',
            'outbound_lead_payment',
            'outbound_payment',
        ],
        'allCallTypes' => [
            'inbound',
            'inbound_payment',
            'inbound_autopay',
            'inbound_cleared',
            'inbound_collection',
            'inbound_customer',
            'inbound_lead',
            'inbound_lead_payment',
            'inbound_nolead_hangup_cust_sys',
            'outbound',
            'outbound_followup',
            'outbound_callback',
            'outbound_collection',
            'outbound_lead_payment',
            'outbound_payment',
        ],
        'env' => 'test',
        'logLevel' => ''
    ],
    'aws' => [
        'buckets' => [
            'recordings' => 'test-recordings',
        ]
    ],
]);
