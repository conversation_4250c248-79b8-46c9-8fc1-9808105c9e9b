{"name": "lambda-functions", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/lambda-functions/src", "projectType": "application", "tags": [], "implicitDependencies": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "lambda-functions:build"}, "configurations": {"development": {"buildTarget": "lambda-functions:build:development"}, "production": {"buildTarget": "lambda-functions:build:production"}}}, "test": {"executor": "@nx/jest:jest", "options": {"jestConfig": "apps/lambda-functions/jest.config.ts", "passWithNoTests": true, "codeCoverage": true, "testPathPattern": ["apps/lambda-functions/src/app"], "coverageDirectory": "{workspaceRoot}/coverage/lambda-functions", "coverageReporters": ["lcov", "text"]}, "dependsOn": ["lambda-functions:build"]}, "build-lambda": {"executor": "nx:run-commands", "cache": false, "options": {"commands": ["nx run lambda-functions:build-lambda-webpack", "npx bugsnag-source-maps upload-node --api-key 3ae5f368b904298a59fb98571a9226f8 --app-version 1.0.0 --source-map dist/apps/lambda-functions/main.js.map --bundle dist/apps/lambda-functions/main.js --overwrite"], "parallel": false}}, "build-lambda-webpack": {"executor": "@nx/webpack:webpack", "options": {"webpackConfig": "apps/lambda-functions/webpack.config.lambda.js", "outputPath": "dist/apps/lambda-functions"}, "dependsOn": ["pay-by-phone-domain:build"]}}}