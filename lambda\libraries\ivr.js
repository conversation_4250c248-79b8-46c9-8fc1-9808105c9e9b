"use strict";
const mainconfig = require('../config/config');

/**
 * IVR Class
 *
 * Handles interactive voice response functionality
 *
 * @package    Callpotential
 * @subpackage Application
 * @category   Libraries
 * <AUTHOR>
 * @link       void
 */
const ivr = class IVR {

  /**
   * Initializes required instance variables
   *
   * @access public
   * @param  array $config
   * @return void
   */
  constructor(config, req_q_data, req_p_data) {
    this.req_q_data = req_q_data;
    this.req_p_data = req_p_data;
    if (config) {
      this.log_id = config.log_id;
      this.name = config.name;
      this.extensions = config.extensions;

      if (config.uploaded_mp3_url) {
        if (config.uploaded_mp3_url.includes('uploads/call_route_config/') && ! config.uploaded_mp3_url.includes('http')) {
          this.uploaded_mp3_url = mainconfig.CP_CDN_URL.slice(0, -1) + config.uploaded_mp3_url;
        } else {
          this.uploaded_mp3_url = config.uploaded_mp3_url;
        }
      }

      this.queue_manager = config.queue_manager;

      this.nextStepUrl = `${mainconfig.call_url}twilio/process_next_step/${this.log_id}?next_step`;
    }
  }

  /**
   * Requests the customer, specifying all the extension options
   *
   * @access public
   * @param  void
   * @return mixed void/int
   */
  get_customer_input() {
    const self = this;

    /*

      CCC-179
      This function is called at the beginning of a call route and returns 
      <Gather/> TwiML that will play the IVR menu and wait for user input.

      However, this function is ALSO called AFTER the user inputs a digit 
      (or doesn't).  If the user doesn't provide any input, there is no 
      mechanism to supply a default value and the IVR will be replayed in
      an endless loop.

      The solution that solves this problem is: 
      ON FIRST RUN
      - return an initial <Gather/> response that 
        - explicitly presents the IVR menu twice
        - contains a redirect to repeat this step (as is already the flow)
          - the redirect url will contain an additional querystring parameter,
            `autoivr` indicating that next time this function is entered, we 
            should automatically select an IVR extension.

      ON SECOND RUN
      - set self.req_p_data.Digits = automatic selection of the first available IVR extension
      - return self.get_extension_response() which returns the extension number.

    */

    // THIS CONDITION EXECUTES ON SECOND RUN
    // eslint-disable-next-line no-prototype-builtins
    if (self.req_p_data.Digits || self.req_q_data.hasOwnProperty('autoivr')) {

      // eslint-disable-next-line no-prototype-builtins
      if (self.req_q_data.hasOwnProperty('autoivr')){
        const { ext } = self.extensions[0];
        self.req_p_data.Digits = ext;
      }

      // Match the pressed key against valid values
      // If matches against a list of pre-specified numbers
      // Then returns the digit
      return self.get_extension_response();

    }


    // THIS CODE PATH EXECUTES ON FIRST RUN
    const gather = self.queue_manager.twilio_response.gather({
      numDigits: 1,
      action: `${mainconfig.call_url}twilio/process_next_step/${self.log_id}`,
    });


    if (self.uploaded_mp3_url) {
      gather.play(self.uploaded_mp3_url);
    } else {
      for (const {ext, name} of self.extensions) {
        gather.say(`for ${name}, press ${ext}`);
      }
    }
    gather.pause({ length: 3 });
    gather.say('Sorry, we didn\'t get your response.');
    if (self.uploaded_mp3_url) {
      gather.play(self.uploaded_mp3_url);
    } else {
      for (const {ext, name} of self.extensions) {
        gather.say(`for ${name}, press ${ext}`);
      }
    }
    gather.pause({ length: 3 });
    gather.say('Sorry, we didn\'t get your response.');

    // REDIRECT TO SECOND RUN (CALLER DIDN'T INPUT ANYTHING)
    self.queue_manager.twilio_response.redirect(`${self.nextStepUrl}&autoivr`);

    self.queue_manager.is_output_set = 1;
  }

  /**
   * Gets the pressed digit response from customer for a particular action
   *
   * @access public
   * @param  void
   * @return void/int
   */

  get_extension_response() {
    const self = this;
    const userPushed = self.req_p_data.Digits;

    // Unset this variable as we don't need it anymore,
    // also to prevent skipping of next IVR steps.
    delete self.req_p_data.Digits;

    for(const { ext } of self.extensions){
      if (`${ext}` === `${userPushed}`){
        return userPushed;
      }
    }

    self.queue_manager.twilio_response.say('Sorry, Invalid key pressed.');

    // Repeat customer input request step
    self.queue_manager.twilio_response.redirect(self.nextStepUrl);
    self.queue_manager.is_output_set = 1;
  }
}

module.exports = {
  'IVR': ivr
}
