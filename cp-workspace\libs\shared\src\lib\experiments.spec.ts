import { Inject, Injectable, Module } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";

describe('Nest Module Experiments', () => {

  describe('Using Dynamic Modules', () => {

    it('Should use Provider1 to doWork', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [ DynamicModuleConsumerA ],
      })
      .compile();
  
      const consumerService = module.get<Consumer1>(Consumer1);
      const result = consumerService.doWork();
      expect(result).toBe('Provider1.doWork');
    });
  
    it('Should use Provider2 to doWork', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [ DynamicModuleConsumerB.forRoot({ provider: 'Provider2' }), ],
      })
      .compile();
  
      const consumerService = module.get<Consumer1>(Consumer1);
      const result = consumerService.doWork();
      expect(result).toBe('Provider2.doWork');
    });

    it('Should use Provider3 to doWork', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [ DynamicModuleConsumerC ],
      })
      .compile();
  
      const consumerService = module.get<Consumer1>(Consumer1);
      const result = consumerService.doWork();
      expect(result).toBe('Provider3.doWork');
    });
    
  });
  
  describe('Using Static Modules', () => {
  
    it('Should use Provider1 to doWork', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [ StaticModuleConsumerA ],
      })
      .compile();
  
      const consumerService = module.get<Consumer1>(Consumer1);
      const result = consumerService.doWork();
      expect(result).toBe('Provider1.doWork');
    });
  
    it('Should use Provider2 to doWork', async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [ StaticModuleConsumerB ],
      })
      .compile();
  
      const consumerService = module.get<Consumer1>(Consumer1);
      const result = consumerService.doWork();
      expect(result).toBe('Provider2.doWork');
    });

    // it('Should use Provider3 to doWork', async () => {
    //   const module: TestingModule = await Test.createTestingModule({
    //     imports: [ StaticModuleConsumerC ],
    //   })
    //   .compile();
  
    //   const consumerService = module.get<Consumer1>(Consumer1);
    //   const result = consumerService.doWork();
    //   expect(result).toBe('Provider3.doWork');
    // });

  });
  
});

@Injectable()
class ProviderBase {
  doWork() {
    return 'ProviderBase.doWork';
  }
}

@Injectable()
class Provider1 {
  doWork() {
    return 'Provider1.doWork';
  }
}

@Injectable()
class Provider2 {
  doWork() {
    return 'Provider2.doWork';
  }
}

@Injectable()
class Provider3 {
  doWork() {
    return 'Provider3.doWork';
  }
}

@Module({
  imports: [],
  controllers: [],
  providers: [
    ProviderBase,
    Provider1,
    Provider2,
    Provider3,
    {
      provide: 'PROVIDER_TYPE',
      useClass: ProviderBase
    },
  ],
  exports: [
    ProviderBase,
    Provider1,
    Provider2,
    Provider3,
    'PROVIDER_TYPE'
  ],
})
class ModuleBase {
  static forRoot(options: any) {
    if (options.provider === 'Provider1') {
      return {
        module: ModuleBase,
        providers: [
          {
            provide: 'PROVIDER_TYPE',
            useClass: Provider1
          },
          ProviderBase,
          Provider1,
          Provider2,
          Provider3,
        ],
        exports: [
          ProviderBase,
          Provider1,
          Provider2,
          Provider3,
          'PROVIDER_TYPE'
        ],
      }
    }
    if (options.provider === 'Provider2') {
      return {
        module: ModuleBase,
        providers: [
          {
            provide: 'PROVIDER_TYPE',
            useClass: Provider2
          },
          ProviderBase,
          Provider1,
          Provider2,
          Provider3,
        ],
        exports: [
          ProviderBase,
          Provider1,
          Provider2,
          Provider3,
          'PROVIDER_TYPE'
        ],
      }
    }
    if (options.provider === 'Provider3') {
      return {
        module: ModuleBase,
        providers: [
          {
            provide: 'PROVIDER_TYPE',
            useClass: Provider3
          },
          ProviderBase,
          Provider1,
          Provider2,
          Provider3,
        ],
        exports: [
          ProviderBase,
          Provider1,
          Provider2,
          Provider3,
          'PROVIDER_TYPE'
        ],
      }
    }
    return {
      module: ModuleBase,
      providers: [
        ProviderBase,
        Provider1,
        Provider2
      ],
      exports: [
        ProviderBase,
        Provider1,
        Provider2
      ],
    }
  }
}


@Injectable()
class Consumer1 {
  constructor(@Inject('PROVIDER_TYPE') private provider: ProviderBase) { }
  doWork() {
    return this.provider.doWork();
  }
}

@Module({
  imports: [
    ModuleBase.forRoot({ provider: 'Provider1' })
  ],
  controllers: [],
  providers: [
    Consumer1
  ],
  exports: [
    Consumer1
  ],
})
class DynamicModuleConsumerA {}

@Module({
  // imports: [
  //   /**
  //    * If you need to pass options down from a consumer
  //    * of this module to the ModuleBase, in this example,
  //    * you cannot import the module here.
  //    * 
  //    * If you import the ModuleBase here, these settings
  //    * will take precedence over the settings passed in
  //    * the forRoot method.
  //    */
  //   //ModuleBase.forRoot({ provider: 'Provider2' })
  // ],
  // controllers: [],
  // providers: [
  //   Consumer1,
  // ],
  // exports: [
  //   Consumer1,
  // ],
})
class DynamicModuleConsumerB {
  static forRoot(options: any) {
    return {
      module: DynamicModuleConsumerB,
      imports: [
        ModuleBase.forRoot(options)
      ],
      providers: [
        Consumer1
      ],
      exports: [
        Consumer1,
      ],
    }
  }
}

/**
 * This module is an example of a consumer that
 * must use a module that, already configures the
 * ModuleBase provider to use a specific class.
 * 
 * This module wishes to override the provider
 * to use a different class.  In order to do this,
 * the module we import here must be engineered to
 * accept options that can be passed in to override
 * the default settings.  This must be done using 
 * dynamic modules.
 */
@Module({
  imports: [
    DynamicModuleConsumerB.forRoot({ provider: 'Provider3' }),
  ],
  controllers: [],
  exports: [],
})
class DynamicModuleConsumerC {}


@Module({
  imports: [
    ModuleBase
  ],
  controllers: [],
  providers: [
    Consumer1,
    { provide: 'PROVIDER_TYPE', useClass: Provider1 }
  ],
  exports: [
    Consumer1
  ],
})
class StaticModuleConsumerA {}

@Module({
  imports: [
    ModuleBase
  ],
  controllers: [],
  providers: [
    Consumer1,
    { provide: 'PROVIDER_TYPE', useClass: Provider2 },
  ],
  exports: [
    Consumer1,
    ModuleBase,
    'PROVIDER_TYPE'
  ],
})
class StaticModuleConsumerB { }

// @Module({
//   imports: [
//     ModuleBase,
//     // ModuleBase.forRoot({ provider: 'Provider3' }),
//     StaticModuleConsumerB
//   ],
//   controllers: [],
//   providers: [
//     // Consumer1,
//     { provide: 'PROVIDER_TYPE', useClass: Provider3 }
//   ],
//   exports: [
//     StaticModuleConsumerB,
//     // 'PROVIDER_TYPE'
//   ],
// })
// class StaticModuleConsumerC { }
