import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodPostalCodeConfirm extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Read the customer's confirmation
     * 2. If they have confirmed the postal code
     * 2a. Transition to FinalPayAmountPrompt
     * 2b. If they reject, they want to re-enter the number, so transition to PayMethodPostalCodePrompt
     */

    const { request } = context;
    const confirmationSelection = request.Digits;

    if (confirmationSelection === '1') {
      return { nextState: PayByPhoneState.FinalPayAmountPrompt };
    } else {
      return { nextState: PayByPhoneState.PayMethodPostalCodePrompt };
    }
  }
}
