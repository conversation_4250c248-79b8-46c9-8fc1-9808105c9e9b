<?php
/**
 * RecordingController
 *
 * Created by PhpStorm.
 * User: cwalker
 * Date: 3/30/17
 * Time: 4:38 PM
 *
 * @category RecordingController
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

use CallPotential\CPCommon\Util;
use CallPotential\CPCommon\EventRouter;
use CallPotential\CPCommon\HttpStatusCode;
use Phalcon\Db\Enum;

/**
 * Swagger
 *
 * @SWG\Definition(type="object",definition="CallHistoryRecordingUrl",
 * @SWG\Property(property="recording_url",type="string"),
 * )
 */

/**
 * RecordingController
 *
 * @category RecordingController
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 */
class RecordingController extends \CallPotential\CPCommon\Controllers\BaseController
{
    use \CallPotential\CPCommon\Controllers\SessionTrait;
    use \CallPotential\CPCommon\Controllers\ElasticSearchTrait {
        getItem as protected esTraitGetItem;
    }

    protected $outputFormat = '';

    /**
     * Get name of models to interact with database layer (Mysql/ES/Dynamo)
     *
     * @return string
     */
    public function getModelName(): string
    {
        return "\\CallHistory"; //model
    }

    /**
     * Constructor to initialize data
     *
     * @return void
     */
    public function initialize()
    {
        parent::initialize();
        $this->outputFormat = $this->request->getHeader('Accept');
        if (array_key_exists('of', $_GET)) {
            $this->outputFormat = $_GET['of'];
        }
    }

    /**
     * Intermediate function to check before calling get action
     *
     * @param mixed $id primary key value of the record
     *
     * @return mixed
     */
    public function getItem($id): array
    {
        $result = [];
        $mySQLRecord = '';
        $datestamp = '';
        if (is_numeric($id) && $id > 0) {
            $mySQLRecord = $this->getDBCallDetail($id);
            if (!empty($mySQLRecord) && isset($mySQLRecord["datestamp"])) {
                $datestamp = $mySQLRecord["datestamp"];
            }
            $result = $this->getItemFromEs($id, $datestamp);
            $result = $result[0] ?? $result;
        } else {
            $result = $this->getEsData($id);
            if (isset($result['hits']['total']) && $result['hits']['total'] > 0) {
                $result = Util::array_get('_source', $result['hits']['hits'][0], []);
            } else {
                $result = [];
            }
        }

        if (empty($result["recording_url"]) && !empty($result["log_id"])) {
            if (empty($mySQLRecord)) {
                $mySQLRecord = $this->getDBCallDetail($result["log_id"]);
            }
            if (!empty($mySQLRecord) && isset($mySQLRecord["recording_url"])) {
                $result["recording_url"] = $mySQLRecord["recording_url"];
            }
        }

        return $result;
    }

    /**
     * Get record from ES
     *
     * @param mixed  $log_id    call log Id
     * @param string $datestamp call datetime
     *
     * @return array
     */
    public function getItemFromEs($log_id, $datestamp) : array
    {
        $modelName = $this->getModelName();
        $model = new $modelName();
        if (!empty($datestamp)) {
            $model->setIndexSuffix($datestamp);
        } else {
            $model->indexSuffix = "-*";
        }
        $objData = new \stdClass();
        $objData->log_id = $log_id;
        $result = $model->searchAll(['match' => $objData]);

        return $result;
    }

    /**
     * BulkSave
     *
     * @override bulksave method to overcome error from BaseController
     * as it is not implemented
     *
     * @return \Phalcon\Http\Response
     */
    public function bulkSave(): \Phalcon\Http\Response
    {
         return $this->sendNotImplemented();
    }

    /**
     * Handles bulk PUT requests, called from saveAction
     *
     * @return array
     */
    public function doBulkSave(): array
    {
        return $this->sendNotImplemented();
    }

    /**
     * Handles bulk POST requests, called from createAction
     *
     * @return array
     */
    public function doBulkCreate()
    {
        // TODO: Implement doBulkCreate() method.
    }

    /**
     * Swagger content
     *
     * @SWG\Get(
     *     tags={"Call Data"},
     *     path="/recording/{call_sid}",
     *     description="Returns Presigned recording url of aws S3",
     *     summary="Returns Presigned recording url",
     *     operationId="RecordingPresignedUrlById",
     *     produces={
     *          "application/octet-stream",
     *          "audio/wav",
     *          "application/json"
     *     },
     * @SWG\Parameter(
     *     description="Authorization token",
     *     type="string",
     *     name="Authorization",
     *     in="header"
     * ),
     * @SWG\Parameter(
     *     description="log id/call sid/encrypted log id",
     *     format="int64",
     *     type="integer",
     *     name="call_sid",
     *     in="path",
     *     required=true
     * ),
     * @SWG\Response(
     *     response=200,
     *     description="user response",
     *     @SWG\Schema(ref="#/definitions/CallHistoryRecordingUrl")
     * ),
     * @SWG\Response(
     *     response="403",
     *     description="Not Authorized Missing Authorization header",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     * ),
     * @SWG\Response(
     *     response="401",
     *     description="Access Denied Invalid Authorization header",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     * ),
     * @SWG\Response(
     *     response="404",
     *     description="Not Found",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     * ),
     * @SWG\Response(
     *     response=500,
     *     description="unexpected error",
     *     @SWG\Schema(ref="#/definitions/ErrorResponse")
     * )
     * )
     */

    /**
     * Method Http accept: GET
     *
     * @return Phalcon\Http\Response
     */
    public function getAction(): Phalcon\Http\Response
    {
        $checkOutputFormat = in_array(
            $this->outputFormat,
            ['application/octet-stream', 'audio/wav', 'application/json']
        );
        if (!$checkOutputFormat) {
            $this->outputFormat = 'application/octet-stream';
        }
        $data = [];
        $data['recording_url'] = '';
        $paramID = $this->getParameterId();
        $callHistory = $this->getItem($paramID);

        // stream or download the recording url
        if ($this->outputFormat !== 'application/json') {
            if (!empty($callHistory['recording_url'])) {
                $keyname = ltrim(parse_url($callHistory['recording_url'], PHP_URL_PATH), '/');
                $getfilename = explode('/', $keyname);
                $file_name = end($getfilename);
                $file_parts = pathinfo($file_name);

                $recording_url = $callHistory['recording_url'];
                $headers = get_headers($recording_url, 1);

                if (!isset($file_parts['extension'])) {
                    $extention = $this->getExtenstionFromContentType($headers['Content-Type']);
                    $file_name = $file_name.'.'.$extention;
                } else {
                    $extention = $file_parts['extension'];
                }
                $audio_type = "wav";
                if ($extention === 'mp3') {
                    $audio_type = "mp3";
                }
                $audio_content = ('wav' === $audio_type) ? 'audio/wav' : 'audio/mpeg';

                // Get file size of mp3 with HEAD request
                $size = 1;
                $recording_file = null;

                // Content lenght parameter is mendatory to send to
                // browser in order to determine
                // the audio play length
                if (isset($headers['Content-Length'])) {
                    $size = (int) $headers['Content-Length'];
                } else {
                    // If headers are not sending content length parameter,
                    //  then we do not have any other option
                    // than downloading file itself and calculating size.
                    $recording_file = file_get_contents($recording_url);
                    $size = strlen($recording_file);
                }
                $begin = 0;
                $ending = $size - 1;
                $curl_range = '';

                // In case the file was streaming browser may request file in between
                // for seeking file in middle, where audio file was
                // not downloaded completely
                // In such cases we send the proper content-range in bytes
                if (isset($_SERVER['HTTP_RANGE'])) {
                    if (preg_match(
                        '/bytes=\h*(\d+)-(\d*)[\D.*]?/i',
                        $_SERVER['HTTP_RANGE'],
                        $matches
                    )) {
                        $begin = (int) $matches[1];

                        if ($begin >= $size) {
                            $begin = $size - 1;
                        }

                        if (!empty($matches[2])) {
                            $ending = (int) $matches[2];
                        } else {
                            $ending = $begin + 400000;

                            if ($ending > $size) {
                                $ending = $size - 1;
                            }
                        }
                    }

                    header('HTTP/1.1 206 Partial Content');
                    header('Content-Range: bytes ' . $begin . '-' . $ending . '/' . $size);
                    $size = $ending - $begin + 1;
                    $curl_range = $begin . '-' . $ending;
                }

                header('Content-Type: ' . 'application/octet-stream');
                header('Content-Type: ' . $audio_content);
                header('Content-Length: ' . $size);
                if ($this->outputFormat === 'application/octet-stream') {
                    header('Content-Description: File Transfer');
                    header('Content-Disposition: attachment; filename="' . $file_name . '"');
                }
                header('Cache-Control: public, must-revalidate, max-age=0');
                header('Pragma: no-cache');
                header('Accept-Ranges: bytes');
                header('Content-Transfer-Encoding: binary');

                // Stream the file
                $file_pointer = fopen('php://output', 'w');
                // If we had downloaded file incase where we did
                // not get content length
                // send that file directly instead of fresh new cURL request
                $recordingParams = [
                    'recording_file' => $recording_file,
                    'recording_url' => $recording_url,
                    'curl_range' => $curl_range,
                    'begin' => $begin,
                    'size' => $size,
                ];
                $recordingFile = $this->getRecordingFile($recordingParams);
                fwrite($file_pointer, $recordingFile);
                fflush($file_pointer);
                fclose($file_pointer);
                exit;
            } else {
                echo 'Recording doesnt exist';
                exit;
            }
        }

        // return aws s3 presigned url for recording url.
        if ($this->outputFormat === 'application/json') {
            if (!$this->validSession()) {
                return $this->sendAccessDeniedResponse();
            }

            if (!empty($callHistory['recording_url'])) {
                if (strpos($callHistory['recording_url'], 's3.amazonaws.com') !== false) {
                    $coreClient = ClientFactory::getCoreClient($this->getRequestAuthToken());
                    $get_aws_credentials = $coreClient->postIam('reportExport');

                    $coreClient->deleteSysConfigCache();
                    $get_sysconfig = $coreClient->getSysConfig();

                    $key = $get_aws_credentials->Credentials->AccessKeyId;
                    $secret = $get_aws_credentials->Credentials->SecretAccessKey;
                    $region = $get_sysconfig->recording_url_buckets_region;

                    $bucket = $get_sysconfig->buckets->recordings;
                    $keyname = ltrim(
                        parse_url(
                            $callHistory['recording_url'],
                            PHP_URL_PATH
                        ),
                        '/'
                    );

                    $s3Client = new Aws\S3\S3Client(
                        [
                            "credentials" => [
                                "key" => $key,
                                "secret" => $secret,
                            ],
                            "region" => $region,
                            "version" => 'latest',
                        ]
                    );

                    $cmd = $s3Client->getCommand('GetObject', [
                        'Bucket' => $bucket,
                        'Key'    => $keyname,
                    ]);

                    $request = $s3Client->createPresignedRequest($cmd, '+2 hours');
                    $presignedUrl = (string) $request->getUri();

                    $callHistory['recording_url'] = $presignedUrl;
                    $data['recording_url'] = $presignedUrl;
                } else {
                    $data['recording_url'] = $callHistory['recording_url'];
                }
            } else {
                return $this->sendNotFound();
            }

            if ($data['recording_url']) {
                $this->response->setJsonContent($data);

                return $this->response;
            } else {
                return $this->sendNotFound();
            }
        }
    }

    /**
     * Determine if user has read access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasReadAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has write access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasWriteAccess(array $data): bool
    {
        unset($data);

        return true;
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param array $data array of POST body / query params in key value pair
     *
     * @return bool
     */
    protected function userHasDeleteAccess(array $data): bool
    {
        if ($this->isStaticToken($this->authToken)) {
            return true;
        }
        $accountId = $this->getCurrentAccountId();

        return ($accountId === (int) Util::array_get('account_id', $data));
    }

    /**
     * Determine if user has delete access to the data
     *
     * @param string $contentType Content Type
     *
     * @return string
     */
    private function getExtenstionFromContentType(string $contentType = '') : string
    {
        switch ($contentType) {
            case 'audio/mpeg':
                $extention = "mp3";
                break;
            case 'audio/x-wav':
                $extention = "wav";
                break;
            case 'audio/wav':
                $extention = "wav";
                break;
            case 'audio/mp4':
                $extention = "mp4";
                break;
            default:
                $extention = "wav";
        }

        return $extention;
    }

    /**
     * Get Recording File
     *
     * @param array $params Request Params
     *
     * @return string
     */
    private function getRecordingFile(array $params = []) :string
    {
        if (empty($params['recording_file'])) {
            $curl = curl_init($params['recording_url']);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            if (!empty($params['curl_range'])) {
                curl_setopt($curl, CURLOPT_RANGE, $params['curl_range']);
            }
            $recordingFile = curl_exec($curl);
            curl_close($curl);
        } else {
            $recordingFile = substr($params['recording_file'], $params['begin'], $params['size']);
        }

        return $recordingFile;
    }

    /**
     * Retrive parameter id from arguments.
     *
     * @return string
     */
    private function getParameterId() : string
    {
        $paramID = $this->getParamID();
        $decryptedParamID = Util::simpleDecodeCI($paramID);
        //Call log_id decode from given encrypted log id
        if (!is_numeric($this->getParamID()) &&
            !empty($decryptedParamID) &&
            is_numeric($decryptedParamID)) {
            return $decryptedParamID;
        }

        return $paramID;
    }

    /**
     * Get record from MySql
     *
     * @param mixed $log_id call log Id
     *
     * @return object
     */
    private function getDBCallDetail($log_id)
    {
        $callDetailData = CallDetail::findFirst(
            [
            "conditions" => "log_id = ".$log_id,
            ]
        );

        if ($callDetailData) {
            return $callDetailData->toArray();
        }

        return [];
    }
}
