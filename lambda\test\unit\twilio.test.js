jest.mock('@vendia/serverless-express', () => ({
  getCurrentInvoke: jest.fn().mockReturnValue({
    event: {
      asyncBody: {
        call: {
          call_number: '1234567890',
          call_name: '<PERSON>',
          location_name: 'Location',
          ad_name: 'Ad',
          customer_name: 'Customer',
          customer_type: 'Type',
          twilio_id: 'twilio123',
        },
        duration: 60,
        emails: ['<EMAIL>'],
      },
    },
  }),
}));

jest.mock('../../../lambda/libraries/cpapi-client', () => {
  return {
    emailClient: jest.fn().mockImplementation(() => ({
      postData: jest.fn(),
    })),
  };
});

const request = require('supertest');
const express = require('express');
const router = require('../../../lambda/routes/twillio');
const { emailClient } = require('../../../lambda/libraries/cpapi-client');
const miscellaneous = require('../../config/miscellaneous');

describe('POST /send_email_async', () => {
  let app;
  let mockPostData;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/', router);

    mockPostData = emailClient.mock.instances[0]?.postData || jest.fn();
    emailClient.mockImplementation(() => ({ postData: mockPostData }));
  });

  afterEach(() => {
    jest.clearAllMocks(); // Reset mocks after each test
  });

  it('should send an email using the voicemail email', async () => {
    const response = await request(app)
      .post('/send_email_async')
      .send({});

    // Verify the response
    expect(response.status).toBe(200);

    // Check that the email client was called
    expect(mockPostData).toHaveBeenCalled();

    // Verify the email payload
    expect(mockPostData).toHaveBeenCalledWith('email', expect.objectContaining({
      from: {
        email: miscellaneous.EMAIL_DETAILS.VOICEMAIL_EMAIL,
        name: 'CallPotential',
      },
      to: expect.arrayContaining([{ email: '<EMAIL>', name: '' }]),
      subject: 'Voicemail message',
      body: expect.stringContaining('You have a new Voicemail Message'),
    }));
  });
});