import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { PayByPhoneStateRetryHandler } from "./_Utils";

@Injectable()
export class FinalPayAmountConfirm extends PayByPhoneStateBase {
  @PayByPhoneStateRetryHandler(
    3,
    PayByPhoneState.FinalPayAmountPrompt,
    [PayByPhoneState.FinalPayAmountConfirm],
    PayByPhoneState.TransferToAgent,
    "pay-by-phone.max-retry")
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { twilioResponse } = context;
    const customerResponse = context.request.Digits;
    const noResponseFromCustomer = customerResponse === undefined || customerResponse.length === 0;
    const customerConfirmedPayment = customerResponse && customerResponse === '1';
    const shouldRepeatPrompt = noResponseFromCustomer || !customerConfirmedPayment;
    if (shouldRepeatPrompt) {
      return { nextState: PayByPhoneState.FinalPayAmountPrompt };
    }

    twilioResponse.sayInLocale({
      messageId: 'pay-by-phone.payment-under-process',
      locale: context.storage.locale
    });
    return { nextState: PayByPhoneState.FinalPayAmountSubmitted };
  }
}
