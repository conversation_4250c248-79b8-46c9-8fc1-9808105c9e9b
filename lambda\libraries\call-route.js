"use strict";
var CallLogModel = require('../models/call-log');
var LocationModel = require('../models/location').LocationModel;
var QueueManager = require('./queue-manager').QueueManager;
var SmartRoute = require('./smart-route').SmartRoute;
var Connect = require('./connect').Connect;
var VoiceMail = require('./voicemail').VoiceMail;
const Payment = require('./payment-new').Payment;
var IVR = require('./ivr').IVR;
const Util = require('../routes/utils/twillio')
var _ = require('lodash');
const vsprintf = require("sprintf-js").vsprintf;
const querystring = require('querystring');
const mainconfig = require('../config/config');
const cpapiClient = require('./cpapi-client');
const console_c = require('../config/logger').console;
const AWS = require("aws-sdk");
var twilio = require('twilio');
const locationSettings = require('../config/location_setting/location-settings');
const got = require('got');
const dataDogService = require('./shared/datadog-service');

// eslint-disable-next-line no-useless-escape
const SIP_PATTERN = /[^\:]*\:([0-9]*)@.*/;

var callRoute = class CallRoute {
  constructor(config, req_q_data, req_p_data) {
    this.route_config = null;
    this.is_route_complete = 0;
    this.log_id = null;
    this.call_info = null;
    this.current_step = null;
    this.queue_manager = null;
    this.location_id = null;
    this.req_q_data = req_q_data;
    this.req_p_data = req_p_data;

    this.exit = false;

    if (config) {
      this.log_id = config['log_id'];

      // If it is a location dial call request or (handle voicemail request
      // on hangup)
      // then do not exit inside as recording url and duration of call
      // has to be saved

      if (req_q_data && !req_q_data['location_dial'] && !('handle_message' in req_q_data) && req_p_data['CallStatus'] && req_p_data['CallStatus'] == 'completed') {
        // Call was ended by user
        // Mark the process as complete
        this.exit = true;
      }
    }
  }

  async chk_or_exit_update_call_log(req_q_data, log_id, log_info, req_p_data) {
    await this.log_pbp_end('hangup');
    // With regards to CC-844 For location dial
    // When user disconnects then CallStatus and DialCallStatus is 'completed'
    // But when customer disconnects DialCallStatus is 'completed' but CallStatus is 'in-progress'

    // Get log details from dynamodb and save it to call_logs when call completed
    // var dynamo_log_id = log_info.log_id;
    // delete log_info.log_id;
    // var log_info = data.Item;
    if (req_p_data['DialCallStatus'] && req_p_data['DialCallStatus'] === 'completed' && req_p_data['DialCallDuration'] && req_p_data['RecordingUrl']) {
      log_info.is_route_complete = 1;
      log_info.duration = req_p_data['DialCallDuration'];
      log_info.recording_url = req_p_data['RecordingUrl'];
      log_info.recording_sid = req_p_data['RecordingSid'];

      if (req_q_data && req_q_data['locationTaskSid']) {
        let dynamodb = new AWS.DynamoDB({
          region: process.env.AWS_REGION || 'us-west-2'
        });

        let params = {
          Key: {
            "id": {
              N: log_info['account_id'].toString()
            }
          },
          TableName: mainconfig.dynamodb.acctTable
        };

        try {
          let data = await dynamodb.getItem(params).promise();
          if (data && data.Item) {
            _.forEach(data.Item, function(value, key) {
              data.Item[key] = value[Object.keys(value)[0]];
            });

            let twilioClient = new twilio(data.Item['account_sid'], data.Item['authtoken']);

            await twilioClient.taskrouter.workspaces(data.Item['workspace_sid'])
             .tasks(req_q_data['locationTaskSid'])
             .fetch()
             .then(async (task) => {
                let assignmentStatus = 'canceled';
                if (task.assignmentStatus && task.assignmentStatus === 'assigned') {
                  assignmentStatus = 'completed';
                }
                await twilioClient.taskrouter.workspaces(data.Item['workspace_sid'])
                  .tasks(req_q_data['locationTaskSid'])
                  .update({
                    assignmentStatus: assignmentStatus,
                    reason: 'Call completed'
                  })
                  .catch((e) => {
                    console.error(e, new Error().stack);
                  })
              })
             .catch((e) => {
                console.error(e, new Error().stack);
              })

            let update = _.pick(log_info, ['is_route_complete', 'duration', 'recording_url', 'recording_sid']);
            let update_res = await new CallLogModel()
              .update_dynamodb(_.pick(log_info, ['twilio_id', 'location_id', 'log_id']), update);
            return update_res;
          }
        } catch (e) {
          console.log(e, new Error().stack);
          let update = _.pick(log_info, ['is_route_complete', 'duration', 'recording_url', 'recording_sid']);
          let update_res = await new CallLogModel()
            .update_dynamodb(_.pick(log_info, ['twilio_id', 'location_id', 'log_id']), update);
          return update_res;
        }
      } else {
        // Add identifier that this is recording for external dial to differentiat it from location dial recording
        log_info.recording_url += '?external_connect'
        let update = _.pick(log_info, ['is_route_complete', 'duration', 'recording_url', 'recording_sid']);
        let update_res = await new CallLogModel()
          .update_dynamodb(_.pick(log_info, ['twilio_id', 'location_id', 'log_id']), update);
        return update_res;
      }
    } else {
      if (req_q_data && req_q_data['locationTaskSid']) {
        let dynamodb = new AWS.DynamoDB({
          region: process.env.AWS_REGION || 'us-west-2'
        });

        let params = {
          Key: {
            "id": {
              N: log_info['account_id'].toString()
            }
          },
          TableName: mainconfig.dynamodb.acctTable
        };

        try {
          let data = await dynamodb.getItem(params).promise();
          if (data && data.Item) {
            _.forEach(data.Item, function(value, key) {
              data.Item[key] = value[Object.keys(value)[0]];
            });

            let twilioClient = new twilio(data.Item['account_sid'], data.Item['authtoken']);

            await twilioClient.taskrouter.workspaces(data.Item['workspace_sid'])
             .tasks(req_q_data['locationTaskSid'])
             .fetch()
             .then(async (task) => {
                let assignmentStatus = 'canceled';
                if (task.assignmentStatus && task.assignmentStatus === 'assigned') {
                  assignmentStatus = 'completed';
                }
                await twilioClient.taskrouter.workspaces(data.Item['workspace_sid'])
                 .tasks(req_q_data['locationTaskSid'])
                 .update({
                    assignmentStatus: assignmentStatus,
                    reason: 'Call moved to next step'
                  })
                 .catch((e) => {
                    console.error(e, new Error().stack);
                  })
              })
             .catch((e) => {
                console.error(e, new Error().stack);
              })

            log_info.is_route_complete = 1;
            let update = _.pick(log_info, ['is_route_complete']);
            let update_res = await new CallLogModel()
              .update_dynamodb(_.pick(log_info, ['twilio_id', 'location_id', 'log_id']), update);
            return update_res;
          }
        } catch (e) {
          console.log(e, new Error().stack)
          log_info.is_route_complete = 1;
          let update = _.pick(log_info, ['is_route_complete']);
          let update_res = await new CallLogModel()
            .update_dynamodb(_.pick(log_info, ['twilio_id', 'location_id', 'log_id']), update);
          return update_res;
        }
      } else {
        log_info.is_route_complete = 1;
        let update = _.pick(log_info, ['is_route_complete']);
        let update_res = await new CallLogModel()
          .update_dynamodb(_.pick(log_info, ['twilio_id', 'location_id', 'log_id']), update);
        return update_res;
      }
    }
  }

  async set_call_info(log_id, log_info) {
    console.debug('set_call_info start');
    this.call_info = log_info;

    this.call_info.parent_user_id = log_info.account_id;
    this.call_info.twilio_call_sid = log_info.twilio_id;

    // If location greeting is not set (mp3 or text) then set record=1
    if (!(this.call_info['greeting_type'] == 1 && this.call_info['greeting_mp3']) && !this.call_info['greeting']) {
      this.call_info['record'] = 1;
    }

    var TrackingNumberModel = require('../models/twilliocommon').TrackingNumberModel;
    let trackingNumber = await new TrackingNumberModel().get_tracking_number(log_info.call_name.replace('+', ''), log_info.account_id);
    if (trackingNumber && trackingNumber.record === 0) {
      this.call_info['record'] = 0;
    }

    var dynamodb = new AWS.DynamoDB({
      region: process.env.AWS_REGION || 'us-west-2'
    });

    var params = {
      Key: {
        "id": {
          N: log_info['account_id'].toString()
        }
      },
      TableName: mainconfig.dynamodb.acctTable
    };
    try {
      let data = await dynamodb.getItem(params).promise();
      if (data && data.Item) {
        _.forEach(data.Item, function(value, key) {
          data.Item[key] = value[Object.keys(value)[0]];
        });
        this.call_info['workspace_sid'] = data.Item['workspace_sid'];
        console_c.log("# Account Data #", JSON.stringify(data.Item));
        if (data.Item['voice_workflow']) {
          this.call_info['workflow_sid'] = data.Item['voice_workflow'];
        }
        if (data.Item['payment_workflow_sid']) {
          this.call_info['payment_workflow'] = data.Item['payment_workflow_sid'];
        }
        this.call_info['sid'] = data.Item['account_sid'];
        this.call_info['authtoken'] = data.Item['authtoken'];
      }

      if (!this.queue_manager) {
        this.queue_manager = new QueueManager({
          'account_sid': this.call_info['sid'],
          'auth_token': this.call_info['authtoken'],
          'workspace_sid': this.call_info['workspace_sid'],
          'workflow_sid': this.call_info['workflow_sid'],
          'account_id': log_info['account_id']
        });
      }
      this.queue_manager.log_detail = log_info;
      this.queue_manager.log_id = log_id;

      const acctClient = new cpapiClient.acctClient(mainconfig.db.serviceTokens.readWrite);
      const locClient = new cpapiClient.locClient(mainconfig.db.serviceTokens.readWrite);
      const ads = await acctClient.cache.getData(`ad/${this.call_info.ad_id}`);
      if (ads && ads.ad_name) {
        this.call_info['ad_name'] = ads.ad_name
      }

      let locData = await locClient.getLocation(this.call_info.location_id);

      if (locData && locData.location_name) {
        this.call_info['location_name'] = locData.location_name;
        this.call_info['primary_sip_setup'] = locData.primary_sip_setup;
      }

      this.call_info['queue_manager'] = this.queue_manager;
      console.debug('set_call_info end');
      return this.call_info;
    } catch (e) {
      console.error(e, new Error().stack)
      return this.call_info;
    }
  }

  /**
   * Returns the child step of current executed step
   *
   * @access public
   * @param  array $current_step
   * @return array
   */
  get_child_step(current_step) {
    // For some steps, this check is not required
    // like in case of root_node, there will always be a next
    // one and only child step
    if (current_step && current_step['children'] && current_step['children'].length > 0) {
      return current_step['children'][0];
    }

    return null;
  }

  /**
   * Processed the child step and returns the only child step from
   * (which is after) the current executed step
   *
   * @access public
   * @param  array $current_step
   * @return array
   */
  async process_child_step(current_step) {
    var self = this;
    var child_step = self.get_child_step(current_step);
    if (child_step) {
      // console_c.log("# child_step #", child_step);
      let step_res = await self.process_current_step(child_step);
      return step_res;
    } else {
      return {};
    }
  }

  /**
   * Finds step in route.
   *
   * @access public
   * @param1 array  $route_config
   * @param2 string $uuid
   * @return mixed
   */
  find_step(route_config, uuid) {
    var self = this;

    if (route_config['uuid'] == uuid) {
      return route_config;
    }

    var config = '';

    _.each(route_config['children'], function(child) {
      // If config found, return it, else search in next child element.
      // CP-2930.
      config = self.find_step(child, uuid);
      if (config) {
        return false;
      }
    });

    return config;

  }

  async process_current_step(current_step) {
    var self = this;
    if (current_step) {
      self.queue_manager.current_step = current_step;

      if (current_step['type']) {
        console.debug("Processing route step:", current_step['type']);
        switch (current_step['type']) {
          case 'smart_route': {
            var customer_types = [];
            var valid_customer_types = ['include_lead',
              'include_customer_current', 'include_customer_delinquent',
              'include_other'
            ];

            for (var cust_type in current_step) {
              var cust_val = current_step[cust_type];
              if (valid_customer_types.indexOf(cust_type) !== -1 && cust_val['checked']) {
                customer_types.push(cust_type);
              }
            }

            var smart_route = new SmartRoute({
              'location_id': self.call_info['location_id'],
              'name': current_step['name']['value'],
              'customer_types': customer_types,
              'log_id': self.log_id
            });
            var customer_type = self.call_info['customer_type'];
            console_c.log("# customer_type 1#", customer_type);
            customer_type = smart_route.cust_type_map[customer_type];

            // Array variable to store child step for selected
            // customer type in the route config smart route
            // customer type list
            var selected_child_step = null;

            // Select particular customer type execution branch
            // to proceed as the next step
            // Should always match one of the connector values
            console_c.log("# customer_type 2#", customer_type, "#", current_step['children'].length);
            var other_cust_child_step = null;
            for (var i = 0; i < current_step['children'].length; i++) {
              var child_step = current_step['children'][i];
              // console_c.log("# child_step #", i, "#", child_step['connect_type']['selected']);
              if (child_step['connector_label'] == 'other') {
                other_cust_child_step = child_step;
              }

              if (child_step['connector_label'] == customer_type) {
                // Proceed to the next step
                selected_child_step = child_step;
                break;
              }
            }
            // console_c.log("# other_cust_child_step #", other_cust_child_step);
            // If no match is found and children array is not empty,
            // then proceed with the "other" child step
            if (!selected_child_step && other_cust_child_step) {
              selected_child_step = other_cust_child_step;
            }
            // console_c.log("# selected_child_step #", selected_child_step);
            let step_res = await self.process_current_step(selected_child_step);
            return step_res;
          }
          case 'route_connect': {
            console_c.log("# self.req_p_data['DialCallStatus'] #", self.req_p_data['DialCallStatus']);
            if (self.req_p_data['DialCallStatus']) {
              if (self.req_q_data['location_dial']) {
                // So that it can be used only once
                // and not on subsequent dial calls
                delete self.req_q_data['location_dial'];
              }

              if (self.req_p_data['DialCallStatus'] != 'completed') {
                // Unset the request variable DialCallStatus
                // since if there are consequeunt dial opertaions
                // the same request parameter will be used
                // and it will be bypassed
                delete self.req_p_data['DialCallStatus'];

                // Proceed to the next step
                // due to call busy, hang up etc.
                let step_res = await self.process_child_step(current_step);
                return step_res;
              } else {

                // Update the recording_url and duration of call
                // and mark the call route process to be complete
                // In case of location webhook enabled callback, Duration is
                // present instead of DialCallDuration.
                var update = {
                  'is_route_complete': 1,
                  'duration': self.req_p_data['DialCallDuration'] ? self.req_p_data['DialCallDuration'] : self.req_p_data['Duration']
                };

                if (0 == update['duration']) {
                  update['call_type'] = 'inbound_noanswer';
                }

                // If recording is enabled
                if (self.req_p_data['RecordingUrl']) {
                  update['recording_url'] = self.req_p_data['RecordingUrl'] + '?external_connect';
                  update['recording_sid'] = self.req_p_data['RecordingSid'];
                }

                // update dynamodb
                await new CallLogModel().update_dynamodb(_.pick(self.call_info,
                  ['twilio_id', 'location_id', 'log_id']), update);

                self.is_route_complete = 1;
                return self.get_child_step(current_step);
              }
            } else {
              var connect = new Connect(self.req_q_data, self.req_p_data);
              let arr_step_res = await connect.process_step(current_step, self.call_info);
              if (arr_step_res && arr_step_res.type === 'exit') {
                return current_step;
              }

              var result = arr_step_res ? arr_step_res.result : null;
              // If connect call step was skipped
              if (result === false) {
                // Proceed to the next step
                let step_res = await self.process_child_step(current_step);
                return step_res;
              } else if (typeof result === "object") {
                // If current step was modified, return it
                return result;
              } else {
                // null means nothing returned so process current step
                // result holds 'continue' in some case
                // Return the current step since it is still in
                // processing
                return current_step;
              }
            }
          }
          case 'voicemail': {
            var config = {
              'log_id': self.log_id,
              'name': current_step['name']['value'],
              'emails': current_step['additional_email']['value'],
              'send_to_location': current_step['send_to_location']['checked'],
              'uploaded_mp3_url': current_step['mp3_file']['value'],
              'queue_manager': self.queue_manager,
              'skip_outside_businesshr': current_step['skip_outside_businesshr']['checked']
            };
            var voicemail = new VoiceMail(config, self.req_q_data, self.req_p_data);
            var voicemail_result = await voicemail.process_voicemail_step(config, self.call_info);

            // console_c.log("# voicemail_result #", voicemail_result);
            // If connect call step was skipped
            if (voicemail_result === false) {
              // Proceed to the next step
              let step_res = await self.process_child_step(current_step);
              return step_res;
            } else if ('handle_message' in self.req_q_data) {
              // console_c.log("# in handle message #");
              let is_complete = await voicemail.handle_message(self.call_info);
              if (is_complete) {
                self.is_route_complete = 1;
              }
              return self.get_child_step(current_step);
            } else {
              try {
                let voicemailLabel = current_step['name']['value'] ?? '';
                await new CallLogModel().update_dynamodb(_.pick(self.call_info, ['twilio_id', 'location_id']),{'is_voicemail': voicemailLabel});
              } catch (e) {
                console.error('ERROR while updating the call log for inbound_autopay', e, new Error().stack);
              }

              voicemail.record_message();
              return current_step;
            }
          }
          case 'ivr': {
            var IVR_obj = new IVR({
              'log_id': self.log_id,
              'name': current_step['name']['value'],
              'extensions': current_step['extensions']['active_extensions'],
              'uploaded_mp3_url': current_step['mp3_file']['value'],
              'queue_manager': self.queue_manager
            }, self.req_q_data, self.req_p_data);
            var customer_input = IVR_obj.get_customer_input(self.log_id);

            if (customer_input) {
              // Proceed with the child step which matches
              // the pressed input digit
              let step_res = false;
              for (let child_step of current_step['children']) { 
                if (child_step['connector_label'] == customer_input) {
                  step_res = await self.process_current_step(child_step)
                }
              }

              if (step_res) {
                return step_res;
              }

              if (current_step['loopback'] &&
                current_step['loopback'][customer_input]) {
                // Get parent loopback step from db

                const route_config_id = JSON.parse(self.call_info['route_config']);
                const locModel = new LocationModel();
                let call_number = self.call_info['call_name'];
                if (_.includes(call_number, 'sip')) {
                  var to_match = SIP_PATTERN.exec(call_number);
                  call_number = '+' + to_match[1];
                }
                let destination_data = await locModel.get_inbound_route_info(call_number.replace('+', ''), self.call_info['account_id'], route_config_id);
                let route_configs = destination_data['callRouteConfig'];

                await Util.parseCallRouteVariables(route_configs, self.call_info['location_id'], self.call_info['account_id'], destination_data);

                const complete_route_config = JSON.parse(route_configs.config);
                child_step = self.find_step(complete_route_config, current_step['loopback'][customer_input]);
                let step_res = await self.process_current_step(child_step);
                return step_res;
              } else {
                console_c.log('CPBUG-351: process_current_step no child step found for input', customer_input);
                return {};
              }
            } else {
              // The current step is still in processing state
              return current_step;
            }
          }
          case 'root_node': {
            // Since root node will have only one child
            let step_res = await self.process_child_step(current_step);
            return step_res;
          }
          case 'payment': {
            let get_data = self.req_q_data;

            try {
              await new CallLogModel().update_dynamodb(_.pick(self.call_info, ['twilio_id', 'location_id']),{'call_type': 'inbound_autopay'});
            } catch (e) {
              console.error('ERROR while updating the call log for inbound_autopay', e, new Error().stack);
            }

            // Check for manager connect
            if (get_data.newPbpManagerConnect == 1) {
              let step_res = false;
              for (let child_step of current_step['children']) { 
                if ('other' === child_step['connector_label']) {
                  step_res = await self.process_current_step(child_step)
                }
              }

              if (step_res) {
                return step_res;
              }
              return current_step;
            }
            
            //check for payment success or failure from new pbp
            if (get_data.PaymentState) {
              //go through a success or failure connector if configured
              for (let child_step of current_step['children']) { 
                if (get_data.PaymentState === child_step['connector_label']) {
                  return await self.process_current_step(child_step);
                }
              }

              //fallback to disconnect call if no connectors are configured
              self.queue_manager.twilio_response.hangup();
              self.queue_manager.is_output_set = true;
              return current_step;
            }

            //check if we should redirect to new pbp
            const isPbpRefactorFeatureEnabled = await locationSettings.getLocationSetting('CollectionsManager::PayByPhone::UseBeta', self.call_info.account_id, self.call_info.location_id);
            const hasExistingPbpSession = await Payment.hasExistingPbpSession(self.req_p_data.CallSid);
            const shouldUsePbpRefactor = isPbpRefactorFeatureEnabled && !hasExistingPbpSession;
            if(shouldUsePbpRefactor)
            {
              let override_locale = current_step.locale && current_step.locale.selected ? current_step.locale.selected : current_step['language']['selected'] ?? 'en';
              try {
                let route_utils = require('../routes/utils/twillio');
                let dialing_code = await route_utils.get_dialing_code(self.call_info.location_id);
                let call_number = route_utils.remove_dialing_code(self.req_p_data.From, dialing_code);
                const url = mainconfig.comms_api_url + 'pay-by-phone';
                let get_data_with_payment_process = {...get_data, payment_process: 2};
                let query_string = querystring.stringify(get_data_with_payment_process, '&', '=');
                const postBody = {
                  CallSid: self.req_p_data.CallSid,
                  LocationId: self.call_info.location_id,
                  OverrideLocale: override_locale,
                  From: call_number,
                  TransferToAgentUrl: mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?newPbpManagerConnect=1&' + query_string,
                  PaymentSuccessRedirectUrl: mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?PaymentState=success&" + query_string,
                  PaymentFailureRedirectUrl: mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?PaymentState=failure&" + query_string,
                  Origin: 'callroute',
                };
                const options = { 
                  body: JSON.stringify(postBody),
                  headers: {
                    'Content-Type': 'application/json'
                  }
                 };
                const response = await got.post(url, options);
                console.log('new pbp initiated successfully:', response.body);
                self.queue_manager.override_twilio_response = response.body;
                return current_step;
              } catch (error) {
                console.error('Error posting data to new pbp endpoint:', error);
              }
            }

            let step_language = current_step['language']['selected'] ? current_step['language']['selected'] : 'en';

            let lang_mod;
            if (step_language === 'en') {
              lang_mod = require('../config/language/en').lang;
            } else {
              lang_mod = require('../config/language/es').lang;
            }

            // Initiate Payment process
            let payment = await Payment.build(self.call_info, current_step, self.req_q_data, self.req_p_data);

            if (payment.language == 'es') {
              payment.language = 'es-MX';
            }
            let langSelect = {'language': payment.language};


            if ((payment.digits == 0 && get_data.managerConnect == 1) || get_data.redirectOther == 1 || payment.pbpData.attemptCount > 4) {
              // console.log('pbp return current_step');
              await this.log_pbp_end('manager');
              if (payment.pbpData.attemptCount > 4) {
                self.queue_manager.twilio_response.say(lang_mod['call_route_max_tries'], langSelect);
              }

              let step_res = false;
              for (let child_step of current_step['children']) { 
                if ('other' === child_step['connector_label']) {
                  step_res = await self.process_current_step(child_step)
                }
              }

              if (step_res) {
                return step_res;
              }
              return current_step;
            }

            delete get_data.managerConnect;
            let query_string = querystring.stringify(get_data, '&', '=');

            // console.log('payState', payment.payState, payment.paySubState);

            const invalidManualNumberInput = payment.paySubState == 'manualNumberSearch' && (!payment.digits || (payment.digits && payment.digits.length != 10));
            const shouldManualNumberSearch = payment.paySubState == 'manualNumberSearch' && payment.digits && payment.digits.length == 10;
            const shouldRetryManualNumberPrompt = invalidManualNumberInput || (get_data.retryManualNumberPrompt == 1 && !shouldManualNumberSearch);
            switch (payment.payState) {

              case 'customerSearch':

                if (payment.paySubState == 'numberSearch') {

                  if (payment.playGreeting && payment.greetingMp3) {
                    self.queue_manager.twilio_response.play(payment.greetingMp3);
                    self.queue_manager.twilio_response.redirect(
                      mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?payment_process=1");
                    self.queue_manager.is_output_set = true;
                    return current_step;
                  }

                  await payment.executeAsync();

                  for (let i = 0; i < 5; i++) {
                    self.queue_manager.twilio_response.say(
                      lang_mod['call_route_wait_for_account_fetch'], langSelect);

                    self.queue_manager.twilio_response.pause({
                      'length': 10
                    });
                  }

                  self.queue_manager.is_output_set = true;
                  return current_step;
                }

                if (payment.paySubState == 'manualNumberPrompt' || shouldRetryManualNumberPrompt) {
                  await payment.updatePbpData({'paySubState' : 'manualNumberSearch'});

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 10,
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?managerConnect=1&' + query_string
                  })

                  if(!invalidManualNumberInput)
                  {
                    gather.say(lang_mod['call_route_account_not_found'], langSelect);
                  }
                  if (!payment.number_valid) {
                    gather.say(lang_mod['call_route_10_digit'], langSelect);
                  }
                  gather.pause({
                    'length': 3
                  });

                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);
                  const url_option = get_data.retryManualNumberPrompt == 1 ? '?redirectOther=1&' : '?retryManualNumberPrompt=1&';
                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + url_option + query_string);

                  self.queue_manager.is_output_set = true;
                  return current_step;
                }

                if (payment.paySubState == 'manualNumberSearch') {

                  await payment.updatePbpData({'fromNo' : payment.digits, 'paySubState' : 'numberSearch'});

                  await payment.executeAsync();

                  for (let i = 0; i < 5; i++) {
                    self.queue_manager.twilio_response.say(
                      lang_mod['call_route_wait_for_account_fetch'], langSelect);

                    self.queue_manager.twilio_response.pause({
                      'length': 10
                    });
                  }

                  self.queue_manager.is_output_set = true;
                  return current_step;
                }
                break;

              case 'customerFound':

                if (payment.paySubState == 'askUnitSelect') {

                  await payment.updatePbpData({'paySubState' : 'confirmUnitSelect'});

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 1,
                    'timeout': 10,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string
                  });

                  gather.say(lang_mod['call_route_multiple_accounts'], langSelect);
                  gather.say(lang_mod['call_route_pay_all'], langSelect);

                  payment.pbpData.unitArray.forEach((unitItem, index) => {
                    if (unitItem != null && typeof unitItem.ledgerId !== 'undefined') {
                      gather.say(vsprintf(lang_mod['call_route_customer_verify'], 
                        [
                          index,
                          unitItem['name'],
                          unitItem['unitNumber']
                        ]), langSelect);
                      }

                  });

                  gather.say(lang_mod['call_route_start_over'], langSelect);

                  gather.pause({'length': 3});
                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'confirmUnitSelect') {

                  if (['2', '3', '4', '5', '6', '7', '8'].includes(payment.digits)) {
                    await payment.updatePbpData({'paySubState' : 'confirmLedger', 'unitSelected' : payment.digits});
                  } else if (payment.digits == 1) {
                    let totalDue = payment.pbpData.unitArray
                      .map(item => (item == null) ? 0 : item.amountDue)
                      .filter(amt => amt > 0)
                      .reduce((total, amt) => { return parseFloat(total) + parseFloat(amt)}, 0);

                    await payment.updatePbpData({
                      'paySubState' : 'confirmLedger',
                      'unitSelected' : payment.digits,
                      'totalAmount' : totalDue
                    });
                  } else {
                    await payment.updatePbpData({'paySubState' : 'askUnitSelect'});
                  }

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'confirmLedger') {

                  let gather_string;
                  let gather;

                  await payment.updatePbpData({
                    'esTenantId' : payment.units_found['esTenantId'],
                    'ledgerId' : payment.units_found['ledgerId'],
                    'tenantId' : payment.units_found['tenantId'],
                    'prepayMonth' : 0,
                    'unitId' : payment.units_found['esUnitId']
                  });

                  await payment.generatePaymentHash(payment.units_found['tenantId'], payment.units_found['ledgerId']);

                  gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 1,
                    'timeout': 5,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?managerConnect=1&' + query_string
                  });

                  if (payment.pbpData.unitSelected && payment.pbpData.unitSelected == 1) {
                    if (payment.pbpData.allowPrepay && payment.pbpData.allowPrepay == true) {
                      await payment.updatePbpData({'paySubState' : 'askPrepay'});
                    } else {
                      await payment.updatePbpData({'paySubState' : 'confirmPayAmount'});
                    }
                    let totalAmount = parseFloat(payment.pbpData.totalAmount);
                    if (totalAmount > 0 && payment.pbpData.convenienceFeeAmount != null) {
                      totalAmount = parseFloat(totalAmount) + parseFloat(payment.pbpData.convenienceFeeAmount);
                    }
                    gather_string = lang_mod['call_route_total_balance'];
                    gather.say(vsprintf(gather_string, 
                      [
                        parseFloat(totalAmount).toFixed(2)
                      ]), langSelect);
                  } else {
                    if (payment.units_found['ledgerStatus'] == 'Current') {
                      await payment.updatePbpData({'paySubState' : 'askPrepay'});
                    } else {
                      await payment.updatePbpData({'paySubState' : 'confirmPayAmount'});
                    }
                    let amountDue = parseFloat(payment.units_found['amountDue']);
                    if (amountDue > 0 && payment.pbpData.convenienceFeeAmount != null) {
                      amountDue = parseFloat(amountDue) + parseFloat(payment.pbpData.convenienceFeeAmount);
                    }
                    gather_string = lang_mod['call_route_account_found'];
                    gather.say(vsprintf(gather_string, 
                      [
                        payment.units_found['name'], 
                        payment.units_found['unitNumber'], 
                        parseFloat(amountDue).toFixed(2),
                        payment.units_found['dueDateFormatted']
                      ]), langSelect);
                  }

                  gather.pause({'length': 3});
                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'payByPhoneNotAllowed') {

                  await payment.updatePbpData({'paySubState' : 'numberSearch', 'payState' : 'customerSearch'});

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 10,
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?managerConnect=1&' + query_string
                  })

                  let gather_string = lang_mod['call_route_not_allowed_speak_manager'];
                  gather.say(gather_string, langSelect);

                  gather.pause({
                    'length': 3
                  });

                  self.queue_manager.is_output_set = true;
                  return current_step;
                }

                if (payment.paySubState == 'askPrepay') {
                  let attemptCount = 1;
                  if (payment.pbpData.attemptCount) {
                    attemptCount = payment.pbpData.attemptCount+1;
                  }
                  await payment.updatePbpData({'attemptCount' : attemptCount, 'paySubState' : 'waitForPrepayFetch'});

                  let gather_string = lang_mod['call_route_ask_prepay'];

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 1,
                    'timeout': 5,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?' + query_string
                  });
                  gather.say(gather_string, langSelect);
                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'waitForPrepayFetch') {

                  if (payment.digits) {
                    await payment.updatePbpData({
                      'esTenantId' : payment.units_found['esTenantId'],
                      'ledgerId' : payment.units_found['ledgerId'],
                      'tenantId' : payment.units_found['tenantId'],
                      'prepayMonth' : payment.digits,
                      'unitId' : payment.units_found['esUnitId'],
                      'attemptCount' : 0,
                    });

                    await payment.executeAsync();

                    for (let i = 0; i < 5; i++) {
                      self.queue_manager.twilio_response.say(
                        lang_mod['call_route_wait_for_account_fetch'], langSelect);

                      self.queue_manager.twilio_response.pause({
                        'length': 10
                      });
                    }
                  } else {
                    await payment.updatePbpData({'paySubState' : 'askPrepay'});
                    self.queue_manager.twilio_response.redirect(
                      mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                  }
                }

                if (payment.paySubState == 'confirmaskPrepay') {
                  await payment.updatePbpData({'paySubState' : 'confirmPayAmount'});

                  var gather_string = lang_mod['call_route_prepay_selection'];

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 1,
                    'timeout': 5,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?' + query_string
                  });
                  let prepayAmount = parseFloat(payment.pbpData.prepayAmount);
                  if (prepayAmount > 0 && payment.pbpData.convenienceFeeAmount != null) {
                    prepayAmount = parseFloat(prepayAmount) + parseFloat(payment.pbpData.convenienceFeeAmount);
                  }
                  gather.say(vsprintf(gather_string, [payment.pbpData.prepayMonth, parseFloat(prepayAmount).toFixed(2)]), langSelect);
                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'confirmPayAmount') {
                  if (payment.pbpData.prepayMonth && payment.pbpData.prepayMonth > 0) {
                    if (payment.digits == 1) {
                      await payment.updatePbpData({
                        'payState' : 'gathercardDetails',
                        'paySubState' : 'checkSavedCard',
                        'totalAmount' : payment.pbpData.prepayAmount
                      });
                      let prepayAmount = parseFloat(payment.pbpData.prepayAmount);
                      if (prepayAmount > 0 && payment.pbpData.convenienceFeeAmount != null) {
                        prepayAmount = parseFloat(prepayAmount) + parseFloat(payment.pbpData.convenienceFeeAmount);
                      }
                      self.queue_manager.twilio_response.say(
                        vsprintf(lang_mod['call_route_charge_confirm'], [parseFloat(prepayAmount).toFixed(2)]), langSelect);
                    } else {
                      await payment.updatePbpData({'paySubState' : 'askPrepay'});
                    }
                  } else {

                    let totalAmount = payment.units_found['amountDue'];
                    if (payment.pbpData.unitSelected && payment.pbpData.unitSelected == 1) {
                      totalAmount = payment.pbpData.totalAmount;
                    }

                    if (totalAmount == 0) {
                      await payment.updatePbpData({'paySubState' : 'askPrepay'});
                      self.queue_manager.twilio_response.say(lang_mod['call_route_ask_prepay_current_customer'], langSelect);
                    } else {
                      await payment.updatePbpData({
                          'payState' : 'gathercardDetails',
                          'paySubState' : 'checkSavedCard',
                          'totalAmount' : totalAmount
                        });
                      self.queue_manager.twilio_response.say(
                        vsprintf(lang_mod['call_route_charge_confirm'], [parseFloat(totalAmount).toFixed(2)]), langSelect);
                    }
                  }

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                self.queue_manager.is_output_set = true;

                return current_step;

              case 'gathercardDetails':
                if (payment.paySubState == 'checkSavedCard') {

                  if (!payment.pbpData.savedCard) {
                    await payment.updatePbpData({'paySubState' : 'askCC'});

                    let query_string = querystring.stringify(get_data, '&', '=');
                    self.queue_manager.twilio_response.redirect(`${mainconfig.call_url}twilio/process_next_step/${self.log_id}?${query_string}`);
                    self.queue_manager.is_output_set = true;

                    return current_step;
                  }

                  await payment.updatePbpData({'paySubState' : 'confirmSavedCard'});

                  let gather_string = lang_mod['call_route_ask_saved_cc'];

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 4,
                    'timeout': 5,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?' + query_string
                  });

                  gather.say(gather_string, langSelect);
                  gather.pause({'length': 3});

                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);
                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                  self.queue_manager.is_output_set = true;

                  return current_step;
                }

                if (payment.paySubState == 'confirmSavedCard') {
                  let digits = '';
                  if (typeof payment.digits != 'undefined') {
                     digits = payment.digits.replace('*', '');
                  }

                  let cardStatus = await payment.verifySavedCard(digits);

                  if (!cardStatus) {

                    if (digits > 0) {
                      self.queue_manager.twilio_response.say(lang_mod['call_route_card_not_found'], langSelect);
                    }

                    await payment.updatePbpData({'paySubState' : 'askCC', 'useSavedCard' : false});
                  } else {
                    await payment.updatePbpData({'paySubState' : 'askCVC', 'useSavedCard' : true});
                  }

                  let query_string = querystring.stringify(get_data, '&', '=');
                  self.queue_manager.twilio_response.redirect(`${mainconfig.call_url}twilio/process_next_step/${self.log_id}?${query_string}`);
                  self.queue_manager.is_output_set = true;

                  return current_step;
                }

                if (['repeatCC', 'repeatExpiry', 'repeatCVC', 'repeatZip'].includes(payment.paySubState)) {

                  let cardDigits = '';
                  let cardInputValid = '';
                  let taskUpdateObj = {};

                  if (typeof payment.digits != 'undefined') {
                     cardDigits = payment.digits.replace('*', '');
                  }

                  let validateCC = require("card-validator");
                  if (payment.paySubState == 'repeatCC') {
                    cardInputValid = validateCC.number(cardDigits).isValid;
                    taskUpdateObj = {
                      'paySubState' : payment.paySubState.replace('repeat', 'ask'),
                      'invalidCard' : true,
                    };
                  }

                  if (payment.paySubState == 'repeatExpiry') {
                    cardInputValid = validateCC.expirationDate(cardDigits).isValid;
                    taskUpdateObj = {
                      'paySubState' : payment.paySubState.replace('repeat', 'ask'),
                      'invalidExpiry' : true,
                    };
                  }

                  if (payment.paySubState == 'repeatCVC') {
                    cardInputValid = false;
                    const cvvLength = [3,4];
                    if (!isNaN(cardDigits) && cvvLength.includes(cardDigits.length)) {
                        cardInputValid = true;
                    }
                    taskUpdateObj = {
                      'paySubState' : payment.paySubState.replace('repeat', 'ask'),
                      'invalidCvv' : true,
                    };
                  }

                  if (payment.paySubState == 'repeatZip') {
                    cardInputValid = validateCC.postalCode(cardDigits).isValid;
                    taskUpdateObj = {
                      'paySubState' : payment.paySubState.replace('repeat', 'ask'),
                      'invalidZip' : true,
                    };
                  }

                  if (!cardInputValid || !cardDigits) {
                    await payment.updatePbpData(taskUpdateObj);

                    let query_string = querystring.stringify(get_data, '&', '=');
                    self.queue_manager.twilio_response.redirect(`${mainconfig.call_url}twilio/process_next_step/${self.log_id}?${query_string}`);
                    self.queue_manager.is_output_set = true;

                    return current_step;
                  } else {
                    await payment.updatePbpData({'attemptCount' : 0});
                  }

                  let gather_string = lang_mod['call_route_num_confirm'];

                  let query_string = querystring.stringify(get_data, '&', '=');

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 1,
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?' + query_string
                  });

                  let digits = cardDigits.replace(/(\d)/g, "$1 ");
                  gather.say(vsprintf(gather_string, [digits]), langSelect);
                  gather.pause({'length': 3});

                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);
                  self.queue_manager.twilio_response.redirect(`${mainconfig.call_url}twilio/process_next_step/${self.log_id}?${query_string}`);
                  self.queue_manager.is_output_set = true;

                  await payment.saveCCData(payment.paySubState, cardDigits);
                  await payment.updatePbpData({'paySubState' : `confirm${payment.paySubState}`});

                  return current_step;
                }

                if (['confirmrepeatCC', 'confirmrepeatExpiry', 'confirmrepeatCVC', 'confirmrepeatZip'].includes(payment.paySubState)) {
                  if (payment.digits == 1) {

                    let confirmMap = {
                      confirmrepeatCC     : 'askExpiry',
                      confirmrepeatExpiry : 'askCVC',
                      confirmrepeatCVC    : 'askZip',
                      confirmrepeatZip    : 'processPayment',
                    }

                    await payment.updatePbpData({'paySubState' : confirmMap[payment.paySubState]});
                  } else {
                    await payment.updatePbpData({'paySubState' : payment.paySubState.replace('confirm', '').replace('repeat', 'ask')});
                  }

                  let query_string = querystring.stringify(get_data, '&', '=');
                  self.queue_manager.twilio_response.redirect(`${mainconfig.call_url}twilio/process_next_step/${self.log_id}?${query_string}`);
                  self.queue_manager.is_output_set = true;

                  return current_step;
                }

                if (['askCC', 'askExpiry', 'askCVC', 'askZip'].includes(payment.paySubState)) {
                  let attemptCount = 1;
                  if (payment.pbpData.attemptCount) {
                    attemptCount = payment.pbpData.attemptCount+1;
                  }
                  await payment.updatePbpData({'attemptCount' : attemptCount});
                  let gather_string = '';

                  switch (payment.paySubState) {
                    case 'askCC':

                      if (payment.pbpData.invalidCard) {
                        let totalAmount = payment.units_found['amountDue'];
                        if (payment.pbpData.unitSelected && payment.pbpData.unitSelected == 1) {
                          totalAmount = payment.pbpData.totalAmount;
                        }
                        if (totalAmount == 0) {
                          totalAmount = payment.pbpData.prepayAmount;
                        }
                        gather_string = lang_mod['call_route_wrong_input_cc'] + ' ';
                        gather_string += vsprintf(lang_mod['call_route_charge_confirm'], [parseFloat(totalAmount).toFixed(2)]) + ' ';
                      }

                      await payment.updatePbpData({'savedCard' : false , 'invalidCard' : false});
                      gather_string += lang_mod['call_route_enter_cc'];

                      await payment.updatePbpData({'paySubState' : 'repeatCC'});
                      break;

                    case 'askExpiry':
                      if (payment.pbpData.invalidExpiry) {
                        gather_string = lang_mod['call_route_wrong_input_date'] + ' ';
                      }

                      gather_string += lang_mod['call_route_enter_expiry'];

                      await payment.updatePbpData({'paySubState' : 'repeatExpiry', 'invalidExpiry' : false});
                      break;

                    case 'askCVC':
                      if (payment.pbpData.invalidCvv) {
                        gather_string = lang_mod['call_route_wrong_input_cvc'] + ' ';
                      }

                      gather_string += lang_mod['call_route_enter_ccv'];

                      await payment.updatePbpData({'paySubState' : 'repeatCVC', 'invalidCvv' : false});
                      break;

                    case 'askZip':
                      if (payment.pbpData.invalidZip) {
                        gather_string = lang_mod['call_route_invalid_input'] + ' ';
                      }

                      gather_string += lang_mod['call_route_enter_zip'];

                      await payment.updatePbpData({'paySubState' : 'repeatZip', 'invalidZip' : false});
                      break;
                  }

                  let query_string = querystring.stringify(get_data, '&', '=');

                  var gather = self.queue_manager.twilio_response.gather({
                    'finishOnKey': '*',
                    'timeout': 10,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?' + query_string
                  });

                  gather.say(gather_string, langSelect);

                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);
                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                  self.queue_manager.is_output_set = true;

                  return current_step;
                }

                if (payment.paySubState == 'processPayment') {
                  await payment.updatePbpData({'payState' : 'processPayment', 'paySubState' : 'askFinalPayment'});

                  let query_string = querystring.stringify(get_data, '&', '=');
                  self.queue_manager.twilio_response.redirect(`${mainconfig.call_url}twilio/process_next_step/${self.log_id}?${query_string}`);
                  self.queue_manager.is_output_set = true;

                  return current_step;
                }
                break;

              case 'processPayment':

                if (payment.paySubState == 'askFinalPayment') {
                  let attemptCount = 1;
                  if (payment.pbpData.attemptCount) {
                    attemptCount = payment.pbpData.attemptCount+1;
                  }
                  await payment.updatePbpData({'attemptCount' : attemptCount, 'paySubState' : 'confirmFinalPayment'});

                  let gather = self.queue_manager.twilio_response.gather({
                    'numDigits': 1,
                    'timeout': 5,
                    'method': 'POST',
                    'action': mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + '?' + query_string
                  });

                  gather.say(lang_mod['call_route_confirm_payment'], langSelect);
                  self.queue_manager.twilio_response.say(lang_mod['call_route_missing_response'], langSelect);

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'confirmFinalPayment') {
                  if (payment.digits == 1) {
                    await payment.updatePbpData({'attemptCount' : 0, 'paySubState' : 'makePayment'});
                  } else {
                    await payment.updatePbpData({'paySubState' : 'askFinalPayment'});
                  }

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);
                }

                if (payment.paySubState == 'makePayment') {

                  await payment.executeAsync();

                  for (let i = 0; i < 5; i++) {
                    self.queue_manager.twilio_response.say(
                      lang_mod['call_route_under_process'], langSelect);

                    self.queue_manager.twilio_response.pause({
                      'length': 10
                    });
                  }
                }

                self.queue_manager.is_output_set = true;

                return current_step;

              case 'paymentStatus': {

                if (typeof payment.pbpData.paymentMessage === 'undefined') {
                  await payment.updatePbpData({'paymentMessage' : true});

                  if (payment.paySubState == 'success') {
                    self.queue_manager.twilio_response.say(lang_mod['call_route_payment_success'], langSelect);
                  } else {
                    self.queue_manager.twilio_response.say(lang_mod['call_route_payment_fail'], langSelect);
                  }

                  const wasSuccessfulPayment = payment.paySubState == 'success';
                  const durationTags = wasSuccessfulPayment ? [`payment:success`] : [];
                  await this.log_pbp_end('exit', durationTags);

                  self.queue_manager.twilio_response.redirect(
                    mainconfig.call_url + 'twilio/process_next_step/' + self.log_id + "?" + query_string);

                  self.queue_manager.is_output_set = true;

                  return current_step;
                }

                let step_res = false;
                for (let child_step of current_step['children']) { 
                  if (payment.paySubState === child_step['connector_label']) {
                    step_res = await self.process_current_step(child_step);
                    return step_res;
                  }
                }

                if (step_res) {
                  return step_res;
                }
                return current_step;
              }
            }

            break;
          }
        }
        // switch case end
      }
    } else {
      return current_step;
    }
  }

  async get_next_step(req_q_data, req_p_data) {

    var self = this;
    const config = require('../config/config');
    let callbacktoggle = 1;
    const mccClient = new cpapiClient.mccClient(config.db.serviceTokens.readWrite);
    let accountData = await mccClient.cache.getData(`callcenter/${self.call_info.account_id}`);
    callbacktoggle = (typeof accountData.callback_toggle !== "undefined") ? accountData.callback_toggle : 1;

    if (req_q_data['leave_queue'] && !self.current_step && self.is_route_complete) {
      // Process leave queue request when there is no further step
      // to be executed and the call route is complete
      self.queue_manager.log_id = self.log_id;
      self.queue_manager.log_detail = self.call_info;
      await self.queue_manager.leave_queue(req_p_data);
      return decodeURIComponent(self.queue_manager.twilio_response.toString());

    } else if (req_p_data['Digits'] && req_p_data['Digits'] == 8 && req_q_data['wait_in_queue'] && req_q_data['wait_in_queue'] == 1 && callbacktoggle == 1) {
      // This is to process callback request from customer
      // on inbound call and if valid number.

      let route_utils = require('../routes/utils/twillio');

      let dialing_code = await route_utils.get_dialing_code(self.call_info['location_id']);
      let call_number = route_utils.remove_dialing_code(self.call_info['call_number'], dialing_code);
      let valid_number = (call_number.length === 10);

      if (valid_number) {
          self.queue_manager.log_id = self.log_id;
          req_p_data['callback_request'] = true;
          req_p_data['self_call_info'] = self.call_info;
          await self.queue_manager.leave_queue(req_p_data);
          return decodeURIComponent(self.queue_manager.twilio_response.toString())
      } else {
          let mp3_url = mainconfig.CP_CDN_URL + 'uploads/voice_broadcast/mp3/cannot_callback.mp3';
          self.queue_manager.twilio_response.play(mp3_url);
          return decodeURIComponent(self.queue_manager.twilio_response.toString())
      }

    } else {
      // Process current step if not empty
      // One case where the stored step is empty and will be processed
      // is when the empty array is returned by smart_route step in
      // process_current_step when no customer type match is found

      if ((Array.isArray(self.current_step) && self.current_step.length > 0) || (!Array.isArray(self.current_step) && self.current_step)) {
        // if (self.current_step) {
        // console_c.log("# inside  self.current_step #", self.current_step);
        // Process the current step and store the next current step to be
        // processed in future on getting request for same
        console_c.log('CPBUG-351: get_next_step before process_current_step', self.current_step);
        let current_step = await self.process_current_step(self.current_step);
        console_c.log('CPBUG-351: get_next_step after process_current_step', current_step);
        
        if (req_q_data['locationTaskSid']) {
          let reason = 'Call moved to next step';
          if ((current_step && current_step.type && current_step.type === 'exit') || self.is_route_complete) {
            reason = 'Call completed';
          }

          let twilioClient = new twilio(self.call_info['sid'], self.call_info['authtoken']);

          await twilioClient.taskrouter.workspaces(self.call_info['workspace_sid'])
           .tasks(req_q_data['locationTaskSid'])
           .fetch()
           .then(async (task) => {
              let assignmentStatus = 'canceled';
              if (task.assignmentStatus && task.assignmentStatus === 'assigned') {
                assignmentStatus = 'completed';
              }
              await twilioClient.taskrouter.workspaces(self.call_info['workspace_sid'])
               .tasks(req_q_data['locationTaskSid'])
               .update({
                  assignmentStatus: assignmentStatus,
                  reason: reason
                })
               .catch((e) => {
                  console.error(e, new Error().stack);
                })
            })
           .catch((e) => {
              console.error(e, new Error().stack);
            })
        }

        if (current_step && current_step.type && current_step.type === 'exit') {
          return decodeURIComponent(self.queue_manager.twilio_response.toString());
        } else {
          self.current_step = current_step;
        }
      }

      if (!self.current_step) {
        self.current_step = [];
      }

      // Update current_route_step for the call
      let update = {
        'current_route_step': JSON.stringify(self.current_step)
      };
      if (self.current_step.length === 0) {
        console_c.log('CPBUG-351: get_next_step current_route_step is empty', self.current_step, self.call_info, self.log_id);
      }
      let cond_para_list = ['twilio_id', 'location_id'];

      // If call route process is complete, then hangup the call
      if (self.is_route_complete) {
        self.queue_manager.twilio_response.hangup();
        self.queue_manager.is_output_set = 1;
      }

      if (update.is_route_complete) {
        update['is_route_complete'] = 1;
        cond_para_list.push('log_id');
      }

      await new CallLogModel().update_dynamodb(_.pick(self.call_info, cond_para_list), update);

      //allow new pbp to override twilio response
      const twilioResponseString = self.queue_manager.override_twilio_response ?? self.queue_manager.twilio_response.toString();
      self.queue_manager.override_twilio_response = null; //one time use
      return decodeURIComponent(twilioResponseString);
    }
  }

  async log_pbp_end(reason, tags = []) {
    const pbpData = await Payment.getPbpDataFromRedis(this.req_p_data.CallSid);
    const hasPbpData = pbpData && Object.keys(pbpData).length !== 0;
    if(!hasPbpData) return;
    
    const parsedPbpData = JSON.parse(pbpData);
    const startTime = parsedPbpData['startTime'];
    const locale = parsedPbpData['locale'];
    if(startTime !== undefined)
    {
      const totalDuration = Date.now() - startTime;
      dataDogService.recordDistribution('old_pay_by_phone.session.duration', totalDuration, tags);
    }
    dataDogService.incrementCounter('old_pay_by_phone.session.total', [
      `locale:${locale}`,
      `end_reason:${reason}`
    ]);
    
  }
};

module.exports = {
  'CallRoute': callRoute
}
