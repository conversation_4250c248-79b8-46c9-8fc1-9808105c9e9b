import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodSecurityCodeConfirm extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. If they have confirmed the security code
     * 1a. Transition to PayMethodPostalCodePrompt
     * 2. If they reject, they want to re-enter the number
     * 2a. transition to PayMethodSecurityCodePrompt
     */

    const { request } = context;

    const confirmationSelection = request.Digits;

    if (confirmationSelection === '1') {
      const locationDetails =
        await this.services.locationService.getLocationDetails(
          context.storage.locationId
        );

      if (locationDetails.country?.toLowerCase() !== 'us') {
        return { nextState: PayByPhoneState.FinalPayAmountPrompt };
      }

      return { nextState: PayByPhoneState.PayMethodPostalCodePrompt };
    } else {
      return { nextState: PayByPhoneState.PayMethodSecurityCodePrompt };
    }
  }
}
