import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { UnitsToPayPrepayMonthsPrompt } from './UnitsToPayPrepayMonthsPrompt';
import { Locale } from '@cp-workspace/shared';

describe('UnitsToPayPrepayMonthsPrompt', () => {
  let unitsToPayPrepayMonthsPrompt: UnitsToPayPrepayMonthsPrompt;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UnitsToPayPrepayMonthsPrompt],
    }).compile();

    unitsToPayPrepayMonthsPrompt = module.get<UnitsToPayPrepayMonthsPrompt>(UnitsToPayPrepayMonthsPrompt);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.UnitsToPayPrepayMonthsPrompt,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should prompt user to enter prepay months and gatherWithLocaleSay response', async () => {
      const response: PayByPhoneStateHandlerResponse = await unitsToPayPrepayMonthsPrompt.handler(context);

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
        numDigits: 1,
        method: 'POST',
        timeout: 10
      }, [{
        messageId: 'pay-by-phone.ask-prepay',
        locale: context.storage.locale
      }]);
      expect(response.nextState).toBe(PayByPhoneState.UnitsToPayPrepayMonthsSelection);
    });
  });
});
