<?php
namespace Test;

class CallDetailTest extends IntegrationCase
{
    public function testCallDetailGeyByIdAction()
    {
        $this->validateSwaggerPath(
            'call',
            'CalldetailGetById',
            '/calldetail/{id}',
            'get'
        );
        $response = $this->runGET('/calldetail/1');
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/calldetail/1 GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'CalldetailGetById', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'CalldetailGetById', [200, 404, 500]);
        
    }

    public function testCallDetailListWithoutPagination()
    {
        $this->validateSwaggerPath(
            'call',
            'ListCallDetails',
            '/calldetail',
            'get'
        );

        $params = $this->getListParams(1);
        $response = $this->runGET('/calldetail', true, $params);
        $responseCode = $response->getStatusCode();
        $responseData = json_decode($response->getBody(), true);
        $this->assertEquals(200, $responseCode,
            '/calldetail GET response code is not 200.  Actual response: '.$responseCode
        );
        $this->validateResponse('call', 'ListCallDetails', $responseCode, $responseData);
        $this->verifyExpectedResponses('call', 'ListCallDetails', [200,401,403,500]);
    }

    /**
     * @param array $defaultValues
     * @return mixed
     */
    protected function getPostData($defaultValues = []) : array
    {
        // TODO: Implement getPostData() method.
    }

    /**
     * @param array $newValues
     * @return mixed
     */
    protected function getPutData($newValues = []) : array
    {
        // TODO: Implement getPutData() method.
    }

    /**
     * @param array $defaultValues
     * @param string $requestType
     * @param int $responseCode
     * @return mixed
     */
    protected function getRequestData($defaultValues = [], $requestType = 'post', $responseCode = null) : array
    {
        // TODO: Implement getRequestData() method.
    }

    /**
     * @param integer $case
     * @return array
     */
    private function getListParams($case = 0) :array
    {
        switch ($case) {
            case 1:
                return [
                    'startDate' => '2016-01-01',
                    'endDate' => date('Y-m-d')
                ];
                break;
            default:
                return [];
                break;
        }
    }
}
