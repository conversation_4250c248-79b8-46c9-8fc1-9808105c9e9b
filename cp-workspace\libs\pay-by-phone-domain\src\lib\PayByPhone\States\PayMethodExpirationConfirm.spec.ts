import { PayMethodExpirationConfirm } from './PayMethodExpirationConfirm';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodExpirationConfirm', () => {
  let service: PayMethodExpirationConfirm;

  beforeEach(() => {
    service = new PayMethodExpirationConfirm();
  });

  it('should transition to PayMethodSecurityCodePrompt when confirmation selection is 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodSecurityCodePrompt);
  });

  it('should transition to PayMethodExpirationPrompt when confirmation selection is not 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '2',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodExpirationPrompt);
  });
});
