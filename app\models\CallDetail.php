<?php
/**
 * CallDetail model
 *
 * @category CallDetail
 * @package  Cpapi-call
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://www.callpotential.com/
 */

/**
 * CallDetail model
 *
 * @category CallDetail
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.gnu.org/copyleft/gpl.html GNU General Public License
 * @link     http://callpotential.com/
 *
 * @SWG\Definition(definition="CallDetail")
 */
class CallDetail extends \CallPotential\CPCommon\RestModel
{
    use CallPotential\CPCommon\JsonModelTrait;

    /**
     * Log Id
     *
     * @var int
     *
     * @Primary
     * @Identity
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $log_id;

    /**
     * Fk lead Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $fk_lead_id;

    /**
     * Twilio Id
     *
     * @var string
     *
     * @Column(type="string", length=256, nullable=true)
     * @SWG\Property()
     */
    protected $twilio_id;

    /**
     * User Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $user_id;

    /**
     * Recording SID
     *
     * @var string
     *
     * @Column(type="string", length=34, nullable=true)
     * @SWG\Property()
     */
    protected $recording_sid;

    /**
     * Call number
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $call_number;

    /**
     * Caller name
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $caller_name;

    /**
     * Call name
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $call_name;

    /**
     * Datestamp
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $datestamp;

    /**
     * Call type
     *
     * @var string
     *
     * @Column(type="string", length=100, nullable=true)
     * @SWG\Property()
     */
    protected $call_type;

    /**
     * Call processed by
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $call_processed_by;

    /**
     * Recording URL
     *
     * @var string
     *
     * @Column(type="string", length=512, nullable=true)
     * @SWG\Property()
     */
    protected $recording_url;

    /**
     * Duration
     *
     * @var int
     *
     * @Column(type="integer", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $duration;

    /**
     * Call destination
     *
     * @var string
     *
     * @Column(type="string", length=255, nullable=true)
     * @SWG\Property()
     */
    protected $call_destination;

    /**
     * Rollover index
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $rollover_index;

    /**
     * Answered by
     *
     * @var string
     *
     * @Column(type="string", length=15, nullable=true)
     * @SWG\Property()
     */
    protected $answered_by;

    /**
     * Ad Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $ad_id;

    /**
     * Location Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $location_id;

    /**
     * Employee Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $employee_id;

    /**
     * Grade
     *
     * @var string
     *
     * @Column(type="string", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $grade;

    /**
     * Halloffame
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $halloffame;

    /**
     * Gradesheet
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $gradesheet;

    /**
     * Gradesheet Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $gradesheet_id;

    /**
     * Customer card
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $customer_card;

    /**
     * Call status
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $call_status;

    /**
     * Gradesheet points appointed
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $gradesheet_points_appointed;

    /**
     * Gradesheet points possible
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $gradesheet_points_possible;

    /**
     * Manager score
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $manager_score;

    /**
     * Confirm action
     *
     * @var string
     *
     * @Column(type="string", length=10, nullable=true)
     * @SWG\Property()
     */
    protected $confirm_action;

    /**
     * Confirmed by
     *
     * @var int
     *
     * @Column(type="integer", length=11, nullable=true)
     * @SWG\Property()
     */
    protected $confirmed_by;

    /**
     * Is excluded
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_excluded;

    /**
     * Route config
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $route_config;

    /**
     * Current route step
     *
     * @var string
     *
     * @Column(type="string", nullable=true)
     * @SWG\Property()
     */
    protected $current_route_step;

    /**
     * Is route complete
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_route_complete;

    /**
     * Is assigned
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_assigned;

    /**
     * Customer type
     *
     * @var string
     *
     * @Column(type="string", length=30, nullable=true)
     * @SWG\Property()
     */
    protected $customer_type;

    /**
     * Customer name
     *
     * @var string
     *
     * @Column(type="string", length=200, nullable=true)
     * @SWG\Property()
     */
    protected $customer_name;

    /**
     * Customer Id
     *
     * @var string
     *
     * @Column(type="string", length=50, nullable=true)
     * @SWG\Property()
     */
    protected $customer_id;

    /**
     * Is auto call
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $is_auto_call;

    /**
     * Neighbor location Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $neighbor_location_id;

    /**
     * Version Id
     *
     * @var int
     *
     * @Column(type="integer", length=20, nullable=true)
     * @SWG\Property()
     */
    protected $version_id;

    /**
     * Processed
     *
     * @var int
     *
     * @Column(type="integer", length=1, nullable=true)
     * @SWG\Property()
     */
    protected $processed;

    /**
     * Is voicemail
     *
     * @var string
     *
     * @Column(type="string", length=256, nullable=true)
     * @SWG\Property()
     */
    protected $is_voicemail;

    /**
     * Channel
     *
     * @var string
     *
     * @Column(type="string", length=25, nullable=true)
     * @SWG\Property()
     */
    protected $channel;


    /**
     * Method to set the value of field log_id
     *
     * @param integer $log_id value to set
     *
     * @return $this
     */
    public function setLogId(int $log_id)
    {
        $this->log_id = $log_id;

        return $this;
    }

    /**
     * Method to set the value of field fk_lead_id
     *
     * @param integer $fk_lead_id value to set
     *
     * @return $this
     */
    public function setFkLeadId(int $fk_lead_id)
    {
        $this->fk_lead_id = $fk_lead_id;

        return $this;
    }

    /**
     * Method to set the value of field twilio_id
     *
     * @param mixed $twilio_id value to set
     *
     * @return $this
     */
    public function setTwilioId($twilio_id)
    {
        $this->twilio_id = $twilio_id;

        return $this;
    }

    /**
     * Method to set the value of field user_id
     *
     * @param integer $user_id value to set
     *
     * @return $this
     */
    public function setUserId($user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    /**
     * Method to set the value of field recording_sid
     *
     * @param string $recording_sid value to set
     *
     * @return $this
     */
    public function setRecordingSid(string $recording_sid)
    {
        $this->recording_sid = $recording_sid;

        return $this;
    }

    /**
     * Method to set the value of field call_number
     *
     * @param string $call_number value to set
     *
     * @return $this
     */
    public function setCallNumber(string $call_number)
    {
        $this->call_number = $call_number;

        return $this;
    }

    /**
     * Method to set the value of field caller_name
     *
     * @param string $caller_name value to set
     *
     * @return $this
     */
    public function setCallerName($caller_name)
    {
        $this->caller_name = $caller_name;

        return $this;
    }

    /**
     * Method to set the value of field call_name
     *
     * @param string $call_name value to set
     *
     * @return $this
     */
    public function setCallName(string $call_name)
    {
        $this->call_name = $call_name;

        return $this;
    }

    /**
     * Method to set the value of field datestamp
     *
     * @param string $datestamp value to set
     *
     * @return $this
     */
    public function setDatestamp(string $datestamp)
    {
        $this->datestamp = $datestamp;

        return $this;
    }

    /**
     * Method to set the value of field call_type
     *
     * @param string|null $call_type value to set
     *
     * @return $this
     */
    public function setCallType($call_type)
    {
        $this->call_type = $call_type;

        return $this;
    }

    /**
     * Method to set the value of field call_processed_by
     *
     * @param string|null $call_processed_by value to set
     *
     * @return $this
     */
    public function setCallProcessedBy($call_processed_by)
    {
        $this->call_processed_by = $call_processed_by;

        return $this;
    }

    /**
     * Method to set the value of field recording_url
     *
     * @param string|null $recording_url value to set
     *
     * @return $this
     */
    public function setRecordingUrl($recording_url)
    {
        $this->recording_url = $recording_url;

        return $this;
    }

    /**
     * Method to set the value of field duration
     *
     * @param integer $duration value to set
     *
     * @return $this
     */
    public function setDuration(int $duration)
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * Method to set the value of field call_destination
     *
     * @param string $call_destination value to set
     *
     * @return $this
     */
    public function setCallDestination(string $call_destination)
    {
        $this->call_destination = $call_destination;

        return $this;
    }

    /**
     * Method to set the value of field rollover_index
     *
     * @param integer $rollover_index value to set
     *
     * @return $this
     */
    public function setRolloverIndex(int $rollover_index)
    {
        $this->rollover_index = $rollover_index;

        return $this;
    }

    /**
     * Method to set the value of field answered_by
     *
     * @param string|null $answered_by value to set
     *
     * @return $this
     */
    public function setAnsweredBy($answered_by)
    {
        $this->answered_by = $answered_by;

        return $this;
    }

    /**
     * Method to set the value of field ad_id
     *
     * @param integer $ad_id value to set
     *
     * @return $this
     */
    public function setAdId(int $ad_id)
    {
        $this->ad_id = $ad_id;

        return $this;
    }

    /**
     * Method to set the value of field location_id
     *
     * @param integer $location_id value to set
     *
     * @return $this
     */
    public function setLocationId(int $location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * Method to set the value of field employee_id
     *
     * @param integer $employee_id value to set
     *
     * @return $this
     */
    public function setEmployeeId(int $employee_id)
    {
        $this->employee_id = $employee_id;

        return $this;
    }

    /**
     * Method to set the value of field grade
     *
     * @param string|null $grade value to set
     *
     * @return $this
     */
    public function setGrade($grade)
    {
        $this->grade = $grade;

        return $this;
    }

    /**
     * Method to set the value of field halloffame
     *
     * @param integer|null $halloffame value to set
     *
     * @return $this
     */
    public function setHalloffame($halloffame)
    {
        $this->halloffame = $halloffame;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet
     *
     * @param string $gradesheet value to set
     *
     * @return $this
     */
    public function setGradesheet(string $gradesheet)
    {
        $this->gradesheet = $gradesheet;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_id
     *
     * @param integer|null $gradesheet_id value to set
     *
     * @return $this
     */
    public function setGradesheetId($gradesheet_id)
    {
        $this->gradesheet_id = $gradesheet_id;

        return $this;
    }

    /**
     * Method to set the value of field customer_card
     *
     * @param string|null $customer_card value to set
     *
     * @return $this
     */
    public function setCustomerCard($customer_card)
    {
        $this->customer_card = $customer_card;

        return $this;
    }

    /**
     * Method to set the value of field call_status
     *
     * @param string|null $call_status value to set
     *
     * @return $this
     */
    public function setCallStatus($call_status)
    {
        $this->call_status = $call_status;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_points_appointed
     *
     * @param integer $gradesheet_points_appointed value to set
     *
     * @return $this
     */
    public function setGradesheetPointsAppointed(int $gradesheet_points_appointed)
    {
        $this->gradesheet_points_appointed = $gradesheet_points_appointed;

        return $this;
    }

    /**
     * Method to set the value of field gradesheet_points_possible
     *
     * @param integer $gradesheet_points_possible value to set
     *
     * @return $this
     */
    public function setGradesheetPointsPossible(int $gradesheet_points_possible)
    {
        $this->gradesheet_points_possible = $gradesheet_points_possible;

        return $this;
    }

    /**
     * Method to set the value of field manager_score
     *
     * @param integer $manager_score value to set
     *
     * @return $this
     */
    public function setManagerScore(int $manager_score)
    {
        $this->manager_score = $manager_score;

        return $this;
    }

    /**
     * Method to set the value of field confirm_action
     *
     * @param string $confirm_action value to set
     *
     * @return $this
     */
    public function setConfirmAction(string $confirm_action)
    {
        $this->confirm_action = $confirm_action;

        return $this;
    }

    /**
     * Method to set the value of field confirmed_by
     *
     * @param integer $confirmed_by value to set
     *
     * @return $this
     */
    public function setConfirmedBy(int $confirmed_by)
    {
        $this->confirmed_by = $confirmed_by;

        return $this;
    }

    /**
     * Method to set the value of field is_excluded
     *
     * @param integer|null $is_excluded value to set
     *
     * @return $this
     */
    public function setIsExcluded($is_excluded)
    {
        $this->is_excluded = $is_excluded;

        return $this;
    }

    /**
     * Method to set the value of field route_config
     *
     * @param string $route_config value to set
     *
     * @return $this
     */
    public function setRouteConfig(string $route_config)
    {
        $this->route_config = $route_config;

        return $this;
    }

    /**
     * Method to set the value of field current_route_step
     *
     * @param string $current_route_step value to set
     *
     * @return $this
     */
    public function setCurrentRouteStep(string $current_route_step)
    {
        $this->current_route_step = $current_route_step;

        return $this;
    }

    /**
     * Method to set the value of field is_route_complete
     *
     * @param integer $is_route_complete value to set
     *
     * @return $this
     */
    public function setIsRouteComplete(int $is_route_complete)
    {
        $this->is_route_complete = $is_route_complete;

        return $this;
    }

    /**
     * Method to set the value of field is_assigned
     *
     * @param integer $is_assigned value to set
     *
     * @return $this
     */
    public function setIsAssigned(int $is_assigned)
    {
        $this->is_assigned = $is_assigned;

        return $this;
    }

    /**
     * Method to set the value of field customer_type
     *
     * @param string|null $customer_type value to set
     *
     * @return $this
     */
    public function setCustomerType($customer_type)
    {
        $this->customer_type = $customer_type;

        return $this;
    }

    /**
     * Method to set the value of field customer_name
     *
     * @param string|null $customer_name value to set
     *
     * @return $this
     */
    public function setCustomerName($customer_name)
    {
        $this->customer_name = $customer_name;

        return $this;
    }

    /**
     * Method to set the value of field customer_id
     *
     * @param string|null $customer_id value to set
     *
     * @return $this
     */
    public function setCustomerId($customer_id)
    {
        $this->customer_id = $customer_id;

        return $this;
    }

    /**
     * Method to set the value of field is_auto_call
     *
     * @param integer $is_auto_call value to set
     *
     * @return $this
     */
    public function setIsAutoCall(int $is_auto_call)
    {
        $this->is_auto_call = $is_auto_call;

        return $this;
    }

    /**
     * Method to set the value of field neighbor_location_id
     *
     * @param integer|null $neighbor_location_id value to set
     *
     * @return $this
     */
    public function setNeighborLocationId($neighbor_location_id)
    {
        $this->neighbor_location_id = $neighbor_location_id;

        return $this;
    }

    /**
     * Method to set the value of field version_id
     *
     * @param integer $version_id value to set
     *
     * @return $this
     */
    public function setVersionId($version_id)
    {
        $this->version_id = $version_id;

        return $this;
    }

    /**
     * Method to set the value of field processed
     *
     * @param integer $processed value to set
     *
     * @return $this
     */
    public function setProcessed($processed)
    {
        $this->processed = $processed;

        return $this;
    }

    /**
     * Method to set the value of field is_voicemail
     *
     * @param string $is_voicemail value to set
     *
     * @return $this
     */
    public function setIsVoicemail($is_voicemail)
    {
        $this->is_voicemail = $is_voicemail;

        return $this;
    }

    /**
     * Returns the value of field log_id
     *
     * @return integer
     */
    public function getLogId(): int
    {
        return $this->log_id;
    }

    /**
     * Returns the value of field fk_lead_id
     *
     * @return integer
     */
    public function getFkLeadId()
    {
        return $this->fk_lead_id;
    }

    /**
     * Returns the value of field twilio_id
     *
     * @return string
     */
    public function getTwilioId()
    {
        return $this->twilio_id;
    }

    /**
     * Returns the value of field user_id
     *
     * @return integer
     */
    public function getUserId()
    {
        return $this->user_id;
    }

    /**
     * Returns the value of field recording_sid
     *
     * @return string
     */
    public function getRecordingSid()
    {
        return $this->recording_sid;
    }

    /**
     * Returns the value of field call_number
     *
     * @return string
     */
    public function getCallNumber()
    {
        return $this->call_number;
    }

    /**
     * Returns the value of field caller_name
     *
     * @return string
     */
    public function getCallerName()
    {
        return $this->caller_name;
    }

    /**
     * Returns the value of field call_name
     *
     * @return string
     */
    public function getCallName()
    {
        return $this->call_name;
    }

    /**
     * Returns the value of field datestamp
     *
     * @return string
     */
    public function getDatestamp()
    {
        return $this->datestamp;
    }

    /**
     * Returns the value of field call_type
     *
     * @return string
     */
    public function getCallType()
    {
        return $this->call_type;
    }

    /**
     * Returns the value of field call_processed_by
     *
     * @return string
     */
    public function getCallProcessedBy()
    {
        return $this->call_processed_by;
    }

    /**
     * Returns the value of field recording_url
     *
     * @return string
     */
    public function getRecordingUrl()
    {
        return $this->recording_url;
    }

    /**
     * Returns the value of field duration
     *
     * @return integer
     */
    public function getDuration(): int
    {
        return $this->duration;
    }

    /**
     * Returns the value of field call_destination
     *
     * @return string
     */
    public function getCallDestination()
    {
        return $this->call_destination;
    }

    /**
     * Returns the value of field rollover_index
     *
     * @return integer
     */
    public function getRolloverIndex(): int
    {
        return $this->rollover_index;
    }

    /**
     * Returns the value of field answered_by
     *
     * @return string
     */
    public function getAnsweredBy()
    {
        return $this->answered_by;
    }

    /**
     * Returns the value of field ad_id
     *
     * @return integer
     */
    public function getAdId(): int
    {
        return $this->ad_id;
    }

    /**
     * Returns the value of field location_id
     *
     * @return integer
     */
    public function getLocationId(): int
    {
        return $this->location_id;
    }

    /**
     * Returns the value of field employee_id
     *
     * @return integer
     */
    public function getEmployeeId(): int
    {
        return $this->employee_id;
    }

    /**
     * Returns the value of field grade
     *
     * @return string
     */
    public function getGrade()
    {
        return $this->grade;
    }

    /**
     * Returns the value of field halloffame
     *
     * @return integer
     */
    public function getHalloffame()
    {
        return $this->halloffame;
    }

    /**
     * Returns the value of field gradesheet
     *
     * @return string
     */
    public function getGradesheet()
    {
        return $this->gradesheet;
    }

    /**
     * Returns the value of field gradesheet_id
     *
     * @return integer
     */
    public function getGradesheetId()
    {
        return $this->gradesheet_id;
    }

    /**
     * Returns the value of field customer_card
     *
     * @return string
     */
    public function getCustomerCard()
    {
        return $this->customer_card;
    }

    /**
     * Returns the value of field call_status
     *
     * @return string
     */
    public function getCallStatus()
    {
        return $this->call_status;
    }

    /**
     * Returns the value of field gradesheet_points_appointed
     *
     * @return integer
     */
    public function getGradesheetPointsAppointed(): int
    {
        return $this->gradesheet_points_appointed;
    }

    /**
     * Returns the value of field gradesheet_points_possible
     *
     * @return integer
     */
    public function getGradesheetPointsPossible(): int
    {
        return $this->gradesheet_points_possible;
    }

    /**
     * Returns the value of field manager_score
     *
     * @return integer
     */
    public function getManagerScore()
    {
        return $this->manager_score;
    }

    /**
     * Returns the value of field confirm_action
     *
     * @return string
     */
    public function getConfirmAction()
    {
        return $this->confirm_action;
    }

    /**
     * Returns the value of field confirmed_by
     *
     * @return integer
     */
    public function getConfirmedBy()
    {
        return $this->confirmed_by;
    }

    /**
     * Returns the value of field is_excluded
     *
     * @return integer
     */
    public function getIsExcluded()
    {
        return $this->is_excluded;
    }

    /**
     * Returns the value of field route_config
     *
     * @return string
     */
    public function getRouteConfig()
    {
        return $this->route_config;
    }

    /**
     * Returns the value of field current_route_step
     *
     * @return string
     */
    public function getCurrentRouteStep()
    {
        return $this->current_route_step;
    }

    /**
     * Returns the value of field is_route_complete
     *
     * @return integer
     */
    public function getIsRouteComplete(): int
    {
        return $this->is_route_complete;
    }

    /**
     * Returns the value of field is_assigned
     *
     * @return integer
     */
    public function getIsAssigned(): int
    {
        return $this->is_assigned;
    }

    /**
     * Returns the value of field customer_type
     *
     * @return string
     */
    public function getCustomerType(): string
    {
        return $this->customer_type;
    }

    /**
     * Returns the value of field customer_name
     *
     * @return string
     */
    public function getCustomerName()
    {
        return $this->customer_name;
    }

    /**
     * Returns the value of field customer_id
     *
     * @return string
     */
    public function getCustomerId()
    {
        return $this->customer_id;
    }

    /**
     * Returns the value of field is_auto_call
     *
     * @return integer
     */
    public function getIsAutoCall(): int
    {
        return $this->is_auto_call;
    }

    /**
     * Returns the value of field neighbor_location_id
     *
     * @return integer
     */
    public function getNeighborLocationId()
    {
        return $this->neighbor_location_id;
    }

    /**
     * Returns the value of field version_id
     *
     * @return integer
     */
    public function getVersionId()
    {
        return $this->version_id;
    }

    /**
     * Returns the value of field processed
     *
     * @return integer
     */
    public function getProcessed()
    {
        return $this->processed;
    }

    /**
     * Returns the value of field is_voicemail
     *
     * @return string
     */
    public function getIsVoicemail()
    {
        return $this->is_voicemail;
    }

    /**
     * Initialize method for model.
     *
     * @return void
     */
    public function initialize()
    {
        $this->setConnectionService('dbLegacy');
        $this->setSource("call_logs");
    }

    /**
     * Allows to query a set of records that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallLogs[]|CallLogs
     */
    public static function find($parameters = null): Phalcon\Mvc\Model\ResultsetInterface
    {
        return parent::find($parameters);
    }

    /**
     * Allows to query the first record that match the specified conditions
     *
     * @param mixed $parameters parameters to match with records
     *
     * @return CallLogs
     */
    public static function findFirst($parameters = null)
    {
        return parent::findFirst($parameters);
    }
}
