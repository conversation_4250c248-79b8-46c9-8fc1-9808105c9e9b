{"AWSTemplateFormatVersion": "2010-09-09", "Description": "AWS Serverless Express.", "Parameters": {"AwsServerlessExpressS3Bucket": {"Type": "String", "Description": "The S3 bucket in which the lambda function code is stored. Bucket names are region-unique, so you must change this."}, "LambdaFunctionS3Key": {"Type": "String", "AllowedPattern": ".*\\.zip", "Description": "The S3 object for the lambda function code package.", "Default": "lambda-function-gateway-twillio.zip"}, "ApiGatewaySwaggerS3Key": {"Type": "String", "AllowedPattern": ".*\\.yaml", "Description": "The S3 object for the swagger definition of the API Gateway API.", "Default": "simple-proxy-api.yaml"}}, "Resources": {"ApiGatewayApi": {"Type": "AWS::ApiGateway::RestApi", "Properties": {"Description": "AWS Serverless Express API", "BodyS3Location": {"Bucket": {"Ref": "AwsServerlessExpressS3Bucket"}, "Key": {"Ref": "ApiGatewaySwaggerS3Key"}}}}, "ApiGatewayApiDeployment": {"Type": "AWS::ApiGateway::Deployment", "Properties": {"RestApiId": {"Ref": "ApiGatewayApi"}, "StageName": "prod"}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": {"Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}}, "Path": "/", "Policies": [{"PolicyName": "root", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface"], "Resource": "arn:aws:logs:*:*:*"}]}}]}}, "LambdaApiGatewayExecutionPermission": {"Type": "AWS::Lambda::Permission", "Properties": {"Action": "lambda:InvokeFunction", "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:aws:execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "ApiGatewayApi"}, "/*/*"]]}}}, "LambdaFunction": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Ref": "AwsServerlessExpressS3Bucket"}, "S3Key": {"Ref": "LambdaFunctionS3Key"}}, "FunctionName": "AwsServerlessExpressFunction", "Handler": "lambda.handler", "MemorySize": "AwsLambdaMemorySize", "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs4.3", "Timeout": 90}}}, "Outputs": {"LambdaFunctionConsoleUrl": {"Description": "Console URL for the Lambda Function.", "Value": {"Fn::Join": ["", ["https://", {"Ref": "AWS::Region"}, ".console.aws.amazon.com/lambda/home?region=", {"Ref": "AWS::Region"}, "#/functions/", {"Ref": "LambdaFunction"}]]}}, "ApiGatewayApiConsoleUrl": {"Description": "Console URL for the API Gateway API's Stage.", "Value": {"Fn::Join": ["", ["https://", {"Ref": "AWS::Region"}, ".console.aws.amazon.com/apigateway/home?region=", {"Ref": "AWS::Region"}, "#/apis/", {"Ref": "ApiGatewayApi"}, "/stages/prod"]]}}, "ApiUrl": {"Description": "Invoke URL for your API. Clicking this link will perform a GET request on the root resource of your API.", "Value": {"Fn::Join": ["", ["https://", {"Ref": "ApiGatewayApi"}, ".execute-api.", {"Ref": "AWS::Region"}, ".amazonaws.com/prod/"]]}}}}