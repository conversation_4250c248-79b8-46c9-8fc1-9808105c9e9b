import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneStateRetryHandler } from "./_Utils";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class CollectionsConfirm extends PayByPhoneStateBase {
  @PayByPhoneStateRetryHandler(
    3,
    PayByPhoneState.CollectionsPrompt,
    [PayByPhoneState.CollectionsConfirm],
    PayByPhoneState.DisconnectCall,
    "pay-by-phone.max-retry")
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, request } = context;
    const customerResponse = request.Digits;
    const noResponseFromCustomer = customerResponse === undefined || customerResponse.length === 0;
    const makePaymentResponse = customerResponse === "1";
    const transferToAgentResponse = customerResponse === "2";
    const optOutResponse = customerResponse === "9";
    const invalidResponse = (!makePaymentResponse && !transferToAgentResponse && !optOutResponse);
    const invalidMakePaymentResponse = makePaymentResponse && !storage.payByPhoneAllowed;
    const shouldRepeatPrompt = noResponseFromCustomer || invalidResponse || invalidMakePaymentResponse;
    if (shouldRepeatPrompt) {
      return { nextState: PayByPhoneState.CollectionsPrompt };
    }

    if(makePaymentResponse && storage.payByPhoneAllowed) {
      const tenant = await this.services.integrationService.getTenantById(storage.tenantId!);
      if(!tenant) {
        return { nextState: PayByPhoneState.TransferToAgent };
      }
      storage.selectedTenant = tenant;
      storage.matchedTenants = [tenant];
      const response = await this.services.integrationService.getLedgerData(storage.tenantId!, {skipCache: false});
      storage.selectedUnits = response.items;
      storage.selectedUnits.forEach((unit) => {
        unit.tenant_id_es = storage.tenantId!;
      });
      return { nextState: PayByPhoneState.SayAmountDue };
    }

    if(transferToAgentResponse) {
      return { nextState: PayByPhoneState.TransferToAgent };
    }

    return { nextState: PayByPhoneState.CustomerOptOut };
  }
}
