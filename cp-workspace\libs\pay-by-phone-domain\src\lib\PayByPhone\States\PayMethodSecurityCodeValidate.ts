import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import * as CardValidator from 'card-validator';
import { PaymentToken } from '@cp-workspace/shared';
@Injectable()
export class PayMethodSecurityCodeValidate extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. If security code is invalid
     * 1a. Transition to PayMethodSecurityCodePrompt
     * 2. If security code is valid
     * 2a. Compose TwiML to ask for confirmation
     * 2b. Compose TwiML to gather the input
     * 2c. Transition to PayMethodSecurityCodeConfirm
     */

    const { request, twilioResponse, storage } = context;
    const coreClient = this.services.coreService;
    const enteredSecurityCode = request.Digits;
    const allowedDigitRange = [3,4];

    if (!enteredSecurityCode || !allowedDigitRange.includes(enteredSecurityCode.length)) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.PayMethodSecurityCodePrompt };
    }

    if (!storage.paymentToken && storage.useSavedCardId) {
      storage.paymentToken = {} as PaymentToken;
    }

    const requestToken: PaymentToken = {
      securityCode: enteredSecurityCode,
    };

    storage.paymentToken!.securityCode = (await coreClient.encodePaymentToken(requestToken)).securityCode;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      numDigits: 1,
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.security-code-confirm',
      locale: storage.locale,
      i18nOptions: { args: [{ enteredCvc: enteredSecurityCode.split('').join(' ') }] }
    }]);


    return { nextState: PayByPhoneState.PayMethodSecurityCodeConfirm };
  }
}
