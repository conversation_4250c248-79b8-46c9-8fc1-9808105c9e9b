import { PayMethodSecurityCodePrompt } from './PayMethodSecurityCodePrompt';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodSecurityCodePrompt', () => {
  let service: PayMethodSecurityCodePrompt;

  beforeEach(() => {
    service = new PayMethodSecurityCodePrompt();
  });

  it('should gather security code input and transition to PayMethodSecurityCodeValidate', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '123',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      finishOnKey: "*",
      timeout: 10
    }, [{
      messageId: 'pay-by-phone.enter-ccv',
      locale: context.storage.locale
    }]);
    expect(result.nextState).toBe(
      PayByPhoneState.PayMethodSecurityCodeValidate
    );
  });
});
