# Generators

This library contains various code generators and utility tools for automating development tasks.

## Available Generators

The following generators are available:

1. [State Diagram Code Generator](src/lib/state-diagram-code/README.md) - Converts Mermaid.js state diagrams into TypeScript code artifacts
2. [Dependency Analysis Tool](src/lib/dependency-analysis/README.md) - Analyzes bundle files to extract and manage dependencies
3. [I18n Type Generator](src/lib/I18n/README.md) - Generates TypeScript type definitions from internationalization files

## Common Utilities

The generators share common utilities defined in `src/lib/common.ts`, which include:

- File saving functionality
- Case conversion utilities
- Directory creation helpers
- Type definitions for code artifacts

## Adding New Generators

To add a new generator:

1. Create a new directory under `src/lib/`
2. Implement your generator logic
3. Use the common utilities where appropriate
4. Add documentation in a README.md file
5. Update this main README.md to include your new generator
