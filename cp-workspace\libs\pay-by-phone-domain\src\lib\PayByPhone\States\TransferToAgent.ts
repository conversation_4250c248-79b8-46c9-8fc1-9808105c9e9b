import { Injectable } from "@nestjs/common";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { PayByPhoneStateContext, PayByPhoneStateBase, ExitPayByPhoneStateHandlerResponse } from "../PayByPhone.model";

@Injectable()
export class TransferToAgent extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<ExitPayByPhoneStateHandlerResponse> {
    return {
      redirectUrl: context.storage.transferToAgentUrl,
    };
  }
}
